package com.glory.mes.pvc.mlot.compmaterialaction;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.pvc.mlot.materialaction.PvcMLotReceiveDialog;

public class PvcCompMLotReceiveDialog extends PvcMLotReceiveDialog {

	private static final String FIELD_WOID = "woId";

	protected RefTableField woIdField;

	public PvcCompMLotReceiveDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		woIdField = sourceMLotInfoField.getFieldByControlId(FIELD_WOID, RefTableField.class);
	}

	@Override
	protected void okPressed() {
		try {
			List<Object> objects = (List<Object>) receiveMLotActionField.getListTableManager().getInput();
			if (objects == null || objects.size() == 0) {
				UI.showInfo(Message.getString("mm.please_add_mlot"));
				return;
			}

			if (!UI.showConfirm(Message.getString("common.submit_confirm"))) {
				return;
			}
			MMManager mmManager = Framework.getService(MMManager.class);

			for (Object object : objects) {
				MLot mlot = (MLot) object;

				// �Դ��������������б��
				Map udf = mlot.getUdf();
				udf.put("isVirtual", "Y");
				udf.put("isRework", "N");

				MLotAction mLotAction = new MLotAction();
				mLotAction.setMainQty(mlot.getTransMainQty());
				List<MLot> receiveMLotList = new ArrayList<MLot>();
				receiveMLotList.add(mlot);
				mmManager.receiveMLots2Warehouse(receiveMLotList, mLotAction, Env.getSessionContext());
			}

			UI.showInfo(Message.getString("common.receive.success"));
			setReturnCode(OK);
			close();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}