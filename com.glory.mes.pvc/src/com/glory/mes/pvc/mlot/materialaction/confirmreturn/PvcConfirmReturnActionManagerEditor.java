package com.glory.mes.pvc.mlot.materialaction.confirmreturn;


import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.state.model.MaterialState;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.pvc.client.PvcMLotManager;

public class PvcConfirmReturnActionManagerEditor extends PvcGlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.mlot.materialaction.confirmreturn.PvcConfirmReturnActionManagerEditor";
	
	private static final String EVENT_ID_CANCEL_RETURN = "CANCELRETURN";

	private static final String FIELD_MLOTQUERY = "mlotQuery";

	private static final String BUTTON_APPROVE = "approve";
	private static final String BUTTON_UNAPPROVE = "unapprove";

	protected QueryFormField mlotQueryField;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		mlotQueryField = form.getFieldByControlId(FIELD_MLOTQUERY, QueryFormField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_APPROVE), this::approveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNAPPROVE), this::unapproveAdapter);
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void approveAdapter(Object object) {
		try {
			List<Object> objects = mlotQueryField.getQueryForm().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			
			List<MLot> checkMlots = (List) objects.stream().map(selectObj -> (MLot) selectObj)
					.collect(Collectors.toList());
			
			MMManager mmManager = Framework.getService(MMManager.class);
			PvcMLotManager mLotManager = Framework.getService(PvcMLotManager.class);
			String[] warehouseInfo = checkMlots.get(0).getLotComment().split(";");
			boolean isReturnEmpty = checkMlots.get(0).getReservedMainQty().compareTo(BigDecimal.ZERO) == 0 ? true : false;
			
			if (!isReturnEmpty) {
				for (MLot mLot : checkMlots) {
					List<MLotStorage> storages = mmManager.getLotStorages(mLot.getObjectRrn().longValue());
					if (CollectionUtils.isNotEmpty(storages)) {
						if (storages.size() > 1) {
							UI.showWarning(Message.getString("wms.lot_in_multi_warehouse_or_storage"));
							return;
						}
						MLotStorage storage = (MLotStorage) storages.get(0);
						adManager.deleteEntity(storage, Env.getSessionContext());
					}
					mLot.setPreState(MaterialState.STATE_IN);
					mLotManager.saveMLot(mLot);
					
					mLot.setTransMainQty(mLot.getReservedMainQty());
				}
			}
			
			String storageType = null;
			String storageId = null;
			if (warehouseInfo.length != 1) {
				storageType = warehouseInfo[1];
				storageId = warehouseInfo[2];
			}
			
			if (isReturnEmpty) {
				checkMlots = mLotManager.returnEmptyMLots(checkMlots, new MLotAction(), Long.valueOf(warehouseInfo[0]), null, storageType, storageId, Env.getSessionContext());
			} else {
				checkMlots = mmManager.returnMLots(checkMlots, MLot.RECEIVE_TYPE_IN, new MLotAction(), Long.valueOf(warehouseInfo[0]), null, storageType, storageId, Env.getSessionContext());
				checkMlots.forEach(mLot -> {
					mLot.setLotComment(null);
					mLot.setReservedMainQty(BigDecimal.ZERO);
					mLotManager.saveMLot(mLot);
				});
			}
			
			UI.showInfo(Message.getString("common.operation_successed"));
			refreshAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void unapproveAdapter(Object object) {
		try {
			List<Object> objects = mlotQueryField.getQueryForm().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			
			List<MLot> checkMlots = (List) objects.stream().map(selectObj -> (MLot) selectObj)
					.collect(Collectors.toList());
			
			MMManager mmManager = Framework.getService(MMManager.class);
			PvcMLotManager pvcMLotManager = Framework.getService(PvcMLotManager.class);
			for (MLot mLot : checkMlots) {
				//�޸Ļ�Դ״̬
				mLot = mmManager.changeMLotState(mLot, EVENT_ID_CANCEL_RETURN, mLot.getPreState(), Env.getSessionContext());
				mLot.setReservedMainQty(BigDecimal.ZERO);
				mLot.setLotComment(null);
				pvcMLotManager.saveMLot(mLot);
			}
			
			UI.showInfo(Message.getString("common.operation_successed"));
			refreshAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void refreshAdapter(Object object) {
		mlotQueryField.getQueryForm().refresh();
		mlotQueryField.refresh();
	}

}