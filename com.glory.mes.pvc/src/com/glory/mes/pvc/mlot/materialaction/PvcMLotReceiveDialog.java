package com.glory.mes.pvc.mlot.materialaction;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.model.Material;
import com.google.common.collect.Lists;

public class PvcMLotReceiveDialog extends GlcBaseDialog { 

	private static final String FIELD_SOURCEMLOTINFO = "sourceMLotInfo";
	private static final String FIELD_MLOTID = "mLotId";
	private static final String FIELD_RECEIVEMLOTACTION = "receiveMLotAction";
	private static final String FIELD_MATERIALNAME = "materialName";
	private static final String FIELD_MATERIALDESC = "materialDesc";
	private static final String FIELD_MATERIALVERSION = "materialVersion";
	private static final String FIELD_MATERIALTYPE = "materialType";
	private static final String FIELD_MAINQTY = "mainQty";
	private static final String FIELD_UOMID = "uomId";
	private static final String FIELD_PARTNERLOTID = "partnerLotId";
	private static final String FIELD_LOTCOMMENT = "lotComment";
	private static final String FIELD_TRANSWAREHOUSERRN = "transWarehouseRrn";
	private static final String FIELD_TRANSSTORAGEID = "transStorageId";
	private static final String FIELD_TRANSSTORAGETYPE = "transStorageType";

	protected EntityFormField sourceMLotInfoField;
	protected TextField mLotIdField;
	protected ListTableManagerField receiveMLotActionField;
	protected RefTableField materialNameField;
	protected TextField materialDescField;
	protected TextField materialVersionField;
	protected TextField materialTypeField;
	protected TextField mainQtyField;
	protected TextField uomIdField;
	protected TextField partnerLotIdField;
	protected TextField lotCommentField;
	protected RefTableField transWarehouseRrnField;
	protected RefTableField transStorageIdField;
	protected TextField transStorageTypeField;
	
	protected List<MLot> mLotList = Lists.newArrayList();
	
	public PvcMLotReceiveDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		sourceMLotInfoField = form.getFieldByControlId(FIELD_SOURCEMLOTINFO, EntityFormField.class);
		mLotIdField = form.getFieldByControlId(FIELD_MLOTID, TextField.class);
		receiveMLotActionField = form.getFieldByControlId(FIELD_RECEIVEMLOTACTION, ListTableManagerField.class);
		materialNameField = sourceMLotInfoField.getFieldByControlId(FIELD_MATERIALNAME, RefTableField.class);
		materialDescField = sourceMLotInfoField.getFieldByControlId(FIELD_MATERIALDESC, TextField.class);
		materialVersionField = sourceMLotInfoField.getFieldByControlId(FIELD_MATERIALVERSION, TextField.class);
		materialTypeField = sourceMLotInfoField.getFieldByControlId(FIELD_MATERIALTYPE, TextField.class);
		mainQtyField = sourceMLotInfoField.getFieldByControlId(FIELD_MAINQTY, TextField.class);
		uomIdField = sourceMLotInfoField.getFieldByControlId(FIELD_UOMID, TextField.class);
		partnerLotIdField = sourceMLotInfoField.getFieldByControlId(FIELD_PARTNERLOTID, TextField.class);
		lotCommentField = sourceMLotInfoField.getFieldByControlId(FIELD_LOTCOMMENT, TextField.class);
		transWarehouseRrnField = sourceMLotInfoField.getFieldByControlId(FIELD_TRANSWAREHOUSERRN, RefTableField.class);
		transStorageIdField = sourceMLotInfoField.getFieldByControlId(FIELD_TRANSSTORAGEID, RefTableField.class);
		transStorageTypeField = sourceMLotInfoField.getFieldByControlId(FIELD_TRANSSTORAGETYPE, TextField.class);
		
		changeMLotIdEvent();
	}
	
	// ע������ſؼ�enter�¼�
	protected void changeMLotIdEvent() {
		mLotIdField.getTextControl().addKeyListener(new KeyAdapter() {
			@SuppressWarnings("unchecked")
			@Override
			public void keyPressed(KeyEvent event) {
				Text mLotIdText = ((Text) event.widget);
				mLotIdText.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String mLotId = mLotIdText.getText();
					if (StringUtil.isEmpty(mLotId)) {
						UI.showError(Message.getString("mm.please_enter_mlot_id"));
						return;
					} else {
						try {
							if (sourceMLotInfoField.validate()) {
								// У���Ƿ��Ѿ����б���
								List<MLot> mlots = (List<MLot>) (Object) receiveMLotActionField.getListTableManager().getInput();
								Boolean flag = false;
								for (MLot mlot : mlots) {
									if (mlot.getmLotId().equals(mLotId)) {
										flag = true;
										break;
									}
								}
								if (flag) {
									UI.showError(Message.getString("wip.box_already_exists"));
									mLotIdField.setText("");
									mLotIdField.getTextControl().forceFocus();
									return;
								}

								MMManager mmManager = Framework.getService(MMManager.class);
								MLot entityField = (MLot) sourceMLotInfoField.getValue();
								
								// ��ֹ���������ظ�
								Material material = mmManager.getMaterial(Env.getOrgRrn(), entityField.getMaterialName());
								if (material == null) {
									UI.showError(Message.getString("mm.material_not_found"));
									return;
								}
								entityField.setBatchType(material.getBatchType());
								
								MLot existMLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), mLotId);
								if (existMLot != null) {
									if (Material.BATCH_TYPE_LOT.equals(material.getBatchType()) || Material.BATCH_TYPE_LOT.equals(existMLot.getBatchType())) {
										UI.showError(Message.getString("wip.changeId_have"));
										mLotIdField.setText("");
										return;
									}
									if(!existMLot.getWoId().equals(entityField.getWoId())) {
										UI.showError(Message.getString("error.work_order_cannot_received"));
										return;
									}
								}
								
			                    MLot mlot = new MLot();
			                    PropertyUtil.copyProperties(mlot, entityField);
			                    mlot.setmLotId(mLotId);
			                    mlot.setMainQty(entityField.getMainQty());
			                    mlot.setTransMainQty(entityField.getTransMainQty());
								entityField.setTransMainQty(entityField.getMainQty());
								mLotList.add(mlot);
								receiveMLotActionField.getListTableManager().setInput(mLotList);
								mLotIdField.setText("");
								mLotIdField.getTextControl().forceFocus();
							}
						} catch (Exception e) {
							UI.showError(Message.getString("common.system_occur_error"));
						}
					}
					break;
				}
			}
		});
	}
	
	@SuppressWarnings("unchecked")
	protected void okPressed() {
		try {
			List<Object> objects = (List<Object>) receiveMLotActionField.getListTableManager().getInput();
			if (objects == null || objects.size() == 0) {
				UI.showInfo(Message.getString("mm.please_add_mlot"));
				return;
			}
			
			if (!UI.showConfirm(Message.getString("common.submit_confirm"))) {
				return;
			}
			MMManager mmManager = Framework.getService(MMManager.class);
			
			for (Object object : objects) {
				MLot mlot = (MLot) object;
				
				MLotAction mLotAction = new MLotAction();
				mLotAction.setMainQty(mlot.getTransMainQty());
				List<MLot> receiveMLotList = new ArrayList<MLot>();
				receiveMLotList.add(mlot);
				mmManager.receiveMLots2Warehouse(receiveMLotList, mLotAction, Env.getSessionContext());
			}

			UI.showInfo(Message.getString("common.receive.success"));
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
}