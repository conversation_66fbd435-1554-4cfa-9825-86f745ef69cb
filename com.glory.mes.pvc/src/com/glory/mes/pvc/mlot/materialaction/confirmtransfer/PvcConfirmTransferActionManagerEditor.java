package com.glory.mes.pvc.mlot.materialaction.confirmtransfer;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.state.model.MaterialEvent;
import com.glory.mes.pvc.PvcGlcEditor;
import com.glory.mes.pvc.client.PvcMLotManager;

public class PvcConfirmTransferActionManagerEditor extends PvcGlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.pvc/com.glory.mes.pvc.mlot.materialaction.confirmtransfer.PvcConfirmTransferActionManagerEditor";

	private static final String FIELD_MLOTQUERY = "mlotQuery";

	private static final String BUTTON_APPROVE = "approve";
	private static final String BUTTON_UNAPPROVE = "unapprove";

	protected QueryFormField mlotQueryField;
	
	private static final String EVENT_ID_CANCEL_TRANSFER = "CANCELTRANSFER";

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		mlotQueryField = form.getFieldByControlId(FIELD_MLOTQUERY, QueryFormField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_APPROVE), this::approveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNAPPROVE), this::unapproveAdapter);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	private void approveAdapter(Object object) {
		try {
			List<Object> objects = mlotQueryField.getQueryForm().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			
			List<MLot> checkMlots = (List) objects.stream().map(selectObj -> (MLot) selectObj)
					.collect(Collectors.toList());
			
			MMManager mmManager = (MMManager) Framework.getService(MMManager.class);
			PvcMLotManager mLotManager = Framework.getService(PvcMLotManager.class);
			checkMlots = mLotManager.getMLotStorageByMLots(Env.getOrgRrn(), checkMlots);
			Map<String, MLotStorage> mLotStorageMap = new HashMap<String, MLotStorage>();
			String[] warehouseInfo = checkMlots.get(0).getLotComment().split(";");
			for (MLot mLot : checkMlots) {
				List<MLotStorage> storages = mmManager.getLotStorages(mLot.getObjectRrn().longValue());
				mLotStorageMap.put(mLot.getmLotId(), storages.get(0));
			}
			
			String storageType = null;
			String storageId = null;
			if (warehouseInfo.length != 1) {
				storageType = warehouseInfo[1];
				storageId = warehouseInfo[2];
			}
			
			checkMlots = mmManager.transferMLots(checkMlots, new MLotAction(), mLotStorageMap, Long.valueOf(warehouseInfo[0]), null, storageType, storageId, Env.getSessionContext());
			
			checkMlots.forEach(mLot -> {
				//�޸�״̬ΪOUT
				mLot = mmManager.changeMLotState(mLot, MaterialEvent.EVENT_INVENTORYOUT, null, Env.getSessionContext());
				mLot.setLotComment(null);
				mLotManager.saveMLot(mLot);
			});
			
			UI.showInfo(Message.getString("common.operation_successed"));
			refreshAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void unapproveAdapter(Object object) {
		try {
			List<Object> objects = mlotQueryField.getQueryForm().getCheckedObject();
			if (CollectionUtils.isEmpty(objects)) {
				UI.showInfo(Message.getString("common.select_object"));
				return;
			}
			
			List<MLot> checkMlots = (List) objects.stream().map(selectObj -> (MLot) selectObj)
					.collect(Collectors.toList());
			
			MMManager mmManager = Framework.getService(MMManager.class);
			PvcMLotManager pvcMLotManager = Framework.getService(PvcMLotManager.class);
			for (MLot mLot : checkMlots) {
				//�޸Ļ�Դ״̬
				mLot = mmManager.changeMLotState(mLot, EVENT_ID_CANCEL_TRANSFER, mLot.getPreState(), Env.getSessionContext());
				mLot.setLotComment(null);
				pvcMLotManager.saveMLot(mLot);
			}
			
			UI.showInfo(Message.getString("common.operation_successed"));
			refreshAdapter(null);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void refreshAdapter(Object object) {
		mlotQueryField.getQueryForm().refresh();
		mlotQueryField.refresh();
	}

}