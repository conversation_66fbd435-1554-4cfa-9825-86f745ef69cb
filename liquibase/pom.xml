<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.glory.mes</groupId>
		<artifactId>mes-pom</artifactId>
		<version>8.4.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>liquibase</artifactId>
	<name>liquibase</name>
	<description>数据库版本控制器</description>
	<packaging>ejb</packaging>
	
	<dependencies>
		<dependency>
			<groupId>com.glory.mes</groupId>
			<artifactId>wip</artifactId>
			<version>${mes.version}</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>		
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.7</version>
				<dependencies>
					<dependency>
						<groupId>org.apache.maven.shared</groupId>
						<artifactId>maven-filtering</artifactId>
						<version>1.3</version>
					</dependency>
				</dependencies>
			</plugin>	
			<plugin>
				<groupId>org.liquibase</groupId>
				<artifactId>liquibase-maven-plugin</artifactId>
				<version>3.9.0</version>
				<configuration>
					<!--properties文件路径，该文件记录了数据库连接信息等 -->
					<propertyFile>src/main/resources/db/liquibase.properties</propertyFile>
					<propertyFileWillOverride>true</propertyFileWillOverride>
				</configuration>
				<executions>
					<execution>
						<phase>update</phase>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>