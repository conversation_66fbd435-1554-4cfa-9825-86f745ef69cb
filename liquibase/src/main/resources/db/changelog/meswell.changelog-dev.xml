<?xml version="1.1" encoding="GBK" standalone="no"?>
<databaseChangeLog
	xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
	xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
	xmlns:pro="http://www.liquibase.org/xml/ns/pro"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.9.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.9.xsd">
	<changeSet id="MESWELL-20200713-CELL" author="Clark">			
	 	<createTable remarks="Cell表" tableName="WIP_CELL" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_CELL"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>     
            <column name="LOT_RRN" remarks="批次主键" type="NUMBER(19, 0)"/>
            <column name="COMPONENT_RRN" remarks="组件主键" type="NUMBER(19, 0)"/>
            <column name="CELL_ID" remarks="Cell编号" type="VARCHAR2(32 BYTE)"/>
            <column name="CELL_TYPE" remarks="Cell类型" type="VARCHAR2(32 BYTE)"/>
            <column name="PARENT_CELL_RRN" remarks="父Cell主键" type="NUMBER(19, 0)"/>
            <column name="SEQ_NO" remarks="序号" type="NUMBER(19, 0)"/>
            <column name="X_POSITION" remarks="Cell在X的位置(从0开始)" type="NUMBER(19, 0)"/>
            <column name="Y_POSITION" remarks="Cell在Y的位置(从0开始)" type="NUMBER(19, 0)"/>
            <column name="X_POSITION_ID" remarks="Cell在X的编号" type="VARCHAR2(32 BYTE)"/>
            <column name="Y_POSITION_ID" remarks="Cell在Y的编号" type="VARCHAR2(32 BYTE)"/>
            <column name="STATE" remarks="Cell状态" type="VARCHAR2(32 BYTE)"/>
            <column name="MAIN_QTY" remarks="主数量" type="NUMBER(19, 0)"/>
            <column name="SUB_QTY" remarks="子数量" type="NUMBER(19, 0)"/>
            <column name="GRADE1" remarks="等级1" type="VARCHAR2(32 BYTE)"/>
            <column name="GRADE2" remarks="等级2" type="VARCHAR2(32 BYTE)"/>
            <column name="JUDGE1" remarks="判定1" type="VARCHAR2(32 BYTE)"/>
            <column name="JUDGE2" remarks="判定2" type="VARCHAR2(32 BYTE)"/>
            <column name="SUBSTRATE_ID1" remarks="底材编号1" type="VARCHAR2(32 BYTE)"/>
            <column name="SUBSTRATE_ID2" remarks="底材编号2" type="VARCHAR2(32 BYTE)"/>
            <column name="CELL_COMMENT" remarks="备注" type="VARCHAR2(128 BYTE)"/>
            <column name="ACTION_CODE" remarks="动作码" type="VARCHAR2(32 BYTE)"/>
            <column name="ACTION_COMMENT" remarks="动作备注" type="VARCHAR2(128 BYTE)"/>
            <column name="RESERVED1" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED2" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED3" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED4" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED5" type="VARCHAR2(32 BYTE)"/>        
	    </createTable>	    
									 		    
	    <createTable remarks="Cell历史表" tableName="WIP_CELL_HIS" tablespace="TS_MES_HIS_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_CELL_HIS"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TYPE" remarks="事务类型" type="VARCHAR2(32 BYTE)"/>
           	<column name="TRANS_TIME" remarks="事务类型" type="date"/>
           	<column name="HISTORY_SEQ" remarks="事务号" type="VARCHAR2(32 BYTE)"/>
           	<column name="HISTORY_SEQ_NO" remarks="事务序号" type="NUMBER(19, 0)"/>          	
            <column name="LOT_RRN" remarks="批次主键" type="NUMBER(19, 0)"/>
            <column name="COMPONENT_RRN" remarks="组件主键" type="NUMBER(19, 0)"/>
            <column name="CELL_ID" remarks="Cell编号" type="VARCHAR2(32 BYTE)"/>
            <column name="CELL_TYPE" remarks="Cell类型" type="VARCHAR2(32 BYTE)"/>
            <column name="PARENT_CELL_RRN" remarks="父Cell主键" type="NUMBER(19, 0)"/>
            <column name="SEQ_NO" remarks="序号" type="NUMBER(19, 0)"/>
            <column name="X_POSITION" remarks="Cell在X的位置(从0开始)" type="NUMBER(19, 0)"/>
            <column name="Y_POSITION" remarks="Cell在Y的位置(从0开始)" type="NUMBER(19, 0)"/>
            <column name="X_POSITION_ID" remarks="Cell在X的编号" type="VARCHAR2(32 BYTE)"/>
            <column name="Y_POSITION_ID" remarks="Cell在Y的编号" type="VARCHAR2(32 BYTE)"/>
            <column name="STATE" remarks="Cell状态" type="VARCHAR2(32 BYTE)"/>
            <column name="MAIN_QTY" remarks="主数量" type="NUMBER(19, 0)"/>
            <column name="SUB_QTY" remarks="子数量" type="NUMBER(19, 0)"/>
            <column name="GRADE1" remarks="等级1" type="VARCHAR2(32 BYTE)"/>
            <column name="GRADE2" remarks="等级2" type="VARCHAR2(32 BYTE)"/>
            <column name="JUDGE1" remarks="判定1" type="VARCHAR2(32 BYTE)"/>
            <column name="JUDGE2" remarks="判定2" type="VARCHAR2(32 BYTE)"/>
            <column name="SUBSTRATE_ID1" remarks="底材编号1" type="VARCHAR2(32 BYTE)"/>
            <column name="SUBSTRATE_ID2" remarks="底材编号2" type="VARCHAR2(32 BYTE)"/>
            <column name="CELL_COMMENT" remarks="备注" type="VARCHAR2(128 BYTE)"/>
            <column name="ACTION_CODE" remarks="动作码" type="VARCHAR2(32 BYTE)"/>
            <column name="ACTION_COMMENT" remarks="动作备注" type="VARCHAR2(128 BYTE)"/>
            <column name="RESERVED1" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED2" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED3" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED4" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED5" type="VARCHAR2(32 BYTE)"/>        
	    </createTable>	    	   
  
	    <createTable remarks="Cell缺陷表" tableName="WIP_CELL_DEFECT" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_CELL_DEFECT"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>      
            <column name="CELL_RRN" remarks="Cell主键" type="NUMBER(19, 0)"/>
            <column name="X_POSITION" remarks="缺陷位置X坐标" type="VARCHAR2(32 BYTE)"/>
            <column name="Y_POSITION" remarks="缺陷位置Y坐标" type="VARCHAR2(32 BYTE)"/>          
            <column name="DEFECT_CODE" remarks="缺陷码" type="VARCHAR2(32 BYTE)"/>
 			<column name="DEFECT_COMMENT" remarks="缺陷备注" type="VARCHAR2(128 BYTE)"/>
            <column name="RESERVED1" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED2" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED3" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED4" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED5" type="VARCHAR2(32 BYTE)"/>        
	    </createTable>
	    
	    <createTable remarks="CellID生成表" tableName="WIP_CELL_ID_GENERATOR" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_CELL_ID_GENERATOR"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>              
            <column name="NAME" remarks="名称" type="VARCHAR2(32 BYTE)"/>
            <column name="DESCRIPTION" remarks="描述" type="VARCHAR2(32 BYTE)"/>
            <column name="AXIS_DIRECTION" remarks="轴线方向" type="VARCHAR2(32 BYTE)"/>          
            <column name="ID_TYPE" remarks="Id规则类型" type="VARCHAR2(32 BYTE)"/>
 			<column name="CELL_COUNT" remarks="合计数量" type="NUMBER(19, 0)"/>
 			<column name="X_COUNT" remarks="X轴最大值" type="NUMBER(19, 0)"/>
 			<column name="Y_COUNT" remarks="Y轴最大值" type="NUMBER(19, 0)"/>
 			<column name="X_ID_CODE" remarks="X轴命名规则" type="VARCHAR2(32 BYTE)"/>
 			<column name="Y_ID_CODE" remarks="Y轴命名规则" type="VARCHAR2(32 BYTE)"/>			
 			<column name="CELL_ID_STRING_FORMAT" remarks="使用StringFormat功能生成Id" type="VARCHAR2(32 BYTE)"/>
 			<column name="CELL_ID_GENERATOR" remarks="使用IdGenerator生成Id" type="VARCHAR2(32 BYTE)"/>      
	    </createTable>	    
	</changeSet>
	
	<changeSet id="MESWELL-20200714-LOTPROCESSOR" author="Clark">
		<sqlFile path="sql/WIP-20200707-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
	    <sqlFile path="sql/WIP-20200709-001.sql" encoding="GBK" relativeToChangelogFile="true"/>    
	</changeSet>
	
	<changeSet id="MESWELL-20200722-RAS" author="WangXiang">
		<sqlFile path="sql/RAS-20200722-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
	</changeSet>
	
	<changeSet id="MESWELL-20200724-RAS" author="YanChao">
		<sqlFile path="sql/RAS-20200724-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
	</changeSet>
	
	<changeSet id="MESWELL-20200731-init-lot-tool-id" author="HeTao">
		<sqlFile path="sql/BAS-20200731-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>修改批次ID规则，之前的ID规则不能生成正常的ID；添加Tool ID规则。</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200805-WORKSTATION" author="Clark">		
		<addColumn tableName="BAS_WORK_STATION">
			<column name="LABEL_CONFIG_NAME" remarks="连接标签系统配置名称" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<sqlFile path="sql/BAS-20200805-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
	</changeSet>

	<changeSet id="MESWELL-20200806-mm-message" author="HeTao">
		<sqlFile path="sql/MM-20200806-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>增加物料相关message</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200806-wip-processor-batch" author="DaiWenBin">
		<sqlFile path="sql/WIP_20200806-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>批次批量动作补充message，动态表</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200807-future-flow-action-modify" author="HeTao">
		<sqlFile path="sql/WIP-20200807-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>批次未来流程变更修改</comment>
	</changeSet>

	<changeSet id="MESWELL-20200810-PACKAGEHIERARCHY" author="Zhougelong">	
	       <createTable remarks="包装层级管理表" tableName="MM_PACKAGE_HIERARCHY" tablespace="TS_MES_DAT">
	           <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                  <constraints nullable="false" primaryKey="true" primaryKeyName="PK_MM_PACKAGE_HIERARCHY"/>
               </column>	
                   <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
                   <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
                   <column name="CREATED" remarks="创建时间" type="date"/>
                   <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
                   <column name="UPDATED" remarks="更新时间" type="date"/>
                   <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
                   <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>
                   <column name="NAME" remarks="名称" type="VARCHAR2(32 BYTE)"/>
                   <column name="DESCRIPTION" remarks="描述" type="VARCHAR2(300 BYTE)"/>
                   <column name="PACKAGE_LEVEL" remarks="包装等级" type="NUMBER(19)"/>
                   <column name="RELATION_TYPE" remarks="包装关系" type="VARCHAR2(32 BYTE)"/>
                   <column name="DEFAULT_PACKAGE_TYPE" remarks="包装类型" type="VARCHAR2(32 BYTE)"/>
                   <column name="OBJECT_TYPE" remarks="包装类型LOT/Mlot" type="VARCHAR2(32 BYTE)"/>
			       <column name="MAIN_MAT_TYPE" remarks="物料类型" type="VARCHAR2(32 BYTE)"/>
		       	   <column name="IS_OPTIONAL" remarks="预留栏位" type="VARCHAR2(32 BYTE)"/>
              </createTable>
    </changeSet>
    
    <changeSet id="MESWELL-20200825-PACKAGEHIERARCHY" author="Zhougelong">	
		<addUniqueConstraint constraintName="UK_MM_PACKAGE_HIERARCHY" columnNames="NAME,PACKAGE_LEVEL" tableName="MM_PACKAGE_HIERARCHY" tablespace="TS_MES_IDX">
		</addUniqueConstraint>
    </changeSet>
    
	<changeSet id="MESWELL-20200811-MM-FUNCTIONMENU" author="tangjiacheng">
		<sqlFile path="sql/MM-20200811-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>菜单子功能位置更改</comment>
	</changeSet>

    	<changeSet id="MESWELL-20200811-MM-MMLOT" author="tangjiacheng">
		<sqlFile path="sql/MM-20200811-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>新做物料批管理动态表和权限</comment>
	</changeSet>
    
   	<changeSet id="MESWELL-20200810-wip-processor-batch" author="DaiWenBin">
		<sqlFile path="sql/WIP-20200810-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>批次批量动作补充动态表</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200812-pack-query" author="HeTao">
		<sqlFile path="sql/MM-20200812-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>包装查询功能</comment>
	</changeSet>

    <changeSet id="MESWELL-20200813-MLOTPROCESSOR" author="Clark">
		<sqlFile path="sql/MM-20200813-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>物料批次批量动作处理功能</comment>
	</changeSet>
	
	<changeSet id="RAS-20200814-MM-KITTING" author="ZouYiSong">
		 <createTable tableName="RAS_POSITION_SET_LINE" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_RAS_POSITION_SET_LINE"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="POSITION_SET_RRN" remarks="设备上料位置集表主键" type="NUMBER(19, 0)"/>
            <column name="POSITION_NAME" remarks="设备上料位置名称" type="VARCHAR2(64 BYTE)"/>
            <column name="POSITION_DESC" remarks="设备上料位置描述" type="VARCHAR2(256 BYTE)"/>
            <column name="SEQ_NO" remarks="顺序" type="NUMBER(19, 0)"/>
            <column name="MATERIAL_TYPE_SRC" remarks="物料类型" type="VARCHAR2(512 BYTE)"/>      
            <column name="REPLENISH" remarks="是否补充" type="VARCHAR2(1 BYTE)"/>
            <column name="GRADE_SRC" remarks="等级" type="VARCHAR2(64 BYTE)"/>
            <column name="COLOR" remarks="颜色" type="VARCHAR2(32 BYTE)"/>
            <column name="MATERIAL_CATEGORY" remarks="物料品种" type="VARCHAR2(32 BYTE)"/>
            <column name="CONSUME_TYPE" remarks="消耗类型" type="VARCHAR2(32 BYTE)"/>
            <column name="REPLENISH_TYPE" remarks="补充类型" type="VARCHAR2(32 BYTE)"/>
            <column name="POSITION_GROUP" remarks="位置组" type="VARCHAR2(32 BYTE)"/>
	    </createTable>
	    <createTable tableName="RAS_POSITION_SET_LINE_HIS" tablespace="TS_MES_HIS_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)"/>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="POSITION_SET_HIS_RRN" remarks="设备上料位置集历史表主键" type="NUMBER(19, 0)"/>
            <column name="POSITION_SET_RRN" remarks="设备上料位置集表主键" type="NUMBER(19, 0)"/>
            <column name="POSITION_NAME" remarks="设备上料位置名称" type="VARCHAR2(64 BYTE)"/>
            <column name="POSITION_DESC" remarks="设备上料位置描述" type="VARCHAR2(256 BYTE)"/>
            <column name="SEQ_NO" remarks="顺序" type="NUMBER(19, 0)"/>
            <column name="MATERIAL_TYPE_SRC" remarks="物料类型" type="VARCHAR2(512 BYTE)"/>      
            <column name="REPLENISH" remarks="是否补充" type="VARCHAR2(1 BYTE)"/>
            <column name="GRADE_SRC" remarks="等级" type="VARCHAR2(64 BYTE)"/>
            <column name="COLOR" remarks="颜色" type="VARCHAR2(32 BYTE)"/>
            <column name="MATERIAL_CATEGORY" remarks="物料品种" type="VARCHAR2(32 BYTE)"/>
            <column name="CONSUME_TYPE" remarks="消耗类型" type="VARCHAR2(32 BYTE)"/>
            <column name="REPLENISH_TYPE" remarks="补充类型" type="VARCHAR2(32 BYTE)"/>
            <column name="POSITION_GROUP" remarks="位置组" type="VARCHAR2(32 BYTE)"/>
	    </createTable>
	    <comment>重构RAS_POSITION_SET_LINE表/RAS_POSITION_SET_LINE_HIS(请手动执行MM-20200814-002.sql重构表结构)</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200814-mm-kittng" author="ZouYiSong">
		<sqlFile path="sql/RAS-20200814-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<sqlFile path="sql/MM-20200814-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>物料Kit功能</comment>
	</changeSet>
	
	  <changeSet id="MESWELL-20200817-TOOLPROCESSOR" author="DaiWenBin">
		<sqlFile path="sql/RAS-20200817-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>治具批量动作处理功能</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200817-MATERIAL-STATEMODEL" author="ZouYiSong">
		<sqlFile path="sql/MM-20200817-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>物料状态管理相关功能</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200819-PACK-QUERY" author="HeTao">
		<sqlFile path="sql/WIP-20200819-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>包装查询SQL补充</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200819-MLOTCARRIER" author="Clark">
		<sqlFile path="sql/MM-20200819-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>物料批绑定载具</comment>
	</changeSet>
	<changeSet id="MESWELL-20200820-MLOTIQC" author="Clark">
		<sqlFile path="sql/MM-20200820-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>物料批绑定载具</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200821-PACK-QUERY" author="HeTao">
		<sqlFile path="sql/WIP-20200821-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>包装查询修改</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200824-MLOTPROCESSOR" author="Clark">
		<sqlFile path="sql/MM-20200824-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>隐藏物料批一些功能，采用物料批动作管理</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200824-MM-KITTING" author="ZouYiSong">
		<sqlFile path="sql/MM-20200824-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>物料Kitting修改</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200827-MM-TOOL-BATCH-IN-OUT" author="HeTao">
		<sqlFile path="sql/MM-20200827-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>治具批量操作增加出入库功能</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200827-EDC-NEW-COLLECTION-TABEL" author="HeTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_SET_LINE" columnName="AD_TABLE_NAME"/>
			</not>
		</preConditions>
		
		<addColumn tableName="EDC_SET_LINE">
			<column name="AD_TABLE_NAME" remarks="动态表名称" type="VARCHAR2(64 BYTE)"/>
		</addColumn>
		<comment>新数据收集表结构</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20200827-EDC-NEW-COLLECTION" author="HeTao">
		<sqlFile path="sql/EDC-20200827-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>新数据收集功能</comment>
	</changeSet>
	
	<changeSet author="HeTao" id="MESWELL-202000907-EDC-MANUAL-SAMPLE-DATA">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="EDC_MANUAL_SAMPLE_DATA"/>
			</not>
		</preConditions>
        <createTable remarks="手动抽样数据表" tableName="EDC_MANUAL_SAMPLE_DATA" tablespace="TS_MES_DAT">
            <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建人" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>
            <column name="DOC_ID" remarks="工单ID" type="VARCHAR2(64 BYTE)"/>
            <column name="DOC_TYPE" remarks="类型" type="VARCHAR2(64 BYTE)"/>
            <column name="DOC_STATUS" remarks="工单状态" type="VARCHAR2(32 BYTE)"/>
            <column name="CREATED_USER" remarks="创建人" type="VARCHAR2(32 BYTE)"/>
            <column name="CREATED_DATE" remarks="创建时间" type="date"/>
            <column name="APPROVED_USER" remarks="审核人" type="VARCHAR2(32 BYTE)"/>
            <column name="APPROVED_DATE" remarks="审核时间" type="date"/>
            <column name="OWNER" remarks="管理者" type="VARCHAR2(32 BYTE)"/>
            <column name="SAMPLE_PLAN_RRN" remarks="抽样计划RRN" type="NUMBER(19, 0)"/>
            <column name="SAMPLE_PLAN_NAME" remarks="抽样计划名" type="VARCHAR2(32 BYTE)"/>
            <column name="SAMPLE_PLAN_DESC" remarks="抽样计划描述" type="VARCHAR2(64 BYTE)"/>
            <column name="SAMPLE_PLAN_TYPE" remarks="抽样计划类型" type="VARCHAR2(32 BYTE)"/>
            <column name="SAMPLE_DATE" remarks="抽样日期（不包含时间）" type="date"/>
            <column name="SAMPLE_TIME" remarks="抽样时间（不包含日期）" type="date"/>
            <column name="SAMPLE_SIZE" remarks="样本大小" type="NUMBER(9, 0)"/>
            <column name="SAMPLE_REASON" remarks="抽样原因" type="VARCHAR2(64 BYTE)"/>
            <column name="SHIFT_ID" remarks="班次" type="VARCHAR2(32 BYTE)"/>
            <column name="TEAM_ID" remarks="班别" type="VARCHAR2(32 BYTE)"/>
            <column name="LINE_ID" remarks="线别" type="VARCHAR2(32 BYTE)"/>
            <column name="EQUIPMENT_ID" remarks="设备ID" type="VARCHAR2(32 BYTE)"/>
            <column name="SUB_EQUIPMENT_ID1" remarks="子设备1" type="VARCHAR2(32 BYTE)"/>
            <column name="SUB_EQUIPMENT_ID2" remarks="子设备2" type="VARCHAR2(32 BYTE)"/>
            <column name="LOT_ID" remarks="批次ID" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_DESC" remarks="工步描述" type="VARCHAR2(64 BYTE)"/>
            <column name="EDC_DATA_HIS_SEQ" remarks="采集数据RRN" type="VARCHAR2(64 BYTE)"/>
            <column name="RE_EDC_DATA_HIS_SEQ" remarks="重测数据RRN" type="VARCHAR2(64 BYTE)"/>
            <column name="JUDGE1" remarks="判定1" type="VARCHAR2(32 BYTE)"/>
            <column name="JUDGE2" remarks="判定2" type="VARCHAR2(32 BYTE)"/>
            <column name="COMMENTS" remarks="备注" type="VARCHAR2(256 BYTE)"/>
            <column name="RESERVED1" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED2" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED3" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED4" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED5" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED6" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED7" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED8" type="VARCHAR2(64 BYTE)"/>
        </createTable>
    </changeSet>
    
    <changeSet author="HeTao" id="MESWELL-202000907-EDC-MANUAL-SAMPLE-PLAN">
    	<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="EDC_MANUAL_SAMPLE_PLAN"/>
			</not>
		</preConditions>
        <createTable remarks="手动抽样计划表" tableName="EDC_MANUAL_SAMPLE_PLAN" tablespace="TS_MES_DAT">
            <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_EDC_MANAL_SAMPLE_PLAN" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域ID" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="对象是否可用" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者ID" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者ID" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>
            <column name="NAME" remarks="名称" type="VARCHAR2(32 BYTE)"/>
            <column name="DESCRIPTION" remarks="描述" type="VARCHAR2(64 BYTE)"/>
            <column name="VERSION" remarks="版本" type="NUMBER(19, 0)"/>
            <column name="STATUS" remarks="UnFrozen/Frozen/Active/InActive" type="VARCHAR2(32 BYTE)"/>
            <column name="ACTIVE_TIME" remarks="激活时间" type="date"/>
            <column name="ACTIVE_USER" remarks="激活者" type="VARCHAR2(32 BYTE)"/>
            <column name="SAMPLE_TYPE" remarks="抽样类型 BYDAY,按天抽,如每天抽几次 BYSHIFT,按班次抽,如每班次抽几次  BYTIME,按时间间隔抽,如没2小时抽一次" type="VARCHAR2(32 BYTE)"/>
            <column name="STYLE" type="NUMBER(1, 0)"/>
            <column name="SAMPLE_FREQUENCY" remarks="抽样频率" type="NUMBER"/>
            <column name="SAMPLE_SIZE" remarks="样本大小" type="NUMBER(8, 0)"/>
            <column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
            <column name="EDC_SET_RRN" remarks="采集项集RRN" type="NUMBER(19, 0)"/>
            <column name="INIT_INSPECTION_LEVEL" remarks="初始建议级别默认S" type="VARCHAR2(1 BYTE)"/>
            <column name="S2T_TYPE" remarks="标准到加严类型(Q连续数)" type="VARCHAR2(1 BYTE)"/>
            <column name="S2T_QUALIFICATION_SIZE" remarks="连续不合格数" type="NUMBER(19, 0)"/>
            <column name="S2T_GROUP_QUALIFICATION" remarks="不连续不合格数" type="NUMBER(19, 0)"/>
            <column name="S2T_GROUP_SIZE" remarks="不连续组" type="NUMBER(19, 0)"/>
            <column name="T2S_TYPE" remarks="加严到标准到类型(Q连续数)" type="VARCHAR2(1 BYTE)"/>
            <column name="T2S_QUALIFICATION_SIZE" remarks="连续合格数" type="NUMBER(19, 0)"/>
            <column name="T2S_GROUP_QUALIFICATION" remarks="不连续合格数" type="NUMBER(19, 0)"/>
            <column name="T2S_GROUP_SIZE" remarks="不连续组" type="NUMBER(19, 0)"/>
            <column name="S2L_TYPE" remarks="标准到放宽类型(Q连续数)" type="VARCHAR2(1 BYTE)"/>
            <column name="S2L_QUALIFICATION_SIZE" remarks="连续合格数" type="NUMBER(19, 0)"/>
            <column name="S2L_GROUP_QUALIFICATION" remarks="不连续合格数" type="NUMBER(19, 0)"/>
            <column name="S2L_GROUP_SIZE" remarks="不连续组" type="NUMBER(19, 0)"/>
            <column name="L2S_TYPE" remarks="放宽到标准类型(Q连续数)" type="VARCHAR2(1 BYTE)"/>
            <column name="L2S_QUALIFICATION_SIZE" remarks="连续不合格数" type="NUMBER(19, 0)"/>
            <column name="L2S_GROUP_QUALIFICATION" remarks="不连续不合格数" type="NUMBER(19, 0)"/>
            <column name="L2S_GROUP_SIZE" remarks="不连续组" type="NUMBER(19, 0)"/>
            <column name="RESERVED1" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED2" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED3" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED4" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED5" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED6" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED7" type="VARCHAR2(64 BYTE)"/>
            <column name="RESERVED8" type="VARCHAR2(64 BYTE)"/>
        </createTable>
    </changeSet>
    
    <changeSet id="MESWELL-202000907-DROP-EDC-DATA-INDEX" author="HeTao">
    	<preConditions onFail="MARK_RAN">
    		<indexExists tableName="EDC_DATA" indexName="IDX_EDC_DATA_U1"/>
    	</preConditions>
    	<dropIndex tableName="EDC_DATA" indexName="IDX_EDC_DATA_U1"/>
    </changeSet>
    
    <changeSet id="MESWELL-202000907-ADD-EDC-DATA-INDEX" author="HeTao">
    	<preConditions onFail="MARK_RAN">
    		<not>
    			<indexExists tableName="EDC_DATA" indexName="IDX_EDC_DATA_HIS_SEQ"/>
    		</not>
    	</preConditions>
    	<createIndex indexName="IDX_EDC_DATA_HIS_SEQ" tableName="EDC_DATA" tablespace="TS_MES_IDX" unique="false">
            <column name="HISTORY_SEQ"/>
        </createIndex>
    </changeSet>
    
    <changeSet id="MESWELL-202000907-EDC-MANUAL-SAMPLING" author="HeTao">
    	<sqlFile path="sql/EDC-20200907-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>手动抽检管理</comment>
     </changeSet>
     
     <changeSet id="MESWELL-202000910-FRIST-COPYFROM" author="tangjiacheng">
    	<sqlFile path="sql/MM-20200910-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>设备首检栏位添加</comment>
    </changeSet>
    
      <changeSet id="MESWELL-202000911-RAS-TOOL" author="tangjiacheng">
    	<sqlFile path="sql/RAS-20200911-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>治具接收栏位添加</comment>
    </changeSet>
    
    <changeSet id="MESWELL-202000916-MM-STATUS-MODEL" author="ZouYiSong">
    	<sqlFile path="sql/MM-20200916-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>物料状态模型管理栏位添加，修改一些不规则参考表名称</comment>
    </changeSet>
    
     <changeSet id="MESWELL-202000916-MM-BOM-CONTEXT" author="Clark">
    	<sqlFile path="sql/MM-20200916-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>Bom通过Context管理</comment>
    </changeSet>
	
	<changeSet id="MESWELL-202000916-CARRIER-RETURN" author="tangjiacheng">
    	<sqlFile path="sql/MM-20200916-003.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>载具退回</comment>
    </changeSet>
    
    <changeSet id="MESWELL-202000916-MM-WAFER-IQC" author="ZouYiSong">
    	<sqlFile path="sql/MM-20200916-004.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>晶圆库管理修改一些国际化翻译</comment>
    </changeSet>
    
     <changeSet id="MESWELL-202000918-USE-CATEGORY" author="tangjiacheng">
    	<sqlFile path="sql/MM-20200918-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>useCateGory校验</comment>
    </changeSet>
    
    <changeSet id="MESWELL-202000921-component-scrap-unscrap" author="HeTao">
    	<sqlFile path="sql/WIP-20200921-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>1，按片报废功能；2，新的反报废功能</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20200922-child-package-hold-check-table" author="HeTao">
    	<preConditions onFail="MARK_RAN">
    		<not>
    			<columnExists tableName="MM_MATERIAL" columnName="PACKAGE_HIERARCHY_NAME"/>
    		</not>
    	</preConditions>
    	<addColumn tableName="MM_MATERIAL">
    		<column name="PACKAGE_HIERARCHY_NAME" remarks="包装方式" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="MM_MATERIAL_HIS">
    		<column name="PACKAGE_HIERARCHY_NAME" remarks="包装方式" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<comment>物料表增加包装层级栏位，在物料、产品是包装料时需要</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20200922-child-package-hold-check" author="HeTao">
    	<preConditions onFail="MARK_RAN">
    		<not>
    			<columnExists tableName="MM_MATERIAL" columnName="PACKAGE_HIERARCHY_NAME"/>
    		</not>
    	</preConditions>
    	<sqlFile path="sql/MM-20200922-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>物料、产品功能增加包装层级字段</comment>
    </changeSet>
    
    <changeSet id="MESWELL-202000923-MMSTATEEVENT_LIMITCOUNT" author="Clark">
    	<sqlFile path="sql/MM-20200923-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>状态次数限制</comment>
    </changeSet>
    <changeSet id="MESWELL-202000923-RASSTATEEVENT_LIMITCOUNT" author="Clark">
    	<sqlFile path="sql/RAS-20200923-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>状态次数限制</comment>
    </changeSet>
    <changeSet id="MESWELL-202000928-LOTBONDING" author="Clark">
    	<sqlFile path="sql/WIP-20200928-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>批次Bonding</comment>
    </changeSet>
    <changeSet id="MESWELL-202000928-LOTBONDINGTABLE" author="Clark">			
	 	<createTable remarks="Cell表" tableName="WIP_COMPONENTUNIT_BONDING" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_COMPONENTUNIT_BONDING"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>   
              
            <column name="BONDING_TYPE" remarks="Bond类型" type="VARCHAR2(32 BYTE)"/>
            <column name="LOT_ID" remarks="批次号" type="VARCHAR2(32 BYTE)"/>
            <column name="DURABLE" remarks="载具" type="VARCHAR2(32 BYTE)"/>
            <column name="POSITION" remarks="组件位置" type="VARCHAR2(32 BYTE)"/>
            <column name="COMPONENT_RRN" remarks="组件主键" type="NUMBER(19, 0)"/>
            <column name="COMPONENT_ID" remarks="组件号" type="VARCHAR2(32 BYTE)"/>
            <column name="COMPONENT_NG_COUNT" remarks="组件Ng数量" type="NUMBER(19, 0)"/>
            <column name="EQUIPMENT_ID" remarks="设备号" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_VERSION" remarks="工步版本" type="NUMBER(19, 0)"/>
            <column name="STEP_RRN" remarks="工步主鍵" type="NUMBER(19, 0)"/>
            <column name="BONDING_LOT_ID" remarks="被键合批次" type="VARCHAR2(32 BYTE)"/>
            <column name="BONDING_DURABLE" remarks="被键合载具" type="VARCHAR2(32 BYTE)"/>
            <column name="BONDING_POSITION" remarks="被键合组件位置" type="VARCHAR2(32 BYTE)"/>
            <column name="BONDING_COMPONENT_RRN" remarks="被键合组件主键" type="NUMBER(19, 0)"/>
            <column name="BONDING_COMPONENT_NG_COUNT" remarks="被键合组件Ng数量" type="NUMBER(19, 0)"/>
            <column name="BONDING_COMPONENT_ID" remarks="被键合组件号" type="VARCHAR2(32 BYTE)"/>
            <column name="BONDING_STATE" remarks="键合状态" type="VARCHAR2(32 BYTE)"/>
            <column name="NG_COUNT" remarks="Ng数量" type="NUMBER(19, 0)"/>
            <column name="NG_PAIRING_COUNT" remarks="Ng数量" type="NUMBER(19, 0)"/>
            <column name="ACTION_CODE" remarks="动作码" type="VARCHAR2(32 BYTE)"/>
            <column name="ACTION_REASON" remarks="动作原因" type="VARCHAR2(64 BYTE)"/>
            <column name="ACTION_COMMENT" remarks="动作备注" type="VARCHAR2(128 BYTE)"/>                               
            <column name="RESERVED1" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED2" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED3" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED4" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED5" type="VARCHAR2(32 BYTE)"/>        
	    </createTable>	    
	</changeSet>
	 <changeSet id="MESWELL-202000928-CARRIER-PROCESSOR" author="tangjiacheng">
    	<sqlFile path="sql/MM-20200928-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>载具退回改成批量操作</comment>
    </changeSet>	
    
    <changeSet id="MESWELL-20200930-ADD-TENANTID" author="Clark">
    	<addColumn tableName="WIP_LOT">
    		<column name="TENANT_ID" remarks="租户" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_LOT_HIS">
    		<column name="TENANT_ID" remarks="租户" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WF_PROCESSDEFINITION">
    		<column name="TENANT_ID" remarks="租户" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<comment>三表增加租户栏位</comment>
    </changeSet>		
    <changeSet id="MESWELL-202000930-UNSCRAP" author="HeTao">
    	<sqlFile path="sql/WIP-20200930-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>报废、反报废修改</comment>
    </changeSet>	
    <changeSet id="MESWELL-20200930-WIPPILOTEDCDATEQUERY" author="ZhouGeLong">
    	<sqlFile path="sql/WIP-20200930-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>pilot收集数据查询</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20201020-WORK-ORDER-BIND-EQP" author="HeTao">
    	<sqlFile path="sql/WIP-20201020-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>工单绑定设备</comment>
    </changeSet>
    <changeSet id="MESWELL-20201020-WORK-ORDER-BIND-EQP-TABLE" author="HeTao">			
	 	<createTable remarks="工单绑定设备表" tableName="WIP_WO_EQP" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_WO_EQP"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>   
              
            <column name="WO_RRN" remarks="工单主键" type="NUMBER(19, 0)"/>
            <column name="WO_ID" remarks="工单号" type="VARCHAR2(32 BYTE)"/>
            <column name="EQUIPMENT_RRN" remarks="设备主键" type="NUMBER(19, 0)"/>
            <column name="EQUIPMENT_ID" remarks="设备号" type="VARCHAR2(32 BYTE)"/>
	    </createTable>	    
	</changeSet>		
	
	<changeSet id="MESWELL-20201027-WORK-ORDER-BIND-EQP-TABLE" author="HeTao">
		<addColumn tableName="WIP_WO_EQP">
			<column name="SEQ_NO" remarks="序列号" type="NUMBER(3, 0)"/>
            <column name="STATUS" remarks="状态" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>工单绑定设备表修改</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20201027-WORK-ORDER-BIND-EQP" author="HeTao">
    	<sqlFile path="sql/WIP-20201027-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>工单绑定设备修改</comment>
    </changeSet>
    
	<changeSet id="MESWELL-20201028-EDC-MANUAL-SAMPLING" author="DaiWenBin">
    	<sqlFile path="sql/EDC-20201028-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>手动抽检系统栏位参考值补充</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20201104-MATERIALTENANT" author="Clark">		
		<addColumn tableName="MM_MATERIAL">
			<column name="TENANT_ID" remarks="租户" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
	</changeSet>
	<changeSet id="MESWELL-20201104-MATERIALHISTENANT" author="Clark">		
		<addColumn tableName="MM_MATERIAL_HIS">
			<column name="TENANT_ID" remarks="租户" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
	</changeSet>
	
	<changeSet id="MESWELL-20201105-AUTO-Refactor" author="HeTao">		
		<sqlFile path="sql/AUTO-20201105-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>auto模块重构</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20201111-AUTO-Refactor" author="HeTao">		
		<sqlFile path="sql/AUTO-20201111-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>auto模块重构测试修改</comment>
	</changeSet>
	
    <changeSet id="MESWELL-202001111-RAS-TOOL" author="DaiWenBin">
    	<sqlFile path="sql/RAS-20201111-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>治具接收SQL乱码重新提交</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20201112-MODEL-UPDATE" author="HeTao">
    	<sqlFile path="sql/module/delete-aps.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/delete-other.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-alm.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-common-archive.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-common-context.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-common-dryrun.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-common-if.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-common-label.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-common-loganalysis.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-common-task.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-framework-active.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-framework-base.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-framework-security.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-gtm.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-mes-bas.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-mes-edc.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-mes-mm-parts.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-mes-mm.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-mes-prd.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-mes-ras.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-mes-tcard.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-mes-wip.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-mes-wipadv.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-pms.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-rtd.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-spc.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<sqlFile path="sql/module/update-wms.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>Model关联SQL</comment>
    </changeSet>
    
    <changeSet id="MESWELL-202001113-RAS-TOOL" author="DaiWenBin">
    	<sqlFile path="sql/RAS-20201113-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>英译中修改</comment>
    </changeSet>
    
    <changeSet id="MESWELL-202001119-RAS-TOOL" author="HeTao">
    	<sqlFile path="sql/RAS-20201119-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>隐藏不需要的Tool单独功能</comment>
    </changeSet>
    
    <changeSet id="MESWELL-202001120-LOCATION-ADDPARENT" author="Clark">		
		<addColumn tableName="BAS_LOCATION">
			<column name="PARENT_LOCATION_RRN" remarks="父级主键" type="NUMBER(19, 0)"/>
		</addColumn>
	</changeSet>
	
     <changeSet id="MESWELL-202001120-LOCATION" author="CLark">
    	<sqlFile path="sql/RAS-20201120-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>增加Location层级管理</comment>
     </changeSet>
     <changeSet id="MESWELL-202001120-RAS-POSITION" author="YangJiaHui">
    	<sqlFile path="sql/RAS-20201120-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>删除设备Position功能</comment>
     </changeSet>
     
     <changeSet id="MESWELL-202001120-IMPORT" author="YangJiaHui">
    	<sqlFile path="sql/RAS-20201120-003.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>治具规格和设备保养可配置导入</comment>
     </changeSet>
     
      <changeSet id="MESWELL-202001120-RAS-UPDATE" author="YangJiaHui">
    	<sqlFile path="sql/RAS-20201120-004.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>修改设备事件动作码添用户栏位</comment>
     </changeSet>
     
      <changeSet id="MESWELL-202001120-RAS-MESSAGE" author="YangJiaHui">
    	<sqlFile path="sql/RAS-20201123-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>设备事件删除及文件弹框限制</comment>
     </changeSet>
     
     <changeSet id="MESWELL-202001124-WIP-scrap-table" author="HeTao">
    	<sqlFile path="sql/WIP-20201124-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>报废界面遗漏动态表补充</comment>
     </changeSet>
     
     <changeSet id="MESWELL-202001124-WIP-scrap-supplement" author="HeTao">
    	<sqlFile path="sql/WIP-20201124-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>报废补充功能</comment>
     </changeSet>
     
     <changeSet id="MESWELL-202001124-WIP-LOTQUERY" author="DaiWenBin">
    	<sqlFile path="sql/WIP-20201116-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>在制品查询添加流程工艺设备描述字段</comment>
     </changeSet>
     
     <changeSet id="MESWELL-20201125-MM-PACKAGETYPE-CONTEXT" author="Clark">
    	<sqlFile path="sql/MM-20201125-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>报废补充功能</comment>
     </changeSet>
     
      <changeSet id="MESWELL-20201125-DEL-MMPACKAGETYPERELATION" author="Clark">
        <dropTable tableName="MM_PACKAGE_TYPE_RELATION"/>
     </changeSet>

     <changeSet id="MESWELL-20201127-EDC-MANUAL-SAMPLING" author="HeTao">
        <sqlFile path="sql/EDC-20201127-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>手动抽检功能增强</comment>
     </changeSet>

     <changeSet id="MESWELL-20201127-MM-BOMANDMESSAGE" author="YangJiaHui">
        <sqlFile path="sql/MM-20201127-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
        <comment>bom管理全部导出/不准重复暂停约束弹框</comment>
     </changeSet>
     
     <changeSet id="MESWELL-202001201-WIP-scrap-supplement" author="HeTao">
    	<sqlFile path="sql/WIP-20201201-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>报废补充功能修改</comment>
     </changeSet>
     
     <changeSet id="MESWELL-20201203-PRD-PARAMUPLOAD" author="YangJiaHui">
    	<sqlFile path="sql/PRD-20201203-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>“产品及流程”导入参数支持多对多</comment>
     </changeSet>
     
     <changeSet id="MESWELL-20201210-PRD-DEL-PART" author="WangXiang">
    	<sqlFile path="sql/PRD-20201210-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>删除产品时增加工单检查</comment>
     </changeSet>
     
     <changeSet id="MESWELL-20201210-PRD-DEL-PROCESS" author="WangXiang">
    	<sqlFile path="sql/PRD-20201210-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>删除工艺时增加可选流程检查</comment>
     </changeSet>
     
     <changeSet id="MESWELL-20201211-MM-DEL-MATERIAL" author="WangXiang">
    	<sqlFile path="sql/MM-20201211-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>删除物料时增加对物料批的检查</comment>
     </changeSet>
     
     <changeSet id="MESWELL-20201214-MM-DEL-MATERIAL" author="WangXiang">
    	<sqlFile path="sql/MM-20201214-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>删除物料时增加对物料批的检查</comment>
     </changeSet>
     
     <changeSet id="MESWELL-20201215-RAS-EQP-EVENT-QUERY" author="YangJiaHui">
    	<sqlFile path="sql/RAS-20201215-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>设备事件历史增加两栏位显示</comment>
     </changeSet>
     
     <changeSet id="MESWELL-20201229-BAS-STEP-AUTHORITY" author="WangXiang">
    	<sqlFile path="sql/BAS-20201229-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>工步权限界面栏位属性修改</comment>
     </changeSet>
	 
	 <changeSet id="MESWELL-20210107-RAS-CONSTRAINT-MESSAGE" author="FanChengMing">
    	<sqlFile path="sql/RAS-20210107-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>工艺限制报错时加上对应的设备号和设备类型</comment>
     </changeSet>
     
   	 <changeSet id="BAS-20210118-AD_SYS_PARAMETER_VALUE" author="DaiWenBin">
    	<sqlFile path="sql/BAS-20210118-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>添加系统参数</comment>
     </changeSet>
     
     <changeSet id="MM-20210118-MM_MATERIAL_EQP_KITTING" author="ZouYiSong">
    	<sqlFile path="sql/MM-20210118-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>修改物料kitting界面栏位(数量)属性</comment>
     </changeSet>
     
     <changeSet id="EDC-20210129-AUTO-EDC" author="HeTao">
    	<sqlFile path="sql/EDC-20210129-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>AUTO EDC</comment>
     </changeSet>
     
     <changeSet id="EDC-20210201-AUTO-EDC" author="HeTao">
    	<sqlFile path="sql/EDC-20210201-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>AUTO EDC回滚</comment>
     </changeSet>
     
     <changeSet id="MM-20210201-STATUS-MODEL" author="HeTao">
    	<sqlFile path="sql/MM-20210202-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>Status Model界面调整</comment>
     </changeSet>
     
     <changeSet id="WIP-20210203-STATUS-MODEL" author="FanChengMing">
    	<sqlFile path="sql/WIP-20210203-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>增加获取指定时间和工步数范围内的批次列表的AD_QUERY查询</comment>
     </changeSet>
     
     <changeSet id="MESWELL-20210204-TECN" author="FanChengMing">			
	 	<createTable remarks="EDC临时工程变更表" tableName="EDC_TECN" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_EDC_TECN"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/> 
            <column name="NAME" remarks="名称" type="VARCHAR2(32 BYTE)"/>
            <column name="DESCRIPTION" remarks="描述" type="VARCHAR2(32 BYTE)"/>
            <column name="VERSION" remarks="版本" type="VARCHAR2(32 BYTE)"/>
            <column name="STATUS" remarks="状态" type="VARCHAR2(32 BYTE)"/>
            <column name="ACTIVE_TIME" remarks="激活时间" type="date"/>
            <column name="ACTIVE_USER" remarks="激活者" type="VARCHAR2(32 BYTE)"/>
            <column name="ECN_TYPE" remarks="ecn类型" type="VARCHAR2(32 BYTE)"/>
            <column name="ACTION_TYPE" remarks="动作类型" type="VARCHAR2(32 BYTE)"/>
            <column name="CONTEXT_RRN" remarks="context类型主键" type="NUMBER(19, 0)"/>
            <column name="CONTEXT_VALUE_RRN" remarks="context数据主键" type="NUMBER(19, 0)"/>
            <column name="LOT_ID" remarks="批次号" type="VARCHAR2(32 BYTE)"/>
            <column name="EQUIPMENT_ID" remarks="设备号" type="VARCHAR2(32 BYTE)"/>    
            <column name="COMPONENT_LIST" remarks="组件列表" type="VARCHAR2(256 BYTE)"/>
            <column name="EDC_NAME" remarks="数据采集项集名称" type="VARCHAR2(64 BYTE)"/>
            <column name="EDC_ITEM_NAME" remarks="数据采集项名称" type="VARCHAR2(64 BYTE)"/>
            <column name="ITEM" remarks="ITEM数" type="NUMBER(19, 0)"/> 
            <column name="ITEM_DESC" remarks="ITEM描述" type="VARCHAR2(32 BYTE)"/>
            <column name="COMP" remarks="COMP数" type="NUMBER(19, 0)"/> 
            <column name="COMP_DESC" remarks="COMP描述" type="VARCHAR2(32 BYTE)"/>
            <column name="SITE" remarks="SITE数" type="NUMBER(19, 0)"/> 
            <column name="SITE_DESC" remarks="SITE描述" type="VARCHAR2(32 BYTE)"/>
            <column name="BUMP" remarks="BUMP数" type="NUMBER(19, 0)"/> 
            <column name="BUMP_DESC" remarks="BUMP描述" type="VARCHAR2(32 BYTE)"/>
            <column name="COMP_SLOT_SRC" remarks="Component Slot号" type="VARCHAR2(32 BYTE)"/>            
            <column name="USL" remarks="USL" type="VARCHAR2(32 BYTE)"/>
            <column name="SL" remarks="SL" type="VARCHAR2(32 BYTE)"/>
            <column name="LSL" remarks="LSL" type="VARCHAR2(32 BYTE)"/>
            <column name="SAMPLING_PLAN_NAME" remarks="采样计划名称" type="VARCHAR2(64 BYTE)"/>
            <column name="SUBGROUP_PLAN_NAME" remarks="子计划名称" type="VARCHAR2(64 BYTE)"/>       
	    </createTable>  
	</changeSet>
     
     <changeSet id="EDC-20210204-EDC-SET-CURRENT" author="FanChengMing">
    	<sqlFile path="sql/EDC-20210204-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>EDC_SET_CURRENT表增加TEST_TYPE,TECN_RRNS栏位字段</comment>
     </changeSet>
     
     <changeSet id="MM-20210201-EDC-AUTO" author="HeTao">
    	<sqlFile path="sql/EDC-20210205-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>AUTO EDC补充</comment>
     </changeSet>
     
     <changeSet id="WIP-20210302-LOT-STEP-BACK" author="HeTao">
    	<sqlFile path="sql/WIP-20210302-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>退步卡控信息</comment>
     </changeSet>
     
     <changeSet id="BAS-20210226-BAS-ID-RULE" author="ShiJianPing">
    	<sqlFile path="sql/BAS-20210226-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>ID生成规则管理，变量型规则增加补位字符的显示</comment>
     </changeSet>
     
	 <changeSet id="EDC-20210304-EDCTEXT-UPDATE" author="DaiWenBin">
        <sqlFile path="sql/EDC-20210304-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>完善英译中和修改文本数据收集按钮权限</comment>
    </changeSet>	
    
    <changeSet id="WIP-20210308-LOT-STEP-BACK" author="HeTao">
    	<sqlFile path="sql/WIP-20210308-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>endif支持按历史记录退步</comment>
   	</changeSet>
   	
   	<changeSet id="MESWELL-20210308-001-TECN" author="Clark">
   		<modifyDataType tableName="AD_FIELD" columnName="CUSTOM_COMPOSITE" newDataType="VARCHAR2(64 BYTE)"/>
    	<sqlFile path="sql/EDC-20210308-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>EdcTecn变更功能Sql</comment>
   	</changeSet>
   	
   	<changeSet id="MESWELL-20210309-001-QTIME" author="MoYouMing">
    	<sqlFile path="sql/WIP-20210309-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>Qtime多工步增加栏位Sql</comment>
   	</changeSet>
   	
   	<changeSet id="MESWELL-20210311-001-POLLUTION" author="Clark">
    	<sqlFile path="sql/WIP-20210311-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>设备污染增加栏位Sql</comment>
   	</changeSet>
		<changeSet id="MESWELL-20210312-ADD-WIP_FUTURE_ACTION" author="MoYouMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_ACTION" columnName="END_STEP_PLACEMENT"/>
				<columnExists tableName="WIP_FUTURE_ACTION" columnName="PARENT_RRN"/>
			</not>
		</preConditions>
    	<addColumn tableName="WIP_FUTURE_ACTION">
    		<column name="END_STEP_PLACEMENT" remarks="结束工步定位" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_FUTURE_ACTION">
    		<column name="PARENT_RRN" remarks="父Rrn,当parentRrn与objectRrn一致时为主数据" type="NUMBER(19, 0)"/>
    	</addColumn>
    	<comment>WIP_FUTURE_ACTION表增加栏位</comment>
    </changeSet>
	<changeSet id="MESWELL-20210312-ADD-WIP_FUTURE_ACTION_HIS" author="MoYouMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_ACTION_HIS" columnName="END_STEP_PLACEMENT"/>
				<columnExists tableName="WIP_FUTURE_ACTION_HIS" columnName="PARENT_RRN"/>
			</not>
		</preConditions>
    	<addColumn tableName="WIP_FUTURE_ACTION_HIS">
    		<column name="END_STEP_PLACEMENT" remarks="结束工步定位" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_FUTURE_ACTION_HIS">
    		<column name="PARENT_RRN" remarks="父Rrn,当parentRrn与objectRrn一致时为主数据" type="NUMBER(19, 0)"/>
    	</addColumn>
    	<comment>WIP_FUTURE_ACTION_HIS表增加栏位</comment>
    </changeSet>
	<changeSet id="MESWELL-20210312-ADD-WIP_FUTURE_TIMER_INSTANCE" author="MoYouMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_TIMER_INSTANCE" columnName="END_STEP_PLACEMENT"/>
				<columnExists tableName="WIP_FUTURE_TIMER_INSTANCE" columnName="STEP_PLACEMENT"/>
				<columnExists tableName="WIP_FUTURE_TIMER_INSTANCE" columnName="PARENT_INSTANCE_RRN"/>
			</not>
		</preConditions>
    	<addColumn tableName="WIP_FUTURE_TIMER_INSTANCE">
    		<column name="END_STEP_PLACEMENT" remarks="结束工步定位" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_FUTURE_TIMER_INSTANCE">
    		<column name="STEP_PLACEMENT" remarks="工步定位" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
		<addColumn tableName="WIP_FUTURE_TIMER_INSTANCE">
    		<column name="PARENT_INSTANCE_RRN" remarks="父Rrn,当parentRrn与objectRrn一致时为主数据" type="NUMBER(19, 0)"/>
    	</addColumn>
    	<comment>WIP_FUTURE_TIMER_INSTANCE表增加栏位</comment>
    </changeSet>
	<changeSet id="MESWELL-20210312-001-QTIME" author="MoYouMing">
    	<createTable remarks="未来定时实例历史表" tableName="WIP_FUTURE_TIMER_INSTANCE_HIS">
            <column name="OBJECT_RRN" remarks="对象区域号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_FUTURE_TIMER_INS_HIS"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否生效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建人ID" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新人ID" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>
            <column name="ACTION" remarks="动作" type="VARCHAR2(32 BYTE)"/>
            <column name="NAME" remarks="名称" type="VARCHAR2(32 BYTE)"/>
            <column name="LOT_RRN" remarks="批次表主键" type="NUMBER(19, 0)"/>
            <column name="LOT_ID" remarks="批次ID" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_NAME" remarks="工步名" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_VERSION" remarks="工步版本" type="NUMBER(19, 0)"/>
            <column name="STEP_STATE_RRN" remarks="工步状态" type="NUMBER(19, 0)"/>
            <column name="PATH" remarks="路径" type="VARCHAR2(256 BYTE)"/>
            <column name="PROCEDURE_NAME" remarks="流程名" type="VARCHAR2(32 BYTE)"/>
            <column name="PROCEDURE_VERSION" remarks="流程版本" type="NUMBER(19, 0)"/>
            <column name="END_PATH" remarks="结束路径" type="VARCHAR2(256 BYTE)"/>
            <column name="END_PROCEDURE_NAME" remarks="结束流程名称" type="VARCHAR2(32 BYTE)"/>
            <column name="END_PROCEDURE_VERSION" remarks="结束流程版本" type="NUMBER(19, 0)"/>
            <column name="END_STEP_STATE_NAME" remarks="结束工步状态" type="VARCHAR2(32 BYTE)"/>
            <column name="END_STEP_NAME" remarks="结束工步名称" type="VARCHAR2(32 BYTE)"/>
            <column name="HOLD_CODE" remarks="hold码" type="VARCHAR2(32 BYTE)"/>
            <column name="HOLD_REASON" remarks="hold原因" type="VARCHAR2(256 BYTE)"/>
            <column name="HOLD_PWD" remarks="hold密码" type="VARCHAR2(32 BYTE)"/>
            <column name="HOLD_LEVEL" remarks="hold等级" type="VARCHAR2(32 BYTE)"/>
            <column name="HOLD_OWNER" remarks="hold管理人" type="VARCHAR2(32 BYTE)"/>
            <column name="NOTE" remarks="笔记" type="VARCHAR2(4000 BYTE)"/>
            <column name="TIMER_TYPE" remarks="定时器类型" type="VARCHAR2(32 BYTE)"/>
            <column name="TIMER_DURATION" remarks="定时器持续时间" type="NUMBER(19, 0)"/>
            <column name="EARLY_PERIOD" remarks="提前期" type="NUMBER(19, 0)"/>
            <column name="TIMER_ACTION" remarks="定时器动作" type="VARCHAR2(32 BYTE)"/>
            <column name="REWORK_CODE" remarks="返工码" type="VARCHAR2(32 BYTE)"/>
            <column name="REWORK_PROCEDURE" remarks="返工流程" type="VARCHAR2(32 BYTE)"/>
            <column name="REWORK_PROCEDURE_VERSION" remarks="返工流程版本" type="NUMBER(19, 0)"/>
            <column name="OWNER" remarks="管理者" type="VARCHAR2(32 BYTE)"/>
            <column name="START_TIME" remarks="开始时间" type="date"/>
            <column name="EXPIRE_TIME" remarks="到期时间" type="date"/>
            <column name="TIMER_FLAG" remarks="定时器标志" type="VARCHAR2(32 BYTE)"/>
            <column name="DONE_TIME" remarks="完成时间" type="date"/>
            <column name="MESSAGE" remarks="消息" type="VARCHAR2(4000 BYTE)"/>
            <column name="USER_NAME" remarks="用户名" type="VARCHAR2(32 BYTE)"/>
            <column name="EXPIRE_TIMER_ID" type="VARCHAR2(64 BYTE)"/>
            <column name="EARLY_TIME" type="date"/>
            <column name="EARLY_TIMER_ID" type="VARCHAR2(64 BYTE)"/>
            <column name="STEP_STATE_NAME" type="VARCHAR2(32 BYTE)"/>
            <column name="PROCESS_NAME" type="VARCHAR2(32 BYTE)"/>
            <column name="PROCESS_VERSION" type="NUMBER(19, 0)"/>
            <column name="COMPONENT_ID" type="VARCHAR2(32 BYTE)"/>
            <column name="PARENT_INSTANCE_RRN" remarks="多结束工步时用来找主timer将下面的子instance结束掉" type="NUMBER(19, 0)"/>
            <column name="TRANS_TIME" remarks="操作时间" type="date"/>
            <column name="TRANS_TYPE" remarks="操作类型" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_PLACEMENT" remarks="工步定位" type="VARCHAR2(32 BYTE)"/>
            <column name="END_STEP_PLACEMENT" remarks="结束工步定位" type="VARCHAR2(32 BYTE)"/>
        </createTable>
   	</changeSet>
	<changeSet id="MESWELL-20210312-002-QTIME" author="MoYouMing">
    	<preConditions onFail="MARK_RAN">
    		<not>
    			<indexExists tableName="WIP_FUTURE_TIMER_INSTANCE" indexName="IDX_WIP_FUTURE_TIMER_PARENT"/>
    		</not>
    	</preConditions>
    	<createIndex indexName="IDX_WIP_FUTURE_TIMER_PARENT" tableName="WIP_FUTURE_TIMER_INSTANCE" tablespace="TS_MES_IDX" unique="false">
            <column name="PARENT_INSTANCE_RRN"/>
        </createIndex>
    </changeSet>
	
	<changeSet id="MESWELL-20210317-WIP_WO_HIS-ADD-RESERVED" author="YanChao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_WO_HIS" columnName="RESERVED1"/>
			</not>
		</preConditions>
    	<addColumn tableName="WIP_WO_HIS">
    		<column name="RESERVED1" remarks="预留字段1" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_WO_HIS">
    		<column name="RESERVED2" remarks="预留字段2" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_WO_HIS">
    		<column name="RESERVED3" remarks="预留字段3" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_WO_HIS">
    		<column name="RESERVED4" remarks="预留字段4" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_WO_HIS">
    		<column name="RESERVED5" remarks="预留字段5" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_WO_HIS">
    		<column name="RESERVED6" remarks="预留字段6" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_WO_HIS">
    		<column name="RESERVED7" remarks="预留字段7" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_WO_HIS">
    		<column name="RESERVED8" remarks="预留字段8" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>
    	<comment>工单历史表换成hbn.xml方式，并增加预留字段1-8</comment>
    </changeSet>
    <changeSet id="WIP-20210322-STEP_MATERIAL_RULE" author="YangJiaHui">
    	<sqlFile path="sql/WIP-20210322-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>工步物料规则</comment>
   	</changeSet>
   	<changeSet id="PRD-20210324-STEP_MATERIAL" author="YangJiaHui">
    	<sqlFile path="sql/PRD-20210324-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>工步物料信息功能</comment>
   	</changeSet>
   	<changeSet id="PRD-20210325-STEP_MATERIAL_TAB" author="YangJiaHui">
    	<sqlFile path="sql/PRD-20210325-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>工步管理tab物料信息</comment>
   	</changeSet>
   	<changeSet id="BAS-20210326-ADTABLE_ADV" author="ShiJianPing">
   	<sqlFile path="sql/BAS-20210326-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>动态表编辑页面增加按ObjectRrn查询</comment>
   	</changeSet>
   	   	<changeSet id="MM-20210327-AD-MESSAGE" author="DaiWenBin">
    	<sqlFile path="sql/MM-20210327-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>补充英译中</comment>
   	</changeSet>
	
	<changeSet id="MESWELL-20210331-LOTHIS-1" author="Tony">
    	<preConditions onFail="MARK_RAN">
    		<not>
    			<columnExists tableName="WIP_LOT_HIS" columnName="LOT_CREATE_TIME"/>
    		</not>
    	</preConditions>
		<addColumn tableName="WIP_LOT_HIS">
    		<column name="LOT_CREATE_TIME" remarks="批次创建时间" type="date"/>
    	</addColumn>
    </changeSet>
	
	<changeSet id="MESWELL-20210331-LOTHIS-2" author="Tony">
    	<preConditions onFail="MARK_RAN">
    		<indexExists tableName="WIP_LOT_HIS" indexName="IDX_WIP_LOT_HIS1"/>
    	</preConditions>
		<dropIndex tableName="WIP_LOT_HIS" indexName="IDX_WIP_LOT_HIS1"/>
    </changeSet>
	
	<changeSet id="MESWELL-20210331-LOTHIS-3" author="Tony">
    	<preConditions onFail="MARK_RAN">
    		<indexExists tableName="WIP_LOT_HIS" indexName="IDX_WIP_LOT_HIS5"/>
    	</preConditions>
		<dropIndex tableName="WIP_LOT_HIS" indexName="IDX_WIP_LOT_HIS5"/>
    </changeSet>
	
	<changeSet id="MESWELL-20210331-LOTHIS-4" author="Tony">
    	<preConditions onFail="MARK_RAN">
    		<indexExists tableName="WIP_LOT_HIS" indexName="IDX_WIP_LOT_HIS6"/>
    	</preConditions>
		<dropIndex tableName="WIP_LOT_HIS" indexName="IDX_WIP_LOT_HIS6"/>
    </changeSet>
	
	<changeSet id="MESWELL-20210331-LOTHIS-5" author="Tony">
    	<preConditions onFail="MARK_RAN">
    		<indexExists tableName="WIP_LOT_HIS" indexName="IDX_WIP_LOT_HIS2"/>
    	</preConditions>
		<dropIndex tableName="WIP_LOT_HIS" indexName="IDX_WIP_LOT_HIS2"/>
    </changeSet>
	
	<changeSet id="MESWELL-20210331-LOTHIS-6" author="Tony">
    	<preConditions onFail="MARK_RAN">
			<not>
				<indexExists tableName="WIP_LOT_HIS" indexName="IDX_WIP_LOT_HIS_LOTRRN"/>
			</not>
    	</preConditions>
		<createIndex indexName="IDX_WIP_LOT_HIS_LOTRRN" tableName="WIP_LOT_HIS" tablespace="TS_MES_HIS_IDX" unique="false">
            <column name="LOT_RRN"/>
        </createIndex>    
	</changeSet>
	
	<changeSet id="MESWELL-20210331-COMPHIS-1" author="Tony">
    	<preConditions onFail="MARK_RAN">
    		<not>
    			<columnExists tableName="WIP_COMPONENTUNIT_HIS" columnName="COMPONENTUNIT_CREATE_TIME"/>
    		</not>
    	</preConditions>
		<addColumn tableName="WIP_COMPONENTUNIT_HIS">
    		<column name="COMPONENTUNIT_CREATE_TIME" remarks="Component创建时间" type="date"/>
    	</addColumn>
    </changeSet>
	
	<changeSet id="MESWELL-20210331-COMPHIS-2" author="Tony">
    	<preConditions onFail="MARK_RAN">
    		<indexExists tableName="WIP_COMPONENTUNIT_HIS" indexName="IDX_WIP_COMPONENTUNIT_HIS1"/>
    	</preConditions>
		<dropIndex tableName="WIP_COMPONENTUNIT_HIS" indexName="IDX_WIP_COMPONENTUNIT_HIS1"/>
    </changeSet>
	
	<changeSet id="MESWELL-20210331-COMPHIS-3" author="Tony">
    	<preConditions onFail="MARK_RAN">
			<not>
				<indexExists tableName="WIP_COMPONENTUNIT_HIS" indexName="IDX_WIP_COMPONENTUNIT_COMPRRN"/>
			</not>
    	</preConditions>
		<createIndex indexName="IDX_WIP_COMPONENTUNIT_COMPRRN" tableName="WIP_COMPONENTUNIT_HIS" tablespace="TS_MES_HIS_IDX" unique="false">
            <column name="COMPONENT_RRN"/>
        </createIndex>    
	</changeSet>
	<changeSet author="HeTao" id="MESWELL-20210331-WIP_LOT_PREPARE">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="WIP_LOT_PREPARE"/>
			</not>
		</preConditions>
        <createTable tableName="WIP_LOT_PREPARE">
            <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_PREPARE" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域ID" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="对象是否生效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建人ID" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新人ID" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>
            <column name="EQUIPMENT_ID" remarks="设备号" type="VARCHAR2(32 BYTE)"/>
            <column name="JOB_ID" remarks="任务号" type="VARCHAR2(32 BYTE)"/>
            <column name="LOT_ID" remarks="批次号" type="VARCHAR2(32 BYTE)"/>
            <column name="BATCH_ID" remarks="批量ID" type="VARCHAR2(64 BYTE)"/>
        </createTable>
        
        <createIndex indexName="UK_WIP_LOT_PREPARE" tableName="WIP_LOT_PREPARE" tablespace="TS_MES_IDX" unique="true">
            <column name="LOT_ID"/>
        </createIndex>
    </changeSet>
    
    <changeSet author="HeTao" id="MESWELL-20210331-BY-EQP-GLC">
    	<sqlFile path="sql/WIP-20210331-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>按设备作业GLC</comment>
    </changeSet>
    
    <changeSet author="HeTao" id="MESWELL-20210401-Antipollution">
    	<sqlFile path="sql/WIP-20210401-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>解决防污染管理权限按钮BUG</comment>
    </changeSet>
	
	<changeSet id="MESWELL-20210401-001-COMP-HISSEQ" author="Tony">
    	<createTable remarks="Component事务号与创建时间关系表" tableName="WIP_COMPONENTUNIT_HISSEQ_MAP">
            <column name="OBJECT_RRN" remarks="对象区域号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_COMPONENT_HISSEQ_MAP"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否生效" type="VARCHAR2(1 BYTE)"/>
            <column name="ACTION" remarks="动作" type="VARCHAR2(32 BYTE)"/>
            <column name="NAME" remarks="名称" type="VARCHAR2(32 BYTE)"/>
            <column name="HISTORY_SEQ" remarks="历史事务号" type="VARCHAR2(32 BYTE)"/>
            <column name="COMPONENT_CREATE_TIME" remarks="Component创建时间(精确到天)" type="date"/>
         </createTable>
		 <createIndex indexName="IDX_WIP_COMP_HISSEQ_MAP_SEQ" tableName="WIP_COMPONENTUNIT_HISSEQ_MAP" tablespace="TS_MES_HIS_IDX" unique="true">
            <column name="HISTORY_SEQ"/>
         </createIndex>
		 <createIndex indexName="IDX_WIP_COMP_HISSEQ_MAP_TIME" tableName="WIP_COMPONENTUNIT_HISSEQ_MAP" tablespace="TS_MES_HIS_IDX" unique="true">
            <column name="COMPONENT_CREATE_TIME"/>
         </createIndex>
   	</changeSet>

	<changeSet id="MESWELL-20210402-FUTUREHOLD" author="Clark">
    	<preConditions onFail="MARK_RAN">
    		<not>
    			<columnExists tableName="WIP_FUTURE_ACTION" columnName="RUN_CARD_ID"/>
    		</not>
    	</preConditions>
    	<addColumn tableName="WIP_FUTURE_ACTION">
    		<column name="RUN_CARD_ID" remarks="RunCard编号" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<addColumn tableName="WIP_FUTURE_ACTION_HIS">
    		<column name="RUN_CARD_ID" remarks="RunCard编号" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    	<comment>未来动作表增加RunCard编号栏位</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20210407-LOT-PREPARE" author="HeTao">
    	<sqlFile path="sql/WIP-20210407-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>Lot Prepare补充</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20210407-COMPHIS" author="HeTao">
    	<preConditions onFail="MARK_RAN">
    		<not>
    			<columnExists tableName="WIP_COMPONENTUNIT_HIS" columnName="COMPONENT_CREATE_TIME"/>
    		</not>
    	</preConditions>
    	<dropColumn tableName="WIP_COMPONENTUNIT_HIS">
    		<column name="COMPONENTUNIT_CREATE_TIME"></column>
    	</dropColumn>
    	
		<addColumn tableName="WIP_COMPONENTUNIT_HIS">
    		<column name="COMPONENT_CREATE_TIME" remarks="Component创建时间" type="date"/>
    	</addColumn>
    </changeSet>
    
    <changeSet id="MESWELL-20210407-COMPHISMAP" author="HeTao">
    	<preConditions onFail="MARK_RAN">
   			<indexExists tableName="WIP_COMPONENTUNIT_HISSEQ_MAP" indexName="IDX_WIP_COMP_HISSEQ_MAP_SEQ"/>
    	</preConditions>
    	<dropIndex tableName="WIP_COMPONENTUNIT_HISSEQ_MAP" indexName="IDX_WIP_COMP_HISSEQ_MAP_SEQ"/>
    	<dropIndex tableName="WIP_COMPONENTUNIT_HISSEQ_MAP" indexName="IDX_WIP_COMP_HISSEQ_MAP_TIME"/>
    	
    	<createIndex indexName="IDX_WIP_COMP_HISSEQ_MAP_SEQ" tableName="WIP_COMPONENTUNIT_HISSEQ_MAP" tablespace="TS_MES_HIS_IDX" unique="false">
            <column name="HISTORY_SEQ"/>
         </createIndex>
		 <createIndex indexName="IDX_WIP_COMP_HISSEQ_MAP_TIME" tableName="WIP_COMPONENTUNIT_HISSEQ_MAP" tablespace="TS_MES_HIS_IDX" unique="false">
            <column name="COMPONENT_CREATE_TIME"/>
         </createIndex>
         <comment>修改唯一索引不唯一</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20210709-DISABLE-FIRST-INSPECT" author="HeTao">
    	<sql>update AD_AUTHORITY set IS_ACTIVE = 'N' where NAME = 'EquipmentFirstInspectManager'</sql>
    	<comment>隐藏首检模块</comment>
    </changeSet>
    
    
    <changeSet id="MESWELL-20210407-CDI" author="FanChengMing">			
	 	<createTable remarks="Cdi表" tableName="WIP_LOT_FLOW_CDI_POINT" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_FLOW_CDI_POINT"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>     
            <column name="CDI_POINT_NAME" remarks="注入点名称" type="VARCHAR2(32 BYTE)"/>
            <column name="CDI_ACTION_DESC" remarks="注入点描述" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_CATEGORY" remarks="工步类别" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
            <column name="PRIORITY" remarks="调用次序" type="NUMBER(19, 0)"/>
            <column name="CDI_ACTION_NAME" remarks="注入服务名称" type="VARCHAR2(32 BYTE)"/>
            <column name="IS_ENABLE" remarks="服务是否有效" type="VARCHAR2(1 BYTE)"/>       
	    </createTable>	    
									 		    
	    <createTable remarks="Cdi历史表" tableName="WIP_LOT_FLOW_CDI_POINT_HIS" tablespace="TS_MES_HIS_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_FLOW_CDI_POINT_HIS"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TYPE" remarks="事务类型" type="VARCHAR2(32 BYTE)"/>
           	<column name="TRANS_TIME" remarks="事务类型" type="date"/>
           	<column name="CDI_POINT_NAME" remarks="注入点名称" type="VARCHAR2(32 BYTE)"/>
           	<column name="CDI_ACTION_DESC" remarks="注入点描述" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_CATEGORY" remarks="工步类别" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
            <column name="PRIORITY" remarks="调用次序" type="NUMBER(19, 0)"/>
            <column name="CDI_ACTION_NAME" remarks="注入服务名称" type="VARCHAR2(32 BYTE)"/>
            <column name="IS_ENABLE" remarks="服务是否有效" type="VARCHAR2(1 BYTE)"/>      
	    </createTable>   
	</changeSet>
	
    <changeSet id="MESWELL-20210412-AD-MESSAGE" author="DaiWenBin">
    	<sqlFile path="sql/WIP-20210412-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>英译中补充</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20210413-VARIABLEINSTANCE-ADDMAPPEDNAME" author="FanChengMing">
    	<preConditions onFail="MARK_RAN">
    		<not>
    			<columnExists tableName="WF_PARAMETER_INSTANCE" columnName="MAPPED_NAME"/>
    		</not>
    	</preConditions>		
		<addColumn tableName="WF_PARAMETER_INSTANCE">
			<column name="MAPPED_NAME" remarks="映射名字" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
	</changeSet>
	
	<changeSet id="MESWELL-20210413-CDI-ACTION-DESC" author="FanChengMing">
		<modifyDataType tableName="WIP_LOT_FLOW_CDI_POINT" columnName="CDI_ACTION_DESC" newDataType="VARCHAR2(256 BYTE)"/>
		<modifyDataType tableName="WIP_LOT_FLOW_CDI_POINT_HIS" columnName="CDI_ACTION_DESC" newDataType="VARCHAR2(256 BYTE)"/>
	</changeSet>
	
	<changeSet id="MESWELL-20210413-FLOWCDIPOINT" author="FanChengMing">
    	<sqlFile path="sql/WIP-20210413-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>增加cdi通用管理和工步管理界面</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20210414-PARAMETER" author="FanChengMing">
    	<sqlFile path="sql/WIP-20210414-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>参数界面增加mapped栏位,CDI栏位调整</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20210414-EQUIPMENT_MATERIAL" author="ShiJianPing">
    	<sqlFile path="sql/MM-20210414-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>设备物料查询动态表栏位调整</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20210415-PARAMETERL" author="FanChengMing">
    	<sqlFile path="sql/PRD-20210415-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>产品和工步等form页面增加映射属性字段</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20210416-EQP-SORTINGJOB" author="Clark">
    	<sqlFile path="sql/WIP-20210416-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>载具任务等form页面增加映射属性字段</comment>
    </changeSet>
    
    <changeSet id="BAS-20210419-AD-REFNAME" author="DaiWenBin">
    	<sqlFile path="sql/BAS-20210419-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>补充缺失的用户栏位参考值</comment>
    </changeSet>
    
    <changeSet id="WIP-20210419-WIP_COMPONENTUNIT_EQP_UNIT_HIS-CREATE" author="HeTao">
    	<preConditions onFail="MARK_RAN">
    		<not>
    			<tableExists tableName="WIP_COMPONENTUNIT_EQP_UNIT_HIS"/>
    		</not>
    	</preConditions>
    	
    	<createTable remarks="组件设备加工历史表" tableName="WIP_COMPONENTUNIT_EQP_UNIT_HIS" tablespace="TS_MES_HIS_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TYPE" remarks="事务类型" type="VARCHAR2(32 BYTE)"/>
           	<column name="TRANS_TIME" remarks="事务类型" type="date"/>
           	<column name="HISTORY_SEQ" remarks="事务号" type="VARCHAR2(32 BYTE)"/>
           	<column name="HISTORY_SEQ_NO" remarks="事务序号" type="NUMBER(19, 0)"/>
           	
           	<column name="COMPONENT_RRN" remarks="组件主键号" type="NUMBER(19, 0)"/>
           	<column name="COMPONENT_ID" remarks="组件ID" type="VARCHAR2(32 BYTE)"/>
            <column name="EQUIPMENT_ID" remarks="设备ID" type="VARCHAR2(32 BYTE)"/>
            <column name="UNIT_ID" remarks="UnitID" type="VARCHAR2(32 BYTE)"/>
            <column name="SUB_UNIT_ID" remarks="子UnitID" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
            <column name="SUB_STEP_NAME" remarks="子工步名称" type="VARCHAR2(32 BYTE)"/>    
            <column name="RECIPE_NAME" remarks="工艺菜单名称" type="VARCHAR2(32 BYTE)"/>  
            <column name="COMPONENT_CREATE_TIME" remarks="组件创建时间" type="date"/>  
	    </createTable>
	    
	    <createIndex tableName="WIP_COMPONENTUNIT_EQP_UNIT_HIS" indexName="IDX_WIP_COMP_EQP_UNIT_HIS1" tablespace="TS_MES_HIS_IDX" unique="false">
	    	<column name="COMPONENT_RRN"/>
	    </createIndex>
	    <createIndex tableName="WIP_COMPONENTUNIT_EQP_UNIT_HIS" indexName="IDX_WIP_COMP_EQP_UNIT_HIS2" tablespace="TS_MES_HIS_IDX" unique="false">
	    	<column name="TRANS_TIME"/>
	    </createIndex>
	    <createIndex tableName="WIP_COMPONENTUNIT_EQP_UNIT_HIS" indexName="IDX_WIP_COMP_EQP_UNIT_HIS3" tablespace="TS_MES_HIS_IDX" unique="false">
	    	<column name="HISTORY_SEQ"/>
	    </createIndex>
    </changeSet>
    
    <changeSet id="MM-20210419-001-KITTING" author="Clark">
    	<sqlFile path="sql/MM-20210419-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>动态表变成不可编辑</comment>
    </changeSet>
    
    <changeSet id="WIP-20210607-001-FIX" author="HeTao">
    	<sqlFile path="sql/WIP-20210607-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>按设备作业增加列表栏位，增加Message提示</comment>
    </changeSet>
    
    <changeSet id="MESWELL-20210425-CDI-ACTION-DESC" author="FanChengMing">
		<modifyDataType tableName="EDC_SET_CURRENT" columnName="TECN_RRNS" newDataType="VARCHAR2(256 BYTE)"/>
		<createIndex indexName="IDX_WIP_LOT_FLOW_CDI_POINT_HIS" tableName="WIP_LOT_FLOW_CDI_POINT_HIS" tablespace="TS_MES_HIS_IDX" unique="false">
            <column name="TRANS_TIME"/>
        </createIndex>
	</changeSet>
	
	<changeSet id="PRD-20210520-FLOWTABLE" author="FanChengMing">
    	<sqlFile path="sql/PRD-20210520-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>产品模块流程参数表</comment>
    </changeSet>
    
    <changeSet id="MM-20210521-001-PARAMETER" author="FanChengMing">
    	<sqlFile path="sql/MM-20210528-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>只有流程模块的参数tab有映射参数属性</comment>
    </changeSet>
    
    <changeSet id="PRD-20210602-001-QTIME" author="FanChengMing">
    	<sqlFile path="sql/PRD-20210602-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>单工步定时器增加开始结束事件</comment>
    </changeSet>
    <changeSet id="PRD-20210603-PACKAGE_TYPE" author="ShiJianPing">
    	<addColumn tableName="MM_PACKAGE_TYPE">
    		<column name="RESERVED01" remarks="预留栏位" type="VARCHAR2(64 BYTE)"/>
    		<column name="RESERVED02" remarks="预留栏位" type="VARCHAR2(64 BYTE)"/>
    		<column name="RESERVED03" remarks="预留栏位" type="VARCHAR2(64 BYTE)"/>
    		<column name="RESERVED04" remarks="预留栏位" type="VARCHAR2(64 BYTE)"/>
    		<column name="RESERVED05" remarks="预留栏位" type="VARCHAR2(64 BYTE)"/>
    		<column name="RESERVED06" remarks="预留栏位" type="VARCHAR2(64 BYTE)"/>
    		<column name="RESERVED07" remarks="预留栏位" type="VARCHAR2(64 BYTE)"/>
    		<column name="RESERVED08" remarks="预留栏位" type="VARCHAR2(64 BYTE)"/>
    	</addColumn>
    	<comment>包装类型表增加预留栏位，用户扩展客制化的内容</comment>
    </changeSet>
    
    <changeSet id="WIP-20210602-HOLDACITON" author="DaiWenBin">
    	<sqlFile path="sql/WIP-20210602-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>暂停和批量暂停添加动态表</comment>
    </changeSet>
    
   	<changeSet id="WIP-20210608-BYLOT" author="DaiWenBin">
    	<sqlFile path="sql/WIP-20210608-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>按批次作业批次详情添加字段</comment>
    </changeSet>
    
    <changeSet id="PRD-20210608-001-FIELD" author="FanChengMing">
    	<sqlFile path="sql/PRD-20210608-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>QTIME timer事件必输,设备视图显示问题sql修改</comment>
    </changeSet>
    
    <changeSet id="WIP-20210626-001-BY-LOT" author="HeTao">
    	<sqlFile path="sql/WIP-20210626-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>按批次作业ByCarrier时批次列表参考表更新</comment>
    </changeSet>
	
	<changeSet id="PRD-20210609-STEP" author="Tony">
    	<addColumn tableName="WF_PROCESSDEFINITION">
    		<column name="IN_MAIN_MAT_TYPE" remarks="进站主物料类型" type="VARCHAR2(32 BYTE)"/>
    		<column name="OUT_MAIN_MAT_TYPE" remarks="出站转换主物料类型" type="VARCHAR2(32 BYTE)"/>
    	</addColumn>
    </changeSet>
    
    <changeSet id="PRD-20210611-001-PARTSTEP" author="FanChengMing">
    	<sqlFile path="sql/PRD-20210611-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>增加产品工步查询页面</comment>
    </changeSet>

    <changeSet id="PRD-********-001-PARTSTEPGLC" author="FanChengMing">
    	<sqlFile path="sql/PRD-********-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>产品工步查询页面转Glc风格</comment>
    </changeSet>
    
	 <changeSet id="PRD-********-001-DELSTEPMATERIAL" author="Clark">
    	<sqlFile path="sql/PRD-********-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>删除工步页面上的StepMaterial信息</comment>
    </changeSet>
    
    <changeSet id="WIP-********-002-BANK" author="Clark">
    	<sqlFile path="sql/WIP-********-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>BANK Action信息</comment>
    </changeSet>
	
	<changeSet id="PRD-********-PROCEDURE-STEP" author="YanChao">
    	<sqlFile path="sql/PRD-********-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>新建工艺选择流程时，支持查询流程工步等信息</comment>
     </changeSet>
     <changeSet id="WIP-********-COMPONENTUNIT-HISQUERY" author="Tangjiacheng">
    	<sqlFile path="sql/WIP-********-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>组件事务历史查询</comment>
     </changeSet>
     
     <changeSet id="DATATRANSFER-********-VALIDATELOG" author="FanChengMing">
    	<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="PRD_VALIDATE_LOG"/>
			</not>
		</preConditions>
		<createTable remarks="流程校验日志表" tableName="PRD_VALIDATE_LOG" tablespace="TS_MES_HIS_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_PRD_VALIDATE_LOG"/>
             </column>
             <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
             <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
             <column name="CREATED" remarks="创建时间" type="date"/>
             <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
             <column name="UPDATED" remarks="更新时间" type="date"/>
             <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
             <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  
             <column name="TRANS_ID" remarks="事务号" type="VARCHAR2(32 BYTE)"/>
			 <column name="LOG_LEVEL" remarks="日志级别" type="VARCHAR2(32 BYTE)"/>
			 <column name="OBJECT_TYPE" remarks="对象类型" type="VARCHAR2(32 BYTE)"/>
			 <column name="OBJECT_NAME" remarks="对象名称" type="VARCHAR2(200 BYTE)"/>
			 <column name="OBJECT_VERSION" remarks="对象版本" type="NUMBER(19, 0)"/>
			 <column name="MESSAGE" remarks="日志消息" type="VARCHAR2(400 BYTE)"/>	 
		</createTable>
		<comment>流程校验日志表</comment>
	</changeSet>
	
	<changeSet id="WIP-20210705-DISABLE-OLD-UNSCRAP" author="HeTao">
		<sql>update AD_AUTHORITY t set t.is_active = 'N' WHERE NAME = 'Wip.UnScrap'</sql>
		<comment>隐藏废弃的取消报废功能</comment>
	</changeSet>
	
     <changeSet id="BAS-20210607-BAS_ID_GENERATOR_RULE" author="DaiWenBin">
    	<sqlFile path="sql/BAS-20210706-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>ID生成规则英译中修改</comment>
     </changeSet>
     
     <changeSet id="WIP-20210708-001-BYEQP" author="Clark">
    	<sqlFile path="sql/WIP-20210708-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>按设备作业加上设备，批次，载具输入框</comment>
     </changeSet>
     
     <changeSet id="EDC-20210708-001-EDCDATA" author="Clark">
    	<sqlFile path="sql/WIP-20210708-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>数据收集加入了检查Component相关的Message</comment>
     </changeSet>
     
     <changeSet id="WIP-20210706-LOT-SPLIT" author="LiuTao">
    	<sqlFile path="sql/WIP-20210706-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>更新 ad_message,优化载具分批合批 显示效果</comment>
     </changeSet>
     
     <changeSet id="WIP-20210707-ADD-Contamination-Properties" author="HeTao">
     	<preConditions onFail="MARK_RAN">
     		<not>
     			<columnExists tableName="WIP_LOT" columnName="CONTAMINATION_LEVEL"/>
     		</not>
     	</preConditions>
     	<addColumn tableName="WIP_LOT">
     		<column name="CONTAMINATION_LEVEL" remarks="污染级别" type="VARCHAR2(32 BYTE)"/>	 
     	</addColumn>
     	<addColumn tableName="WIP_LOT_HIS">
     		<column name="CONTAMINATION_LEVEL" remarks="污染级别" type="VARCHAR2(32 BYTE)"/>	 
     	</addColumn>
     	
     	<addColumn tableName="RAS_EQP">
     		<column name="CONTAMINATION_FLAG" remarks="污染覆盖标识" type="VARCHAR2(1 BYTE)"/>
     		<column name="CONTAMINATION_LEVEL" remarks="污染级别" type="VARCHAR2(32 BYTE)"/>
     	</addColumn>
     	
     	<addColumn tableName="RAS_EQP_EVENT_HIS">
     		<column name="CONTAMINATION_FLAG" remarks="污染覆盖标识" type="VARCHAR2(1 BYTE)"/>
     		<column name="CONTAMINATION_LEVEL" remarks="污染级别" type="VARCHAR2(32 BYTE)"/>
     	</addColumn>
     	
     	<addColumn tableName="WF_PROCESSDEFINITION">
     		<column name="CONTAMINATION_LEVEL" remarks="污染级别" type="VARCHAR2(32 BYTE)"/>
     	</addColumn>
		<comment>增加防污染字段</comment>
     </changeSet>
     
     <changeSet id="WIP-20210707-ADD-Contamination-Field-Change" author="HeTao">
     	<sqlFile path="sql/WIP-20210707-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
    	<comment>修改防污染对应ADField</comment>
     </changeSet>
     
     <changeSet id="WIP-20210708-ADD-Contamination-Properties" author="HeTao">
     	<preConditions onFail="MARK_RAN">
     		<not>
     			<columnExists tableName="WF_PROCESSDEFINITION_HIS" columnName="CONTAMINATION_LEVEL"/>
     		</not>
     	</preConditions>
     	<addColumn tableName="WF_PROCESSDEFINITION_HIS">
     		<column name="CONTAMINATION_LEVEL" remarks="污染级别" type="VARCHAR2(32 BYTE)"/>
     	</addColumn>
		<comment>增加防污染字段</comment>
     </changeSet>
     
     <changeSet id="WIP-20210708-AD_TABLE" author="DaiWenBin">
    	<sqlFile path="sql/WIP-20210708-003.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>补充MMMLotIQCICDetail动态表</comment>
     </changeSet>
     
     <changeSet id="WIP-20210708-ADD-EquipmentUser-Properties" author="HeTao">
     	<preConditions onFail="MARK_RAN">
     		<not>
     			<columnExists tableName="RAS_EQP_USER" columnName="EQP_TYPE"/>
     		</not>
     	</preConditions>
     	<addColumn tableName="RAS_EQP_USER">
     		<column name="EQP_TYPE" remarks="设备类型" type="VARCHAR2(32 BYTE)"/>
     	</addColumn>
		<comment>增加设备类型字段，可以按设备类型控制设备权限</comment>
     </changeSet>
     
     <changeSet id="WIP-20210708-Equipment-Author-Change" author="HeTao">
     	<sqlFile path="sql/RAS-20210709-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
     	<comment>设备权限功能修改</comment>
     </changeSet>
     
      <changeSet id="WIP-20210709-001-SORTINGJOB" author="Clark">
    	<sqlFile path="sql/WIP-20210709-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>SortingJob功能修改</comment>
     </changeSet>
     
     <changeSet id="WIP-20210713-AD_MESSAGE" author="DaiWenBin">
    	<sqlFile path="sql/WIP-20210713-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>英译中提交</comment>
     </changeSet>
     
     <changeSet id="WIP-20210713-AD_MESSAGE" author="HeTao">
    	<sqlFile path="sql/WIP-20210713-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>track in时处理batch</comment>
     </changeSet>
     
     <changeSet id="WIP-20210715-by-eqp" author="HeTao">
    	<sqlFile path="sql/WIP-20210715-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>修改按设备作业的扫描功能</comment>
     </changeSet>
     
     <changeSet author="HeTao" id="MESWELL-20210715-RAS_EQP_STATE_CASCADE">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="RAS_EQP_STATE_CASCADE"/>
			</not>
		</preConditions>
        <createTable remarks="设备状态级联" tableName="RAS_EQP_STATE_CASCADE" tablespace="TS_MES_DAT">
            <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" 
                	primaryKeyName="PK_RAS_EQP_STATE_CASCADE" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建人" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>
            
            <column name="EQUIPMENT_ID" remarks="设备ID" type="VARCHAR2(32 BYTE)"/>
            <column name="STATE" remarks="级联状态" type="VARCHAR2(32 BYTE)"/>
            <column name="SUB_STATE" remarks="级联子状态" type="VARCHAR2(32 BYTE)"/>
            <column name="IS_ENABLE" remarks="是否生效" type="VARCHAR2(1 BYTE)"/>
            <column name="SEQ_NO" remarks="序号" type="VARCHAR2(32 BYTE)"/>
            <column name="CONDITION" remarks="级联条件" type="VARCHAR2(256 BYTE)"/>
            <column name="DESCRIPTION" remarks="描述" type="VARCHAR2(64 BYTE)"/>
        </createTable>
    </changeSet>
    
    <changeSet author="HeTao" id="MESWELL-20210716-RAS_EQP_STATE_CASCADE_HIS">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="RAS_EQP_STATE_CASCADE_HIS"/>
			</not>
		</preConditions>
        <createTable remarks="设备状态级联历史" tableName="RAS_EQP_STATE_CASCADE_HIS" tablespace="TS_MES_HIS_DAT">
            <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            
            <column name="UPDATED_BY" remarks="创建人" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TYPE" remarks="事物类型" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TIME" remarks="事物时间" type="date"/>
            <column name="HIS_SEQ" remarks="历史序列号" type="VARCHAR2(64 BYTE)"/>
            <column name="HIS_SEQ_NO" remarks="历史序列号次序" type="NUMBER(19, 0)"/>
            
            <column name="EQUIPMENT_ID" remarks="设备ID" type="VARCHAR2(32 BYTE)"/>
            <column name="STATE" remarks="级联状态" type="VARCHAR2(32 BYTE)"/>
            <column name="SUB_STATE" remarks="级联子状态" type="VARCHAR2(32 BYTE)"/>
            <column name="IS_ENABLE" remarks="是否生效" type="VARCHAR2(1 BYTE)"/>
            <column name="SEQ_NO" remarks="序号" type="VARCHAR2(32 BYTE)"/>
            <column name="CONDITION" remarks="级联条件" type="VARCHAR2(256 BYTE)"/>
            <column name="DESCRIPTION" remarks="描述" type="VARCHAR2(64 BYTE)"/>
        </createTable>
    </changeSet>
    
    <changeSet id="RAS-20210708-Equipment-State-Cascade" author="HeTao">
     	<sqlFile path="sql/RAS-20210719-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
     	<comment>设备状态级联功能</comment>
     </changeSet>
     
     <changeSet id="WIP-20210722-001-MESSAGE" author="Clark">
    	<sqlFile path="sql/WIP-20210722-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>英译中提交</comment>
     </changeSet>
     
     <changeSet id="WIP-20210722-002-PARTCHANGEINFO" author="ZhouGeLong">
    	<sqlFile path="sql/WIP-20210722-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>产品修改信息动态表参数修改、产品流程查询菜单名称修改</comment>
     </changeSet>
     
     <changeSet id="WIP-20210708-ADD-EquipmentUser-Scrap-Lot-RRN" author="HeTao">
     	<preConditions onFail="MARK_RAN">
     		<not>
     			<columnExists tableName="WIP_LOT_SCRAP" columnName="SCRAP_LOT_RRN"/>
     		</not>
     	</preConditions>
     	<addColumn tableName="WIP_LOT_SCRAP">
     		<column name="SCRAP_LOT_RRN" remarks="" type="NUMBER(19, 0)"/>
     	</addColumn>
		<comment>增加报废批次RRN</comment>
     </changeSet>
     
     <changeSet id="MM-20210728-MM-STATUSMODEL" author="ZhouGeLong">
		<sqlFile path="sql/MM-20210728-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>英译中提交</comment>
	</changeSet>
	
	 <changeSet id="WIP-20210728-001-TRACKBYEQP" author="ZouYiSong">
		<sqlFile path="sql/WIP-20210728-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>修改按设备作业作业准备界面栏位配置</comment>
	</changeSet>
	
	<changeSet id="WIP-20210728-SCRAP-ID-RULE" author="HeTao">
		<sqlFile path="sql/WIP-20210728-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>报废分批参数、ID生成规则及反报废提示信息</comment>
	</changeSet>
	
	<changeSet id="RAS-20210728-Equipment-state-Cascade" author="HeTao">
		<sqlFile path="sql/RAS-20210728-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>设备状态级联界面调整</comment>
	</changeSet>
	
	<changeSet id="BAS-20210802-BAS-CALENDAR-DROP-VIEW" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<viewExists viewName="V_BAS_CALENDAR"/>
		</preConditions>
		<dropView viewName="V_BAS_CALENDAR"/>
		<comment>工作日历详情日历编号参考值修改，删除视图</comment>
	</changeSet>
	
	<changeSet id="WIP-20210730-LOT-FUTUREHOLD-PARAMETER-VALUE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20210730-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>添加批次未来暂停流程是否自动展开节点的参数</comment>
	</changeSet>
	
	<changeSet id="BAS-20210802-BAS-CALENDAR" author="tangjiacheng">
		<sqlFile path="sql/BAS-20210802-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>工作日历详情日历编号参考值修改</comment>
	</changeSet>
	
	<changeSet id="BAS-20210802-REFLIST-DATEFORMAT" author="ShiJianPing">
		<sqlFile path="sql/BAS-20210802-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>ID规则增加支持日期格式</comment>
	</changeSet>
	
	<changeSet id="WIP-20210803-SORTING_JOB" author="DaiWenBin">
		<sqlFile path="sql/WIP-20210803-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>载具任务添加刷新按钮</comment>
	</changeSet>
	
	<changeSet id="WIP-20210804-PART_STEP" author="FanChengMing">
		<sqlFile path="sql/WIP-20210804-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>产品流程查看修改model内容</comment>
	</changeSet>
	
	<changeSet id="WIP-20210805-BATCH-LOT" author="DaiWenBin">
		<sqlFile path="sql/WIP-20210805-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>batch作业卡控添加英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20210803-WORKORDER-BOM-LINE" author="ZhouGeLong">
		<sqlFile path="sql/WIP-20210805-002.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>工单bom动态栏位修改</comment>
	</changeSet>
	
	<changeSet id="PRD-20210806-001-STEP" author="Clark">
		<sqlFile path="sql/PRD-20210806-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>更新几个工步属性</comment>
	</changeSet>

	<changeSet id="WIP-20210806-001-FUTUREACTION-HIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_ACTION_HIS" columnName="EQUIPMENT_ID"/>
			</not>
		</preConditions>	
		<addColumn tableName="WIP_FUTURE_ACTION_HIS">
			<column name="EQUIPMENT_ID" remarks="设备" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
	</changeSet>
	
	<changeSet id="WIP-20210806-002-FUTUREACTION-HIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_ACTION_HIS" columnName="IS_START_PILOT_USE_CURRENTSTEP"/>
			</not>
		</preConditions>	
		<addColumn tableName="WIP_FUTURE_ACTION_HIS">
			<column name="IS_START_PILOT_USE_CURRENTSTEP" remarks="是否当前工步作为开始工步" type="VARCHAR2(1 BYTE)"/>
		</addColumn>
	</changeSet>
	
	<changeSet id="RAS-20210804-001-CODNITION" author="HeTao">
		<sqlFile path="sql/RAS-20210804-001.sql" encoding="GBK" relativeToChangelogFile="true"/>
		<comment>更新动态表条件</comment>
	</changeSet>
	
	<changeSet id="WIP-20210809-001_CHANGE_PARTSTEP" author="FanChengMing">
       <sqlFile path="sql/WIP-20210809-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>更新产品流程导出的动态表</comment>
	</changeSet>
	
	<changeSet id="MM-20210810-001_BOM_COPY" author="ZhouGeLong">
       <sqlFile path="sql/MM-20210810-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>新建BOM拷贝从动态栏位参考表</comment>
	</changeSet>
	
	<changeSet id="MESWELL-20210908-CDI-ACTION-NAME" author="ZhouGeLong">
		<modifyDataType tableName="WIP_LOT_FLOW_CDI_POINT" columnName="CDI_ACTION_NAME" newDataType="VARCHAR2(64 BYTE)"/>
		<modifyDataType tableName="WIP_LOT_FLOW_CDI_POINT_HIS" columnName="CDI_ACTION_NAME" newDataType="VARCHAR2(64 BYTE)"/>
	</changeSet>
	
	<changeSet id="WIP-********-001_DEFECT_BY_COMPONENT_REV01" author="IvanLee">
       <sqlFile path="sql/WIP-********-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <sqlFile path="sql/WIP-********-002.sql" relativeToChangelogFile="true" encoding="GBK"/>
       <comment>Component缺陷登记</comment>
	</changeSet>
	
	<changeSet id="WIP-********-002_GRADE_COMPONENT_REV01" author="IvanLee">
       <sqlFile path="sql/WIP-********-005.sql" relativeToChangelogFile="true" encoding="GBK"/>
       <sqlFile path="sql/WIP-********-006.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Component分档</comment>
	</changeSet>
	
	<changeSet id="WIP-2021104-004_BANKMANAGE" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
		 <sqlCheck expectedResult="0">
			select count(*) from ad_form t where t.name = 'WIPBankLotManageEditor'		 
		 </sqlCheck>
		</preConditions>
       <sqlFile path="sql/WIP-********-004.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Bank管理页面</comment>
	</changeSet>
	
	<changeSet id="WIP-********-012_GRADE_COMPONENT" author="WangLuoPeng">
       <sqlFile path="sql/WIP-********-012.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Component分档</comment>
	</changeSet>
	<changeSet id="RAS-********-001_ProjectConstraint" author="MoYouMing">
       <sqlFile path="sql/RAS-********-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>工程linkGlc重构</comment>
	</changeSet>
	
	<changeSet id="WIP-2021104-001_MANUALMOVENEXT" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
		 <sqlCheck expectedResult="0">
			select count(*) from ad_form t where t.name = 'LotManualMoveNextForm'		 
		 </sqlCheck>
		</preConditions>
       <sqlFile path="sql/WIP-********-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <sqlFile path="sql/WIP-********-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <sqlFile path="sql/WIP-********-003.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <sqlFile path="sql/WIP-********-004.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Bank管理页面</comment>
	</changeSet>
	
	<changeSet id="WIP-2021104-005_MANUALMOVENEXT" author="WangLuoPeng">
       <sqlFile path="sql/WIP-********-005.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>手动过站</comment>
	</changeSet>
	<changeSet id="WIP-********-001_MANUALMOVENEXT" author="MoYouMing">
       <sqlFile path="sql/WIP-********-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>批次在线返工优化</comment>
	</changeSet>
	<changeSet id="WIP-********-001_LOTTRACKUISHOW10STEPS" author="MoYouMing">
       <sqlFile path="sql/WIP-********-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>批次进站显示futuresteps</comment>
	</changeSet>
	
	<changeSet id="WIP-********-002_RECIPECONDITION" author="WangLuoPeng">
       <sqlFile path="sql/WIP-********-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>进站SelectEquipmentPage显示Condition</comment>
	</changeSet>
	
	<changeSet id="WIP-********-003-TOKEN-ADD-COLUMN" author="WangLuoPeng">	
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_TOKEN" columnName="HI_SUPER_START_NODE_RRN"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WF_TOKEN">
    		<column name="HI_SUPER_START_NODE_RRN" remarks="本流程结束后,返回到父流程的父流程的开始位置" type="NUMBER(19)"/>
    	</addColumn>
    </changeSet>
    
	<changeSet id="WIP-********-004-NODE-ADD-COLUMN" author="WangLuoPeng">	
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_NODE" columnName="HI_SUPER_INSTRUCTION"/>
			</not>
		</preConditions>
		
    	<addColumn tableName="WF_NODE">
    		<column name="HI_SUPER_INSTRUCTION" remarks="返工返回上上级流程指示" type="VARCHAR2(256)"/>
    	</addColumn>
    </changeSet>
	<changeSet id="WIP-********-005_LOTTRACKUISHOW10STEPS" author="MoYouMing">
       <sqlFile path="sql/WIP-********-005.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>批次进站显示futuresteps</comment>
	</changeSet>
    
    <changeSet id="WIP-20211208-001-ADD-TABLE_REWORKRETURNTRANSITION" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="PRD_REWORK_RETURN_TRANSITION"/>
			</not>
		</preConditions>		
	 	<createTable remarks="hiRework返工返回节点信息" tableName="PRD_REWORK_RETURN_TRANSITION" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_PRD_REWORK_RETURN_TRANSITION"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19)"/>
            
            <column name="SUPER_PROCEDURE_NAME" remarks="父流程名称（pilot流程）" type="VARCHAR2(32 BYTE)"/>
            <column name="SUPER_PROCEDURE_VERSION" remarks="父流程版本" type="NUMBER(19)"/>
            <column name="REWORK_PROCEDURE_NAME" remarks="返工流程名称" type="VARCHAR2(32 BYTE)"/>
            <column name="REWORK_PROCEDURE_VERSION" remarks="返工流程版本" type="NUMBER(19)"/>
            <column name="HI_SUPER_INSTRUCTION" remarks="返工返回上上级流程指示" type="VARCHAR2(256)"/>
            <column name="RETURN_PROCEDURE_NAME" remarks="返回流程名称" type="VARCHAR2(32 BYTE)"/>
            <column name="RETURN_PROCEDURE_VERSION" remarks="返回流程版本" type="NUMBER(19)"/>
            <column name="RETURN_STEP_NAME" remarks="返回工步名称" type="VARCHAR2(32 BYTE)"/>
	    </createTable>	    
    </changeSet>
     <changeSet id="WIP-20211209-001-delete-TABLE_REWORKRETURNTRANSITION" author="WangLuoPeng">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="PRD_REWORK_RETURN_TRANSITION"/>
		</preConditions>		
	    <dropTable tableName="PRD_REWORK_RETURN_TRANSITION"/>
    </changeSet>
	
	<changeSet id="WIP-20211215-001-ADD-TABLE_WIP_LOT_DISPATCH_FORBIDDEN" author="MoYouMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="WIP_LOT_DISPATCH_FORBIDDEN"/>
			</not>
		</preConditions>		
	 	<createTable remarks="Lot禁止派工信息表" tableName="WIP_LOT_DISPATCH_FORBIDDEN" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_DISPATCH_FORBIDDEN"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19)"/>
            <column name="LOT_RRN" remarks="批次RRN" type="NUMBER(19)"/>
            <column name="FORBIDDEN_FLAG" remarks="是否禁止派工" type="VARCHAR2(1 BYTE)"/>
	    </createTable>	
		<createIndex indexName="IDX_WIP_LOT_DISPATCH_FORBIDDEN" tableName="WIP_LOT_DISPATCH_FORBIDDEN" tablespace="TS_MES_IDX" unique="true">
            <column name="ORG_RRN"/>
			<column name="LOT_RRN"/>
         </createIndex>		
    </changeSet>
    <changeSet id="WIP-20211215-001_LOTDISPATCHFORBIDDEN" author="MoYouMing">
       <sqlFile path="sql/WIP-20211215-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>批次派工禁止功能</comment>
	</changeSet>	
	<changeSet id="WIP-20211216-003_LOTDISPATCHFORBIDDEN" author="MoYouMing">
       <sqlFile path="sql/WIP-20211216-003.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>批次派工禁止功能优化</comment>
	</changeSet>
	
	<changeSet id="WIP-20211220-001-WF_PROCESSDEFINITION_ADD" author="MoYouMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_PROCESSDEFINITION" columnName="MAX_REWORK_COUNT"/>
			</not>
		</preConditions>	
		<addColumn tableName="WF_PROCESSDEFINITION">
			<column name="MAX_REWORK_COUNT" remarks="最大返工次数" type="NUMBER(19)"/>
		</addColumn>
	</changeSet>
	<changeSet id="WIP-20211220-001_PRDProcedure_ADD" author="MoYouMing">
       <sqlFile path="sql/WIP-20211220-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>流程增加返工信息栏位</comment>
	</changeSet>
	<changeSet id="WIP-20211220-001-WF_PROCESSDEFINITIONHIS_ADD" author="MoYouMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_PROCESSDEFINITION_HIS" columnName="MAX_REWORK_COUNT"/>
			</not>
		</preConditions>	
		<addColumn tableName="WF_PROCESSDEFINITION_HIS">
			<column name="MAX_REWORK_COUNT" remarks="最大返工次数" type="NUMBER(19)"/>
		</addColumn>
	</changeSet>
	<changeSet id="WIP-20211220-001_WipLotReworkInfo" author="MoYouMing">
       <sqlFile path="sql/WIP-20211220-003.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>批次详情增加返工信息</comment>
	</changeSet>
	<changeSet id="RAS-20211221-001_RAS_EQUIP_ALARM_FORWARD_TO_AMS" author="IvanLee">
       <sqlFile path="sql/RAS-20211221-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>设备警报是否通过AMS转发出去</comment>
	</changeSet>
	
	<changeSet id="WIP-20211222-001_REWORK_PROCEDURE" author="WangLuoPeng">
       <sqlFile path="sql/WIP-20211222-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>增加REWORK UI 流程筛选返工流程</comment>
	</changeSet>
	
	<changeSet id="WIP-20211222-002_LOTquery" author="MoYouMing">
       <sqlFile path="sql/WIP-20211222-002.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>批次详情显示未来10工步</comment>
	</changeSet>
	
	<changeSet id="WIP-20220121-001_Stage_Import" author="yanjiazhi">
       <sqlFile path="sql/WIP-20220121-001.sql" relativeToChangelogFile="true" encoding="GBK"/> 
       <comment>Stage增加导入功能</comment>
	</changeSet>

	<changeSet id="CHJS-20220805-001-WIP_LOT_EQUIPMENT_UNIT_ADD_COLUMN" author="Bing.Ma">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_EQUIPMENT_UNIT" columnName="DUMMY_CAPACITY"/>
			</not>
		</preConditions>		
		<addColumn tableName="WIP_LOT_EQUIPMENT_UNIT">
			<column name="DUMMY_CAPACITY" remarks="Dummy容量" type="NUMBER(19, 0)"/>
		</addColumn>
	   <comment>WIP_LOT_EQUIPMENT_UNIT表增加DUMMY_CAPACITY字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20220805-002-EQP_BUFFER_POSITION_ENHANCE" author="Bing.Ma">
		<sqlFile path="sql/WIP-20220805-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>Equipment Buffer Position功能增强与优化</comment>
	</changeSet>
</databaseChangeLog> 