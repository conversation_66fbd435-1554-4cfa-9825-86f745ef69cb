<?xml version="1.1" encoding="GBK" standalone="no"?>
<databaseChangeLog
	xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
	xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
	xmlns:pro="http://www.liquibase.org/xml/ns/pro"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.9.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.9.xsd">

	<changeSet id="RAS-20220907-001-ADD_EQPGROUP" author="Clark">
		<sqlFile path="sql/RAS-20220907-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>增加设备组功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20221114-001-ByEqp-Locaction" author="HeTao">
		<sqlFile path="sql/WIP-20221114-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>按设备、位置作业功能增强</comment>
	</changeSet>
	
	<changeSet id="WIP-20221115-001-ByEqp-Locaction" author="HeTao">
		<sqlFile path="sql/WIP-20221115-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>按设备、位置作业表格功能增强</comment>
	</changeSet>
	
	<changeSet id="WIP-20221117-001-Lot-List-Composite-Authority" author="HeTao">
		<sqlFile path="sql/WIP-20221117-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>Lot List Composite右键菜单权限列表</comment>
	</changeSet>
	
	<changeSet id="BAS-20221121-001-DELETE-X-Sequence Value" author="tangjiacheng">
		<sqlFile path="sql/BAS-20221121-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>ID生成规则移除X-Sequence Value类型</comment>
	</changeSet>
	
	<changeSet id="WIP-20221123-004-BY-LOT-GLC" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221123-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>按批次作业功能改为GLC模式</comment>
	</changeSet>
	
	<changeSet id="WIP-20221123-001-HIDE-AD_AUTHORITY" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221123-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>隐藏旧功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20221123-013-WIP-LOT-QUERY-GLC" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221123-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>在制品查询改为GLC模式并添加右键弹框</comment>
	</changeSet>
	
	<changeSet id="WIP-20221123-010-WIP-LOT-QUERY-GLC-UPDATE" author="zhougelong">
		<sqlFile path="sql/WIP-20221123-010.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>在制品查询改为GLC模式并添加右键弹框</comment>
	</changeSet>
	
	<changeSet id="WIP-20221123-012-WIP-LOT-ACTION-PARAMETER" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221124-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>批次参数修改弹框改为actionDialog</comment>
	</changeSet>
	
	<changeSet id="WIP-20221125-001-WIP-TRACK-COMMENT-UPDATE" author="zhougelong">
		<sqlFile path="sql/WIP-20221124-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>批次作业参数xml提交</comment>
	</changeSet>
	
	<changeSet id="WIP-20221125-002-BY-LOT-AUTHORITY-update" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221125-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>按批次作业添加按钮权限</comment>
	</changeSet>

	<changeSet id="EDC-20221125-001-PROCESS-GROUP" author="HeTao">
		<renameColumn tableName="EDC_PROCESS_GROUP" oldColumnName="PROCESS" newColumnName="PROCESS_NAME"/>
		<renameColumn tableName="EDC_PROCESS_GROUP" oldColumnName="PART" newColumnName="PART_NAME"/>
		<renameColumn tableName="EDC_PROCESS_GROUP" oldColumnName="PROCEDURE" newColumnName="PROCEDURE_NAME"/>
		
		<renameColumn tableName="EDC_PROCESS_GROUP_DATA" oldColumnName="EQUIPMENT" newColumnName="EQUIPMENT_ID"/>
		<renameColumn tableName="EDC_PROCESS_GROUP_DATA" oldColumnName="PROCESS" newColumnName="PROCESS_NAME"/>
		<renameColumn tableName="EDC_PROCESS_GROUP_DATA" oldColumnName="PART" newColumnName="PART_NAME"/>
		<renameColumn tableName="EDC_PROCESS_GROUP_DATA" oldColumnName="PROCEDURE" newColumnName="PROCEDURE_NAME"/>
		<renameColumn tableName="EDC_PROCESS_GROUP_DATA" oldColumnName="RETICLE" newColumnName="RETICLE_ID"/>
		<comment>Process Group数据库字段名修改</comment>
	</changeSet>
	
	<changeSet id="EDC-20221125-001-PROCESS-GROUP-ADFIELD" author="HeTao">
		<sqlFile path="sql/EDC-20221125-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>Process Group动态表字段名修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20221125-002-BY-LOT-AUTHORITY-UPDATE" author="zhougelong">
		<sqlFile path="sql/WIP-20221125-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>不规范message修改及页面名称变更sql</comment>
	</changeSet>
	
	<changeSet id="WIP-20221125-004-AD_MESSAGE-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221125-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>message修改</comment>
	</changeSet>
	
	<changeSet id="MM-20221129-002-CARRIER-ACTION-QUERY" author="tangjiacheng">
		<sqlFile path="sql/MM-20221129-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>载具动作查询功能</comment>
	</changeSet>
	
	<changeSet id="RAS-20221129-003-EQP-ACTION-QUERY" author="tangjiacheng">
		<sqlFile path="sql/RAS-20221129-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>设备动作查询</comment>
	</changeSet>
	
	<changeSet id="MM-20221129-002-MLOT-ACTION-QUERY-2" author="zhougelong">
		<sqlFile path="sql/MM-20221129-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料查询页面重构权限及message</comment>
	</changeSet>
	
	<changeSet id="MM-20221129-002-MLOT-ACTION-DIALOG-XML" author="zhougelong">
		<sqlFile path="sql/MM-20221129-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料查询页面XML及sql</comment>
	</changeSet>
	
	<changeSet id="MM-20221130-001-AUTHORITY-2" author="zhougelong">
		<sqlFile path="sql/MM-20221130-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料查询页面按钮权限</comment>
	</changeSet>
	
	<changeSet id="WIP-20221130-001-WIP-LOT-QUERY-AUTHORITY" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221130-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>在制品查询右键弹框添加权限</comment>
	</changeSet>
	
	<changeSet id="BAS-20221130-003-AD_MESSAGE-ADD" author="ShiJianPing">
		<sqlFile path="sql/BAS-20221130-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>message修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20221201-001-AD_MESSAGE-ADD" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221201-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>message修改</comment>
	</changeSet>
	
	<changeSet id="MM-20221202-001-AUTHORITY-CANCEL" author="zhougelong">
		<sqlFile path="sql/MM-20221202-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料页面取消权限按钮</comment>
	</changeSet>
	
	<changeSet id="WIP-20221202-001-FROM-UPDATE-2" author="zhougelong">
		<sqlFile path="sql/WIP-20221202-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>适用于后道数据库发现的一些问题修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20221202-002-AD_MESSAGE-ADD" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221202-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>设备查询，载具查询页面调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20221202-010-LOT-SKIP" author="HeTao">
		<sqlFile path="sql/WIP-20221202-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>批次跳步功能优化</comment>
	</changeSet>
	
	<changeSet id="WIP-20221202-011-LOT-BACKUP" author="HeTao">
		<sqlFile path="sql/WIP-20221202-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>批次退步功能优化</comment>
	</changeSet>
	
	<changeSet id="WIP-20221206-002-AUTHORITY-IMAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221206-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>功能菜单图片调整</comment>
	</changeSet>
	
	<changeSet id="MM-20221206-001-IMAGE-UPDATE2" author="zhougelong">
		<sqlFile path="sql/MM-20221206-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>菜单页面图标修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20221207-001-VIEW-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221207-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>一些页面修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20221207-002-WO-UN-START" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221207-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>取消投料改为GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20221207-003-QTIME-VIEW-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221207-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>QTime页面调整</comment>
	</changeSet>
	
	<changeSet id="BAS-20221208-001-ID-GENERATOR" author="HeTao">
		<sqlFile path="sql/BAS-20221208-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>id生成器增加Sequence Cycle</comment>
	</changeSet>
	
	<changeSet id="WIP-20221208-010-LOT-PREPARE-ID-GENERATOR" author="HeTao">
		<sqlFile path="sql/WIP-20221208-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>Lot Prepare id生成改为ID生成器生成</comment>
	</changeSet>
	
	<changeSet id="WIP-20221214-011-WO-START-BY-GLC" author="DaiWenBin">
		<sqlFile path="sql/WIP-20221212-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>工单投料改为GLC</comment>
	</changeSet>
	
	<changeSet id="MM-20221212-001-AUTHORITY-UPDATE" author="zhougelong">
		<sqlFile path="sql/MM-20221212-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料查询页面按钮权限修改</comment>
	</changeSet>
	
	<changeSet id="RAS-20221212-001-ADDCOLOUM_WIPLOT" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT" columnName="STEP_STACK"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_LOT">
			<column name="STEP_STACK" remarks="带路径的工步" type="VARCHAR2(512 BYTE)"/>
		</addColumn>
		<comment>批次表增加STEP_STACK字段</comment>
	</changeSet>
	
	<changeSet id="RAS-20221212-002-ADDCOLOUM_WIPLOTHIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_HIS" columnName="STEP_STACK"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_LOT_HIS">
			<column name="STEP_STACK" remarks="带路径的工步" type="VARCHAR2(512 BYTE)"/>
		</addColumn>
		<comment>批次表增加STEP_STACK字段</comment>
	</changeSet>
	
	<changeSet id="RAS-20221212-003-ADDCOLOUM_EQPEVENTHIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP_EVENT_HIS" columnName="STATE_ENTRY_TIME"/>
			</not>
		</preConditions>

		<addColumn tableName="RAS_EQP_EVENT_HIS">
			<column name="STATE_ENTRY_TIME" remarks="状态输入时间" type="date"/>
		</addColumn>
		<comment>设备事件历史表增加STATE_ENTRY_TIME字段</comment>
	</changeSet>
	
	<changeSet id="EDC-20221212-001-IMAGE" author="zhougelong">
		<sqlFile path="sql/EDC-20221212-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>数据收集菜单图标修改</comment>
	</changeSet>

    <changeSet id="WIP-20221213-001-WO-GLC" author="zhougelong">
		<sqlFile path="sql/WIP-20221213-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>工单管理glc</comment>
	</changeSet>
	
	<changeSet id="WIP-20221213-001-WO-UPDATE" author="zhougelong">
		<sqlFile path="sql/WIP-20221213-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>工单管理代码路径修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20221213-001-V8TEST-UPDATE2" author="zhougelong">
		<sqlFile path="sql/WIP-20221213-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>V8Test库一些页面规范修改，mm后台异常message补充</comment>
	</changeSet>
	
	<changeSet id="MM-20221213-001-MM-ACTION-QUERY" author="tangjiacheng">
		<sqlFile path="sql/MM-20221213-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料查询添加报废，取消报废，分批合批，历史信息，详细信息等功能</comment>
	</changeSet>
	
	<changeSet id="MM-20221213-002-AD-AUTHORITY-SETUP" author="tangjiacheng">
		<sqlFile path="sql/MM-20221213-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>隐藏报废，取消报废，分批合批，历史信息，详细信息</comment>
	</changeSet>
	
	<changeSet id="WIP-20221214-011-UPDATE-INTERFACE-EFFECT" author="Daiwenbin">
		<sqlFile path="sql/WIP-20221214-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新一些不正常界面的sql</comment>
	</changeSet>
	
	<changeSet id="WIP-20221214-006-UPDATE_PAGE" author="Clark">
		<sqlFile path="sql/WIP-20221214-006.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新一些页面显示不正常的SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20221214-012-ROLLBACK-MUILTSCHEDULELOT" author="DaiWenBin">
		<sqlFile path="sql/WIP-20221214-012.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>回滚批量批次创建界面的sql</comment>
	</changeSet>
	
	<changeSet id="MM-20221214-010-MANAGER-MESSAGE" author="zhougelong">
		<sqlFile path="sql/MM-20221214-010.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>后台message补充</comment>
	</changeSet>
	
	<changeSet id="RAS-20221214-020-UPDATE-AD_AUTHORITY" author="DaiWenBin">
		<sqlFile path="sql/RAS-20221214-020.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新工艺限制条件的按钮权限</comment>
	</changeSet>
	
	<changeSet id="WIP-20221215-001-WO-UPDATE" author="zhougelong">
		<sqlFile path="sql/WIP-20221215-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>工单管理修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20221219-006-ACTIVE_AUTHORITY" author="Clark">
		<sqlFile path="sql/WIP-20221219-006.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>显示有关采单</comment>
	</changeSet>
	
	<changeSet id="WIP-20221220-006-AUTHORITY_SEQ_UPDATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221219-010.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>部分菜单名称顺序修改</comment>
	</changeSet>
	
	<changeSet id="BAS-20221220-001-ID-GENARATOR" author="hetao">
		<sqlFile path="sql/BAS-20221220-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>ID生成器SequenceCycle Rule优化</comment>
	</changeSet>
	
	<changeSet id="WIP-20221220-010-WO-UPDATE" author="zhougelong">
		<sqlFile path="sql/WIP-20221220-010.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>工单批次列表栏位输入框修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20221220-020-Started-QTime-Query" author="hetao">
		<sqlFile path="sql/WIP-20221220-020.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>已开始QTime功能修改</comment>
	</changeSet>
	
	<changeSet id="MM-20221220-001-MMLOT-PACKAGE" author="tangjiacheng">
		<sqlFile path="sql/MM-20221220-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>一些功能调出来显示，物料批包装的一些message</comment>
	</changeSet>
	
	<changeSet id="WIP-20221220-011-AUTHORITY-AND-REWORK" author="zhougelong">
		<sqlFile path="sql/WIP-20221220-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>将隐藏功能打开，在线返工弹窗页面增加描述等信息</comment>
	</changeSet>
	
	<changeSet id="WIP-20221220-012-OPEN-AUTHORITY" author="DaiWenBin">
		<sqlFile path="sql/WIP-20221220-012.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>将语雀上涉及到的一些功能打开</comment>
	</changeSet>
	
	<changeSet id="WIP-20221220-021-Active-Authority" author="hetao">
		<sql>
			update AD_AUTHORITY a set a.is_active = 'Y' WHERE a.name = 'Wip.ComponentGrade';
			update AD_AUTHORITY a set a.is_active = 'Y' WHERE a.name = 'Wip.LotComponentBondiong';
			update AD_AUTHORITY a set a.is_active = 'Y' WHERE a.name = 'Wip.OutSource';
			update AD_AUTHORITY a set a.is_active = 'Y' WHERE a.name = 'Wip.ByStep';
		</sql>
		<comment>放开隐藏的产品功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20221220-022-Grade-Lot" author="hetao">
		<sql>update AD_FORM f set f.weights = '45;60;59' where f.name = 'GradeLotComponentMainForm';</sql>
		<comment>分档主页面显示比例调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20221221-001-OPEN-WORKORDER" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221221-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>打开一些工单管理下的功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20221221-001-SPLITPACK-EDITOR" author="zhougelong">
		<sqlFile path="sql/WIP-20221221-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>批次拆包editor不正确修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20221222-001-CHANGE-WO-AND-MOVE-MODEL" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221222-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>修改工单投料页面,调整模组分档功能位置</comment>
	</changeSet>
	
	<changeSet id="WIP-20221222-002-CHANGE-WO-AND-MULITWO" author="zhougelong">
		<sqlFile path="sql/WIP-20221222-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>多级工单与工单页面问题修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20221223-002-MODEL-SPLIT" author="tangjiacheng">
		<sqlFile path="sql/WIP-20221223-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>模组分档页面调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20221223-011-UPDATE_AD_FIELD" author="DaiWenBin">
		<sql>update ad_field set is_display = 'N' where table_rrn = 93661714 and name = 'attribute2';</sql>	   
		<comment></comment>
	</changeSet>
	
	<changeSet id="EDC-20221229-001-AQL-PLAN" author="hetao">
		<sqlFile path="sql/EDC-20221229-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>AQL Plan参考值补充</comment>
	</changeSet>
	
	<changeSet id="PRD-20221230-001-SPEC-PARAMETER" author="Clark">
		<sqlFile path="sql/PRD-20221230-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>SPEC参数调整</comment>
	</changeSet>
	
	<changeSet id="MM-20221230-001-TOOL-ACTION-VIEW" author="tangjiacheng">
		<sqlFile path="sql/MM-20221230-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>治具事件管理添加详细信息和历史按钮</comment>
	</changeSet>
	
	<changeSet id="WIP-20230103-011-AD-MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230103-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20230103-012-AD-MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230103-012.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20230104-021-UPDATE-AD-AUTHORITY-LABEL" author="DaiWenBin">
		<sql>update ad_authority set Label = 'MLot' where name = 'MM.MLotQueryGlc';</sql>   
		<comment>修改物料批管理菜单的Label</comment>
	</changeSet>
	
	<changeSet id="WIP-20230104-022-UPDATE-AD-MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230104-022.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="MM-20230105-001-TOOL-ACTION-GLC" author="tangjiacheng">
		<sqlFile path="sql/MM-20220105-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>治具事件管理改为GLC模式</comment>
	</changeSet>
	
	<changeSet id="WIP-20230105-001-AD-MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230105-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20230105-010-ADD-WAFERQUERY-2" author="zhougelong">
		<sqlFile path="sql/WIP-20230105-010.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>新增晶圆来料glc页面</comment>
	</changeSet>
	
	<changeSet id="WIP-20230105-011-AUTHORITY-UPDATE" author="zhougelong">
		<sqlFile path="sql/WIP-20230105-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>Component暂停放行按要求打开</comment>
	</changeSet>

	<changeSet id="APF-20230105-ROUTE-WATCH" author="LiTao">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="APF_WATCH_LOG"/>
			</not>
		</preConditions>
		<createTable remarks="APF route watch log" tableName="APF_WATCH_LOG" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_APF_WATCH_LOG"/>
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
			<column name="CATEGORY" remarks="类别" type="VARCHAR2(32 BYTE)"/>
			<column name="MESSAGE_NAME" remarks="消息名称" type="VARCHAR2(64 BYTE)"/>
			<column name="TRANSACTION_ID" remarks="会话ID" type="VARCHAR2(32 BYTE)"/>
			<column name="ROUTE_ID" remarks="节点ID" type="VARCHAR2(64 BYTE)"/>
			<column name="TRANS_TYPE" remarks="事务类型" type="VARCHAR2(32 BYTE)"/>
			<column name="EVENT_TIME" remarks="事务时间" type="date"/>
			<column name="DURATION" remarks="时长" type="NUMBER(19, 0)"/>
			<column name="REQUEST_MESSAGE" remarks="请求消息" type="CLOB"/>
			<column name="RESPONSE_MESSAGE" remarks="回复消息" type="CLOB"/>
			<column name="RESULT_CODE" remarks="结果码" type="VARCHAR2(32 BYTE)"/>
			<column name="RESULT_MESSAGE" remarks="结果" type="VARCHAR2(1024 BYTE)"/>
			<column name="EQUIPMENT_ID" remarks="设备ID" type="VARCHAR2(64 BYTE)"/>
			<column name="HOST" remarks="HOST NAME" type="VARCHAR2(64 BYTE)"/>
		</createTable>

		<createTable remarks="消息时间记录表" tableName="APF_WATCH_TIME_TABLE" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_APF_WATCH_TIME_TABLE"/>
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
			<column name="CATEGORY" remarks="类别" type="VARCHAR2(32 BYTE)"/>
			<column name="MESSAGE_NAME" remarks="消息名称" type="VARCHAR2(64 BYTE)"/>
			<column name="LAST_PROCESS_TIME" remarks="最后执行时间" type="date"/>
			<column name="EQUIPMENT_ID" remarks="设备ID" type="VARCHAR2(64 BYTE)"/>
			<column name="HOST" remarks="HOST NAME" type="VARCHAR2(64 BYTE)"/>
		</createTable>
	</changeSet>
	
	<changeSet id="WIP-20230105-004-AUTHORITY-IS-ACTIVE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230105-004.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>激活两个隐藏的功能</comment>
	</changeSet>
	
	<changeSet id="BAS-20230106-001-ID-GENERAL-CYCLE" author="HeTao">
		<sqlFile path="sql/BAS-20230106-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>Cycle Sequence支持生成策略</comment>
	</changeSet>
	
	<changeSet id="PRD-20230111-001-PRD_FLOW_CONDITION_RULE" author="DaiWenBin">
		<sqlFile path="sql/PRD-20230111-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充流程条件规则</comment>
	</changeSet>
	
	<changeSet id="PRD-20230111-002-PRD_STEP_FIELD_UPDATE" author="DaiWenBin">
		<sqlFile path="sql/PRD-20230111-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新工步管理动态表</comment>
	</changeSet>
	
	<changeSet id="WIP-20230111-001-LOTACTIONDIALOG-MESSAGE" author="zhougelong">
		<sqlFile path="sql/WIP-20230111-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>在制品查询右键未选择批次message补充</comment>
	</changeSet>
	
	<changeSet id="EDC-20230112-001-AD-AUTHORITY-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/EDC-20230112-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>隐藏EDC数据工步关系映射功能</comment>
	</changeSet>
	
	<changeSet id="EDC-20230128-001-AD-AUTHORITY-UPDATE" author="DaiWenBin">
		<sql>update AD_AUTHORITY set is_active = 'N' where name = 'WIP.BatchResumeManage';</sql>	   
		<comment>隐藏Batch 恢复信息管理</comment>
	</changeSet>
	
	<changeSet id="WIP-20230202-003-PRODUCT-MANAGER-GLC" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230202-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>新增产品glc页面</comment>
	</changeSet>
	
	<changeSet id="WIP-20230206-001-STEP-MANAGER-GLC" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230206-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>新增工步glc页面</comment>
	</changeSet>
	
	<changeSet id="WIP-20230207-001-PROCEDURE-MANAGER-GLC" author="DaiwenBin">
		<sqlFile path="sql/WIP-20230207-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>新增模块glc页面</comment>
	</changeSet>
	
	<changeSet id="RAS-20230208-001-EQUIPMENT-MANAGER-GLC" author="tangjiacheng">
		<sqlFile path="sql/RAS-20230208-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>新增设备glc页面</comment>
	</changeSet>
	
	<changeSet id="RAS-20230208-002-PORT-MANAGER-GLC" author="tangjiacheng">
		<sqlFile path="sql/RAS-20230208-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>新增设备端口glc页面</comment>
	</changeSet>
	
	<changeSet id="WIP-20230208-011-PROCESS-MANAGER-GLC" author="DaiwenBin">
		<sqlFile path="sql/WIP-20230208-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>新增工艺glc页面</comment>
	</changeSet>
	
	<changeSet id="WIP-20230208-012-UPDATE-AD_AUTHORITY" author="DaiwenBin">
		<sql>update ad_authority set is_active = 'N' where name in ('Prd.Process','Prd.Product','Prd.Step','Ras.Equipment','Ras.Port','Prd.Procedure');
			 update ad_authority set label_zh = '模块' where name  = 'Prd.ProcedureGlc';
			 update ad_authority set label_zh = '产品' where name  = 'Prd.ProductGlc';
			 update ad_authority set label_zh = '工步' where name  = 'Prd.StepGlc';
 			 update ad_authority set label_zh = '设备' where name  = 'Ras.EquipmentGlc';
			 update ad_authority set label_zh = '设备端口' where name  = 'Ras.PortGlc';</sql>  
		<comment>隐藏旧版本功能并更新新功能label</comment>
	</changeSet>
	
	<changeSet id="WIP-20230208-CREATE-WIP_LOT_PROCEDURE_CHANGE" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="WIP_LOT_PROCEDURE_CHANGE"/>
			</not>
		</preConditions>
				
    	<createTable remarks="批次流程变更表" tableName="WIP_LOT_PROCEDURE_CHANGE" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_PROCEDURE_CHANGE" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  			

			<column name="CONTROL_ID" remarks="控制号" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_ID" remarks="批次号" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_NAME" remarks="流程名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_VERSION" remarks="流程版本" type="NUMBER(19, 0)"/>	
			<column name="PROCEDURE_STATE_PATH" remarks="流程路径" type="VARCHAR2(400 BYTE)"/>
			<column name="PRE_STATE" remarks="前状态" type="VARCHAR2(32 BYTE)"/>
			<column name="STATE" remarks="状态" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_PROCEDURE_RRN" remarks="LOT_PROCEDURE主键" type="NUMBER(19, 0)"/>			
	    </createTable>	
	</changeSet>
	
	<changeSet id="WIP-20230208-CREATE-WIP_LOT_PROCEDURE_CHANGE_HIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="WIP_LOT_PROCEDURE_CHANGE_HIS"/>
			</not>
		</preConditions>
				
    	<createTable remarks="批次流程变更历史表" tableName="WIP_LOT_PROCEDURE_CHANGE_HIS" tablespace="TS_MES_HIS_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_PROCEDURE_CHANGE_HIS" primaryKeyTablespace="TS_MES_HIS_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>        
            <column name="TRANS_TIME" remarks="事物时间" type="date"/>         
            <column name="TRANS_TYPE" remarks="事物类型" type="VARCHAR2(32 BYTE)"/>
            <column name="HIS_SEQ" remarks="事物号" type="VARCHAR2(32 BYTE)"/>
            <column name="HIS_SEQ_NO" remarks="事物序号" type="NUMBER(19, 0)"/>			

			<column name="LOT_PROCEDURE_CHANGE_RRN" remarks="LOT_PROCEDURE_CHANGE主键" type="NUMBER(19, 0)"/>
			<column name="CONTROL_ID" remarks="控制号" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_ID" remarks="批次号" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_NAME" remarks="流程名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_VERSION" remarks="流程版本" type="NUMBER(19, 0)"/>	
			<column name="PROCEDURE_STATE_PATH" remarks="流程路径" type="VARCHAR2(400 BYTE)"/>
			<column name="PRE_STATE" remarks="前状态" type="VARCHAR2(32 BYTE)"/>
			<column name="STATE" remarks="状态" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_PROCEDURE_RRN" remarks="LOT_PROCEDURE主键" type="NUMBER(19, 0)"/>			
	    </createTable>	
	</changeSet>
	
	<changeSet id="WIP-20230208-CREATE-WIP_LOT_PROCEDURE_CHANGE_STEP" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="WIP_LOT_PROCEDURE_CHANGE_STEP"/>
			</not>
		</preConditions>
				
    	<createTable remarks="批次流程变更工步表" tableName="WIP_LOT_PROCEDURE_CHANGE_STEP" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_PROCEDURE_CHANGE_STEP" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  			

			<column name="LOT_PROCEDURE_CHANGE_RRN" remarks="LOT_PROCEDURE_CHANGE主键" type="NUMBER(19, 0)"/>
			<column name="PROCEDURE_NAME" remarks="流程名称" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_TYPE" remarks="动作类型" type="VARCHAR2(32 BYTE)"/>
			<column name="SEQ_NO" remarks="序号" type="NUMBER(19, 0)"/>				
			<column name="ACTION_STEP_STATE" remarks="动作工步" type="VARCHAR2(32 BYTE)"/>						
			<column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
			<column name="STEP_VERSION" remarks="工步版本" type="NUMBER(19, 0)"/>	
			<column name="LOT_TECN_RRN" remarks="LOT_TECN主键" type="NUMBER(19, 0)"/>			
			<column name="EQUIPMENT_ID" remarks="设备编号" type="VARCHAR2(32 BYTE)"/>
			<column name="RECIPE_NAME" remarks="Recipe名称" type="VARCHAR2(32 BYTE)"/>
			<column name="EQUIPMENT_RECIPE_NAME" remarks="PPID" type="VARCHAR2(32 BYTE)"/>
			<column name="RETICLE_NAME" remarks="光刻版名称" type="VARCHAR2(32 BYTE)"/>
			<column name="EDC_NAME" remarks="数据收集" type="VARCHAR2(32 BYTE)"/>					
	    </createTable>	
	</changeSet>
	
	<changeSet id="WIP-20230208-005-LOT-PROCEDURE—CHANGE" author="Clark">
		<sqlFile path="sql/WIP-20230208-005.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>新增批次流程变更</comment>
	</changeSet>
	
	<changeSet id="WIP-20230209-011-PROCESS-MANAGER-GLC" author="DaiwenBin">
		<sqlFile path="sql/WIP-20230209-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>工艺/模块改动重提交</comment>
	</changeSet>
	
	<changeSet id="WIP-20230209-001-ADTABLE-UPFATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230209-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>新GLC页面导入等按钮读取不到动态表问题</comment>
	</changeSet>
	
	<changeSet id="WIP-20230209-002-DIALOG-MESSAGE-ADD" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230209-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>弹框message添加</comment>
	</changeSet>
	
	<changeSet id="WIP-20230209-012-UPDATE-EXPORT_TEMPLATE" author="DaiwenBin">
		<sqlFile path="sql/WIP-20230210-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>导出模板区域改为0</comment>
	</changeSet>
	
	<changeSet id="EDC-20230217-001-EDCDIALOG-3" author="zhougelong">
		<sqlFile path="sql/EDC-20230217-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>数据收集结束新增弹框sql</comment>
	</changeSet>
	
	<changeSet id="WIP-20230228-003-GLC-NEW-IMPORT-EXPORT" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230228-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>glc功能新模式导入功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20230302-003-GLC-NEW-IMPORT-EXPORT" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230302-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>非glc功能新模式导入功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20230302-005-ADD-UNIQUE" author="tangjiacheng">
		<sql>delete ras_eqp_capa where CAPA_RRN is null;</sql>
		<addUniqueConstraint columnNames="EQUIPMENT_RRN, ORG_RRN, CAPA_RRN" constraintName="UK_RAS_EQP_CAPA" tableName="RAS_EQP_CAPA" tablespace="TS_MES_IDX"/>
	</changeSet>
	
	<changeSet id="WIP-20230302-011-GLC-NEW-IMPORT-EXPORT" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230302-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>非glc功能新模式导入功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20230303-001-ADD-ADTABLE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230303-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充ADTABLE</comment>
	</changeSet>
	
	<changeSet id="WIP-20230303-002-ADD-ADTABLE-ADD-EQP-TEMP" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230303-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充新导入导出缺失数据</comment>
	</changeSet>
	
	<changeSet id="RAS-20230303-001-DROPCOLOUM_RASEQP" author="Clark">
    	<preConditions onFail="MARK_RAN">
			<columnExists tableName="RAS_EQP" columnName="CURRENT_RECIPE_NAME"/>
		</preConditions> 
    	
    	<dropColumn tableName="RAS_EQP">  
        	<column name="CURRENT_RECIPE_NAME"/>  
        	<column name="CURRENT_PART_NAME"/>  
        	<column name="CURRENT_SHEET_QTYPE"/>  
        	<column name="CURRENT_PROCESS_MODE"/>  
    	</dropColumn>  
	</changeSet>
	
	<changeSet id="RAS-20230303-002-ADDCOLOUM_RASEQP" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP" columnName="PROCESS_MODE"/>
			</not>
		</preConditions>

		<addColumn tableName="RAS_EQP">
			<column name="PROCESS_MODE" remarks="作业模式" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>设备表增加PROCESS_MODE字段</comment>
	</changeSet>
	
	<changeSet id="RAS-20230303-003-ADDCOLOUM_RASEQPHIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP_HIS" columnName="PROCESS_MODE"/>
			</not>
		</preConditions>

		<addColumn tableName="RAS_EQP_HIS">
			<column name="PROCESS_MODE" remarks="作业模式" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>设备历史表增加PROCESS_MODE字段</comment>
	</changeSet>
	
	<changeSet id="RAS-20230303-004-DROPCOLOUM_RASEQPHIS" author="Clark">
    	<preConditions onFail="MARK_RAN">
			<columnExists tableName="RAS_EQP_HIS" columnName="CURRENT_RECIPE_NAME"/>
		</preConditions> 
    	
    	<dropColumn tableName="RAS_EQP_HIS">  
        	<column name="CURRENT_RECIPE_NAME"/>  
        	<column name="CURRENT_PART_NAME"/>  
        	<column name="CURRENT_SHEET_QTYPE"/>  
    	</dropColumn>  
	</changeSet>

	<changeSet id="WIP-20230303-006-ADD-ADTABLE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230306-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>生产加工历史查询添加字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20230307-001-ADD_PRD_USERGROUP_STEP" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230307-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>工步权限添加导入</comment>
	</changeSet>
	
	<changeSet id="WIP-20230307-007-RASEQP_RENAME" author="Clark">
  		<renameColumn tableName="RAS_EQP" newColumnName="PROCESS_CATEGORY" oldColumnName="PROCESS_MODE" remarks="作业模式" />
	</changeSet>
	
	<changeSet id="WIP-20230307-008-RASEQPHIS_RENAME" author="Clark">
  		<renameColumn tableName="RAS_EQP_HIS" newColumnName="PROCESS_CATEGORY" oldColumnName="PROCESS_MODE" remarks="作业模式" />
	</changeSet>
	
	<changeSet id="WIP-20230307-002-ADD-PROCESSCATEGROY-REFLIST" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230307-002.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>设备添加作业模式栏位</comment>
	</changeSet>

	<changeSet id="WIP-20230307-006-BYEQP" author="Clark">
		<sqlFile path="sql/WIP-20230307-006.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充按设备作业缺失数据</comment>
	</changeSet>
	
	<changeSet id="WIP-20230307-003-ADD-BUFF-CLEAN-PAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230307-003.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>设备添加buff和clean两个page</comment>
	</changeSet>
	
	<changeSet id="WIP-20230310-001-UPDATE-AD-UPDATE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230310-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>完善工艺与模块的导出模板</comment>
	</changeSet>
	
	<changeSet id="PRD-20230310-001-UPDATE-PRDLINK-FIELD" author="tangjiacheng">
		<sqlFile path="sql/PRD-20230310-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>修改工程连接限制功能页面栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20230310-006-BYSTEP" author="Clark">
		<sqlFile path="sql/WIP-20230310-006.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充按工步作业SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20230313-010-EDC-COLLECTION" author="HeTao">
		<sql>
			delete ad_message where KEY_ID IN ('edc.collection_table_desc', 'edc.collection_table_label', 'edc.collection_table_name', 'edc.collection_table_avg', 'edc.collection_table_sum'); 
			insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 312598099811897344, 0, 'Y', 'edc.collection_table_desc', 'Description', '描述', null); 
			insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 312596644124168192, 0, 'Y', 'edc.collection_table_label', 'Data', '数据', null); 
			insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 312598019914600448, 0, 'Y', 'edc.collection_table_name', 'Name', '名称', null); 
			insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 312598232687448064, 0, 'Y', 'edc.collection_table_avg', 'AVG', '平均值', null); 
			insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 312598341164732416, 0, 'Y', 'edc.collection_table_sum', 'SUM', '总和', null); 
			
			DELETE FROM ad_field WHERE OBJECT_RRN IN (252271353762164736, 312890359854166016);
			insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) 
			values  ( 312890359854166016, 0, 'Y', to_date('2023-03-14 09:54:35', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-14 09:56:56', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 2, 'isFormulaTable', 'Excel公式表格', null, 93582580, null, 63, null, 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'Is  Excel Formula Table', '使用Excel公式表格', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
			insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE)
			values  ( 252271353762164736, 0, 'Y', to_date('2020-08-25 20:21:25', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-14 09:56:56', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'adTableName', 'Excel动态表名称', null, 93582580, null, 65, null, 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Excel Table Name', 'Excel动态表名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
		</sql>   
		<comment>EDC数据收集补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20230313-011-EDC-COLLECTION" author="Hetao">
    	<preConditions onFail="MARK_RAN">
    		<not>
    			<columnExists tableName="EDC_SET_LINE" columnName="IS_FORMULA_TABLE"/>
    		</not>
		</preConditions> 
    	
    	<addColumn tableName="EDC_SET_LINE">
    		<column name="IS_FORMULA_TABLE" remarks="使用可以运行Excel公式的Table" type="VARCHAR2(1 BYTE)"/>
    	</addColumn>
    	
    	<sql>UPDATE EDC_SET_LINE t SET T.IS_FORMULA_TABLE = 'N' WHERE T.EDC_TYPE = 'ITEM'</sql>
	</changeSet>
	
	<changeSet id="WIP-20230314-010-EDC-COLLECTION" author="HeTao">
		<sql>
			DELETE from AD_MESSAGE where key_id in ('edc.edcset_sampleplan_message',
			'edc.edc_formula_setup_message',
			'edc.edcset_additem_message',
			'edc.edcset_changeitem_message',
			'edc.edcset_attribute_title_message');
			
			insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
			values ('312910124094074880', '0', 'Y', 'edc.edcset_sampleplan_message', 'Enter the number of samples and describe each sample', '输入样本数量，并且可以针对每一个样本进行描述', null);
			
			insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
			values ('312909321480450048', '0', 'Y', 'edc.edc_formula_setup_message', 'Please set variables and combine formulas', '请设置变量并组合公式', null);
			
			insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
			values ('312908953312833536', '0', 'Y', 'edc.edcset_additem_message', 'Please select data collection item and data specification', '请选择数据采集项，数据数据规范', null);
			
			insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
			values ('312908481105506304', '0', 'Y', 'edc.edcset_changeitem_message', 'Please enter the info to be modified', '请输入需要修改的信息', null);
			
			insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
			values ('312908103462957056', '0', 'Y', 'edc.edcset_attribute_title_message', 'Please input the sampling quantity and select the defect code', '请输入采样数量，选择缺陷代码', null);

		</sql>
		<comment>EDC数据收集补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20230314-011-EDC-COLLECTION" author="HeTao">
		<sql>
		delete from ad_field where object_rrn in (312890359854166016, 252271353762164736);
		insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  
		( 312890359854166016, 0, 'Y', to_date('2023-03-14 09:54:35', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-14 15:12:39', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 3, 'isFormulaTable', '公式表格', null, 93582580, null, 63, null, 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'Use Formula Table', '使用公式表格', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
		insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  
		( 252271353762164736, 0, 'Y', to_date('2020-08-25 20:21:25', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-14 15:12:39', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'adTableName', '公式动态表名称', null, 93582580, null, 65, null, 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Formula Table Name', '公式动态表名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
		</sql>
		<comment>EDC数据收集，修改不合理的栏位描述</comment>
	</changeSet>
	
	<changeSet id="PRD-20230315-010_message" author="MoYouMing">
		<sqlFile path="sql/PRD-20230315-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>add admessage</comment>
	</changeSet>
	
	<changeSet id="WIP-20230317-001-BY-EQP-BUFFER-PAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230317-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>按Buffer设备作业Buffer按钮弹框Message添加</comment>
	</changeSet>
	
	<changeSet id="WIP-20230317-012-DELETE-UNIQUE" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
				<indexExists indexName="UK_PRD_USERGROUP_STEP"/>
		</preConditions>
		<dropUniqueConstraint tableName="PRD_USERGROUP_STEP" constraintName="UK_PRD_USERGROUP_STEP"/>
	</changeSet>
	
	<changeSet id="WIP-20230320-001-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230320-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中，修改动态表描述</comment>
	</changeSet>
	
	<changeSet id="WIP-20230322-001-AD_AUTHORITY" author="Clark">
		<sqlFile path="sql/WIP-20230322-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充在制品，按工步作业，按设备作业按钮权限记录</comment>
	</changeSet>
	
	<changeSet id="WIP-20230322-002-ADDCOLOUM_LOTPROCEDURE" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_PROCESSDEFINITION" columnName="TYPE"/>
			</not>
		</preConditions>

		<addColumn tableName="WF_PROCESSDEFINITION">
			<column name="TYPE" remarks="类型" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>WF_PROCESSDEFINITION增加TYPE字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20230322-003-DROP_LOTPROCEDURECHANGESTEP" author="Clark">
		<preConditions onFail="MARK_RAN">
			<tableExists  tableName="WIP_LOT_PROCEDURE_CHANGE_STEP"/>
		</preConditions>		
		<dropTable cascadeConstraints="true"  tableName="WIP_LOT_PROCEDURE_CHANGE_STEP"/>  
		<comment>WIP_LOT_PROCEDURE_CHANGE_STEP表删除</comment>
	</changeSet>
	
	<changeSet id="WIP-20230322-004-DROP_LOTPROCEDURECHANGE" author="Clark">
		<preConditions onFail="MARK_RAN">
			<tableExists  tableName="WIP_LOT_PROCEDURE_CHANGE"/>
		</preConditions>		
		<dropTable cascadeConstraints="true"  tableName="WIP_LOT_PROCEDURE_CHANGE"/>  
		<comment>WIP_LOT_PROCEDURE_CHANGE表删除</comment>
	</changeSet>
	
	<changeSet id="WIP-20230322-005-CREATE_LOTPROCEDUREACTION" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="WIP_LOT_PROCEDURE_ACTION"/>
			</not>
		</preConditions>
				
    	<createTable remarks="批次流程变更表" tableName="WIP_LOT_PROCEDURE_ACTION" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_PROCEDURE_ACTION" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  			

			<column name="ACTION" remarks="类型" type="VARCHAR2(32 BYTE)"/>
			<column name="CONTROL_ID" remarks="控制号" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_ID" remarks="批次号" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_NAME" remarks="流程名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_VERSION" remarks="流程版本" type="NUMBER(19, 0)"/>	
			<column name="PROCEDURE_STATE_PATH" remarks="流程路径" type="VARCHAR2(400 BYTE)"/>
			<column name="PRE_STATE" remarks="前状态" type="VARCHAR2(32 BYTE)"/>
			<column name="STATE" remarks="状态" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_PROCEDURE_RRN" remarks="LOT_PROCEDURE主键" type="NUMBER(19, 0)"/>		
			<column name="FROZEN_PROCEDURE_VERSION" remarks="冻结流程版本" type="NUMBER(19, 0)"/>	
	    </createTable>	
	</changeSet>
	
	<changeSet id="WIP-20230322-006-CREATE_LOTPROCEDUREACTIONSTEP" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists  tableName="WIP_LOT_PROCEDURE_ACTION_STEP"/>
			</not>
		</preConditions>
				
    	<createTable remarks="批次流程变更工步表" tableName="WIP_LOT_PROCEDURE_ACTION_STEP" tablespace="TS_MES_DAT">
	        <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_LOT_PROCEDURE_ACTION_STEP" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  			

			<column name="ACTION" remarks="类型" type="VARCHAR2(32 BYTE)"/>
			<column name="LOT_PROCEDURE_ACTION_RRN" remarks="LOT_PROCEDURE_ACTION主键" type="NUMBER(19, 0)"/>
			<column name="PROCEDURE_NAME" remarks="流程名称" type="VARCHAR2(32 BYTE)"/>
			<column name="ACTION_TYPE" remarks="动作类型" type="VARCHAR2(32 BYTE)"/>
			<column name="SEQ_NO" remarks="序号" type="NUMBER(19, 0)"/>				
			<column name="ACTION_STEP_STATE" remarks="动作工步" type="VARCHAR2(32 BYTE)"/>						
			<column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
			<column name="STEP_VERSION" remarks="工步版本" type="NUMBER(19, 0)"/>	
			<column name="LOT_TECN_RRN" remarks="LOT_TECN主键" type="NUMBER(19, 0)"/>			
			<column name="EQUIPMENT_ID" remarks="设备编号" type="VARCHAR2(32 BYTE)"/>
			<column name="RECIPE_NAME" remarks="Recipe名称" type="VARCHAR2(32 BYTE)"/>
			<column name="EQUIPMENT_RECIPE_NAME" remarks="PPID" type="VARCHAR2(32 BYTE)"/>
			<column name="RETICLE_NAME" remarks="光刻版名称" type="VARCHAR2(32 BYTE)"/>
			<column name="EDC_NAME" remarks="数据收集" type="VARCHAR2(32 BYTE)"/>					
	    </createTable>	
	</changeSet>
	
	<changeSet id="WIP-20230322-007-ADDCOLOUM_LOTPROCEDUREHIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_PROCESSDEFINITION_HIS" columnName="TYPE"/>
			</not>
		</preConditions>

		<addColumn tableName="WF_PROCESSDEFINITION_HIS">
			<column name="TYPE" remarks="类型" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>WF_PROCESSDEFINITION增加TYPE字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20230322-008-RENAME_LOTPROCEDUREACTION" author="Clark">
		<renameColumn tableName="WIP_LOT_PROCEDURE_ACTION" oldColumnName="FROZEN_PROCEDURE_VERSION" newColumnName="LOCK_PROCEDURE_VERSION"/>
		<comment>WIP_LOT_PROCEDURE_ACTION字段名修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20230322-009-ADDCOLOUM_LOTPROCEDUREACTION" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_PROCEDURE_ACTION" columnName="SEQ_NO"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_LOT_PROCEDURE_ACTION">
			<column name="SEQ_NO" remarks="序号" type="NUMBER(19, 0)"/>
		</addColumn>
		<comment>WIP_LOT_PROCEDURE_ACTION增加SEQ_NO字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20230323-001-BY-EQP-BY-STEP" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230323-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>按设备作业，按工步作业bug修改</comment>
	</changeSet>
	
	<changeSet id="PRD-20230324-001-PRDPROCESS" author="Clark">
		<sqlFile path="sql/PRD-20230324-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充Process QL</comment>
	</changeSet>
	
	<changeSet id="WIP-20230324-003-IMP-EXP-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230324-003.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>一些导入导出的问题修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20230324-004-STEP-CHANGE-INFO" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230324-004.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>工步修改信息按钮弹框页面栏位修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20230328-011-UPDATE_AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230328-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>修改英译中与动态导出栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20230330-001-BIN-UPLOAD-TABLE-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230330-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>Bin数据采集项集导入动态表栏位修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20230404-001-UPDATE-AD-AUTHORITY" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230404-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>隐藏已删除的按设备清洗载具功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20230407-001-LOT_PROCEDURE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230407-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>批次流程变更界面修改与提示优化</comment>
	</changeSet>
	
	<changeSet id="WIP-20230407-002-AD_MESSAGE" author="DaiWenBin">
		<sql>delete ad_message where KEY_ID in('wip.select_step_exist_rework_procedure_the_arrival_step','wip-002104: prd.node_unsupport_multi_arriving_transition');
		insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
		values ('20230407077', '0', 'Y', 'wip.select_step_exist_rework_procedure_the_arrival_step', 'Select step exist rework procedure the arrival of the work step!', '选择的工步为返工流程到达工步！', null);
		
		insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
		values ('20230407088', '0', 'Y', 'wip-002104: prd.node_unsupport_multi_arriving_transition', 'Node Unsupport Multi Arriving Transition', '节点不支持多个到达', null);
		</sql>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20230407-011-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230407-003.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20230407-012-AD-FIELD-LOTTECN" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230407-012.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>修改批次临时工程变更栏位类型</comment>
	</changeSet>
	
	<changeSet id="BAS-20230409-001-ADMESSAGE" author="Clark">
		<sqlFile path="sql/BAS-20230409-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="EDC-20230412-002-EDC-TECN" author="tangjiacheng">
		<sqlFile path="sql/EDC-20230412-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>EDC临时工程变更添加设备栏位，调整顺序</comment>
	</changeSet>
	
	<changeSet id="PRD-20230412-001-PART_STEP_EXPORT" author="FanChengMing">
		<sqlFile path="sql/PRD-20230412-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>产品流程数据查询改为现有的页面构造和导出方式</comment>
	</changeSet>
	
	<changeSet id="PRD-20230412-010-FLOW_CDI" author="Clark">
		<sqlFile path="sql/PRD-20230412-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>CDI 管理页面SQL</comment>
	</changeSet>
	
	<changeSet id="PRD-20230412-010-ADDCOLOUM_CDI_POINT" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_FLOW_CDI_POINT" columnName="TRIGGER_POINT"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_LOT_FLOW_CDI_POINT">
			<column name="TRIGGER_POINT" remarks="触发点" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>CDI表增加TRIGGER_POINT字段</comment>
	</changeSet>
	
	<changeSet id="PRD-20230412-010-ADDCOLOUM_CDI_POINT_HIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT_FLOW_CDI_POINT_HIS" columnName="TRIGGER_POINT"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_LOT_FLOW_CDI_POINT_HIS">
			<column name="TRIGGER_POINT" remarks="触发点" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>CDI历史表增加TRIGGER_POINT字段</comment>
	</changeSet>
	<changeSet id="PRD-20230413-MOVE_PART_STEP_TO_DATATRANSFER" author="FanChengMing">
		<sqlFile path="sql/PRD-20230413-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>产品流程数据查询移入Data Transfer</comment>
	</changeSet>
	
	<changeSet id="RAS-20230411-001-AD_MESSAGE" author="DingYu">
		<sql>delete ad_message where KEY_ID in('ras.port_num_is_repeat');
		insert into Ad_Message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
		values ('323054972970401792', '0', 'Y', 'ras.port_num_is_repeat', 'Port num is repeat!', '端口号重复！', null);
		</sql>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20230413-003-SORTING-JOB" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230413-003.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>载具任务查询，载具任务异常查询缺失参考表</comment>
	</changeSet>
	
	<changeSet id="EDC-20230413-001-DELETE-AD—BUTTON" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230413-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>删除导出按钮</comment>
	</changeSet>
	
	<changeSet id="WIP-20230415-PRD-PART-SUPPLIER" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="PRD_PART_SUPPLIER"/>
			</not>
		</preConditions>
		<createTable remarks="Part Supplier" tableName="PRD_PART_SUPPLIER" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_PRD_PART_SUPPLIER"/>
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
			<column name="PART_RRN" remarks="产品主键" type="NUMBER(19, 0)"/>
			<column name="SUPPLIER_ID" remarks="供应商" type="VARCHAR2(64 BYTE)"/>
			<column name="SUPPLIER_PART_ID" remarks="供应商产品名称" type="VARCHAR2(32 BYTE)"/>
			<column name="SUPPLIER_PART_DESC" remarks="供应商产品描述" type="VARCHAR2(64 BYTE)"/>
			<column name="ACCEPTE_PROCESS_RRN" remarks="接收工艺主键" type="NUMBER(19, 0)"/>
			<column name="ACCEPTE_PROCESS_ID" remarks="接收工艺名称" type="VARCHAR2(32 BYTE)"/>
			<column name="ACCEPTE_PROCESS_VERSION" remarks="接收工艺版本" type="NUMBER(19, 0)"/>
			<column name="MAIN_MAT_TYPE" remarks="主物料类型" type="VARCHAR2(32 BYTE)"/>
			<column name="SUB_MAT_TYPE" remarks="子物料类型" type="VARCHAR2(32 BYTE)"/>
			<column name="IS_IDENTIFIED" remarks="是否确认" type="VARCHAR2(32 BYTE)"/>
			<column name="RESERVED1" remarks="预留字段1" type="VARCHAR2(64 BYTE)"/>
			<column name="RESERVED2" remarks="预留字段2" type="VARCHAR2(64 BYTE)"/>
			<column name="RESERVED3" remarks="预留字段3" type="VARCHAR2(64 BYTE)"/>
			<column name="RESERVED4" remarks="预留字段4" type="VARCHAR2(64 BYTE)"/>
			<column name="RESERVED5" remarks="预留字段5" type="VARCHAR2(64 BYTE)"/>
		</createTable>
	</changeSet>

	<changeSet id="WIP-20230415-PRD-STEP-FLAG" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="PRD_STEP_FLAG"/>
			</not>
		</preConditions>
		<createTable remarks="定义Step上的各种标记" tableName="PRD_STEP_FLAG" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_PRD_STEP_FLAG"/>
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  			

			<column name="PROCESS_NAME" remarks="工艺名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_NAME" remarks="模块名称" type="VARCHAR2(32 BYTE)"/>
			<column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
			<column name="DFLAG" remarks="去向Flag标记,表示Step代表去向Flag中的哪一位" type="NUMBER(19, 0)"/>
			<column name="DFLAG2" remarks="去向Flag标记,表示Step代表去向Flag中的哪一位" type="NUMBER(19, 0)"/>
			<column name="DFLAG3" remarks="去向Flag标记,表示Step代表去向Flag中的哪一位" type="NUMBER(19, 0)"/>
			<column name="EFLAG" remarks="强化Flag标记,表示Step代表强化Flag中的哪一位" type="NUMBER(19, 0)"/>
			<column name="EFLAG2" remarks="强化Flag标记,表示Step代表强化Flag中的哪一位   先cell   需要两位强化flag" type="NUMBER(19, 0)"/>
			<column name="OISKP" remarks="OIC是否可以跳过(Skip)	" type="VARCHAR2(32 BYTE)"/>
			<column name="CLRES" remarks="Cell判定等级更新规则" type="VARCHAR2(32 BYTE)"/>
			<column name="LMSKP" remarks="自动是否可以跳过(Skip)" type="VARCHAR2(32 BYTE)"/>
			<column name="PLRES" remarks="Plate判定等级更新规则" type="VARCHAR2(32 BYTE)"/>
			<column name="CLDWF" remarks="信息下载标志" type="VARCHAR2(32 BYTE)"/>
			<column name="MASFB" remarks="Mask反馈标志" type="VARCHAR2(32 BYTE)"/>
			<column name="PRPAS" remarks="传递过程中存储的信息标志" type="VARCHAR2(32 BYTE)"/>
			<column name="MUL" remarks="目标标记参考点1" type="VARCHAR2(32 BYTE)"/>
			<column name="INSPE" remarks="检验过程的标志" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCTYP" remarks="制程类型" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCFLG" remarks="制程抽片" type="VARCHAR2(32 BYTE)"/>
			<column name="JUDGECST" remarks="基板判定不良有效标志" type="VARCHAR2(32 BYTE)"/>
			<column name="CFFLAGRULE" remarks="CF强化flag等级合并规则" type="VARCHAR2(32 BYTE)"/>
			<column name="SORTING_MSO_QTY" remarks="数量在范围内的批次作为补充元" type="NUMBER(19, 0)"/>
			<column name="SORTING_MDT_QTY" remarks="数量在范围内的批次作为补充先" type="NUMBER(19, 0)"/>
			<column name="SORTING_TYPE" remarks="MRS设备Sorting类型" type="VARCHAR2(32 BYTE)"/>
			<column name="AOI_FLAG" remarks="AOI台机Flag" type="VARCHAR2(32 BYTE)"/>
			<column name="NG_PORT" remarks="NG_PORT" type="VARCHAR2(32 BYTE)"/>
			<column name="MRS_EQP_ABILITY" remarks="设备能力" type="VARCHAR2(32 BYTE)"/>
			<column name="DL_FLAG" remarks="丁落生成站点" type="VARCHAR2(32 BYTE)"/>
			<column name="LAYER_FLAG" remarks="层" type="VARCHAR2(32 BYTE)"/>
			<column name="KOL_FLAG" remarks="KOL设备flag，判断这个工步是否有KOL设备" type="VARCHAR2(32 BYTE)"/>
			<column name="IS_ODF_FLAG" remarks="是否是ODF进站" type="VARCHAR2(32 BYTE)"/>
			<column name="IS_COA_FLAG" remarks="COA flag" type="VARCHAR2(32 BYTE)"/>
			<column name="IS_REWORK_STEP_FLAG" remarks="是否是返工站点" type="VARCHAR2(32 BYTE)"/>
			<column name="IS_FINAL_FLAG" remarks="is final flag 最终出货站点" type="VARCHAR2(32 BYTE)"/>
		</createTable>
	</changeSet>

	<changeSet id="WIP-20230415-PRD-STEP-FLAG-HIS" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="PRD_STEP_FLAG_HIS"/>
			</not>
		</preConditions>
		<createTable remarks="定义Step上的各种标记历史表" tableName="PRD_STEP_FLAG_HIS" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_PRD_STEP_FLAG_HIS"/>
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  			

			<column name="PROCESS_NAME" remarks="工艺名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_NAME" remarks="模块名称" type="VARCHAR2(32 BYTE)"/>
			<column name="TRANS_TYPE" remarks="事务类型" type="VARCHAR2(32 BYTE)"/>
			<column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
			<column name="DFLAG" remarks="去向Flag标记,表示Step代表去向Flag中的哪一位" type="NUMBER(19, 0)"/>
			<column name="DFLAG2" remarks="去向Flag标记,表示Step代表去向Flag中的哪一位" type="NUMBER(19, 0)"/>
			<column name="DFLAG3" remarks="去向Flag标记,表示Step代表去向Flag中的哪一位" type="NUMBER(19, 0)"/>
			<column name="EFLAG" remarks="强化Flag标记,表示Step代表强化Flag中的哪一位" type="NUMBER(19, 0)"/>
			<column name="EFLAG2" remarks="强化Flag标记,表示Step代表强化Flag中的哪一位   先cell   需要两位强化flag" type="NUMBER(19, 0)"/>
			<column name="OISKP" remarks="OIC是否可以跳过(Skip)	" type="VARCHAR2(32 BYTE)"/>
			<column name="CLRES" remarks="Cell判定等级更新规则" type="VARCHAR2(32 BYTE)"/>
			<column name="LMSKP" remarks="自动是否可以跳过(Skip)" type="VARCHAR2(32 BYTE)"/>
			<column name="PLRES" remarks="Plate判定等级更新规则" type="VARCHAR2(32 BYTE)"/>
			<column name="CLDWF" remarks="信息下载标志" type="VARCHAR2(32 BYTE)"/>
			<column name="MASFB" remarks="Mask反馈标志" type="VARCHAR2(32 BYTE)"/>
			<column name="PRPAS" remarks="传递过程中存储的信息标志" type="VARCHAR2(32 BYTE)"/>
			<column name="MUL" remarks="目标标记参考点1" type="VARCHAR2(32 BYTE)"/>
			<column name="INSPE" remarks="检验过程的标志" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCTYP" remarks="制程类型" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCFLG" remarks="制程抽片" type="VARCHAR2(32 BYTE)"/>
			<column name="JUDGECST" remarks="基板判定不良有效标志" type="VARCHAR2(32 BYTE)"/>
			<column name="CFFLAGRULE" remarks="CF强化flag等级合并规则" type="VARCHAR2(32 BYTE)"/>
			<column name="SORTING_MSO_QTY" remarks="数量在范围内的批次作为补充元" type="NUMBER(19, 0)"/>
			<column name="SORTING_MDT_QTY" remarks="数量在范围内的批次作为补充先" type="NUMBER(19, 0)"/>
			<column name="SORTING_TYPE" remarks="MRS设备Sorting类型" type="VARCHAR2(32 BYTE)"/>
			<column name="AOI_FLAG" remarks="AOI台机Flag" type="VARCHAR2(32 BYTE)"/>
			<column name="NG_PORT" remarks="NG_PORT" type="VARCHAR2(32 BYTE)"/>
			<column name="MRS_EQP_ABILITY" remarks="设备能力" type="VARCHAR2(32 BYTE)"/>
			<column name="DL_FLAG" remarks="丁落生成站点" type="VARCHAR2(32 BYTE)"/>
			<column name="LAYER_FLAG" remarks="层" type="VARCHAR2(32 BYTE)"/>
			<column name="KOL_FLAG" remarks="KOL设备flag，判断这个工步是否有KOL设备" type="VARCHAR2(32 BYTE)"/>
			<column name="IS_ODF_FLAG" remarks="是否是ODF进站" type="VARCHAR2(32 BYTE)"/>
			<column name="IS_COA_FLAG" remarks="COA flag" type="VARCHAR2(32 BYTE)"/>
			<column name="IS_REWORK_STEP_FLAG" remarks="是否是返工站点" type="VARCHAR2(32 BYTE)"/>
			<column name="IS_FINAL_FLAG" remarks="is final flag 最终出货站点" type="VARCHAR2(32 BYTE)"/>
		</createTable>
	</changeSet>

	<changeSet id="WIP-20230415-PRD-STEP-LOCATION" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="PRD_STEP_LOCATION"/>
			</not>
		</preConditions>
		<createTable remarks="定义Location与Step对应关系" tableName="PRD_STEP_LOCATION" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_PRD_STEP_LOCATION"/>
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>  			

			<column name="LOCATION_RRN" remarks="LOCATION主键" type="NUMBER(19, 0)"/>
			<column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
		</createTable>
	</changeSet>
	
	<changeSet id="WIP-20230417-001-MESSAGE" author="Clark">
		<sqlFile path="sql/WIP-20230417-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="PRD-20230417-001-DROP_STEPFLAG" author="Clark">
		<preConditions onFail="MARK_RAN">
			<tableExists  tableName="PRD_STEP_FLAG"/>
		</preConditions>		
		<dropTable cascadeConstraints="true"  tableName="PRD_STEP_FLAG"/>  
		<comment>PRD_STEP_FLAG表删除</comment>
	</changeSet>
	
	<changeSet id="PRD-20230417-001-DROP_STEPFLAGHIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<tableExists  tableName="PRD_STEP_FLAG_HIS"/>
		</preConditions>		
		<dropTable cascadeConstraints="true"  tableName="PRD_STEP_FLAG_HIS"/>  
		<comment>PRD_STEP_FLAG_HIS表删除</comment>
	</changeSet>
	
	<changeSet id="EDC-20230418-001-EDC-SET-DIALOG-UPLOAD" author="tangjiacheng">
		<sqlFile path="sql/EDC-20230418-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>批次数据采集项集弹框导入修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20230421-006-MESSAGE" author="Clark">
		<sqlFile path="sql/WIP-20230421-006.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20230421-010-AD_IMPEXP" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230421-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充动态导入导出</comment>
	</changeSet>
	
	<changeSet id="WIP-20230425-010-AD_IMPEXP" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230425-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充动态导入导出</comment>
	</changeSet>
	
	<changeSet id="WIP-20230427-005-MESSAGE" author="Clark">
		<sqlFile path="sql/WIP-20230427-005.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>

	<changeSet id="MM-20230428-001-WAREHOUSE_PORT" author="LiuTao">
		<sqlFile path="sql/MM_20230428-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加仓库端口管理</comment>
	</changeSet>
	
	<changeSet id="PRD-20230504-010-CDI-POINTS" author="hetao">
		<sqlFile path="sql/PRD-20230504-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>注入点页面描述修改</comment>
	</changeSet>
	
	<changeSet id="PRD-20230505-010-AUTO-CDI-POINTS" author="hetao">
		<sqlFile path="sql/PRD-20230505-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>自动化接口注入点功能</comment>
	</changeSet>

	<changeSet id="WIP-20230505-001-ADDTABLE_WO_LOCK_VERSION" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="WIP_WO_LOCK_VERSION"/>
			</not>
		</preConditions>
		<createTable remarks="工单锁定模块版本" tableName="WIP_WO_LOCK_VERSION" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_WO_LOCK_VERSION"/>
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
			<column name="WORKORDER_RRN" remarks="工单主键" type="NUMBER(19, 0)"/>
			<column name="LOT_ID" remarks="批号" type="VARCHAR2(32 BYTE)"/>
			<column name="SEQ_NO" remarks="序号" type="NUMBER(19, 0)"/>
			<column name="PROCESS_NAME" remarks="工艺名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCESS_VERSION" remarks="工艺版本" type="NUMBER(19, 0)"/>
			<column name="PROCEDURE_NAME" remarks="流程名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_VERSION" remarks="流程版本" type="NUMBER(19, 0)"/>
			<column name="PROCEDURE_STATE_NAME" remarks="流程节点名称" type="VARCHAR2(32 BYTE)"/>
			<column name="PROCEDURE_STATE_PATH" remarks="流程节点路径" type="VARCHAR2(512 BYTE)"/>
		</createTable>
	</changeSet>
	
	<changeSet id="WIP-20230505-002-ADDCOLOUM_WO" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_WO" columnName="IS_LOCK_VERSION"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_WO">
			<column name="IS_LOCK_VERSION" remarks="是否锁模块版本" type="VARCHAR2(1 BYTE)"/>
		</addColumn>
		<comment>工单表增加字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20230505-003-ADDCOLOUM_WO_HIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_WO_HIS" columnName="IS_LOCK_VERSION"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_WO_HIS">
			<column name="IS_LOCK_VERSION" remarks="是否锁模块版本" type="VARCHAR2(1 BYTE)"/>
		</addColumn>
		<comment>工单历史表增加字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20230505-004-SYS_PARAMETER" author="Clark">
		<sqlFile path="sql/WIP-20230505-004.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>增加两个系统参数</comment>
	</changeSet>
	
	<changeSet id="WIP-20230505-005-MESSAGE" author="Clark">
		<sqlFile path="sql/WIP-20230505-005.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="EDC-20230505-001-EDCUPLOAD" author="zhougelong">
		<sqlFile path="sql/EDC-20230510-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>edc导入导出修改</comment>
	</changeSet>
	
	<changeSet id="EDC-20230509-001-EDCDATAQUERY" author="DingYu">
		<sqlFile path="sql/EDC-20230509-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>采集数据查询动态表栏位调整</comment>
	</changeSet>
	
	<changeSet id="PRD-20230510-010-PRD-Designer" author="hetao">
		<sqlFile path="sql/PRD-20230510-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>PRD Designer增加节点</comment>
	</changeSet>
	
	<changeSet id="EDC-20230510-002-EDCGATHER" author="DingYu">
		<sqlFile path="sql/EDC-20230510-002.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>重构离线数据收集</comment>
	</changeSet>
	
	<changeSet id="WIP-20230512-001-SYS_PARAMETER" author="Clark">
		<sqlFile path="sql/WIP-20230512-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>修改一个系统参数</comment>
	</changeSet>
	
	<changeSet id="PRD-20230512-001-RedirectEnd" author="DingYu">
		<sqlFile path="sql/PRD-20230512-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>RedirectEnd对话框优化</comment>
	</changeSet>
	
	<changeSet id="EDC-20230515-001-EDCDATAQUERY" author="DingYu">
		<sqlFile path="sql/EDC-20230515-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>采集数据查询动态表栏位调整</comment>
	</changeSet>
	
	<changeSet id="PRD-20230517-010-PRD-DESIGNER" author="hetao">
		<sqlFile path="sql/PRD-20230517-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>prd designer支持分支流程及重定向节点</comment>
	</changeSet>
	
	<changeSet id="EDC-20230517-001-EDCUPLOAD-2" author="zhougelong">
		<sqlFile path="sql/EDC-20230517-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>离线数据采集批量导入导出</comment>
	</changeSet>
	
	<changeSet id="PRD-20230519-010-MESSAGE" author="DingYu">
		<sqlFile path="sql/PRD-20230519-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="BAS-20220623-DROPTABLE_MERGE_RULE" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<tableExists  tableName="BAS_MERGE_RULE"/>
		</preConditions>
		
		<dropTable cascadeConstraints="true"  tableName="BAS_MERGE_RULE"/>  
	</changeSet>
	
	
	<changeSet author="DaiWenBin" id="BAS-20230517-ADDTABLE_MERGE_RULE">
    	<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="BAS_MERGE_RULE"/>
			</not>
		</preConditions>
        <createTable remarks="合批规则" tableName="BAS_MERGE_RULE" tablespace="TS_MES_DAT">
            <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_BAS_MERGE_RULE" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域ID" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="对象是否可用" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者ID" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者ID" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>
            
            <column name="NAME" remarks="规则名称" type="VARCHAR2(32 BYTE)"/>
            <column name="DESCRIPTION" remarks="规则描述" type="VARCHAR2(32 BYTE)"/>
            <column name="STATUS" remarks="状态" type="VARCHAR2(32 BYTE)"/>
            <column name="VERSION" remarks="版本" type="NUMBER(19, 0)"/>
            <column name="ACTIVE_TIME" remarks="激活时间" type="date"/>
            <column name="ACTIVE_USER" remarks="激活责任人" type="VARCHAR2(32 BYTE)"/>
            <column name="CATEGORY" remarks="类别" type="VARCHAR2(32 BYTE)"/>
        </createTable>
	</changeSet>
	
	<changeSet author="DaiWenBin" id="BAS-20230517-ADDTABLE_MERGE_RULE_LINE">
    	<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="BAS_MERGE_RULE_LINE"/>
			</not>
		</preConditions>
        <createTable remarks="合批规则详情" tableName="BAS_MERGE_RULE_LINE" tablespace="TS_MES_DAT">
            <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_BAS_MERGE_RULE_LINE" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域ID" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="对象是否可用" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者ID" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者ID" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>
            
            <column name="MERGE_RULE_RRN" remarks="顺序号" type="NUMBER(19, 0)"/>
            <column name="RULE_NAME" remarks="规则名称" type="VARCHAR2(32 BYTE)"/>
            <column name="RULE_DESC" remarks="规则描述" type="VARCHAR2(32 BYTE)"/>
            <column name="SEQ_NO" remarks="顺序号" type="NUMBER(19, 0)"/>
            <column name="RULE_TYPE" remarks="规则类型" type="VARCHAR2(32 BYTE)"/>
            
            <column name="VARIABLE_NAME" remarks="变量" type="VARCHAR2(32 BYTE)"/>
            <column name="COUNT_TYPE" remarks="计数类型" type="VARCHAR2(32 BYTE)"/>
            <column name="OPERATOR" remarks="比较符" type="VARCHAR2(32 BYTE)"/>
            <column name="VALUE" remarks="比较值" type="VARCHAR2(32 BYTE)"/>
            <column name="IS_MANDATORY" remarks="是否必须" type="VARCHAR2(1 BYTE)"/>
            <column name="EXPRESSION" remarks="组合表达式，用于组合规则类型" type="VARCHAR2(32 BYTE)"/>
        </createTable>
	</changeSet>
	
	<changeSet id="BAS-20230524-004-MergeRuleManager" author="DaiWenBin">
		<sqlFile path="sql/BAS-20230524-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>合批规则Glc界面</comment>
	</changeSet>
	
	<changeSet id="EDC-20230524-001-MANUAL-DATA-ADD" author="zhougelong">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="EDC_MANUAL_SAMPLE_DATA" columnName="INSPEC_TYPE"/>
			</not>
		</preConditions>

		<addColumn tableName="EDC_MANUAL_SAMPLE_DATA">
			<column name="INSPEC_TYPE" remarks="检查类型" type="VARCHAR2(64 BYTE)"/>
		</addColumn>
		<comment>手动抽样数据表增加检查类型栏位</comment>
	</changeSet>
	
	<changeSet id="BAS-20230526-001-MESSAGE" author="Clark">
		<sqlFile path="sql/BAS-20230526-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20230602-001-PROJECT-CONSTRAINT" author="Clark">
		<sqlFile path="sql/WIP-20230602-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>修改工程链接限制页面栏位</comment>
	</changeSet>
	
	<changeSet id="ADIMPEXP-20230605-UPDATE" author="zhougelong">
		<sql>update AD_IMPEXP set ORG_RRN = '0';</sql>
       <comment>修改导入动态表信息为区域0公用</comment>
	</changeSet>
	
	<changeSet id="WIP-20230606-010-MESSAGE" author="Clark">
		<sqlFile path="sql/WIP-20230606-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20230606-001-AUTHORITY" author="zhougelong">
		<sqlFile path="sql/WIP-20230606-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>菜单页修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20230607-001-REFNAME-UPDATE" author="zhougelong">
		<sqlFile path="sql/WIP-20230607-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>缺失参考表及系统参考值类型补缺</comment>
	</changeSet>
	
	<changeSet id="RAS-20230607-002-MESSAGE" author="Clark">
		<sqlFile path="sql/RAS-20230607-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20230616-005-MESSAGE" author="Clark">
		<sqlFile path="sql/WIP-20230616-005.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="MM-20230612-011-MATERIAL_EVENT" author="DaiWenBin">
		<sqlFile path="sql/MM-20230612-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>补充载具事件</comment>
	</changeSet>
	
	<changeSet id="WIP-20230619-001-FUTURE-ACTION-ADD" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_FUTURE_ACTION" columnName="IS_FORCE"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_FUTURE_ACTION">
			<column name="IS_FORCE" remarks="是否强制合批" type="VARCHAR2(1 BYTE)"/>
		</addColumn>
		
		<addColumn tableName="WIP_FUTURE_ACTION_HIS">
			<column name="IS_FORCE" remarks="是否强制合批" type="VARCHAR2(1 BYTE)"/>
		</addColumn>
		<comment>未来合批增加是否强制合批字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20230619-013-AD_FIELD" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230619-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>变量管理源字段栏位补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20230703-001-LOT_TECN" author="FanChengMing">
		<sqlFile path="sql/WIP-20230703-001.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>用动态编辑器整理LotTecn页面</comment>
	</changeSet>
	
	<changeSet id="WIP-20230703-011-AD_REFLIST" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230704-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>变量参考值修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20230707-011-AD_BUTTON" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230711-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>EDC临时工程变更按钮重置/更新合批规则界面</comment>
	</changeSet>
	
	<changeSet id="WIP-20230711-0001-PRD-SPEC" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230711-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改产品SPEC动态表名称</comment>
	</changeSet>
	
	<changeSet id="WIP-20230711-002-UPDATE-ACTION-POINT-LOCATION" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230711-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改三个动作注入功能菜单位置</comment>
	</changeSet>
	
	<changeSet id="WIP-20230712-001-PRD-SPEC" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230712-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>修改产品SPEC动态表名称</comment>
	</changeSet>
	
	<changeSet id="BAS-20230712-010-AD_FIELD" author="Clark">
		<sqlFile path="sql/BAS-20230712-010.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>合批规则栏位调整</comment>
	</changeSet>
	
	<changeSet id="MM-20230713-010-UPDATE_MM_CARRIER_ACTION_QUERY" author="DaiWenBin">
		<sqlFile path="sql/MM-20230713-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>载具管理按钮名称调整</comment>
	</changeSet>
	
	<changeSet id="EDC-20230713-010-UPDATE_EDC_TECN" author="DaiWenBin">
		<sqlFile path="sql/EDC-20230713-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>EDC临时工程变更动态表重执行</comment>
	</changeSet>
	
	<changeSet id="WIP-20230714-011-BY_LOT_RUN" author="DaiWenBin">
		<sqlFile path="sql/WIP-20230714-011.sql" relativeToChangelogFile="true" encoding="GBK"/>
		<comment>按批次作业更新</comment>
	</changeSet>
	
	<changeSet id="WIP-20230713-020-RAS_EQP_EVENT_HIS" author="zhougelong">
		<modifyDataType tableName="RAS_EQP_EVENT_HIS" columnName="ACTION_REASON" newDataType="VARCHAR2(64 BYTE)"/>
		<comment>修改RAS_EQP_EVENT_HIS字段长度</comment>
	</changeSet>	
	
	<changeSet id="WIP-20230713-002-MENU-LOCATION-UPDATED" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230713-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>V8.2的功能菜单位置sql</comment>
	</changeSet>
	
	<changeSet id="WIP-20230713-003-VIEW-UPDATE-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230713-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>一些页面缺失的message补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20220714-001-MODIFYDATATYPE" author="tangjiacheng">
		<modifyDataType tableName="BAS_MERGE_RULE_LINE" columnName="EXPRESSION" newDataType="VARCHAR2(128 BYTE)"/>
		<comment>修改BAS_MERGE_RULE_LINE字段的长度</comment>
	</changeSet>
	
	<changeSet id="WIP-20220718-001-DATATRANSFER_MENU-LOCATION-UPDATED" author="FanChengMing">
		<sqlFile path="sql/WIP-20230718-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>移动dataTransfer功能菜单位置，mylot界面message优化</comment>
	</changeSet>
	
	 <changeSet id="WIP-20230719-001-LOT_REWORK" author="Clark">
		<modifyDataType tableName="WIP_LOT_REWORK" columnName="REWORK_COUNT" newDataType="NUMBER(3)"/>
		<modifyDataType tableName="WIP_LOT_REWORK" columnName="TOTAL_REWORK_COUNT" newDataType="NUMBER(3)"/>
		<modifyDataType tableName="WIP_LOT_REWORK" columnName="MAX_REWORK_COUNT" newDataType="NUMBER(3)"/>
		<comment>修改WIP_LOT_REWORK表栏位的类型</comment>
	</changeSet>

	<changeSet id="WIP-20220719-001-AD_MESSAGE" author="DaiWenBin">
		<sql>delete ad_message where KEY_ID IN ('prd.lot_all_rework_not_split'); 
			insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 359016460398362624, 0, 'Y', 
			'prd.lot_all_rework_not_split', 'Lot all rework, this operation is not allowed!', '批次整批返工，不允许此操作!', null); </sql>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20220720-001-AD_MESSAGE" author="DaiWenBin">
		<sql>DELETE  AD_MESSAGE WHERE OBJECT_RRN = '202307181011001';
		insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
		values ('202307181011001', '0', 'Y', 'common_excel_value_contain_array_variable_cannot_preview', 'Excel Value Contain Array Variable Connot Preview, Please Use Excel Document', '文件模板参数包含集合类型变量，请使用文档模板预览', null);
	 	</sql>
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20230720-002-AD_MESSAGE-UPDATE" author="Clark">
		<sqlFile path="sql/WIP-20230720-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充英译中</comment>
	</changeSet>

	<changeSet id="RAS-20230725-001-DROPCOLOUM_RASEQP" author="FanChengMing">
    	<preConditions onFail="MARK_RAN">
			<columnExists tableName="RAS_EQP" columnName="IS_USE_RTD"/>
		</preConditions> 
    	
    	<dropColumn tableName="RAS_EQP">  
        	<column name="IS_USE_RTD"/>
        	<column name="IS_AUTO_PREPARE"/>  
    	</dropColumn>  
	</changeSet>
	
	<changeSet id="MM-20230728-001-MIX-LOT_CREATE" author="Clark">
		<sqlFile path="sql/MM-20230728-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>混合料功能SQL</comment>
	</changeSet>
	
	<changeSet id="MM-20230728-002-AD_MESSAGE-UPDATE" author="Clark">
		<sqlFile path="sql/MM-20230728-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="MM-20230728-003-BOM-UPDATE" author="Clark">
		<sqlFile path="sql/MM-20230728-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Bom页面调整</comment>
	</changeSet>
	
	<changeSet id="MM-20230728-004-MM-EVENT-ADIMP" author="tangjiacheng">
		<sqlFile path="sql/MM-20230728-004.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>物料事件物料状态模型ADIMP数据导入</comment>
	</changeSet>
	
	<changeSet id="WIP-20230731-001-SKIP-BACKUP-UPDATE" author="Clark">
		<sqlFile path="sql/WIP-20230731-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>SKIP，BACKUP页面调整</comment>
	</changeSet>
	
	<changeSet id="BAS-20230802-001-DELETE-SYS-PARAMETER" author="Clark">
		<sql>
			delete ad_sys_parameter t where t.name = 'mes_tcard_break_height';
			delete ad_sys_parameter_value t where t.name = 'mes_tcard_break_height';
		</sql>
		<comment>删除系统参数</comment>
	</changeSet>
	
	<changeSet id="WIP-20230802-001-ADD-ADIMP" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230802-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>补充一些缺失的动态导入导出数据，修改事件模型导入模板字段</comment>
	</changeSet>
	
	<changeSet id="RAS-20230804-001-EQUIPMENT-ADD-OWNER" author="tangjiacheng">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP" columnName="OWNER"/>
			</not>
		</preConditions>

		<addColumn tableName="RAS_EQP">
			<column name="OWNER" remarks="所属人" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		
		<addColumn tableName="RAS_EQP_HIS">
			<column name="OWNER" remarks="所属人" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>设备添加所属人字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20230804-001-lot-detail" author="hetao">
		<sqlFile path="sql/WIP-20230804-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次详细信息转GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20230807-003-RASEQP-LOTDETAIL-VIEW-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230807-003.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备页面修改，批次详细信息页面修改，发布XML</comment>
	</changeSet>
	
	<changeSet id="RAS-20230807-001-POSITION" author="Clark">
		<sqlFile path="sql/RAS-20230807-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>Position页面调整</comment>
	</changeSet>
	<changeSet id="MM-20230808-012-MLOT-STOCK-TAKING" author="DaiWenBin">
		<sqlFile path="sql/MM-20230808-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>物料批盘点功能提交</comment>
	</changeSet>
	
	<changeSet id="MM-20230808-MM_MATERIAL_STOCK_TAKING" author="DaiWeBin">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="MM_MATERIAL_STOCK_TAKING"/>
			</not>
		</preConditions>
		<createTable remarks="物料批盘点" tableName="MM_MATERIAL_STOCK_TAKING" tablespace="TS_MES_DAT">
			<column name="DOC_RRN" remarks="盘点Rrn" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_DOC_RRN"/>
			</column>
			<column name="WAREHOUSE_RRN" remarks="仓库Rrn" type="NUMBER(19, 0)"/>
			<column name="WAREHOUSE_ID" remarks="仓库号" type="VARCHAR2(64 BYTE)"/>
			<column name="STOCK_TAKING_USER" remarks="盘点人" type="VARCHAR2(64 BYTE)"/>
			<column name="STOCK_TAKING_DATE" remarks="盘点时间" type="date"/>
		</createTable>

		<createTable remarks="物料批盘点详情" tableName="MM_MATERIAL_STOCK_TAKING_DETAIL" tablespace="TS_MES_DAT">
			<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
				<constraints nullable="false" primaryKey="true" primaryKeyName="PK_STOCK_TAKING_DETAIL"/>
			</column>
			<column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
			<column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
			
			<column name="DOC_RRN" remarks="工单Rrn" type="NUMBER(19, 0)"/>
			<column name="DOC_ID" remarks="工单号" type="VARCHAR2(64 BYTE)"/>
			<column name="SEQ_NO" remarks="序号" type="NUMBER(19, 0)"/>
			<column name="MLOT_RRN" remarks="物料批Rrn" type="NUMBER(19, 0)"/>
			<column name="MLOT_ID" remarks="物料批号" type="VARCHAR2(64 BYTE)"/>
			<column name="LOT_RRN" remarks="批次Rrn" type="NUMBER(19, 0)"/>
			<column name="LOT_ID" remarks="批次号" type="VARCHAR2(64 BYTE)"/>
			<column name="MATERIAL_RRN" remarks="物料Rrn" type="NUMBER(19, 0)"/>
			<column name="MATERIAL_NAME" remarks="物料名称" type="VARCHAR2(64 BYTE)"/>
			<column name="MATERIAL_VERSION" remarks="物料版本" type="NUMBER(19, 0)"/>
			<column name="MATERIAL_DESC" remarks="物料描述" type="VARCHAR2(64 BYTE)"/>
			<column name="MATERIAL_TYPE" remarks="物料类别" type="VARCHAR2(64 BYTE)"/>
			<column name="UOM_ID" remarks="物料单号" type="VARCHAR2(64 BYTE)"/>
			<column name="TRANS_MAIN_QTY" remarks="主数量" type="NUMBER(19, 0)"/>
			<column name="TRANS_SUB_QTY" remarks="子数量" type="NUMBER(19, 0)"/>
			<column name="TRANS_WAREHOUSE_ID" remarks="仓库ID" type="VARCHAR2(64 BYTE)"/>
			<column name="TRANS_STORAGE_TYPE" remarks="库位类别" type="VARCHAR2(64 BYTE)"/>
			<column name="TRANS_STORAGE_ID" remarks="库位Id" type="VARCHAR2(64 BYTE)"/>
			<column name="TRANS_TARGET_WAREHOUSE_ID" remarks="目标仓库ID" type="VARCHAR2(64 BYTE)"/>
			<column name="TRANS_TARGET_STORAGE_TYPE" remarks="目标库位类别" type="VARCHAR2(64 BYTE)"/>
			<column name="TRANS_TARGET_STORAGE_ID" remarks="目标库位ID" type="VARCHAR2(64 BYTE)"/>
			<column name="FLOOR_LIFE_EXPIRE" remarks="批次使用有效期失效时间" type="VARCHAR2(64 BYTE)"/>
		</createTable>
	</changeSet>

	<changeSet id="APF-20230811-001-APF_WATCH_LOG" author="LiTao">
		<modifyDataType tableName="APF_WATCH_LOG" columnName="EVENT_TIME" newDataType="TIMESTAMP(6)"/>
		<comment>修改APF_WATCH_LOG字段类型</comment>
	</changeSet>
	
	<changeSet id="WIP-20230816-001-LOTDETAIL-UPDATE" author="Clark">
		<sqlFile path="sql/WIP-20230816-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次详细信息页面修改，发布XML</comment>
	</changeSet>

	<changeSet id="MM-20230818-001-STOCK_TAKING-UPDATE" author="LiuTao">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="MM_MATERIAL_STOCK_TAKING"/>
		</preConditions>
		<renameTable oldTableName="MM_MATERIAL_STOCK_TAKING" newTableName="MM_MATERIAL_STOCKTAKING"/>
	</changeSet>

	<changeSet id="MM-20230818-002-STOCK_TAKING-UPDATE" author="LiuTao">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="MM_MATERIAL_STOCK_TAKING_DETAIL"/>
		</preConditions>
		<renameTable oldTableName="MM_MATERIAL_STOCK_TAKING_DETAIL" newTableName="MM_MATERIAL_STOCKTAKING_DETAIL"/>
	</changeSet>
	
	<changeSet id="MM-20230818-001-MM-MATERIAL" author="tangjiacheng">
		<sqlFile path="sql/MM-20230818-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>物料信息页面添加Attribute属性维护列表</comment>
	</changeSet>

	<changeSet id="MM-20230823-001-MM-MATERIALCONFIG" author="Clark">
		<sqlFile path="sql/MM-20230823-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>物料控制面板SQL</comment>
	</changeSet>
	
	<changeSet id="RAS-20230825-001-RAS-MODEL-EVENT" author="tangjiacheng">
		<sqlFile path="sql/RAS-20230825-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>设备状态模型页面列表字段修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20230901-001-LOT-TECN-ACTIVE-DIALOG" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230901-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>批次临时变更激活弹框乱码</comment>
	</changeSet>
	
	<changeSet id="MM-20230908-001-BATCH-MLOT" author="tangjiacheng">
		<sqlFile path="sql/MM-20230908-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>上架，下架，出库，移库，Kitting等页面功能修改为支持Batch物料批</comment>
	</changeSet>
	
	<changeSet id="WIP-20230911-001-REWORK-WO-WHERECLAUSE" author="tangjiacheng">
		<sql>
			UPDATE AD_TABLE
			SET WHERE_CLAUSE='docStatus in (''CREATED'',''APPROVED'',''CLOSED'',''STARTED'') and reworkProcessName != null', INIT_WHERE_CLAUSE='docStatus in (''CREATED'',''APPROVED'') and reworkProcessName != null'
			WHERE OBJECT_RRN=93608620;
		</sql>
		<comment>修改返工工单初始查询条件</comment>
	</changeSet>
	
	<changeSet id="MM-20230911-001-RESERVER" author="zhougelong">
		<sqlFile path="sql/MM-20230911-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>提交message</comment>
	</changeSet>
	
	<changeSet id="MM-20230914-001-BATCH-MLOT-KITTING-AND-TRANSFER" author="tangjiacheng">
		<sqlFile path="sql/MM-20230914-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>提交message</comment>
	</changeSet>
	
	<changeSet id="WIP-20230928-001-VIEW-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20230928-001.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>治具CONTEXT BOM页面调整,批次在线返工弹框优化,BOM设置初始查询条件</comment>
	</changeSet>
</databaseChangeLog> 