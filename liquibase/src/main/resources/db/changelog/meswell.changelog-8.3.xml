<?xml version="1.1" encoding="GBK" standalone="no"?>
<databaseChangeLog
	xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
	xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
	xmlns:pro="http://www.liquibase.org/xml/ns/pro"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.9.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.9.xsd">

	<changeSet id="RAS-20230911-001-ADD_TOOLCONTEXTBOM" author="Clark">
		<sqlFile path="sql/RAS-20230911-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>增加ToolContextBom功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20230913-001-ADDCOLOUM_LOT" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WIP_LOT" columnName="DYNAMIC_PRIORITY"/>
			</not>
		</preConditions>

		<addColumn tableName="WIP_LOT">
			<column name="DYNAMIC_PRIORITY" remarks="动态优先级" type="NUMBER(19, 0)"/>
		</addColumn>
		<comment>WIP_LOT增加动态优先级</comment>
	</changeSet>
	
	<changeSet id="MM-20230915-001-MM_STOCKTAKING_DETAIL" author="Clark">
		<modifyDataType tableName="MM_MATERIAL_STOCKTAKING_DETAIL" columnName="TRANS_MAIN_QTY" newDataType="NUMBER"/>
		<modifyDataType tableName="MM_MATERIAL_STOCKTAKING_DETAIL" columnName="TRANS_SUB_QTY" newDataType="NUMBER"/>
		<comment>修改MM_MATERIAL_STOCKTAKING_DETAIL表数量字段支持小数点</comment>
	</changeSet>
	
	<changeSet id="BAS-20230916-001-ADDCOLOUMN_ID_GENERATOR_RULE" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="BAS_ID_GENERATOR_RULE" columnName="RULE_CATEGORY"/>
			</not>
		</preConditions>

		<addColumn tableName="BAS_ID_GENERATOR_RULE">
			<column name="RULE_CATEGORY" remarks="规则类别" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>WIP_LOT增加动态优先级</comment>
	</changeSet>
	
	<changeSet id="BAS-20230918-001-DROPCOLOUMN_ID_GENERATOR_RULE" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="BAS_ID_GENERATOR_RULE" columnName="RULE_CATEGORY"/>
		</preConditions> 
    	
    	<dropColumn tableName="BAS_ID_GENERATOR_RULE">  
        	<column name="RULE_CATEGORY"/>  
    	</dropColumn> 
	</changeSet>
	
	<changeSet id="RAS-20230921-001-ADD_RAS_EQP_TYPE_HIS" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="RAS_EQP_TYPE_HIS"/>
			</not>
		</preConditions>
		
    	<createTable remarks="设备类型历史" tableName="RAS_EQP_TYPE_HIS" tablespace="TS_MES_DAT">
        	<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_RAS_EQP_TYPE_HIS"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/> 
            
            <column name="TRANS_TYPE" remarks="设备号" type="VARCHAR2(32 BYTE)"/>
            <column name="EQP_TYPE" remarks="设备类型" type="VARCHAR2(32 BYTE)"/>
            <column name="DESCRIPTION" remarks="描述" type="VARCHAR2(64 BYTE)"/>
            <column name="ACTION_CODE" remarks="动作码" type="VARCHAR2(32 BYTE)"/>
            <column name="ACTION_REASON" remarks="动作原因" type="VARCHAR2(64 BYTE)"/>
            <column name="ACTION_COMMENT" remarks="动作备注" type="VARCHAR2(64 BYTE)"/>
            <column name="HIS_COMMENT" remarks="历史备注" type="VARCHAR2(4000 BYTE)"/>
    	</createTable>
    	
    	<dropColumn tableName="RAS_EQP">
        	<column name="IS_MANDATORY_INSPECT"/>  
    	</dropColumn>
    	
    	<dropColumn tableName="RAS_EQP_HIS">
        	<column name="IS_MANDATORY_INSPECT"/>  
    	</dropColumn> 
	</changeSet>
	
	<changeSet id="WIP-20230928-001-ADDCOLOUMN_WF_TOKEN" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_TOKEN" columnName="HI_SUPER_INSTRUCTION"/>
			</not>
		</preConditions>

		<addColumn tableName="WF_TOKEN">
			<column name="HI_SUPER_INSTRUCTION" remarks="返回流程路径" type="VARCHAR2(512 BYTE)"/>
		</addColumn>
		<comment>WF_TOKEN增加栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20230928-010-ADD_REWORK" author="Clark">
		<sqlFile path="sql/WIP-20230928-010.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>增加批次特殊返工功能SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20231007-Sql" author="DaiWenBin">
       <sql>delete AD_AUTHORITY where name = 'Wip.RefreshCurrentStep';
			delete ad_editor where object_rrn = 20220708077;
		</sql>
       <comment>删除批次刷新当站功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20231008-008-REWORK_FUNCTION" author="Clark">
		<sqlFile path="sql/WIP-20231008-008.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>整理Rework菜单</comment>
	</changeSet>
	
	<changeSet id="WIP-20231009-011-LOT_SKIP" author="DaiWenBin">
		<sqlFile path="sql/WIP-20231009-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>批次跳步功能转GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20231009-012-LOT_BACKUP" author="tangjiacheng">
		<sqlFile path="sql/WIP-20231009-012.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>批次退步功能转GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20231010--DROP_TABLE_WIP_FLAG" author="DaiWenBin">
    	<preConditions onFail="MARK_RAN">
			<tableExists  tableName="WIP_FLAG"/>
		</preConditions>
		
		<dropTable cascadeConstraints="true"  tableName="WIP_FLAG"/>  
	</changeSet>
	
	<changeSet author="DaiWenBin" id="WIP-20231010-CREATE_TABLE_WIP_FLAG" >
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="WIP_FLAG"/>
			</not>
		</preConditions>
		
    	<createTable remarks="Flag定义" tableName="WIP_FLAG" tablespace="TS_MES_DAT">
        	<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_FLAG"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/> 
            
            <column name="LOT_RRN" remarks="批次Rrn" type="NUMBER(19, 0)"/>
            <column name="COMPONENT_RRN" remarks="ComponentRrn" type="NUMBER(19, 0)"/>
            <column name="CELL_ID" remarks="CellID" type="VARCHAR2(32 BYTE)"/>
            <column name="CATEGORY" remarks="类别" type="VARCHAR2(32 BYTE)"/>
            <column name="flag1" remarks="FLAG1" type="VARCHAR2(32 BYTE)"/>
            <column name="flag2" remarks="FLAG2" type="VARCHAR2(32 BYTE)"/>
            <column name="flag3" remarks="FLAG3" type="VARCHAR2(32 BYTE)"/>
            <column name="flag4" remarks="FLAG4" type="VARCHAR2(32 BYTE)"/>
            <column name="flag5" remarks="FLAG5" type="VARCHAR2(32 BYTE)"/>
            <column name="flag6" remarks="FLAG6" type="VARCHAR2(32 BYTE)"/>
            <column name="flag7" remarks="FLAG7" type="VARCHAR2(32 BYTE)"/>
            <column name="flag8" remarks="FLAG8" type="VARCHAR2(32 BYTE)"/>
            <column name="flag9" remarks="FLAG9" type="VARCHAR2(32 BYTE)"/>
            <column name="flag10" remarks="FLAG10" type="VARCHAR2(32 BYTE)"/>
            <column name="flag11" remarks="FLAG11" type="VARCHAR2(32 BYTE)"/>
            <column name="flag12" remarks="FLAG12" type="VARCHAR2(32 BYTE)"/>
            <column name="flag13" remarks="FLAG13" type="VARCHAR2(32 BYTE)"/>
            <column name="flag14" remarks="FLAG14" type="VARCHAR2(32 BYTE)"/>
            <column name="flag15" remarks="FLAG15" type="VARCHAR2(32 BYTE)"/>
            <column name="flag16" remarks="FLAG16" type="VARCHAR2(32 BYTE)"/>
            <column name="flag17" remarks="FLAG17" type="VARCHAR2(32 BYTE)"/>
            <column name="flag18" remarks="FLAG18" type="VARCHAR2(32 BYTE)"/>
            <column name="flag19" remarks="FLAG19" type="VARCHAR2(32 BYTE)"/>
            <column name="flag20" remarks="FLAG20" type="VARCHAR2(32 BYTE)"/>
            <column name="flag21" remarks="FLAG21" type="VARCHAR2(32 BYTE)"/>
            <column name="flag22" remarks="FLAG22" type="VARCHAR2(32 BYTE)"/>
            <column name="flag23" remarks="FLAG23" type="VARCHAR2(32 BYTE)"/>
            <column name="flag24" remarks="FLAG24" type="VARCHAR2(32 BYTE)"/>
            <column name="flag25" remarks="FLAG25" type="VARCHAR2(32 BYTE)"/>
    	</createTable>
	</changeSet>
	
	<changeSet id="WIP-20231010-DROP_TABLE_WIP_FLAGSTEP" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<tableExists  tableName="WIP_FLAG_STEP"/>
		</preConditions>
		
		<dropTable cascadeConstraints="true"  tableName="WIP_FLAG_STEP"/>  
	</changeSet>
	
	<changeSet id="WIP-20231010-CREATE_TABLE_WIP_FLAGSTEP" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="WIP_FLAG_STEP"/>
			</not>
		</preConditions>
		
    	<createTable remarks="工步Flag" tableName="WIP_FLAG_STEP" tablespace="TS_MES_DAT">
        	<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_FLAG_STEP"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/> 
            
            <column name="CATEGORY" remarks="类别" type="VARCHAR2(32 BYTE)"/>
            <column name="PART_NAME" remarks="产品名称" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_STATE_NAME" remarks="工步节点名称" type="VARCHAR2(32 BYTE)"/>
            <column name="FLAG_BIT" remarks="标志位" type="NUMBER(19, 0)"/>
            <column name="FLAG_VALUE" remarks="标志值" type="VARCHAR2(32 BYTE)"/>
    	</createTable>
	</changeSet>
	
	<changeSet id="WIP-20231010-CREATE_TABLE_WIP_FLAG_DEF" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="WIP_FLAG_DEF"/>
			</not>
		</preConditions>
		
    	<createTable remarks="" tableName="WIP_FLAG_DEF" tablespace="TS_MES_DAT">
        	<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_WIP_FLAG_DEF"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/> 
            
            <column name="CATEGORY" remarks="类别" type="VARCHAR2(32 BYTE)"/>
            <column name="FLAG_BIT" remarks="标志位" type="NUMBER(19, 0)"/>
            <column name="FLAG_NAME" remarks="Flag名称" type="VARCHAR2(32 BYTE)"/>
            <column name="FLAG_DESC" remarks="Flag描述" type="VARCHAR2(32 BYTE)"/>
    	</createTable>
	</changeSet>
	
	<changeSet id="WIP-20231012-003-LOT_CHANGE-PROCESS" author="tangjiacheng">
		<sqlFile path="sql/WIP-20231023-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>批次更改生产工步功能转GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20231013-001-WIP_FLAG_STEP" author="Clark">
		<sqlFile path="sql/WIP-20231013-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>WIPFlagStep功能SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20231013-003-Auto_Merge" author="Clark">
		<sqlFile path="sql/WIP-20231013-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>提交message</comment>
	</changeSet>
	
	<changeSet id="WIP-20231011-001-FLAG_DEF" author="DaiWenBin">
		<sqlFile path="sql/WIP-20231011-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>批次退步功能转GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20231013-001-TEAM" author="zhougelong">
		<sqlFile path="sql/BAS-20231013-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>班组修改适配web端接口</comment>
	</changeSet>
	
	<changeSet id="WIP-20231018-001-NEW_PART-VIEW" author="tangjiacheng">
		<sqlFile path="sql/WIP-20231018-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>批次更改产品功能转GLC</comment>
	</changeSet>
	
	<changeSet id="RAS-20231018-001-ADDCOLOUMN_RAS_EQP_CURRENT" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="RAS_EQP_CURRENT" columnName="CURRENT_WO_ID"/>
			</not>
		</preConditions>

		<addColumn tableName="RAS_EQP_CURRENT">
			<column name="CURRENT_WO_ID" remarks="当前工单号" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<addColumn tableName="RAS_EQP_EVENT_HIS">
			<column name="CURRENT_WO_ID" remarks="当前工单号" type="VARCHAR2(32 BYTE)"/>
			<column name="TARGET_WO_ID" remarks="目标工单号" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>RAS_EQP_CURRENT增加栏位</comment>
	</changeSet>
	
	<changeSet id="RAS-20231018-001-DROPCOLOUMN_RAS_EQP" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="RAS_EQP" columnName="IS_MANDATORY_INSPECT"/>
		</preConditions> 
    	
    	<dropColumn tableName="RAS_EQP">  
        	<column name="IS_MANDATORY_INSPECT"/>  
    	</dropColumn>
    	<dropColumn tableName="RAS_EQP_EVENT_HIS">  
        	<column name="IS_MANDATORY_INSPECT"/>  
    	</dropColumn> 
	</changeSet>
	
	<changeSet id="WIP-20231019-006-WIP_FLAG_STEP" author="Clark">
		<sqlFile path="sql/WIP-20231019-006.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>修改WIPFlagStep功能SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20231024-001-CHANGE_SHIFT_GCL" author="DaiWenBin">
		<sqlFile path="sql/WIP-20231024-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>交接班功能转GLC</comment>
	</changeSet>
	
	<changeSet id="PRD-20231025-001-REWORKSTATE_REMOVE_HISuperInstruction" author="FanChengMing">
		<sql>
			delete ad_field where table_rrn =(select object_rrn from ad_table where object_rrn = 4022) and name = 'hiSuperInstruction';
		</sql>	   
		<comment>返工节点删除返工指示</comment>
	</changeSet>
	
	<changeSet id="PRD-20231026-001-DROPCOLOUMN_WF_TOKEN" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="WF_TOKEN" columnName="HI_SUPER_START_NODE_RRN"/>
		</preConditions> 
    	
    	<dropColumn tableName="WF_TOKEN">  
        	<column name="HI_SUPER_START_NODE_RRN"/>  
    	</dropColumn>
	</changeSet>
	
	<changeSet id="WIP-20231027-001-PP-REWORK-WORKORDER-START" author="tangjiacheng">
		<sqlFile path="sql/WIP-20231027-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>返工工单，返工工单投料功能改GLC</comment>
	</changeSet>
	
	<changeSet id="COM-20231102-001-ADDCOLOUMN_COM_CONTEXT" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="COM_CONTEXT" columnName="DESCRIPTION"/>
			</not>
		</preConditions>

		<addColumn tableName="COM_CONTEXT">
			<column name="DESCRIPTION" remarks="描述" type="VARCHAR2(256 BYTE)"/>
		</addColumn>
		
		<comment>COM_CONTEXT增加DESCRIPTION栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20231103-001-MLOT-KITTING" author="tangjiacheng">
		<sqlFile path="sql/WIP-20231103-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料批KITTING转GLC</comment>
	</changeSet>
	
	<changeSet id="MM-20231103-001-MLOT-IQC" author="DaiWenBin">
		<sqlFile path="sql/MM-20231101-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料信息、物料批IQC功能改GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20231106-001-LOT_DISPATCH_FORBIDDEN" author="FanChengMing">
		<sqlFile path="sql/WIP-20231106-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>将批次禁止派工功能到rtd模块</comment>
	</changeSet>
	
	<changeSet id="WIP-20231106--DROP_TABLE_WIP_LOT_DISPATCH_FORBIDDEN" author="FanChengMing">
    	<preConditions onFail="MARK_RAN">
			<tableExists  tableName="WIP_LOT_DISPATCH_FORBIDDEN"/>
		</preConditions>
		<dropTable cascadeConstraints="true"  tableName="WIP_LOT_DISPATCH_FORBIDDEN"/>  
	</changeSet>
	
	<changeSet id="WIP-20231107-001_UPDATE_PROCEDURE_VERSION" author="Clark">
		<sqlFile path="sql/WIP-20231107-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新流程版本SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20231107-002_UPDATE_UNSCRAP" author="Clark">
		<sqlFile path="sql/WIP-20231107-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>取消报废转GLC SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20231109-001_UPDATE_UNSCRAP" author="Clark">
		<sqlFile path="sql/WIP-20231109-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>取消报废转Glc发布SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20231109-002_UPDATE_CHANGESHELFT" author="tangjiacheng">
		<sqlFile path="sql/WIP-20231109-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>交接班等功能缺失sql补充</comment>
	</changeSet>
	
	<changeSet id="BAS-20231110-001-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/BAS-20231110-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>班组message</comment>
	</changeSet>
	
	<changeSet id="MM-20231110-002-WAREHOUSE-UPLOAD" author="tangjiacheng">
		<sqlFile path="sql/MM-20231110-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>仓库导入导出sql</comment>
	</changeSet>
	
	<changeSet id="BAS-20231110-002-MESSAGE" author="zhougelong">
		<sqlFile path="sql/BAS-20231110-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>班组接口导入效验message</comment>
	</changeSet>
	
	<changeSet id="WIP-20231114-001-AD_REFNAME" author="DaiWenBin">
		<sqlFile path="sql/WIP-20231114-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充AD_REFNAME</comment>
	</changeSet>
	
	<changeSet id="WIP-20231117-005-AD_MESSAGE" author="Clark">
		<sqlFile path="sql/WIP-20231117-005.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充message</comment>
	</changeSet>
	
	<changeSet id="RAS-20231121-001-AD_UREFLIST" author="Clark">
		<sqlFile path="sql/RAS-20231121-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充AD_UREFLIST</comment>
	</changeSet>
	
	<changeSet id="WIP-20231122-001-AD_SYSTEM_PARAM" author="Clark">
		<sqlFile path="sql/WIP-20231122-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>修改系统参数</comment>
	</changeSet>
	
	<changeSet id="PRD-20231125-001-ADDCOLOUMN_PROCESS_STATE" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_TOKEN" columnName="PROCESS_STATE_NAME"/>
			</not>
		</preConditions>

		<addColumn tableName="WF_TOKEN">
			<column name="PROCESS_STATE_NAME" remarks="工步状态" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		
		<addColumn tableName="WF_TOKEN">
			<column name="PROCESS_STEP_RRN" remarks="当前工步RRN" type="NUMBER(19, 0)"/>
		</addColumn>
		
		<comment>WF_TOKEN增加PROCESS_STATE_NAME栏位</comment>
	</changeSet>
	
	<changeSet id="PRD-20231125-002-ADDCOLOUMN_STEP_STATE_NAME" author="Tony">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="WF_PARAMETER_INSTANCE" columnName="STEP_STATE_NAME"/>
			</not>
		</preConditions>

		<addColumn tableName="WF_PARAMETER_INSTANCE">
			<column name="STEP_STATE_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
				
		<comment>WF_PARAMETER_INSTANCE增加STEP_STATE_NAME栏位</comment>
	</changeSet>
	
	<changeSet id="PRD-20231125-003-ADDINDEX_TOKEN_RRN" author="Tony">
		<preConditions onFail="MARK_RAN">
			<indexExists indexName="IDX_WF_PARAMETER_INST_TOKEN" tableName="WF_PARAMETER_INSTANCE"/>
		</preConditions>
		
		<dropIndex indexName="IDX_WF_PARAMETER_INST_TOKEN" tableName="WF_PARAMETER_INSTANCE"/>
		
		<createIndex indexName="IDX_WF_PARAMETER_INST_TOKEN" tableName="WF_PARAMETER_INSTANCE" tablespace="TS_MES_WF_IDX" unique="false">
             <column name="TOKEN_RRN"/>
             <column name="STEP_STATE_NAME"/>
        </createIndex>
	</changeSet>
	
	<changeSet id="WIP-20231127-001-LOT-MANUAL-REWORK-VIEW" author="tangjiacheng">
		<sql>
			UPDATE AD_TABLE SET LABEL = 'Lot Manual Rework#Please Input Rework Info' ，LABEL_ZH = '批次在线放工#请输入批次返工信息' WHERE OBJECT_RRN = '140047829258354688';
		</sql>	   
		<comment>批次在线返工弹框页面修改</comment>
	</changeSet>
	
	<changeSet id="MM-20231201-001-MLOTKITTING-APPEND-DEATECH-DIALOG" author="tangjiacheng">
		<sql>
			 MERGE INTO AD_FORM_RELEASE a
			 USING (SELECT 'MMMLotDetachMaterialDialog' NAME, TO_TIMESTAMP('2023/11/30 11:08:36', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
			 ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
			 WHEN MATCHED THEN 
 			 UPDATE SET a.IS_UPDATE = 'Y'
			 WHEN NOT MATCHED THEN 
 			 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 			 VALUES ('396351364319916034', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
		</sql>	   
		<comment>物料批kittingAppend类型弹框修改</comment>
	</changeSet>
	
	<changeSet id="BAS-20231122-001-SHIFT-MESSAGE" author="zhougelong">
		<sqlFile path="sql/BAS-20231201-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>班次英译中</comment>
	</changeSet>
	
	<changeSet id="BAS-20231122-001-WEBLOCATION" author="zhougelong">
		<sqlFile path="sql/BAS-20231201-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>生产区域sql</comment>
	</changeSet>
	
	<changeSet id="MM-20231204-001-EVENT" author="DaiWenBin">
		<sqlFile path="sql/MM-20231204-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>修改物料事件相关Sql</comment>
	</changeSet>
	
	<changeSet id="PRD-20231208-001-PART-UPLOAD" author="tangjiacheng">
		<sqlFile path="sql/PRD-20231208-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>产品导入导出动态表字段长度修改</comment>
	</changeSet>
	
	<changeSet id="BAS-20231226-001-QUERY-DIALOG-SQL" author="tangjiacheng">
		<sqlFile path="sql/BAS-20231226-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>block页面查询按钮弹框确实sql提交</comment>
	</changeSet>
	
</databaseChangeLog	> 