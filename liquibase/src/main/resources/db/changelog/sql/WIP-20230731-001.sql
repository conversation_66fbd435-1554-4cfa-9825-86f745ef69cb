
DELETE AD_FIELD WHERE TABLE_RRN = '92480035';
DELETE AD_TABLE WHERE OBJECT_RRN = '92480035';

insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 92480035, 0, 'Y', to_date('2016-08-24 11:10:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:55:45', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'WIPLotByCarrier', '载具批次信息', 'WIP_LOT', 'N', 'Lot', 'com.glory.mes.wip.model.Lot', null, null, null, 'N', 2, null, 'Lot Merge(Sorter)', '载具合批', null, 1); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 92480036, 0, 'Y', to_date('2016-08-24 11:12:04', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:55:45', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'lotId', '批次号', null, 92480035, null, 10, 18, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Lot ID', '批次号', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 92480037, 0, 'Y', to_date('2016-08-24 11:12:36', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:55:45', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'priority', '优先级', null, 92480035, null, 20, 9, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', null, null, null, null, null, null, null, null, null, null, 'Priority', '优先级', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 92480039, 0, 'Y', to_date('2016-08-24 11:13:07', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:55:45', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'stepId', '工步名称', null, 92480035, null, 30, 18, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', null, null, null, null, null, null, null, null, null, null, 'Step ID', '工步名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 92480040, 0, 'Y', to_date('2016-08-24 11:13:56', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:55:45', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'stepDesc', '工歩描述', null, 92480035, null, 40, 18, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', null, null, null, null, null, null, null, null, null, null, 'Step Desc', '工歩描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 92480041, 0, 'Y', to_date('2016-08-24 11:14:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:55:45', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'partId', '产品名称', null, 92480035, null, 50, 18, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', null, null, null, null, null, null, null, null, null, null, 'Product Name', '产品名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 92480042, 0, 'Y', to_date('2016-08-24 11:14:37', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:55:45', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'partDesc', '产品描述', null, 92480035, null, 60, 18, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', null, null, null, null, null, null, null, null, null, null, 'Product Desc', '产品描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 363367827727556608, 0, 'Y', to_date('2023-07-31 16:54:01', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:55:45', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 4, 'cstate', '状态', null, 92480035, null, 70, 12, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'State', '状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 

DELETE AD_FIELD WHERE TABLE_RRN = '6033';
DELETE AD_TAB WHERE TABLE_RRN = '6033';
DELETE AD_TABLE WHERE OBJECT_RRN = '6033';

insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 6033, 0, 'Y', to_date('2010-07-06', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2023-07-28 13:32:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 45, 'WIPSkip', 'Skip', 'WIP_LOT', 'N', 'Lot', 'com.glory.mes.wip.model.Lot', null, null, null, 'N', 1, null, 'Skip', 'Skip', null, 1); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 275938854318546945, 0, 'Y', to_date('2022-12-02 10:42:29', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:32:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 43, 'FlowInfo', '流程信息', 6033, 1, 1, 'Flow Info', '流程信息', null, 'Group', null, 65856); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 275984448311435264, 0, 'Y', to_date('2022-12-02 13:43:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:32:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 12, 'ActionInfo', '跳步信息', 6033, 30, 1, 'Ship Info', '跳步信息', null, 'Group', null, 320); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 275939241272451072, 0, 'Y', to_date('2022-12-02 10:44:01', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:32:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 42, 'partId', '产品ID', null, 6033, null, 20, null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Part ID', '产品ID', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 275939535511265280, 0, 'Y', to_date('2022-12-02 10:45:11', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:32:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 39, 'cstate', '状态', null, 6033, null, 30, null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Lot State', '批次状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 275940525320871936, 0, 'Y', to_date('2022-12-02 10:49:07', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:32:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 33, 'attribute1', '备注', null, 6033, 275984448311435264, 40, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'textarea', 'string', null, null, null, null, null, null, null, null, null, 'Comment', '备注', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 275980343056703488, 0, 'Y', to_date('2022-12-02 13:27:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:32:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 18, 'attribute2', null, null, 6033, 275938854318546945, null, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Attribute1', 'Attribute1', 'N', 'N', 'N', null, null, null, null, 'N', null, 16, null); 

DELETE AD_FIELD WHERE TABLE_RRN = '6035';
DELETE AD_TAB WHERE TABLE_RRN = '6035';
DELETE AD_TABLE WHERE OBJECT_RRN = '6035';

insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 6035, 0, 'Y', to_date('2010-07-06', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2023-07-28 13:33:58', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 13, 'WIPBackup', 'Backup', 'WIP_LOT', 'N', 'Lot', 'com.glory.mes.wip.model.Lot', null, null, null, 'N', 1, null, 'Backup', 'Backup', null, 1); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 276008109617598464, 0, 'Y', to_date('2022-12-02 15:17:41', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:33:58', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 12, 'FlowInfo', '流程信息', 6035, 10, 1, 'Flow Info', '流程信息', null, 'Group', null, 320); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 276008109617598466, 0, 'Y', to_date('2022-12-02 15:17:41', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:33:58', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 12, 'BackInfo', '退步信息', 6035, 30, 1, 'Back Info', '退步信息', null, 'Group', null, 320); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 276008853016039425, 0, 'Y', to_date('2022-12-02 15:20:38', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:33:58', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'attribute2', null, null, 6035, 276008109617598464, 10, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, null, null, 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 276008853016039424, 0, 'Y', to_date('2022-12-02 15:20:38', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:33:58', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'attribute1', '备注', null, 6035, 276008109617598466, 20, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'textarea', 'string', null, null, null, null, null, null, null, null, null, 'Comment', '备注', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 276008853016039426, 0, 'Y', to_date('2022-12-02 15:20:38', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:33:58', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'partId', '产品ID', null, 6035, null, 30, null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Part ID', '产品ID', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 276008853016039427, 0, 'Y', to_date('2022-12-02 15:20:38', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-28 13:33:58', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'cstate', '批次状态', null, 6035, null, 40, null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Lots State', '批次状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
