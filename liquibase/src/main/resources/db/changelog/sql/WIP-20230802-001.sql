DELETE AD_IMPEXP_FIELD_MAP WHERE PARENT_RRN IN ('351328029345353728','351333373853339648','351334116744269824','351376950251786240');
DELETE AD_IMPEXP WHERE OBJECT_RRN IN ('351328029345353728','351333373853339648','351334116744269824','351376950251786240');
insert into AD_IMPEXP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_NAME, AUTHORITY_DESC, TYPE, AD_TABLE_NAME, BUTTON_NAME, DESCRIPTION)
values ('351328029345353728', '0', 'Y', to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'MM.MaterialCategory', '物料类别', 'ONE', 'MMMaterialType', null, null);

insert into AD_IMPEXP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_NAME, AUTHORITY_DESC, TYPE, AD_TABLE_NAME, BUTTON_NAME, DESCRIPTION)
values ('351333373853339648', '0', 'Y', to_date('28-06-2023 11:53:23', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-06-2023 11:53:23', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'MM.ComClass', '物料状态大类', 'ONE', 'MMComClass', null, null);

insert into AD_IMPEXP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_NAME, AUTHORITY_DESC, TYPE, AD_TABLE_NAME, BUTTON_NAME, DESCRIPTION)
values ('351334116744269824', '0', 'Y', to_date('28-06-2023 11:56:20', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-06-2023 11:56:20', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'MM.State', '物料状态', 'ONE', 'MMState', null, null);

insert into AD_IMPEXP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_NAME, AUTHORITY_DESC, TYPE, AD_TABLE_NAME, BUTTON_NAME, DESCRIPTION)
values ('351376950251786240', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'MM.MLotQueryGlc', '物料批管理', 'ONE', 'MMLotQueryManager_Table06', null, null);


insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351328029345353729', '0', 'Y', to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351328029345353728', null, 'name', '1', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351328029345353730', '0', 'Y', to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351328029345353728', null, 'description', '5', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351328029345353731', '0', 'Y', to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351328029345353728', null, 'reserved01', '10', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351328029345353732', '0', 'Y', to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351328029345353728', null, 'fifoControl', '15', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351328029345353733', '0', 'Y', to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:32:09', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351328029345353728', null, 'residualControl', '20', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351333373853339649', '0', 'Y', to_date('28-06-2023 11:53:23', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:53:23', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351333373853339648', null, 'objectType', '10', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351333373853339650', '0', 'Y', to_date('28-06-2023 11:53:23', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:53:23', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351333373853339648', null, 'comClass', '20', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351333373853339651', '0', 'Y', to_date('28-06-2023 11:53:23', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:53:23', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351333373853339648', null, 'description', '30', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351334116748464128', '0', 'Y', to_date('28-06-2023 11:56:20', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:56:20', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351334116744269824', null, 'objectType', '10', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351334116748464129', '0', 'Y', to_date('28-06-2023 11:56:20', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:56:20', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351334116744269824', null, 'comClass', '20', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351334116748464130', '0', 'Y', to_date('28-06-2023 11:56:20', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:56:20', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351334116744269824', null, 'state', '30', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351334116748464131', '0', 'Y', to_date('28-06-2023 11:56:20', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 11:56:20', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351334116744269824', null, 'description', '40', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786241', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'mLotId', '1', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786242', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'materialName', '9', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786243', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'state', '10', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786244', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'mainQty', '15', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786245', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'holdState', '20', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786246', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'rootMLotId', '30', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786247', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'transMainQty', '33', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786248', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'partnerLotId', '35', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786249', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'materialVersion', '50', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786250', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'materialDesc', '60', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786251', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'materialType', '70', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786252', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'uomId', '80', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('351376950251786253', '0', 'Y', to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-06-2023 14:46:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '351376950251786240', null, 'lotComment', '90', null, null);

DELETE ad_field WHERE TABLE_RRN = '93626318';
DELETE ad_table WHERE OBJECT_RRN = '93626318';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 93626318, 0, 'Y', to_date('2018-02-11 20:33:03', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 3, 'MMStatusModelAction', '物料状态动作列表', 'COM_SM_ACTION', 'N', 'StateTimer', 'com.glory.common.state.action.StateTimer', null, null, null, 'N', 1, 1, 'MaterialStateAction', '物料状态动作', null, 1); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93626325, 0, 'Y', to_date('2018-02-11 20:34:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'name', '动作名称', null, 93626318, null, 10, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Action Name', '动作名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93626327, 0, 'Y', to_date('2018-02-11 20:35:26', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'description', '动作描述', null, 93626318, null, 20, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Action Description', '动作描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93626329, 0, 'Y', to_date('2018-02-11 20:37:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 7, 'actionState', '动作开始状态', null, 93626318, null, 30, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', 'string', null, null, null, null, 93626374, null, null, null, null, 'Start Action State', '动作开始状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93626331, 0, 'Y', to_date('2018-02-11 20:38:04', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 7, 'endActionState', '动作结束状态', null, 93626318, null, 40, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', null, null, null, null, null, 93626374, null, null, null, null, 'End Action State', '动作结束状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93626776, 0, 'Y', to_date('2018-02-12 14:51:53', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 4, 'timerType', '定时器类型', null, 93626318, null, 50, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'TimerType', null, null, null, 'Timer Type', '定时器类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 20040903, 0, 'Y', to_date('2018-02-11 20:34:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'timerAction', '定时器动作', null, 93626318, null, 55, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Timer Action', '定时器动作', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93626779, 0, 'Y', to_date('2018-02-12 14:53:58', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 4, 'timerDuration', '间隔时间', null, 93626318, null, 60, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Duration', '间隔时间', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93626781, 0, 'Y', to_date('2018-02-12 14:54:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 3, 'earlyPeriod', '提前警告时间', null, 93626318, null, 70, null, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Early Period', '提前警告时间', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93626783, 0, 'Y', to_date('2018-02-12 14:56:24', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 5, 'timerUnit', '时间单位', null, 93626318, null, 80, null, 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'Y', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'CarrierUseTimeUnit', null, null, null, 'Timer Unit', '时间单位', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 20040901, 0, 'Y', to_date('2018-02-11 20:34:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'holdCode', '动作码', null, 93626318, null, 81, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Hold Code', '动作码', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 20040902, 0, 'Y', to_date('2018-02-11 20:34:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'holdReason', '动作备注', null, 93626318, null, 82, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Hold Reason', '动作备注', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93628191, 0, 'Y', to_date('2018-02-27 14:02:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 14:59:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 3, 'isRepeat', '是否重复使用', null, 93626318, null, 90, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', null, null, null, null, null, null, null, null, null, null, 'Is Repeat', '是否重复使用', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 

DELETE ad_field WHERE TABLE_RRN = '361098747101421568';
DELETE ad_table WHERE OBJECT_RRN = '361098747101421568';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 361098747101421568, 0, 'Y', to_date('2023-07-25 10:37:30', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:35:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 7, 'MMStatusModelEventList', '状态模型事件列表', 'COM_SM_STATUS_MODEL_EVENT', 'N', 'StatusModelEvent', 'com.glory.common.state.model.StatusModelEvent', null, null, null, 'N', 1, null, 'StatusModelEvent', '状态模型事件列表', null, 1); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 361098747101421569, 0, 'Y', to_date('2023-07-25 10:37:30', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:35:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 18, 'eventRrn', 'event ID', 'EVENTID', 361098747101421568, null, 30, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 363362909159866368, null, null, null, null, 'Event ID', '事件号', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 361098747101421570, 0, 'Y', to_date('2023-07-25 10:37:30', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:35:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 16, 'eventDescription', 'Description', 'EVENTDESCRIPTION', 361098747101421568, null, 40, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 361098747101421571, 0, 'Y', to_date('2023-07-25 10:37:30', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:35:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 9, 'eventType', 'eventType', 'EVENTTYPE', 361098747101421568, null, 50, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Event Type', '事件类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 361098747101421572, 0, 'Y', to_date('2023-07-25 10:37:30', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:35:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 8, 'userGroupId', 'UserGroup', 'USERGROUPID', 361098747101421568, null, 60, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'User Group', '用户组', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 361098747101421573, 0, 'Y', to_date('2023-07-25 10:37:30', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:35:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 5, 'limitCount', '限制次数', 'LIMITCOUNT', 361098747101421568, null, 70, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Limit Count', '限制次数', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 


DELETE AD_REFTABLE WHERE OBJECT_RRN = '363362909159866368';
insert into AD_REFTABLE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, KEY_FIELD, TEXT_FIELD, WHERE_CLAUSE, ORDER_BY_CLAUSE, IS_DISTIC, IS_QUERY_BY_FIELD)
values ('363362909159866368', '0', 'Y', to_date('31-07-2023 16:34:28', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('31-07-2023 16:34:46', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'MMMLotEvent', '物料批事件列表-eventId', '50505', 'objectRrn', 'eventId', null, null, null, 'N');

DELETE ad_field WHERE TABLE_RRN = '5030';
DELETE ad_table WHERE OBJECT_RRN = '5030';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 5030, 0, 'Y', to_date('2010-07-02 10:20:55', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2023-07-31 16:41:53', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'StatusModelEventList', '状态模型事件列表', 'COM_SM_STATUS_MODEL_EVENT', 'N', 'StatusModelEvent', 'com.glory.common.state.model.StatusModelEvent', null, null, null, 'N', 1, null, 'StatusModelEvent', '状态模型事件列表', null, 1); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 123981, 0, 'Y', to_date('2010-07-08 09:36:03', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2023-07-31 16:41:53', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 17, 'eventRrn', 'event ID', 'EVENTID', 5030, null, 30, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 353911516338552832, null, null, null, null, 'Event ID', '事件号', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 124025, 0, 'Y', to_date('2010-07-08 11:06:06', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2023-07-31 16:41:53', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'eventDescription', 'Description', 'EVENTDESCRIPTION', 5030, null, 40, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 124282, 0, 'Y', to_date('2010-07-12 10:51:56', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2023-07-31 16:41:53', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 8, 'eventType', 'eventType', 'EVENTTYPE', 5030, null, 50, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Event Type', '事件类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 124401, 0, 'Y', to_date('2010-07-13 15:53:19', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2023-07-31 16:41:53', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 7, 'userGroupId', 'UserGroup', 'USERGROUPID', 5030, null, 60, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'User Group', '用户组', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 4211242, 0, 'Y', to_date('2020-09-23 11:33:35', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-31 16:41:53', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 4, 'limitCount', '限制次数', 'LIMITCOUNT', 5030, null, 70, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Limit Count', '限制次数', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 



DELETE AD_IMPEXP_FIELD_MAP WHERE SUB_RRN IN ('361098747168530432','326299608375476224') AND PARENT_RRN IS NULL;
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('363364852875837440', '0', 'Y', to_date('31-07-2023 16:42:11', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('31-07-2023 16:42:11', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326299608375476224', 'eventDescription', '40', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('363364852884226048', '0', 'Y', to_date('31-07-2023 16:42:11', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('31-07-2023 16:42:11', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326299608375476224', 'eventType', '50', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('363364852921974784', '0', 'Y', to_date('31-07-2023 16:42:11', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('31-07-2023 16:42:11', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326299608375476224', 'userGroupId', '60', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('363364852938752000', '0', 'Y', to_date('31-07-2023 16:42:11', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('31-07-2023 16:42:11', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326299608375476224', 'limitCount', '70', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('363364852938752001', '0', 'Y', to_date('31-07-2023 16:42:11', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('31-07-2023 16:42:11', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326299608375476224', 'eventRrn', '30', null, 'EVENTID');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('363363193357516800', '0', 'Y', to_date('31-07-2023 16:35:36', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('31-07-2023 16:35:36', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '361098747168530432', 'eventRrn', '30', null, 'EVENTID');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('363363193378488320', '0', 'Y', to_date('31-07-2023 16:35:36', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('31-07-2023 16:35:36', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '361098747168530432', 'eventDescription', '40', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('363363193399459840', '0', 'Y', to_date('31-07-2023 16:35:36', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('31-07-2023 16:35:36', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '361098747168530432', 'eventType', '50', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('363363193420431360', '0', 'Y', to_date('31-07-2023 16:35:36', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('31-07-2023 16:35:36', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '361098747168530432', 'userGroupId', '60', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('363363193441402880', '0', 'Y', to_date('31-07-2023 16:35:36', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('31-07-2023 16:35:36', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '361098747168530432', 'limitCount', '70', null, null);

