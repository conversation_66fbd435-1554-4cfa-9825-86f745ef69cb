delete ad_field where TABLE_RRN = 131274421;
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DE<PERSON><PERSON><PERSON><PERSON><PERSON>, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('131274427', '0', 'Y', to_date('19-11-2018 18:19:01', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-03-2024 16:20:05', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'name', '名称', null, '131274421', null, '10', null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, '1', null, null, null, null, null, null, null, 'Name', '名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('131274429', '0', 'Y', to_date('19-11-2018 18:19:46', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-03-2024 16:20:05', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'description', '描述', null, '131274421', null, '20', null, 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

delete ad_field where TABLE_RRN = 1213;
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('103161', '0', 'Y', to_date('01-07-2014 13:45:16', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-03-2024 16:25:12', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '13', 'description', '描述', null, '1213', null, '30', '20', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('103183', '0', 'Y', to_date('01-07-2014 13:55:41', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-03-2024 16:25:12', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '17', 'shiftId', '班次号', null, '1213', null, '20', '20', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', null, '1', null, null, null, null, null, null, null, 'Shift ID', '班次号', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('103160', '0', 'Y', to_date('01-07-2014 13:44:32', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-03-2024 16:25:12', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '21', 'calendarId', '日历号', null, '1213', null, '10', '20', 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Calendar ID', '日历号', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('286144', '0', 'Y', to_date('24-12-2014 17:28:44', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-03-2024 16:25:12', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '11', 'seqNo', '序号', null, '1213', null, '40', '20', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Seq No', '序号', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

delete ad_field where TABLE_RRN = 2020;

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('186735', '0', 'Y', to_date('23-02-2011 16:58:30', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 15:09:28', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '22', 'locationType', 'Location Type', 'LOCATIONTYPE', '2020', '119604', '12', null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, '14480', 'LocationType', null, null, null, 'Location Type', 'LOCATION/BAY', 'N', 'N', 'N', null, null, null, null, 'N', null, '8', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('119712', '0', 'Y', to_date('15-04-2010 11:07:05', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 15:09:28', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '6', 'description', '描述', 'DESCRIPTION', '2020', '119604', '20', null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', null, null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, '8', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('293782679554867200', '0', 'Y', to_date('18-12-2020 09:32:36', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('23-03-2023 15:09:28', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '5', 'parentLocationRrn', '父值', 'PARENTLOCATION', '2020', null, '30', null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'Y', 'reftable', 'integer', null, null, null, null, '181037554655625216', null, null, 'objectRrn', null, 'Parent Location Rrn', '父值', 'N', 'N', 'N', null, null, null, null, 'N', null, '8', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('119701', '0', 'Y', to_date('15-04-2010 11:06:22', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 15:09:28', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '12', 'name', '名称', 'NAME', '2020', '119604', '10', '32', 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', null, '^[A-Za-z0-9-_]+$', '1', null, null, null, null, null, null, null, 'Name', '名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, '8', null, null);

delete ad_field where TABLE_RRN = 103;

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('10301', '0', 'Y', to_date('09-10-2008', 'dd-mm-yyyy'), null, to_date('29-03-2024 16:42:33', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '25', 'name', '区域名称', null, '103', null, '10', '32', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', '1', null, null, null, null, null, null, null, null, 'Site Name', '厂区名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('292415452141944832', '0', 'Y', to_date('14-12-2020 14:59:44', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-03-2024 16:42:33', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '12', 'description', '区域描述', null, '103', null, '20', '64', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

delete ad_field where TABLE_RRN = 117512;

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('1175131', '0', 'Y', to_date('29-07-2014 17:31:57', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('01-04-2024 09:54:22', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '12', 'name', '名称', null, '117512', null, '10', null, 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', null, '1', null, null, null, null, null, null, null, 'Name', '名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('1175141', '0', 'Y', to_date('29-07-2014 17:32:35', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('01-04-2024 09:54:22', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '8', 'description', '描述', null, '117512', null, '20', null, 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('1175321', '0', 'Y', to_date('30-07-2014 10:08:48', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('01-04-2024 09:54:22', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '9', 'referenceType', '参考类型', null, '117512', null, '30', null, 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Reference Type', '参考类型', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

delete ad_field where TABLE_RRN = 6053 and NAME = 'name';

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('13201', '0', 'Y', to_date('09-10-2008', 'dd-mm-yyyy'), null, to_date('01-04-2024 09:56:07', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '21', 'name', '字段名', null, '6053', null, '10', '32', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', null, null, '1', null, null, null, null, null, null, null, 'Field Name', '字段名', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

delete ad_field where TABLE_RRN = 4866745 and NAME = 'name';

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('4867065', '0', 'Y', to_date('18-02-2014 09:37:36', 'dd-mm-yyyy hh24:mi:ss'), '1', to_date('01-04-2024 10:00:35', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '15', 'name', '供应商编号', null, '4866745', '4866885', '10', null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', null, '1', null, null, null, null, null, null, null, 'Name', '供应商编号', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);
