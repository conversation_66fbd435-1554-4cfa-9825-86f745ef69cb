delete ad_reflist where REFERENCE_NAME = 'WipFlagCategory';

delete ad_refname where NAME = 'WipFlagCategory';

insert into ad_refname (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, CATEGORY, NAME, DESCRIPTION, REFERENCE_TYPE, LIST_LEVEL)
values ('389075399231946752', '0', 'Y', to_date('10-10-2023 15:26:43', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('10-10-2023 15:26:43', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'SYS', 'WipFlagCategory', 'Flag类别', null, null);

insert into ad_reflist (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('389075869753163776', '0', 'Y', to_date('10-10-2023 15:28:36', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('10-10-2023 15:28:36', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WipFlagCategory', 'E', 'E', '10', '强化Flag', 'Y');

insert into ad_reflist (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('389075869753163777', '0', 'Y', to_date('10-10-2023 15:28:36', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('10-10-2023 15:28:36', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WipFlagCategory', 'D', 'D', '20', '去向Flag', 'Y');

insert into ad_reflist (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('389075869753163778', '0', 'Y', to_date('10-10-2023 15:28:36', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('10-10-2023 15:28:36', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WipFlagCategory', 'P', 'P', '30', '加工完成Flag', 'Y');
