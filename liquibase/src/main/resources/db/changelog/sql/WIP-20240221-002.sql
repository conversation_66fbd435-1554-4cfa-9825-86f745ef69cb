
DELETE AD_MESSAGE WHERE OBJECT_RRN IN ('202302210942001','202302210942002','202302210942003','202302210942004','202302210942005');
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202302210942001', '0', 'Y', 'wip.changewo_lot_success', 'Change Wo Lot Success!', '转投成功!', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202302210942002', '0', 'Y', 'wip.select_old_work_order', 'Select Old WorkOrder!', '请选择父工单!', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAG<PERSON>, MESSAGE_ZH, MESSAGE_RES)
values ('202302210942003', '0', 'Y', 'mm.mainmlot_is_not_found', 'Main MLot Is Not Found!', '物料批次未找到!', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202302210942004', '0', 'Y', 'mm.bom_material_not_found', 'Material Requisition is Null', '领料单为空!', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202302210942005', '0', 'Y', 'mm.bom_requisition_successed', 'Requisition Successed!', '领料成功!', null);

 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'ChangeWorkOrderManager' NAME, TO_TIMESTAMP('2024/02/20 14:34:05', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('436921831879213059', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
  MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'ChangeWorkOrderPartDialog' NAME, TO_TIMESTAMP('2024/02/20 18:18:51', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('437275335537852430', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
  MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WoMaterialRequestManager' NAME, TO_TIMESTAMP('2024/02/19 11:50:51', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('436843519462998019', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');

DELETE AD_EDITOR WHERE OBJECT_RRN IN ('52617','17051501');
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('52617', '0', 'Y', 'WorkOrderEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.changewo.ChangeWorkOrderManagerEditor', '126517', 'ChangeWorkOrderManager', null, 'production_order', null);

insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('17051501', '0', 'Y', 'MaterialRequestEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.wo.mr.WoMaterialRequestManagerEditor', '1751501', 'WoMaterialRequestManager', null, 'production_order', null);
 