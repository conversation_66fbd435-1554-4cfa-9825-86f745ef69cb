delete ad_authority t where t.name = 'DataTransferManager';
insert into ad_authority (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('356037559094853632', '0', 'Y', to_date('11-07-2023 11:26:08', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('11-07-2023 11:26:21', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'DataTransferManager', 'DataTransfer Management', 'M', null, null, '40', '20', 'DataTransfer', '产品流程导入', null, 'subapp_security', 'MES', null, null);
delete ad_authority t where t.NAME in ('Common.Datatransfer.PartStep','Common.DataTransfer');
insert into ad_authority (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021061010', '0', 'Y', to_date('12-06-2020', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:27:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Common.Datatransfer.PartStep', '产品流程查询', 'F', 'E', '2021061010', '356037559094853632', '10', 'PrdData Query', '产品流程查询', null, 'part-setup', 'MES', 'com.glory.common.datatransfer', null);
insert into ad_authority (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('4422', '0', 'Y', to_date('09-10-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:28:28', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Common.DataTransfer', '产品流程导入', 'F', 'E', '4422', '356037559094853632', '20', 'PrdData Import', '产品流程导入', null, 'transation-history', 'MES', 'com.glory.common.datatransfer', null);
delete ad_authority t where t.name = 'Wip.MyGroupLot'; 
insert into ad_authority (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20211215001', '0', 'Y', to_date('06-02-2009', 'dd-mm-yyyy'), 'admin', to_date('18-07-2023 14:49:17', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '5', 'Wip.MyGroupLot', '个人批次', 'F', 'E', '20211215001', '601', '120', 'My Group Lot', '个人批次', null, 'mylot-query', 'MES', 'com.glory.mes.wip', null);

DELETE AD_BUTTON WHERE FORM_RRN IN (SELECT T.OBJECT_RRN FROM AD_FORM t where T.IS_DESIGNER IS NULL AND T.NAME  in ('LotMyGroupDialog','MyGroupLotForm', 'LotMyGroupForm', 'MyGroupLotListDialogForm'));
DELETE AD_FORM_ATTRIBUTE WHERE FORM_RRN IN (SELECT T.OBJECT_RRN FROM AD_FORM t where T.IS_DESIGNER IS NULL AND T.NAME in ('LotMyGroupDialog','MyGroupLotForm', 'LotMyGroupForm', 'MyGroupLotListDialogForm'));
DELETE AD_FORM t where T.IS_DESIGNER IS NULL AND T.NAME in ('LotMyGroupDialog','MyGroupLotForm', 'LotMyGroupForm', 'MyGroupLotListDialogForm');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'MyGroupLotDialog' NAME, TO_TIMESTAMP('2023/7/18 14:34:30', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('358622475634569216', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '个人批次管理', 'Active', b.release_timestamp, 'Y', 'Y');
 
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPLotQueryManager' NAME, TO_TIMESTAMP('2022/11/23 10:25:00', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('270486925904297984', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '在制品查询管理', 'Active', b.release_timestamp, 'Y', 'Y');
 
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'LotMyGroupManager' NAME, TO_TIMESTAMP('2023/07/18 14:57:50', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('270486925904297985', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '个人批次管理功能', 'Active', b.release_timestamp, 'Y', 'Y');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'MyGroupLotListDialog' NAME, TO_TIMESTAMP('2023/07/18 15:02:24', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('270486925904297986', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '个人批次选择列表弹窗', 'Active', b.release_timestamp, 'Y', 'Y'); 
 