DELETE AD_AUTHORITY WHERE OBJECT_RRN IN ('419541528302657536','420538864776638465', '420539525463404545', '420539688307257345');
DELETE AD_EDITOR WHERE OBJECT_RRN IN ('419541528298463232');

insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('419541528298463232', '0', 'Y', 'TrackConfigEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.track.config.TrackConfigEditor', null, 'WIPTrackConfigManager', null, 'basetable', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESC<PERSON>P<PERSON>ON, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('419541528302657536', '0', 'Y', to_date('02-01-2024 17:08:15', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('05-01-2024 16:59:28', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'WIP.TrackConfig', '进出站控制面板', 'F', 'E', '419541528298463232', '510', '200', 'Track Config', '进出站控制面板', null, 'basetable', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('420538864776638465', '0', 'Y', to_date('05-01-2024 11:11:18', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('05-01-2024 11:11:18', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WIP.TrackConfig.save', '保存', 'B', null, null, '419541528302657536', '10', 'Save', '保存', null, 'save', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('420539525463404545', '0', 'Y', to_date('05-01-2024 11:13:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('05-01-2024 11:13:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WIP.TrackConfig.clear', '清除重置', 'B', null, null, '419541528302657536', '20', 'Reset', '重置', null, 'clear', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('420539688307257345', '0', 'Y', to_date('05-01-2024 11:14:34', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('05-01-2024 11:14:34', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WIP.TrackConfig.saveCategoryCdi', '保存', 'B', null, null, '419541528302657536', '30', 'Save', '保存', null, 'save', 'MES', null, null);

DELETE AD_SYS_PARAMETER_VALUE WHERE OBJECT_RRN IN ('1254341', '1254342');
DELETE AD_SYS_PARAMETER WHERE OBJECT_RRN IN ('42341231', '42341232');

insert into AD_SYS_PARAMETER (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, DEFAULT_VALUE, IS_STATIC, IS_GLOBAL, IS_MODIFIABLE)
values ('42341231', '0', 'Y', to_date('05-01-2024', 'dd-mm-yyyy'), 'admin', to_date('05-01-2024', 'dd-mm-yyyy'), 'admin', '0', 'mes_not_use_wfparameter', '不使用流程参数(提升进出站性能)', 'N', 'N', 'Y', null);

insert into AD_SYS_PARAMETER (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, DEFAULT_VALUE, IS_STATIC, IS_GLOBAL, IS_MODIFIABLE)
values ('42341232', '0', 'Y', to_date('05-01-2024', 'dd-mm-yyyy'), 'admin', to_date('05-01-2024', 'dd-mm-yyyy'), 'admin', '0', 'mes_lotid_case_sensitive', '批号区分大小写', 'Y', 'N', 'Y', null);

insert into AD_SYS_PARAMETER_VALUE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, VALUE, DEFAULT_VALUE, IS_STATIC, IS_SYSTEM, IS_GLOBAL, IS_MODIFIABLE, COMMENTS)
values ('1254341', '0', 'Y', to_date('05-01-2024', 'dd-mm-yyyy'), 'admin', to_date('05-01-2024', 'dd-mm-yyyy'), 'admin', '1', 'mes_not_use_wfparameter', '不使用流程参数(提升进出站性能)', 'Y', null, 'N', 'Y', 'Y', 'Y', null);

insert into AD_SYS_PARAMETER_VALUE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, VALUE, DEFAULT_VALUE, IS_STATIC, IS_SYSTEM, IS_GLOBAL, IS_MODIFIABLE, COMMENTS)
values ('1254342', '0', 'Y', to_date('05-01-2024', 'dd-mm-yyyy'), 'admin', to_date('05-01-2024', 'dd-mm-yyyy'), 'admin', '1', 'mes_lotid_case_sensitive', '批号区分大小写', 'Y', null, 'N', 'Y', 'Y', 'Y', null);


delete ad_message where KEY_ID IN ('wip.please_select_action_flow_position'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 1874331, 0, 'Y', 'wip.please_select_action_flow_position', 'Please select a action position or flow position!', '请选择一个动作位置或流程位置!', null); 

delete ad_message where KEY_ID IN ('wip.please_select_action_position'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 1874332, 0, 'Y', 'wip.please_select_action_position', 'Please select a action position!', '请选择一个动作位置!', null); 

delete ad_message where KEY_ID IN ('wip.not_allow_disable');
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 1874333, 0, 'Y', 'wip.not_allow_disable', 'Not allow disable!', '不允许禁用!', null); 

delete ad_message where KEY_ID IN ('wip.confirm_reset');
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 1874334, 0, 'Y', 'wip.confirm_reset', 'Are you sure to reset?', '你确认要重置吗？', null); 


 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPTrackConfigManager' NAME, TO_TIMESTAMP('2024/01/05 16:55:34', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('3945456782174351', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 