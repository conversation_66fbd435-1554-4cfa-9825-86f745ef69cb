delete ad_message where KEY_ID = 'wip.improve_the_details_of_the_multi_step_timer';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('443366621467234304', '0', 'Y', 'wip.improve_the_details_of_the_multi_step_timer', 'Improve the details of the multi step timer', '完善多工步子定时器详情', null);

delete ad_field where TABLE_RRN = ********;
delete ad_table where OBJECT_RRN = ********;
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE, IS_DESIGNER)
values ('********', '0', 'Y', to_date('30-10-2017 14:34:51', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 16:59:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '11', 'MMReceiveWaferLot', '晶圆批接收', 'MM_LOT', 'N', 'MLot', 'com.glory.mes.mm.lot.model.MLot', null, null, null, 'N', '2', '1', 'Receive Wafer Lot', '晶圆来料接收', null, '1', null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594101', '0', 'Y', to_date('30-10-2017 14:37:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 16:59:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '14', 'mLotId', '晶圆来料厂内批号', null, '********', '258185610319568896', '10', null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Wafer Material Lot ID', '晶圆来料厂内批号', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93631483', '0', 'Y', to_date('27-07-2018 20:06:41', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 16:59:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '8', 'partnerLotId', '客户来料批号', null, '********', '258185610319568896', '15', null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Vendor Lot ID', '客户来料批号', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594102', '0', 'Y', to_date('30-10-2017 14:39:24', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 16:59:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '12', 'materialName', '晶圆料号', null, '********', '258185610319568896', '20', null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', 'string', null, null, null, null, '93628312', null, null, null, null, 'Material Name', '晶圆料号', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594103', '0', 'Y', to_date('30-10-2017 14:41:16', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 16:59:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '9', 'partnerCode', '供应商代码', null, '********', '258185610319568896', '30', null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, '2161', null, null, null, null, 'Vendor Code', '供应商代码', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594104', '0', 'Y', to_date('30-10-2017 14:44:32', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 16:59:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '10', 'transMainQty', '晶圆片数', null, '********', '258185610319568896', '40', null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Wafer Qty', '晶圆片数', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594109', '0', 'Y', to_date('30-10-2017 15:11:20', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 16:59:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '14', 'transSubQty', '晶粒数量', null, '********', '258185610319568896', '50', null, 'N', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Die Qty', '晶粒数量', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('********', '0', 'Y', to_date('31-10-2017 10:45:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 16:59:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '27', 'transWarehouseId', '晶圆仓库', null, '********', '258185610319568896', '60', null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, 'WBank', 'Warehouse', '晶圆仓库', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('109952', '0', 'Y', to_date('13-03-2019 16:21:49', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 16:59:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '7', 'durable', '载具号', null, '********', '258185610319568896', '65', null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Carrier ID', '载具号', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('79886867989852160', '0', 'Y', to_date('09-06-2021 10:41:53', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 16:59:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '10', 'subMatType', '子物料类型', null, '********', '258185610319568896', '67', null, 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, 'WAFER', 'Sub Material Type', '子物料类型', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594106', '0', 'Y', to_date('30-10-2017 14:46:53', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 16:59:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '8', 'lotComment', '备注', null, '********', '258185610319568896', '70', null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'textarea', 'string', null, null, null, null, null, null, null, null, null, 'Comment', '备注', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

delete ad_field where TABLE_RRN = 93594094;
delete ad_table where OBJECT_RRN = 93594094;
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE, IS_DESIGNER)
values ('93594094', '0', 'Y', to_date('30-10-2017 13:58:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '20', 'MMMComponentUnit', '晶圆原料表', 'MM_COMPONENTUNIT', 'N', 'MComponentUnit', 'com.glory.mes.mm.lot.model.MComponentUnit', null, null, 'state = ''IN''', 'N', '1', '2', 'Wafer List', '晶圆原料表', null, '1', null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93967985', '0', 'Y', to_date('28-11-2018 11:05:10', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '18', 'position', '位置', 'POSITION', '93594094', null, '5', null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Position', '位置', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594095', '0', 'Y', to_date('30-10-2017 13:59:37', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '27', 'mComponentId', '晶圆片号', 'COMPONETID', '93594094', null, '10', null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, 'IQCBadCode', null, null, 'Wafer ID', '晶圆片号', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594096', '0', 'Y', to_date('30-10-2017 14:00:51', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '19', 'materialName', '晶圆料号', 'MATERIALNAME', '93594094', null, '20', null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', 'string', null, null, null, null, '21444', null, null, null, null, 'Wafer Material Name', '晶圆料号', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594097', '0', 'Y', to_date('30-10-2017 14:01:44', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '22', 'subQty', 'Die数量', null, '93594094', null, '30', null, 'N', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Die Qty', 'Die数量', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('305036557319348226', '0', 'Y', to_date('20-02-2023 17:46:22', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '17', 'partnerLotId', '客户名称', 'CUSTOMERLOTID', '93594094', null, '40', null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'PartnerLotId', 'PartnerLotId', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('305036557319348224', '0', 'Y', to_date('20-02-2023 17:46:22', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '17', 'durable', '载具号', 'CARRIERID', '93594094', null, '50', null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, '29828955', null, null, null, null, 'Durable', 'Durable', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('305036557319348225', '0', 'Y', to_date('20-02-2023 17:46:22', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '17', 'partnerCode', '客户码', 'CUSTOMERCODE', '93594094', null, '60', null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, '2161', null, null, null, null, 'PartnerCode', 'PartnerCode', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('308254923437891584', '0', 'Y', to_date('01-03-2023 14:55:00', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '13', 'lotComment', '备注', 'LOTCOMMENT', '93594094', null, '70', null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'LotComment', 'LotComment', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);

delete ad_field where TABLE_RRN = 93594094;
delete ad_table where OBJECT_RRN = 93594094;
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE, IS_DESIGNER)
values ('93594094', '0', 'Y', to_date('30-10-2017 13:58:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '20', 'MMMComponentUnit', '晶圆原料表', 'MM_COMPONENTUNIT', 'N', 'MComponentUnit', 'com.glory.mes.mm.lot.model.MComponentUnit', null, null, 'state = ''IN''', 'N', '1', '2', 'Wafer List', '晶圆原料表', null, '1', null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93967985', '0', 'Y', to_date('28-11-2018 11:05:10', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '18', 'position', '位置', 'POSITION', '93594094', null, '5', null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Position', '位置', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594095', '0', 'Y', to_date('30-10-2017 13:59:37', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '27', 'mComponentId', '晶圆片号', 'COMPONETID', '93594094', null, '10', null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, 'IQCBadCode', null, null, 'Wafer ID', '晶圆片号', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594096', '0', 'Y', to_date('30-10-2017 14:00:51', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '19', 'materialName', '晶圆料号', 'MATERIALNAME', '93594094', null, '20', null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', 'string', null, null, null, null, '21444', null, null, null, null, 'Wafer Material Name', '晶圆料号', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('93594097', '0', 'Y', to_date('30-10-2017 14:01:44', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '22', 'subQty', 'Die数量', null, '93594094', null, '30', null, 'N', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Die Qty', 'Die数量', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('305036557319348226', '0', 'Y', to_date('20-02-2023 17:46:22', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '17', 'partnerLotId', '客户名称', 'CUSTOMERLOTID', '93594094', null, '40', null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'PartnerLotId', 'PartnerLotId', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('305036557319348224', '0', 'Y', to_date('20-02-2023 17:46:22', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '17', 'durable', '载具号', 'CARRIERID', '93594094', null, '50', null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, '29828955', null, null, null, null, 'Durable', 'Durable', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('305036557319348225', '0', 'Y', to_date('20-02-2023 17:46:22', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '17', 'partnerCode', '客户码', 'CUSTOMERCODE', '93594094', null, '60', null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, '2161', null, null, null, null, 'PartnerCode', 'PartnerCode', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('308254923437891584', '0', 'Y', to_date('01-03-2023 14:55:00', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 17:03:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '13', 'lotComment', '备注', 'LOTCOMMENT', '93594094', null, '70', null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'LotComment', 'LotComment', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);

delete ad_field where TABLE_RRN = 127217062315663360;
delete ad_table where OBJECT_RRN = 127217062315663360;

insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE, IS_DESIGNER)
values ('127217062315663360', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '21', 'WIPADVMultiStepChildTimer', 'Muti Step Timer', 'WIP_FUTURE_ACTION', 'N', 'FutureAction', 'com.glory.mes.wip.future.FutureAction', null, null, null, 'N', '2', null, 'Muti Step Child Timer', '多工步子定时器', null, '1', null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062349217792', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '22', 'name', 'name', null, '127217062315663360', '127217062319857664', '10', null, 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Name', '名称', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412096', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '21', 'description', 'description', null, '127217062315663360', '127217062319857664', '20', null, 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412097', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'partName', '产品名称', null, '127217062315663360', '127217062319857664', '22', null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Product Name', '产品名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412098', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '22', 'timerType', '定时器类型', null, '127217062315663360', '127217062319857664', '40', null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, '14480', 'TimerType', null, null, null, 'TimerType', '定时器类型', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412099', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '21', 'earlyPeriod', '提前警告时间(分)', null, '127217062315663360', '127217062319857664', '45', null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Early Period', '提前警告时间(分)', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412100', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '22', 'timerDuration', 'timerDuration', null, '127217062315663360', '127217062319857664', '50', null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'Y', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Timer Duration', '时间(分)', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412101', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '24', 'holdCode', 'Hold Code', null, '127217062315663360', '127217062319857664', '70', null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'Y', 'N', 'N', 'ownerefcombo', null, null, null, null, null, '129978646593679360', null, null, 'HoldCode', null, 'Hold Code', '暂停码', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412102', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '21', 'holdReason', 'HoldReason', null, '127217062315663360', '127217062319857664', '80', null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Hold Reason', '暂停原因', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412103', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '23', 'holdOwner', 'HoldOwner', null, '127217062315663360', '127217062319857664', '90', null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'Y', 'N', 'N', 'searchmulti', 'string', null, null, null, null, '10134', null, null, null, 'holdCode.owner', 'Hold Owner', '暂停Owner', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412104', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '25', 'timerAction', '定时器动作', null, '127217062315663360', '127217062319857664', '95', null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, '14480', 'FutureTimerAction', null, null, null, 'Timer Action', '定时器动作', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412105', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'procedure', 'procedure', null, '127217062315663360', '127217062319857664', '100', null, 'N', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'reftablecombo', 'string', null, null, null, null, '165915', null, null, null, null, 'Procedure', '流程', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412106', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '11', 'stepName', '启动Timer工步', null, '127217062315663360', '127217062319857664', '110', null, 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Start Step Name', '启动Timer工步', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412107', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'stepPlacement', '启动Timer事件', null, '127217062315663360', '127217062319857664', '120', null, 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'Y', 'N', 'N', 'sysreflist', 'string', null, null, null, null, '14480', 'QTimePlacementType', null, null, null, 'stepPlacement', '启动Timer事件', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412108', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '10', 'endStepName', '结束Timer工步', null, '127217062315663360', '127217062319857664', '130', null, 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'End Step Name', '结束Timer工步', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412109', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '24', 'endStepPlacement', '结束Timer事件', null, '127217062315663360', '127217062319857664', '140', null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'sysreflist', 'string', null, null, null, null, '14480', 'QTimePlacementType', null, null, null, 'endStepPlacement', '结束Timer事件', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412110', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '18', 'actionStepName', '动作工步', null, '127217062315663360', '127217062319857664', '150', null, 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Action Step Name', '动作工步', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412111', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'isAll', '用于所有版本', null, '127217062315663360', '127217062319857664', '160', null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'boolean', null, null, null, null, null, null, null, null, null, null, 'All Version', '用于所有版本', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('127217062353412112', '0', 'Y', to_date('18-10-2021 01:15:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2024 18:26:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '23', 'isComponentLevel', 'Component级别管控', null, '127217062315663360', '127217062319857664', '170', null, 'Y', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'isComponentLevel', 'Component级别管控', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);
