delete ad_message where <PERSON><PERSON><PERSON>_ID IN ('ras-001001: ras.hold_code_is_repeat'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 87431, 0, 'Y', 'ras-001001: ras.hold_code_is_repeat', 'Holds with the same HoldCode exist, cannot duplicate Holds!', '存在相同的HoldCode的Hold,不能重复Hold!', null); 

delete ad_message where KEY_ID IN ('ras-001002: ras.release_pwd_error'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 87432, 0, 'Y', 'ras-001002: ras.release_pwd_error', 'Release password error!', '放行密码错误！', null); 


delete ad_message where KEY_ID IN ('ras-001003: ras.release_owner_error'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 87433, 0, 'Y', 'ras-001003: ras.release_owner_error', 'Current user dose not belong to hold owner!', '当前用户不属于暂停暂停人!', null); 


delete ad_message where KEY_ID IN ('ras-001004: ras.access_mode_is_illegal'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 87434, 0, 'Y', 'ras-001004: ras.access_mode_is_illegal', 'Access mode is illegal!', '访问模式非法!', null); 

delete ad_message where KEY_ID IN ('ras-001005: ras.capa_no_found'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 87435, 0, 'Y', 'ras-001005: ras.capa_no_found', 'Equipment capability not found!', '设备能力没找到!', null); 


delete ad_message where KEY_ID IN ('ras-001006: ras.eqp_port_not_found'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 87436, 0, 'Y', 'ras-001006: ras.eqp_port_not_found', 'Equipment port not found!', '设备Port端口没找到!', null); 


delete ad_message where KEY_ID IN ('ras-001007: ras.eqp_hold_not_found'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 87437, 0, 'Y', 'ras-001007: ras.eqp_hold_not_found', 'Equipment hold data not found!', '设备Hold数据没找到!', null); 

delete ad_message where KEY_ID IN ('ras-001008: ras.eqp_position_not_match'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 87438, 0, 'Y', 'ras-001008: ras.eqp_position_not_match', 'Equipment position and current equipment not match!', '设备Position与当前设备不匹配！', null); 

delete ad_field where OBJECT_RRN = 267013532887756800;
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 267013532887756800, 0, 'Y', to_date('2022-11-07 19:36:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-17 11:27:03', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 3, 'contextFieldValue5', '工步节点名称', null, 135078240864870400, null, 55, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Step State Name', '工步节点名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
