
delete from AD_AUTHORITY t where t.OBJECT_RRN in (392367670256832512, 389057725528563712, 389021556251049985);
delete from AD_EDITOR t where t.OBJECT_RRN in (389057725524369408, 389021556251049984);

insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('389021556251049984', '0', 'Y', 'WipFlagDefManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.flag.def.WipFlagDefManagerEditor', null, 'WipFlagDefManager', null, 'dcop', null);

insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAM<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>_<PERSON>, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('389057725524369408', '0', 'Y', 'FlagStepEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.flag.step.WIPFlagStepEditor', null, 'WIPFlagStepManager', null, 'step', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('392367670256832512', '0', 'Y', to_date('19-10-2023 17:29:02', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('19-10-2023 17:29:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'WipFlagManager', 'Wip Flag Manager', 'M', null, null, '40', '40', 'Flag Manager', 'Flag管理', null, 'subapp_security', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('389057725528563712', '0', 'Y', to_date('10-10-2023 14:16:30', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('19-10-2023 17:34:59', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '5', 'Wip.FlagStep', 'Flag工步设置', 'F', 'E', '389057725524369408', '392367670256832512', '410', 'Flag Step', 'Flag工步', null, 'step', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('389021556251049985', '0', 'Y', to_date('10-10-2023 11:52:46', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('19-10-2023 17:32:29', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '6', 'Wip.FlagDef', 'Flag定义', 'F', 'E', '389021556251049984', '392367670256832512', '400', 'Flag Define', 'Flag定义', null, 'dcop', 'MES', 'com.glory.mes.wip', null);


delete from AD_REFNAME t where t.OBJECT_RRN in (392373463404351488);
delete from AD_REFLIST t where t.OBJECT_RRN in (392373880175562752, 392373880179757056);

insert into AD_REFNAME (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, CATEGORY, NAME, DESCRIPTION, REFERENCE_TYPE, LIST_LEVEL)
values ('392373463404351488', '0', 'Y', to_date('19-10-2023 17:52:03', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('19-10-2023 17:52:03', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'SYS', 'WIPFlagValue', 'WIPFlag?', null, null);

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('392373880175562752', '0', 'Y', to_date('19-10-2023 17:53:42', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('19-10-2023 17:53:42', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WIPFlagValue', '0', '0', '1', '0', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('392373880179757056', '0', 'Y', to_date('19-10-2023 17:53:42', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('19-10-2023 17:53:42', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WIPFlagValue', '1', '1', '2', '1', 'Y');


MERGE INTO AD_FORM_RELEASE a
			USING (SELECT 'WIPFlagStepManager' NAME, TO_TIMESTAMP('2023/10/19 17:55:14', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
			ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
			WHEN MATCHED THEN 
 			UPDATE SET a.IS_UPDATE = 'Y'
			WHEN NOT MATCHED THEN 
 			INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 			VALUES ('366461883109012345', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');

