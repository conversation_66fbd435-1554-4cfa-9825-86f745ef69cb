delete ad_message where OBJECT_RRN in ('64480','64481','64482','64483','64484','64486','17092853','17092854',
'64487','64488','64489','64550','64660','64490','64491');
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64481', '0', 'Y', 'wip-2024: prd.process_is_not_exist', 'Process <%1$s> is not exist!', '工艺<%1$s>不存在', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64482', '0', 'Y', 'wip-2025: prd.capability_is_not_exist', 'Capability <%1$s> is not exist!', '设备能力<%1$s>不存在', null);

delete ad_message where OBJECT_RRN in ('20230309801','20230309802','20230309803');
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20230309801', '0', 'Y', 'wip-2026: prd.process_is_unfrozen', 'Process <%1$s> is unfrozen!', '工艺<%1$s>未冻结！', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20230309802', '0', 'Y', 'wip-2027: prd.procedure_is_unfrozen', 'Procedure <%1$s> is unfrozen!', '流程<%1$s>未冻结！', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20230309803', '0', 'Y', 'wip-2028: prd.step_is_unfrozen', 'Step <%1$s> is unfrozen!', '工步<%1$s>未冻结！', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64483', '0', 'Y', 'wip-2029: prd.part_is_not_active_or_inactive', 'Part <%1$s> is not active or inactinve!', '产品<%1$s>未激活或失效', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64484', '0', 'Y', 'wip-2030: prd.process_is_not_active_or_inactive', 'Process <%1$s> is not active or inactinve!', '工艺<%1$s>未激活或失效', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64486', '0', 'Y', 'wip-2031: prd.procedure_is_not_active_or_inactive', 'Procudere <%1$s> is not active or inactinve!', '流程<%1$s>未激活或失效', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64487', '0', 'Y', 'wip-2032: prd.step_is_not_active_or_inactive', 'Step <%1$s> is not active or inactinve!', '工步<%1$s>未激活或失效', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64488', '0', 'Y', 'wip-2033: prd.active_version_is_exist', '<%1$s> active version is exist!', '<%1$s>已存在激活版本', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64489', '0', 'Y', 'wip-2034: prd.edc_context_is_exist', 'Step <%1$s> edc context <%1$s> is exist!', '工步<%1$s>数据采集项已存在', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64550', '0', 'Y', 'wip-2035: prd.recipe_context_is_exist', 'Step <%1$s> recipe context is exist!', '工步<%1$s>已存在设备菜单', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64660', '0', 'Y', 'wip-2036: prd.reticle_context_is_exist', 'Step <%1$s> reticle context <%1$s> is exist!', '工步<%1$s>已存在光刻版', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64490', '0', 'Y', 'wip-2037: prd.procedure_flow_is_not_equal', 'Procedure flow <%1$s> is not equal!', '工艺流程<%1$s>不相等', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64491', '0', 'Y', 'wip-2038: prd.process_flow_is_not_equal', 'Process flow <%1$s> is not equal!', '流程工步<%1$s>不相等', null);

delete ad_message where OBJECT_RRN in ('64492','64493','64494','64495','64496','64497','64498','64661','64659');
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64492', '0', 'Y', 'wip-2039: prd.replace_current_active_version', 'Replace <%1$s> current active version <%2$s>', '替换<%1$s>当前激活版本<%2$s>', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64493', '0', 'Y', 'wip-2040: prd.current_version_is_not_equal', 'Current version field <%1$s> data is not equal!', '与当前版本栏位<%1$s>数据不相同', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64494', '0', 'Y', 'wip-2041: prd.current_version_is_changed', '<%1$s> current version is changed!', '<%1$s>当前版本数据已经改变', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64495', '0', 'Y', 'wip-2042: prd.current_version_is_frozen_or_inactive', 'Current version <%1$s> is forzen or inactinve!', '当前版本<%1$s>是冻结或者未激活状态', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64496', '0', 'Y', 'wip-2043: prd.step_parameter_is_different', 'Step <%1$s> parameter is different!', '工步<%1$s>参数值不一致', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64497', '0', 'Y', 'wip-2044: prd.part_parameter_is_different', 'Part <%1$s> parameter is different!', '产品<%1$s>参数值不一致', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64498', '0', 'Y', 'wip-2045: prd.edc_context_is_different', 'Edc context <%1$s> is different!', '数据采集项<%1$s>不一致', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64661', '0', 'Y', 'wip-2046: prd.reticle_context_is_different', 'Step <%1$s> import reticle <%2$s> is different with current reticle <%3$s>!', '工步<%1$s>导入光刻版<%2$s>与现有光刻版<%3$s>不同', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64659', '0', 'Y', 'wip-2047: prd.recipe_context_is_different', 'Step <%1$s> import recipe <%2$s> is different with current recipe <%3$s>!', '工步<%1$s>导入设备菜单<%2$s>与现有设备菜单<%3$s>不同', null);

delete ad_message where OBJECT_RRN in ('64499','64500','17092800','64501','64502','64503','55690');
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64499', '0', 'Y', 'wip-2048: prd.edc_set_is_not_exist', 'Edc context <%1$s> is not exist!', '数据采集项<%1$s>不存在', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64500', '0', 'Y', 'wip-2049: prd.flow_is_not_equal', 'Part flow <%1$s> is not equal!', '产品流程<%1$s>不一致', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('17092800', '0', 'Y', 'wip-2050: prd.procedure_is_more', 'Part is not exist procedure <%1$s>!', '产品不存在流程<%1$s>！', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64501', '0', 'Y', 'wip-2051: prd.data_is_not_equal', 'Field data is not equal!', '栏位内容不一致', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64502', '0', 'Y', 'wip-2052: prd.exist_two_active_version', '<%1$s> Exit two active version!', '<%1$s>存在两个激活的版本', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64503', '0', 'Y', 'wip-2053: prd.unsupport_different_version', 'Unsuppoert <%1$s> different version!', '不支持<%1$s>有两个不同版本', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('55690', '0', 'Y', 'wip-2054: prd.step_rework_flow_is_invalid', 'Step <%1$s> rework flow is invalid', '工步<%1$s>返工流程设置错误', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('64480', '0', 'Y', 'wip-2055: prd.part_is_not_exist', 'Part <%1$s> is not exist!', '产品<%1$s>不存在', null);
