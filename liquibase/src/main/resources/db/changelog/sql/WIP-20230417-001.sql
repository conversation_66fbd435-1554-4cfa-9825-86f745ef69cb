delete AD_MESSAGE where <PERSON>EY_<PERSON> in ('wip-005019: wip.lot_step_logic_recipe_data_exception','wip-005020: wip.lot_step_reticle_data_exception');

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAG<PERSON>, MESSAGE_ZH, MESSAGE_RES)
values ('20245111501', '0', 'Y', 'wip-005019: wip.lot_step_logic_recipe_data_exception', 'Lot step logic recipe data exception!', '批次工步LogicRecipe数据异常！', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024511502', '0', 'Y', 'wip-005020: wip.lot_step_reticle_data_exception', 'Lot step reticle data exception!', '批次工步Reticle数据异常！', null);
