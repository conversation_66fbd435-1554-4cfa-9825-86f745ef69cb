
DELETE AD_EDITOR WHERE OBJECT_RRN = '6017123411234';
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('6017123411234', '0', 'Y', 'MultiScheduleEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.multi.schedule.MultiScheduleLotEditor', '110368', 'WipNPWLotScheduleQueryForm', null, 'schedule', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN = '202402291108001';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402291108001', '0', 'Y', 'wip.add_packed_success', 'Add Packed Success', '添加包装成功', null);

 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WorkOrderManager' NAME, TO_TIMESTAMP('2024/02/29 14:35:46', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('403924131881447430', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');

DELETE AD_FIELD WHERE OBJECT_RRN = '118904';
insert into AD_FIELD (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('118904', '0', 'Y', to_date('05-08-2014 15:04:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-02-2024 16:06:55', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '22', 'materialTypeSrc', '物料类型', null, '118903', null, '30', null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', null, null, null, null, null, '11962', null, null, null, null, 'Material Type', '物料类型', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

