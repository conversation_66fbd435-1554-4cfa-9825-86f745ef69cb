delete from AD_EDITOR where OBJECT_RRN in (select editor_rrn from AD_AUTHORITY where name in ('Wip.LotSortingMerge'));
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('2017101801', '0', 'Y', 'CarrierLotMergeManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.carrier.glc.merge.CarrierLotMergeManagerEditor', null, 'CarrierLotMergeManager', null, 'carrier-merge', null);

delete from AD_AUTHORITY where name in ('Wip.LotSortingMerge', 'Wip.SortingSplit.merge');
insert into ad_authority (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2017101801', '0', 'Y', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', to_date('08-01-2024 19:18:00', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'Wip.LotSortingMerge', '载具合批', 'F', 'E', '2017101801', '603', '50', 'Lot Merge(Sorter)', '载具合批', null, 'carrier-merge', 'MES', 'com.glory.mes.wip', null);
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202112310036', '0', 'Y', to_date('08-05-2017', 'dd-mm-yyyy'), 'admin', to_date('08-01-2024 12:05:15', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.LotSortingMerge.merge', 'Wip.LotSortingMerge.merge', 'B', null, null, '2017101801', '10', 'Merge', '合批', null, 'assign', 'MES', null, null);


DELETE AD_FORM_RELEASE WHERE NAME IN ('CarrierLotMergeManager','WIPLotSelectDialog');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'CarrierLotMergeManager' NAME, TO_TIMESTAMP('2024/1/8 18:47:08', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('421748685110456320', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次合批功能', 'Active', b.release_timestamp, 'Y', 'Y');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPLotSelectDialog' NAME, TO_TIMESTAMP('2024/1/8 18:47:08', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('421741603636326400', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次列表选择弹窗', 'Active', b.release_timestamp, 'Y', 'Y');
	
	