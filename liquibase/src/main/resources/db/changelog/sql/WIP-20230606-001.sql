DELETE FROM AD_AUTHORITY where NAME in ('Edc.OffLineGlc','Edc.OffLine','Edc.GeneralEDC','Edc.GeneralEDCGlc');

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2305091618002', '0', 'Y', to_date('07-06-2011', 'dd-mm-yyyy'), 'admin', to_date('07-06-2011', 'dd-mm-yyyy'), 'admin', '1', 'Edc.OffLineGlc', '数据收集(对批次)', 'F', 'E', '2005091820001', '651', '70', 'EDC Offline For Lot', '数据收集(对批次)', null, 'edc_offlinelot', 'MES', 'com.glory.edc', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('700004', '0', 'N', to_date('07-06-2011', 'dd-mm-yyyy'), 'admin', to_date('07-06-2011', 'dd-mm-yyyy'), 'admin', '1', 'Edc.OffLine', '数据收集(对批次)', 'F', 'E', '400030', '651', '70', 'EDC Offline For Lot', '数据收集(对批次)', null, 'edc_offlinelot', 'MES', 'com.glory.edc', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6507', '0', 'N', to_date('08-06-2010', 'dd-mm-yyyy'), 'admin', to_date('12-05-2023 15:57:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Edc.GeneralEDC', '数据收集(对设备)', 'F', 'E', '609', '651', '60', 'EDC Offline For Equipment', '数据收集(对设备)', null, 'edc_offlineeqp', 'MES', 'com.glory.edc', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2305091618001', '0', 'Y', to_date('08-06-2010', 'dd-mm-yyyy'), 'admin', to_date('08-06-2010', 'dd-mm-yyyy'), 'admin', '1', 'Edc.GeneralEDCGlc', '数据收集(对设备)', 'F', 'E', '2305091715001', '651', '60', 'EDC Offline For Equipment', '数据收集(对设备)', null, 'edc_offlineeqp', 'MES', 'com.glory.edc', null);