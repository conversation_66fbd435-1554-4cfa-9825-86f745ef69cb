
DELETE ad_field where TABLE_RRN = '138578147623866368';
DELETE ad_tab where TABLE_RRN = '138578147623866368';
DELETE ad_table where OBJECT_RRN = '138578147623866368';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 138578147623866368, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 44, 'PRDProjectConnectConstraintGLC', '工程连接限制GLC', 'PRD_LINK', 'N', 'PrdLink', 'com.glory.mes.prd.model.PrdLink', null, null, null, 'N', 2, null, 'PRDProjectConnectConstraint', '工程连接限制', null, 1); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 138578147628060672, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 44, 'cennecttab', '工程连接限制内容', 138578147623866368, 1, 2, 'cennecttab', '工程连接限制内容', null, 'Form', null, 0); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147628060673, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 47, 'name', '名称', null, 138578147623866368, 138578147628060672, 10, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', null, null, null, null, null, null, null, null, null, null, 'Name', '名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147632254976, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 44, 'description', '描述', null, 138578147623866368, 138578147628060672, 20, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', null, null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147632254977, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 39, 'linkSource', '所属来源', null, 138578147623866368, 138578147628060672, 21, null, 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, 'MANUAL', 'linkSource', '所属来源', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147632254978, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 39, 'linkSourceName', '所属来源名称', null, 138578147623866368, 138578147628060672, 22, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'LinkSourceName', '所属来源名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147632254979, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 45, 'stepName', '前工歩名称', null, 138578147623866368, 138578147628060672, 30, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', null, null, null, null, null, 10512, null, null, null, null, 'Step Name', '前工歩名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147640643584, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 50, 'equipmentList', '设备', null, 138578147623866368, 138578147628060672, 35, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', null, null, null, null, null, 28626, null, null, null, null, 'Front Equipment', '前设备', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147632254980, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 46, 'stepVersion', '工歩版本', null, 138578147623866368, 138578147628060672, 40, null, 'N', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'reftext', null, null, null, null, null, null, null, null, 'stepName.version', null, 'Step Version', '工歩版本', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147632254981, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 47, 'linkStepName', '连接工步名称', null, 138578147623866368, 138578147628060672, 60, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', null, null, null, null, null, 10512, null, null, null, null, 'Link Step Name', '连接工步名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 311525064451690496, 0, 'Y', to_date('2023-03-10 15:29:23', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 9, 'linkValue', 'Link设备', null, 138578147623866368, 138578147628060672, 65, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 28626, null, null, null, null, 'Link Equipment', '连接设备', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147636449280, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 46, 'linkStepVersion', '连接工步版本', null, 138578147623866368, 138578147628060672, 70, null, 'N', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'reftext', null, null, null, null, null, null, null, null, 'linkStepName.version', null, 'Link Step Version', '连接工步版本', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147636449281, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 47, 'linkTargetType', '连接类型', null, 138578147623866368, 138578147628060672, 80, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'sysreflist', null, null, null, null, null, 14480, 'ProjectConstraintTargetType', null, null, null, 'Link Target Type', '连接类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147636449282, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 47, 'linkCheckFlag', '检查标记', null, 138578147623866368, 138578147628060672, 90, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'sysreflist', null, null, null, null, null, 14480, 'ProjectConstraintCheckType', null, null, null, 'Link Check Flag', '检查标记', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 138578147636449283, 0, 'Y', to_date('2021-11-18 09:40:05', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-06-02 15:05:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 48, 'linkValueType', '判断类型', null, 138578147623866368, 138578147628060672, 100, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'sysreflist', null, null, null, null, null, 14480, 'ProjectConstraintValueType', null, null, null, 'Link Value Type', '判断类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 

