delete ad_editor t where t.object_rrn = (select editor_rrn from ad_authority where name = 'WipAdv.QTime');
insert into ad_editor (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('4000824234', '0', 'Y', 'FlowTimerEditor', null, 'bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.future.flowtimer.FlowTimerEditor', null, 'FlowTimerManager', null, 'timer_started', null);

delete ad_field where table_rrn in (select object_rrn from ad_table where name = 'WIPADVTimerImpExp');
delete ad_table where name = 'WIPADVTimerImpExp';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DE<PERSON><PERSON><PERSON><PERSON><PERSON>, TABLE_NAME, IS_VIEW, M<PERSON><PERSON>_NAME, M<PERSON><PERSON>_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE, IS_DESIGNER)
values ('173370796464463872', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '32', 'WIPADVTimerImpExp', '定时器导入导出', 'WIP_FUTURE_ACTION', 'N', 'FutureAction', 'com.glory.mes.wip.future.FutureAction', null, null, null, 'N', '2', '3', 'Timer Query', '定时器查询', null, '3', null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658200', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '10', 'isComponentLevel', 'Componet级别管控', 'IS COMPONENT LEVEL', '173370796464463872', null, '150', null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'Is Component Level', 'Componet级别管控', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658198', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '16', 'endPath', '结束路径', null, '173370796464463872', null, '130', '42', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'End Path', '结束路径', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658185', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'procedureName', '开始模块名称', 'PROCEDURE NAME', '173370796464463872', null, '58', '24', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, '10513', null, null, null, null, 'Start Procedure Name', '开始模块名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658186', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'procedureVersion', '开始模块版本', 'PROCEDURE VERSION', '173370796464463872', null, '60', '16', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'integer', null, null, null, null, '10521', null, null, null, null, 'Start Procedure Version', '开始模块版本', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658187', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'stepName', '开始工步', 'STEP NAME', '173370796464463872', null, '70', '24', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Start Step', '开始工步', 'Y', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658188', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '12', 'stepPlacement', '开始Timer事件', 'START QTIME ACTION', '173370796464463872', null, '72', null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, '14480', 'QTimePlacementType', null, null, null, 'Start Timer Action', '开始Timer事件', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658189', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'endProcedureName', '结束模块名称', 'END PROCEDURE NAME', '173370796464463872', null, '75', '24', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, '10513', null, null, null, null, 'End Procedure Name', '结束模块名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796464463873', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'action', '动作', 'QTIME TYPE', '173370796464463872', null, '10', '24', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, '1', null, null, null, null, null, null, null, 'Action', '动作', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658176', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'timerAction', '定时器动作', 'QTIME ACTION', '173370796464463872', null, '15', null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, '14480', 'FutureTimerAction', null, null, null, 'QTime Action', '定时器动作', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658177', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '12', 'timerType', '定时器类型', 'TIMER TYPE', '173370796464463872', null, '20', null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, '14480', 'TimerType', null, null, null, 'QTime Type', '定时器类型', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658178', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '32', 'partName', '产品名称', 'PRODUCT NAME', '173370796464463872', null, '22', null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, '6234727', null, null, null, null, 'Product Name', '产品名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658179', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'name', '名称', 'NAME', '173370796464463872', null, '30', '16', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, '1', null, null, null, null, null, null, null, 'Name', '名称', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658180', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'description', '描述', 'DESCRIPTION', '173370796464463872', null, '40', '32', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658181', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '28', 'timerDuration', '时间', 'QTIME DURATION', '173370796464463872', null, '42', '16', 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', null, null, null, null, null, null, null, null, null, null, 'Time(Minute)', '时间(分)', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658182', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'earlyPeriod', '提前警告时间(分)', 'EARLY PERIOD', '173370796464463872', null, '45', null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Early Period', '提前警告时间(分)', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658183', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '27', 'processName', '工艺名称', 'PROCESS NAME', '173370796464463872', null, '55', '24', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, '10509', null, null, null, null, 'Process Name', '工艺名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658184', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'processVersion', '工艺版本', 'PROCESS VERSION', '173370796464463872', null, '56', '16', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'integer', null, null, null, null, '10511', null, null, null, null, 'Process Version', '工艺版本', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658190', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'endProcedureVersion', '结束模块版本', 'END PROCEDURE VERSION', '173370796464463872', null, '76', '16', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'integer', null, null, null, null, '10511', null, null, null, null, 'End Procedure Version', '结束模块版本', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658191', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'endStepName', '结束工步', 'END STEP NAME', '173370796464463872', null, '80', '24', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'End Step', '结束工步', 'Y', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658192', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '12', 'endStepPlacement', '结束Timer事件', 'END QTIME ACTION', '173370796464463872', null, '85', null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, '14480', 'QTimePlacementType', null, null, null, 'End Timer Action', '结束Timer事件', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658193', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '25', 'userName', '用户名', null, '173370796464463872', null, '90', '16', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'User', '用户', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658194', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'holdCode', '暂停码', 'HOLD CODE', '173370796464463872', null, '100', '16', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Hold Code', '暂停码', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658195', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '26', 'holdReason', '暂停原因', 'HOLD REASON', '173370796464463872', null, '110', '16', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Hold Reason', '暂停原因', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658196', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '7', 'holdOwner', '责任组', 'HOLD OWNER', '173370796464463872', null, '115', null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Hold Owner', '责任组', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658197', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '22', 'path', '开始路径', null, '173370796464463872', null, '120', '42', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Path', '开始路径', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('173370796468658199', '0', 'Y', to_date('22-02-2022 09:53:38', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('28-03-2024 17:33:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '11', 'actionStepName', '动作工步', 'ACTION STEP NAME', '173370796464463872', null, '140', null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Action Step Name', '动作工步', 'N', 'N', 'N', null, null, null, null, 'N', null, '12', null, null);

delete AD_IMPEXP_FIELD_MAP t where t.parent_rrn in (select object_rrn from AD_IMPEXP where authority_name = 'WipAdv.QTime');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618817', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '4', '305298459299618816', null, 'action', '10', null, 'QTIME TYPE');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618818', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'timerAction', '15', null, 'QTIME ACTION');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618819', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'partName', '22', null, 'PRODUCT NAME');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618820', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'name', '30', null, 'NAME');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618821', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'description', '40', null, 'DESCRIPTION');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618822', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'timerDuration', '42', null, 'QTIME DURATION');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618823', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'earlyPeriod', '45', null, 'EARLY PERIOD');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618824', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'processName', '55', null, 'PROCESS NAME');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618825', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'processVersion', '56', null, 'PROCESS VERSION');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618826', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'procedureName', '58', null, 'PROCEDURE NAME');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618827', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'procedureVersion', '60', null, 'PROCEDURE VERSION');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618828', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'stepName', '70', null, 'STEP NAME');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618829', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'stepPlacement', '72', null, 'START QTIME ACTION');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618830', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'endProcedureName', '75', null, 'END PROCEDURE NAME');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618831', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'endProcedureVersion', '76', null, 'END PROCEDURE VERSION');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618832', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'endStepName', '80', null, 'END STEP NAME');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618833', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'endStepPlacement', '85', null, 'END QTIME ACTION');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618834', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'holdCode', '100', null, 'HOLD CODE');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618835', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'holdReason', '110', null, 'HOLD REASON');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618836', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'holdOwner', '115', null, 'HOLD OWNER');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618837', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:29:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'actionStepName', '140', null, 'ACTION STEP NAME');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305298459299618838', '0', 'Y', to_date('21-02-2023 11:07:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('28-02-2023 09:30:02', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305298459299618816', null, 'isComponentLevel', '150', null, 'IS COMPONENT LEVEL');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('445249550826573824', '0', 'Y', to_date('13-03-2024 15:42:45', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('13-03-2024 16:26:32', 'dd-mm-yyyy hh24:mi:ss'), null, '3', '305298459299618816', null, 'timerType', '20', 'Y', 'TIMER TYPE');

DELETE AD_FORM_RELEASE WHERE NAME = 'FlowTimerManager';
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'FlowTimerManager' NAME, TO_TIMESTAMP('2022/12/13 17:13:11', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('450730643952447488', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, 'Qtimer设置查询', 'Active', b.release_timestamp, 'Y', 'Y');