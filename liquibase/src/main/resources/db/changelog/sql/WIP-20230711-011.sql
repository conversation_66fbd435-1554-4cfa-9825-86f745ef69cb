delete ad_button t where t.tab_rrn = 318126865121824768;

insert into ad_button (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, STYLE, FORM_RRN, TAB_RRN, SEQ_NO, IMAGE, IS_AUTHORITY, IS_SEPERAOTR, IS_USE_DEFAULT, DEFAULT_HANDLER, BPM_EVENT, LABEL, LABEL_ZH, LABEL_RES, CATEGORY, FIELD_RRN, TABLE_RRN, DATA_FROM)
values ('318544242976305251', '0', 'Y', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'showRule', '0', null, '318126865121824768', '0', 'rule', null, 'Y', 'Y', null, null, 'Show Rule', '规则', null, null, null, '318125114889097216', null);

insert into ad_button (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, STYLE, FORM_RRN, TAB_RRN, SEQ_NO, IMAGE, IS_AUTHORITY, IS_SEPERAOTR, IS_USE_DEFAULT, DEFAULT_HANDLER, BPM_EVENT, LABEL, LABEL_ZH, LABEL_RES, CATEGORY, FIELD_RRN, TABLE_RRN, DATA_FROM)
values ('318544242976305156', '0', 'Y', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'inActive', '0', null, '318126865121824768', '5', 'inactive', 'N', 'Y', 'Y', null, null, 'Inactive', '失效', null, null, null, '318125114889097216', null);

insert into ad_button (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, STYLE, FORM_RRN, TAB_RRN, SEQ_NO, IMAGE, IS_AUTHORITY, IS_SEPERAOTR, IS_USE_DEFAULT, DEFAULT_HANDLER, BPM_EVENT, LABEL, LABEL_ZH, LABEL_RES, CATEGORY, FIELD_RRN, TABLE_RRN, DATA_FROM)
values ('318544242976305157', '0', 'Y', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'delete', '0', null, '318126865121824768', '6', 'delete', null, 'Y', 'Y', null, null, 'Delete', '删除', null, null, null, '318125114889097216', null);

insert into ad_button (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, STYLE, FORM_RRN, TAB_RRN, SEQ_NO, IMAGE, IS_AUTHORITY, IS_SEPERAOTR, IS_USE_DEFAULT, DEFAULT_HANDLER, BPM_EVENT, LABEL, LABEL_ZH, LABEL_RES, CATEGORY, FIELD_RRN, TABLE_RRN, DATA_FROM)
values ('318544242976305158', '0', 'Y', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'entityRefresh', '0', null, '318126865121824768', '7', 'refresh', null, null, 'Y', null, null, 'Refresh', '刷新', null, null, null, '318125114889097216', null);

insert into ad_button (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, STYLE, FORM_RRN, TAB_RRN, SEQ_NO, IMAGE, IS_AUTHORITY, IS_SEPERAOTR, IS_USE_DEFAULT, DEFAULT_HANDLER, BPM_EVENT, LABEL, LABEL_ZH, LABEL_RES, CATEGORY, FIELD_RRN, TABLE_RRN, DATA_FROM)
values ('318544242976305155', '0', 'Y', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'active', '0', null, '318126865121824768', '4', 'active', 'N', 'Y', 'Y', null, null, 'Active', '激活', null, null, null, '318125114889097216', null);

insert into ad_button (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, STYLE, FORM_RRN, TAB_RRN, SEQ_NO, IMAGE, IS_AUTHORITY, IS_SEPERAOTR, IS_USE_DEFAULT, DEFAULT_HANDLER, BPM_EVENT, LABEL, LABEL_ZH, LABEL_RES, CATEGORY, FIELD_RRN, TABLE_RRN, DATA_FROM)
values ('318544242976305152', '0', 'Y', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'new', '0', null, '318126865121824768', '1', 'new', null, 'Y', 'Y', null, null, 'New', '新建', null, null, null, '318125114889097216', null);

insert into ad_button (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, STYLE, FORM_RRN, TAB_RRN, SEQ_NO, IMAGE, IS_AUTHORITY, IS_SEPERAOTR, IS_USE_DEFAULT, DEFAULT_HANDLER, BPM_EVENT, LABEL, LABEL_ZH, LABEL_RES, CATEGORY, FIELD_RRN, TABLE_RRN, DATA_FROM)
values ('318544242976305153', '0', 'Y', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('24-02-2021 17:26:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'save', '0', null, '318126865121824768', '2', 'save', null, 'Y', 'Y', null, null, 'Save', '保存', null, null, null, '318125114889097216', null);

 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'BASMergeRuleManager' NAME, TO_TIMESTAMP('2023/05/24 10:24:53', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('20230524099077', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 