UPDATE AD_TABLE SET MODEL_CLASS = 'com.glory.common.state.model.EventStatus' WHERE NAME = 'RASEventStatus';

delete AD_EDITOR where OBJECT_RRN in ('2017042258','2020072201','18110902');
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('2017042258', '0', 'Y', 'LabelTemplateEditor', null, 'bundleclass://com.glory.common.label/com.glory.common.label.template.LabelTemplateEditor', '93548319', 'LabelTemplateManager', null, 'spec', null);

insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('2020072201', '0', 'Y', 'LabelPrinterEditor', null, 'bundleclass://com.glory.common.label/com.glory.common.label.printer.LabelPrinterEditor', '2020072201', 'LabelPrinterManager', null, 'print', null);

insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('18110902', '0', 'Y', 'LabelConfigManagerEditor', null, 'bundleclass://com.glory.common.label/com.glory.common.label.config.LabelConfigEditor', '94147250', 'LabelConfigManager', null, 'capacity', null);

delete ad_editor where object_rrn in (201810121,201810122,201810123);

insert into ad_editor (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('201810121', '0', 'Y', 'SuccessionRefer', '交班管理', 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.changeshift.refer.ChangeShiftReferEditor', '131166441', 'ChangeShiftRefer', null, 'warehouse_eqp', null);

insert into ad_editor (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('201810122', '0', 'Y', 'SuccessionTo', '接班管理', 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.changeshift.to.ChangeShiftToEditor', '10181012', 'ChangeShiftTo', null, 'warehouse_eqp', null);

insert into ad_editor (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('201810123', '0', 'Y', 'SuccessionQuery', '交接班查询管理', 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.changeshift.query.ChangeShiftQueryEditor', '131186554', 'ChangeShiftQuery', null, 'warehouse_eqp', null);

delete ad_refname where NAME = 'SamplingCalcPlacement';

insert into ad_refname (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, CATEGORY, NAME, DESCRIPTION, REFERENCE_TYPE, LIST_LEVEL)
values ('397419503506497536', '0', 'Y', to_date('02-11-2023 16:03:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('02-11-2023 16:03:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'SYS', 'SamplingCalcPlacement', 'Sampling按片抽样计算位置', null, null);

delete ad_reflist where REFERENCE_NAME = 'SamplingCalcPlacement';

insert into ad_reflist (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('397419696012468224', '0', 'Y', to_date('02-11-2023 16:03:59', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('02-11-2023 16:03:59', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'SamplingCalcPlacement', 'PROCESSEND', 'PROCESSEND', '10', 'PROCESSEND', 'Y');

insert into ad_reflist (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('397419696012468225', '0', 'Y', to_date('02-11-2023 16:03:59', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('02-11-2023 16:03:59', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'SamplingCalcPlacement', 'TRACKOUT', 'TRACKOUT', '20', 'TRACKOUT', 'Y');

delete ad_field where table_rrn = 4339663;
delete ad_tab where table_rrn = 4339663;
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 4339665, 0, 'Y', to_date('2015-05-08 16:30:26', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 32, 'EDCSamplingPlanSetupBaseInfo', '基本信息', 4339663, 1, 2, 'Base Info', '基本信息', null, null, null, 0); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 4339664, 0, 'Y', to_date('2015-05-08 16:25:54', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 31, 'name', '名称', null, 4339663, null, 10, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Name', '名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 4339666, 0, 'Y', to_date('2015-05-08 16:31:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 30, 'description', '描述', null, 4339663, null, 20, null, 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 4339667, 0, 'Y', to_date('2015-05-08 16:34:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 31, 'status', '状态', null, 4339663, null, 30, null, 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, 'UnFrozen', 'Status', '状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 4339668, 0, 'Y', to_date('2015-05-08 16:35:38', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 31, 'version', '版本', null, 4339663, null, 40, null, 'Y', 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Version', '版本', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 4339669, 0, 'Y', to_date('2015-05-08 16:36:44', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 39, 'sampleType', '抽样类型', null, 4339663, null, 50, null, 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'EDCIntervaSampleType', null, null, null, 'Sample Type', '抽样类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93506783, 0, 'Y', to_date('2016-11-03 11:18:10', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 32, 'sampleRule', '抽样规则', null, 4339663, null, 55, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'sysreflist', null, null, null, null, null, 14480, 'IntervalSampleRule', null, null, null, 'Sample Rule', '抽样规则', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93506784, 0, 'Y', to_date('2016-11-03 11:19:36', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 36, 'sampleFirstRule', '首点规则', null, 4339663, null, 57, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'sysreflist', null, null, null, null, null, 14480, 'IntervalSampleFirstRule', null, null, null, 'Sample First Rule', '首点规则', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93506785, 0, 'Y', to_date('2016-11-03 11:20:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 33, 'sampleFirst', '首点位置', null, 4339663, null, 58, null, 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', null, null, null, null, null, null, null, null, null, null, 'Sample First', '首点位置', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 110030352246276096, 0, 'Y', to_date('2021-08-31 15:01:20', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 24, 'equipmentLevel', '监控设备', null, 4339663, null, 59, null, 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'EquipmentLevel', null, null, 'EQP', 'Equipment Level', '监控设备', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 4339675, 0, 'Y', to_date('2015-05-08 16:44:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 35, 'skipPriority', '取消抽样优先级', null, 4339663, null, 60, null, 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'userreflist', null, null, null, null, null, 14479, null, 'LotPriority', null, null, 'Skip Priority', '取消抽样优先级', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93506786, 0, 'Y', to_date('2016-11-03 11:21:38', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 30, 'isIndividual', '是否定义单个抽样计划', null, 4339663, null, 90, null, 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', null, null, null, null, null, null, null, null, null, null, 'Is Individual', '是否定义单个抽样计划', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 98506745051086848, 0, 'Y', to_date('2021-07-30 19:50:38', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 30, 'sampleLineString', '抽样计划', null, 4339663, null, 100, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Sample Line String', '抽样计划', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 1107464553408, 0, 'Y', to_date('2021-09-02 14:26:59', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 20, 'initInspectionLevel', '初始化检查等级', null, 4339663, null, 110, null, 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', '[1-9]?', null, null, null, null, null, null, null, null, 'Init InSpection Level', '初始化检查等级', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 397418757436284928, 0, 'Y', to_date('2023-11-02 16:00:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'calcPlacement', '抽样计算位置', null, 4339663, null, 120, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'SamplingCalcPlacement', null, null, 'PROCESSEND', 'Sampling Placement', '抽样位置', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 4339674, 0, 'Y', to_date('2015-05-08 16:42:11', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-11-09 10:24:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 37, 'isHoldGroup', '是否Hold整个样本组', null, 4339663, null, 130, null, 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'boolean', null, null, null, null, null, null, null, null, null, null, 'Hold Group', 'Hold整个样本组', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 