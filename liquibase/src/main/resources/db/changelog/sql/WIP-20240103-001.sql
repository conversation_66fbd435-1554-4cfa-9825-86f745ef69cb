delete from AD_EDITOR where OBJECT_RRN in (select editor_rrn from AD_AUTHORITY where name in ('Wip.DeassignCarrier'));
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('2016082402', '0', 'Y', 'LotDeassignManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.deassign.LotDeassignManagerEditor', null, 'LotDeassignManager', null, 'deassign', null);

delete from AD_AUTHORITY where name in ('Wip.DeassignCarrier');
insert into ad_authority (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESC<PERSON>PTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2016082402', '0', 'Y', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', to_date('02-01-2024 18:04:32', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.DeassignCarrier', '解绑载具', 'F', 'E', '2016082402', '603', '20', 'Lot Deassign Carrier', '解绑载具', null, 'deassign', 'MES', 'com.glory.mes.wip', null);

DELETE AD_FORM_RELEASE WHERE NAME = 'LotDeassignManager';
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'LotDeassignManager' NAME, TO_TIMESTAMP('2024/1/3 18:47:08', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('419930740860358656', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次解绑载具', 'Active', b.release_timestamp, 'Y', 'Y');
	
	