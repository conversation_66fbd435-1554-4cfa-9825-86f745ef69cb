--治具CONTEXT BOM页面调整
 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'ToolContextBomManager' NAME, TO_TIMESTAMP('2023/09/25 10:35:37', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('377111750971092994', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
 --批次在线返工弹框优化
delete ad_field where TABLE_RRN = '139051484045238272';
delete ad_table where OBJECT_RRN = '139051484045238272';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 139051484045238272, 0, 'Y', to_date('2021-11-19 17:00:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 10:49:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'WIPTrackOutHoldLot', '出站hold批次查询', null, 'N', 'Lot', 'com.glory.mes.wip.model.Lot', null, null, null, 'N', null, 2, null, null, null, 1); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 139051484049432576, 0, 'Y', to_date('2021-11-19 17:00:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 10:49:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'lotId', '批次号', null, 139051484045238272, null, 10, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'LotId', '批次号', 'Y', 'N', 'N', 'text', null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 139051484045238273, 0, 'Y', to_date('2021-11-19 17:00:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 10:49:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'durable', '载具', null, 139051484045238272, null, 20, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Carrier ID', '载具', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 139051484049432577, 0, 'Y', to_date('2021-11-19 17:00:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 10:49:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'lotType', '批次类型', null, 139051484045238272, null, 30, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'LotType', '批次类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 139051484049432579, 0, 'Y', to_date('2021-11-19 17:00:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 10:49:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'partName', '产品', null, 139051484045238272, null, 40, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Product Name', '产品', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 139051484049432580, 0, 'Y', to_date('2021-11-19 17:00:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 10:49:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'processName', '工艺', null, 139051484045238272, null, 50, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'ProcessName', '工艺', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 141488468244156416, 0, 'Y', to_date('2021-11-26 10:24:39', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 10:49:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'procedureName', '流程', null, 139051484045238272, null, 55, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Procedure Name', '流程', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 139051484049432582, 0, 'Y', to_date('2021-11-19 17:00:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 10:49:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'stepName', '工步', null, 139051484045238272, null, 60, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'StepName', '工步', 'Y', 'N', 'N', 'text', null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 139051484049432578, 0, 'Y', to_date('2021-11-19 17:00:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 10:49:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'mainQty', '数量', null, 139051484045238272, null, 70, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'MainQty', '数量', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 139051484049432581, 0, 'Y', to_date('2021-11-19 17:00:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 10:49:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'state', '状态', null, 139051484045238272, null, 80, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'State', '状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 139051484049432583, 0, 'Y', to_date('2021-11-19 17:00:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 10:49:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'holdState', '保留状态', null, 139051484045238272, null, 90, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'HoldState', '保留状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 

delete ad_field where TABLE_RRN = '140046543242805248';
delete ad_tab where TABLE_RRN = '140046543242805248';
delete ad_table where OBJECT_RRN = '140046543242805248';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 140046543242805248, 0, 'Y', to_date('2021-11-22 10:54:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:06:32', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'LotManualTrackMoveReworkBody', '手动过站返工Body部分', null, 'N', 'LotManualTrackMoveReworkBody', 'LotManualTrackMoveReworkBody', null, null, null, 'N', null, null, null, null, null, 0); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 140046543242805249, 0, 'Y', to_date('2021-11-22 10:54:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:06:32', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'tab1', null, 140046543242805248, 10, 2, 'Rework Info', '返工信息', null, 'Section', null, 33088); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 140046543242805250, 0, 'Y', to_date('2021-11-22 10:54:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:06:32', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'tab2', null, 140046543242805248, 20, null, null, null, null, 'Form', null, 320); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 140046543242805251, 0, 'Y', to_date('2021-11-22 10:54:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:06:32', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'reworkMergeInfo', '返工信息', null, 140046543242805248, 140046543242805249, 10, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'entityform', null, null, null, null, null, 140045960968552448, null, null, null, null, null, '返工信息', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 140046543242805252, 0, 'Y', to_date('2021-11-22 10:54:57', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:06:32', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'holdInfo', 'Hold信息', null, 140046543242805248, 140046543242805250, 20, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'glcform', null, null, null, null, null, null, null, null, 'LotManualTrackMoveReworkLeftGLC', null, null, 'Hold信息', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 

delete ad_field where TABLE_RRN = '140038812184231936';
delete ad_tab where TABLE_RRN = '140038812184231936';
delete ad_table where OBJECT_RRN = '140038812184231936';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 140038812184231936, 0, 'Y', to_date('2021-11-22 10:24:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 48, 'LotManualTrackMoveReworkRighBody', '手动过站返工右下部分', null, 'N', 'ComponentUnit', 'com.glory.mes.wip.model.ComponentUnit', null, null, null, 'N', 2, 2, null, null, null, 0); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 140038984230387712, 0, 'Y', to_date('2021-11-22 10:24:55', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 47, 'bodyRightTab', null, 140038812184231936, 10, 2, null, null, null, 'Tab', null, 0); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 140038812184231937, 0, 'Y', to_date('2021-11-22 10:24:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 48, 'reworkCode', '返工码', null, 140038812184231936, 140038984230387712, 10, null, 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'userreflist', 'string', null, null, null, null, 14479, null, 'ReworkCode', null, null, 'ReworkCode', '返工码', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 140038812184231938, 0, 'Y', to_date('2021-11-22 10:24:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 48, 'reworkReason', '返工指示', null, 140038812184231936, 140038984230387712, 20, null, 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 140457748469977088, null, null, null, null, 'Rework Procedure', '返工指示', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 140038812184231939, 0, 'Y', to_date('2021-11-22 10:24:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 48, 'comment', '备注', null, 140038812184231936, 140038984230387712, 30, null, 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'textarea', 'string', null, null, null, null, null, null, null, null, null, 'Comment', '备注', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 140772874263826432, 0, 'Y', to_date('2021-11-24 11:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 20, 'externalSplit', 'ExternalSplit', null, 140038812184231936, 140038984230387712, 40, null, 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, 'Y', 'ExternalSplit', 'ExternalSplit', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 140772874263826433, 0, 'Y', to_date('2021-11-24 11:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 20, 'autoMerge', '自动合批', null, 140038812184231936, 140038984230387712, 50, null, 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, 'Y', 'AutoMerge', '自动合批', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 150294205506318336, 0, 'Y', to_date('2021-12-20 17:35:30', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'autoHold', '返回主流程自动Hold', null, 140038812184231936, 140038984230387712, 55, null, 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'Return Auto Hold', '返回主流程自动Hold', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 140772874263826434, 0, 'Y', to_date('2021-11-24 11:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 20, 'mergeStep', '合批工步', null, 140038812184231936, 140038984230387712, 60, null, 'Y', 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'MergeStep', '合批工步', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 150275765798236160, 0, 'Y', to_date('2021-12-20 16:22:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'returnStep', '主流程返回工步', null, 140038812184231936, 140038984230387712, 70, null, 'Y', 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Return Step', '主流程返回工步', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 150548643827539968, 0, 'Y', to_date('2021-12-21 10:26:33', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 7, 'reworkControlType', '返工控制类型', null, 140038812184231936, 140038984230387712, 80, null, 'Y', 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Rework Control Type', '返工控制类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 150548643827539969, 0, 'Y', to_date('2021-12-21 10:26:33', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-09-25 11:08:52', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 7, 'reworkCount', '返工最大次数', null, 140038812184231936, 140038984230387712, 90, null, 'Y', 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Max Rework Count', '返工最大次数', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 

UPDATE AD_FORM
SET ORG_RRN=0, IS_ACTIVE='Y', CREATED=TIMESTAMP '2021-11-22 11:10:01', CREATED_BY='admin', UPDATED=TIMESTAMP '2023-09-25 10:59:24', UPDATED_BY='admin', LOCK_VERSION=21, NAME='LotManualTrackMoveReworkGLC', DESCRIPTION='手动过站返工界面GLC', "STYLE"=0, FORM_TYPE='SASH', TABLE_RRN=140047829258354688, ORIENTATION='VERTICAL', WEIGHTS='4;6', HINTS=NULL, IS_SECTION=NULL, FORM_SIZE=NULL, IS_ROOT=NULL, IS_DESIGNER=NULL
WHERE OBJECT_RRN=140050332884549632;
UPDATE AD_FORM
SET ORG_RRN=0, IS_ACTIVE='Y', CREATED=TIMESTAMP '2021-11-22 10:56:08', CREATED_BY='admin', UPDATED=TIMESTAMP '2023-09-25 11:11:53', UPDATED_BY='admin', LOCK_VERSION=17, NAME='LotManualTrackMoveReworkBody', DESCRIPTION='手动过站返工BodyGLC', "STYLE"=0, FORM_TYPE='SASH', TABLE_RRN=140046543242805248, ORIENTATION='HORIZONTAL', WEIGHTS='54;50', HINTS=NULL, IS_SECTION=NULL, FORM_SIZE=NULL, IS_ROOT=NULL, IS_DESIGNER=NULL
WHERE OBJECT_RRN=140046840581210112;

--BOM设置初始查询条件
UPDATE AD_TABLE
SET ORG_RRN=0, IS_ACTIVE='Y', CREATED=TIMESTAMP '2014-01-06 14:52:03', CREATED_BY='1', UPDATED=TIMESTAMP '2023-09-25 11:48:39', UPDATED_BY='admin', LOCK_VERSION=14, NAME='MMBom', DESCRIPTION='机种BOM设置', TABLE_NAME='MM_BOM', IS_VIEW='N', MODEL_NAME='Bom', MODEL_CLASS='com.glory.mes.mm.bom.model.Bom', WHERE_CLAUSE='bomUse != ''NPW'' AND bomUse != ''T''', ORDER_BY_CLAUSE=NULL, INIT_WHERE_CLAUSE=NULL, IS_VERTICAL='N', GRID_Y_BASIC=2, GRID_Y_QUERY=1, LABEL='Part BOM', LABEL_ZH='机种BOM设置', LABEL_RES=NULL, "STYLE"=1, IS_DESIGNER=NULL
WHERE OBJECT_RRN=4307461;
