DELETE AD_AUTHORITY WHERE NAME IN ('WIPTrackManager','PPManager','WIP.ChangeShift','WIPPackageManager','WIPCarrierManager',
'ChangeWIPInfo','WipQueryManager','WipFutureActionManager','PrintManager','WIPManager','WIPSeniorManager','WIPMultiCarrierManager','WipDefectAndScrap','WIPTrackCondition');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('608', '0', 'Y', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 11:53:08', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WIPTrackManager', 'Lot Tracking Management', 'M', null, null, '60', '20', 'Lot_Tracking', '作业管理', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('611', '0', 'Y', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 11:53:01', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'PPManager', 'WorkOrder Management', 'M', null, null, '60', '10', 'WorkOrder', '工单管理', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20181012', '0', 'Y', to_date('14-11-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:09:06', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'WIP.ChangeShift', 'Change Shift', 'M', null, null, '60', '120', 'Shift_Change', '交接班管理', null, 'subapp_wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('685', '0', 'Y', to_date('14-11-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:08:58', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'WIPPackageManager', 'Package Management', 'M', null, null, '60', '130', 'Package Management', '包装管理', null, 'subapp_wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('603', '0', 'Y', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 12:09:35', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'WIPCarrierManager', 'Lot Carrier Management', 'M', null, null, '60', '80', 'Lot_Carrier', '载具管理', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('604', '0', 'N', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 12:38:05', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'ChangeWIPInfo', 'Lot Change Management', 'M', null, null, '60', '40', 'Lot_Change', '批次信息变更', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('605', '0', 'Y', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 12:08:42', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'WipQueryManager', 'WIP Query', 'M', null, null, '60', '60', 'WIP_Query', '批次信息查询', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('606', '0', 'Y', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 12:09:28', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'WipFutureActionManager', 'WIP Future Action Management', 'M', null, null, '60', '90', 'Future_Action', '批次未来动作管理', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('680', '0', 'Y', to_date('19-04-2017', 'dd-mm-yyyy'), null, to_date('11-07-2023 12:09:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'PrintManager', 'PrintManagement', 'M', null, null, '60', '110', 'Print Management', '打印管理', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('601', '0', 'Y', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 11:53:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WIPManager', 'Lot Management', 'M', null, null, '60', '30', 'Lot_Management', '批次管理', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('602', '0', 'Y', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 16:04:51', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'WIPSeniorManager', 'Lot Process Change Management', 'M', null, null, '60', '70', 'Lot_Process_Change', '批次流程变更', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('650', '0', 'N', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('24-03-2023 17:50:30', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'WIPMultiCarrierManager', 'Multi Carrier Management', 'M', null, null, '60', '35', 'Multi Carrier Management', '多载具管理', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('631', '0', 'N', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 11:58:08', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WipDefectAndScrap', 'Lot Scrap Management', 'M', null, null, '60', '14', 'Lot_Scrap', '缺陷及报废管理', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('356048224961724416', '0', 'Y', to_date('11-07-2023 12:08:31', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('11-07-2023 12:08:31', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WIPTrackCondition', 'Lot Track Condition Change', 'M', null, null, '60', '50', 'Lot_Track_Condition_Change', '批次作业条件变更', null, 'wip', 'MES', null, null);



DELETE AD_AUTHORITY WHERE NAME IN ('Wip.LotTrackMove','Wip.ByBatch','Wip.LotTrackGlc','Wip.ByLocationGlc','Wip.ByEqpGlc','Wip.LotTrack','Wip.ByEqp','Wip.ByStep');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('17111601', '0', 'N', to_date('19-11-2008', 'dd-mm-yyyy'), 'admin', to_date('19-11-2008', 'dd-mm-yyyy'), 'admin', '1', 'Wip.LotTrackMove', '批次过站', 'F', 'E', '17111601', '608', '30', 'Lot TrackMove', '批次过站', null, 'bylot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2004', '0', 'N', to_date('28-02-2011', 'dd-mm-yyyy'), 'admin', to_date('28-02-2011', 'dd-mm-yyyy'), 'admin', '1', 'Wip.ByBatch', '按Batch作业', 'F', 'E', '20170829', '608', '36', 'Lot Operation By Batch', '按Batch作业', null, 'production_order', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202211071146', '0', 'Y', to_date('19-11-2008', 'dd-mm-yyyy'), 'admin', to_date('19-11-2008', 'dd-mm-yyyy'), 'admin', '1', 'Wip.LotTrackGlc', '按批次作业', 'F', 'E', '202211071146', '608', '40', 'Lot Operation By Lot', '按批次作业', null, 'bylot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('9914', '0', 'N', to_date('09-10-2008', 'dd-mm-yyyy'), null, to_date('06-01-2023 16:13:14', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'Wip.ByLocationGlc', 'Lot Operation By Location', 'F', 'E', '9914', '608', '80', 'Lot Operation By Location', '按区域作业', null, 'location', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('9904', '0', 'N', to_date('09-10-2008', 'dd-mm-yyyy'), null, to_date('09-10-2008', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqpGlc', '按设备作业', 'F', 'E', '9904', '608', '70', 'Lot Tracking From EQP', '按设备作业', null, 'preference', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6008', '0', 'N', to_date('19-11-2008', 'dd-mm-yyyy'), 'admin', to_date('19-11-2008', 'dd-mm-yyyy'), 'admin', '1', 'Wip.LotTrack', '按批次作业', 'F', 'E', '608', '608', '35', 'Lot Operation By Lot', '按批次作业', null, 'bylot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('307580653749252097', '0', 'Y', to_date('27-02-2023 18:15:42', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-03-2023 10:49:46', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '5', 'Wip.ByEqp', '按设备作业', 'F', 'E', '307580653749252096', '608', '70', 'Lot Tracking From EQP', '按设备作业', null, 'preference', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('9003', '0', 'Y', to_date('09-10-2008', 'dd-mm-yyyy'), null, to_date('10-03-2023 16:50:54', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.ByStep', '按工歩作业', 'F', 'E', '153', '608', '50', 'Lot Operation By Step', '按工歩作业', null, 'preference', 'MES', 'com.glory.mes.wip', null);



DELETE AD_AUTHORITY WHERE NAME IN ('Wip.NewLotUnStart','Wip.WorkOrder','Wip.PPWorkOrdeLotStart','WIP.WoStartByMLotManage','Wip.SubWorkOrder','Wip.MulitWorkOrder','Wip.ChangeWorkOrder','Wip.ReworkWorkOrderStart','Wip.ReworkWorkOrder','Wip.MaterialRequest');

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20030601', '0', 'Y', to_date('06-03-2020', 'dd-mm-yyyy'), 'admin', to_date('13-12-2022 17:24:51', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'Wip.NewLotUnStart', '工单取消投料', 'F', 'E', '20030601', '611', '65', 'WO Lot Start Cancel', '工单取消投料', null, 'unskip-lot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6312', '0', 'Y', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:47:08', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.WorkOrder', '工单管理', 'F', 'E', '20221207001', '611', '10', 'WorkOrder', '工单管理', null, 'production_order', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('21091501', '0', 'Y', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', '1', 'Wip.PPWorkOrdeLotStart', '工单批次投料', 'F', 'E', '21091501', '611', '63', 'WO Lot Start From RawWaferLot', '工单批次投料', null, 'newlot_start', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('277836406943993856', '0', 'Y', to_date('07-12-2022 16:22:41', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('11-07-2023 11:47:15', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '5', 'WIP.WoStartByMLotManage', '工单投料', 'F', 'E', '277836406939799552', '611', '20', 'WO Lot Start', '工单投料', null, 'new_wo_start', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('63121', '0', 'Y', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', '1', 'Wip.SubWorkOrder', '子工单管理', 'F', 'E', '5261', '611', '40', 'Sub WorkOrder', '子工单管理', null, 'production_order', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6313', '0', 'Y', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', '1', 'Wip.MulitWorkOrder', '多级工单管理(根据BOM生成多级工单)', 'F', 'E', '537', '611', '30', 'Multi Level WorkOrder', '多级工单管理(根据BOM生成多级工单)', null, 'production_order', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6317', '0', 'Y', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', '1', 'Wip.ChangeWorkOrder', '转投工单', 'F', 'E', '52617', '611', '70', 'Lot Change WorkOrder', '转投工单', null, 'production_order', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6856', '0', 'Y', to_date('22-11-2017', 'dd-mm-yyyy'), 'admin', to_date('22-11-2017', 'dd-mm-yyyy'), 'admin', '1', 'Wip.ReworkWorkOrderStart', '返工工单投料', 'F', 'E', '6856', '611', '90', 'Rework WorkOrder Start', '返工工单投料', null, 'newlot_start', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6855', '0', 'Y', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', '1', 'Wip.ReworkWorkOrder', '返工工单', 'F', 'E', '6855', '611', '80', 'Rework WorkOrder', '返工工单', null, 'production_order', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('17051501', '0', 'Y', to_date('08-05-2017', 'dd-mm-yyyy'), 'admin', to_date('08-05-2017', 'dd-mm-yyyy'), 'admin', '1', 'Wip.MaterialRequest', '工单领料', 'F', 'E', '17051501', '611', '50', 'Material Request', '工单领料', null, 'production_order', 'MES', 'com.glory.mes.wip', null);


DELETE AD_AUTHORITY WHERE NAME IN ('Wip.ChangeShiftRefer','Wip.ChangeShiftTo','Wip.ChangeShiftQuery');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('201810121', '0', 'Y', to_date('15-07-2014', 'dd-mm-yyyy'), 'admin', to_date('08-07-2014', 'dd-mm-yyyy'), 'admin', '0', 'Wip.ChangeShiftRefer', '交班管理', 'F', 'E', '201810121', '20181012', '10', 'Shift Handover', '交班管理', null, 'warehouse_eqp', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('201810122', '0', 'Y', to_date('15-07-2014', 'dd-mm-yyyy'), 'admin', to_date('08-07-2014', 'dd-mm-yyyy'), 'admin', '0', 'Wip.ChangeShiftTo', '接班管理', 'F', 'E', '201810122', '20181012', '20', 'Shift TakeOver', '接班管理', null, 'warehouse_eqp', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('201810123', '0', 'Y', to_date('15-07-2014', 'dd-mm-yyyy'), 'admin', to_date('08-07-2014', 'dd-mm-yyyy'), 'admin', '0', 'Wip.ChangeShiftQuery', '交接班查询管理', 'F', 'E', '201810123', '20181012', '30', 'Shift Change Query', '交接班查询管理', null, 'warehouse_eqp', 'MES', 'com.glory.mes.wip', null);


DELETE AD_AUTHORITY WHERE NAME IN ('Wip.SplitPack','Wip.MultiSplitPack','Wip.MultiSplitUnpack','Wip.PackRelationQuery','Wip.Pack','Wip.PackQuery','Wip.PackageHierarchy','Wip.ScrapPack');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2017112251', '0', 'Y', to_date('09-05-2017', 'dd-mm-yyyy'), 'root', to_date('09-05-2017', 'dd-mm-yyyy'), 'root', '1', 'Wip.SplitPack', '批次拆包', 'F', 'E', '17112251', '685', '155', 'Package Destory Relation', '批次拆包', null, 'mlot_receive', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('1912402', '0', 'Y', to_date('09-05-2017', 'dd-mm-yyyy'), 'root', to_date('09-05-2017', 'dd-mm-yyyy'), 'root', '1', 'Wip.MultiSplitPack', '分包包装', 'F', 'E', '1912402', '685', '180', 'Multi Split Pack Lot', '分包包装', null, 'hold-lot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('1912403', '0', 'Y', to_date('09-05-2017', 'dd-mm-yyyy'), 'root', to_date('09-05-2017', 'dd-mm-yyyy'), 'root', '1', 'Wip.MultiSplitUnpack', '分包拆包', 'F', 'E', '1912403', '685', '190', 'Multi Split Unpack Lot', '分包拆包', null, 'mlot_receive', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('192261', '0', 'Y', to_date('09-05-2017', 'dd-mm-yyyy'), 'root', to_date('09-05-2017', 'dd-mm-yyyy'), 'root', '1', 'Wip.PackRelationQuery', '所属包装关系查询', 'F', 'E', '192261', '685', '300', 'Pack Relation Query', '所属包装关系查询', null, 'wip-query', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2017100901', '0', 'Y', to_date('09-05-2017', 'dd-mm-yyyy'), 'root', to_date('09-05-2017', 'dd-mm-yyyy'), 'root', '1', 'Wip.Pack', '批次包装', 'F', 'E', '17100901', '685', '150', 'Lot Packing', '批次包装', null, 'hold-lot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202007221601', '0', 'Y', to_date('01-12-2008', 'dd-mm-yyyy'), 'admin', to_date('01-12-2008', 'dd-mm-yyyy'), 'admin', '1', 'Wip.PackQuery', '包装查询', 'F', 'E', '202007221604', '685', '150', 'Package Query', '包装查询', null, 'wip_code', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202007221420', '0', 'Y', to_date('01-12-2008', 'dd-mm-yyyy'), 'admin', to_date('01-12-2008', 'dd-mm-yyyy'), 'admin', '1', 'Wip.PackageHierarchy', '包装层级管理', 'F', 'E', '202007221409', '685', '150', 'Package Hierarchy', '包装层级管理', null, 'wip_code', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('331219211592429568', '0', 'Y', to_date('31-03-2021 16:52:01', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('31-03-2021 16:52:01', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '0', 'Wip.ScrapPack', '废品包装', 'F', 'E', '2019031401', '685', '10', 'Scrapped Packing', '废品包装', null, 'hold-lot', 'MES', 'com.glory.mes.wip', null);


DELETE AD_AUTHORITY WHERE NAME IN ('Wip.AssignCarrier','Wip.DeassignCarrier','Wip.ChangeCarrier','Wip.SortingSplit','Wip.LotSortingMerge','Wip.SortingJob','Wip.AbnormalSortingJob','Wip.SortingJobHisQuery');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2016082401', '0', 'Y', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', '1', 'Wip.AssignCarrier', '绑定载具', 'F', 'E', '2016082401', '603', '10', 'Lot Assign Carrier', '绑定载具', null, 'assign', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2016082402', '0', 'Y', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', '1', 'Wip.DeassignCarrier', '解绑载具', 'F', 'E', '2016082402', '603', '20', 'Lot Deassign Carrier', '解绑载具', null, 'deassign', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2019081401', '0', 'Y', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', '1', 'Wip.ChangeCarrier', '载具变更', 'F', 'E', '2019081401', '603', '35', 'Lot Change Carrier', '载具变更', null, 'carrier-change', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2016092901', '0', 'Y', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', '1', 'Wip.SortingSplit', '载具分批', 'F', 'E', '2017082901', '603', '40', 'Lot Split(Sorter)', '载具分批', null, 'carrier-split', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2017101801', '0', 'Y', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', '1', 'Wip.LotSortingMerge', '载具合批', 'F', 'E', '2017101801', '603', '50', 'Lot Merge(Sorter)', '载具合批', null, 'carrier-merge', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2017101201', '0', 'Y', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', '1', 'Wip.SortingJob', '载具任务查询', 'F', 'E', '2017101201', '603', '60', 'Sorter Job', '载具任务查询', null, 'job-query', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202203240010', '0', 'Y', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', '1', 'Wip.AbnormalSortingJob', '载具任务异常处理', 'F', 'E', '202203240010', '603', '70', 'Abnormal Sorter Job', '载具任务异常处理', null, 'job-alm', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('***********', '0', 'Y', to_date('23-05-2022', 'dd-mm-yyyy'), 'admin', to_date('23-05-2022', 'dd-mm-yyyy'), 'admin', '1', 'Wip.SortingJobHisQuery', '载具任务历史查询', 'F', 'E', '***********', '603', '80', 'Sorter Job History Query ', '载具任务历史查询', null, 'job-hisquery', 'MES', 'com.glory.mes.wip', null);




DELETE AD_AUTHORITY WHERE NAME IN ('Wip.LotHistory','Wip.LotDetail','WIP.ForwardLotQuery','Wip.LotHisTrackOut','WIP.ComponentHis','Wip.WipBankQuery','WipAdv.StartedTimer');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6050', '0', 'Y', to_date('25-12-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:36:22', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.LotHistory', '批次事务历史', 'F', 'E', '653', '605', '10', 'Lot Transaction History', '批次事务历史', null, 'transation-history', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6053', '0', 'Y', to_date('26-12-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:37:07', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.LotDetail', '批次详细信息', 'F', 'E', '650', '605', '50', 'Lot Detail Query', '批次详细信息', null, 'lot-detail', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20211008001', '0', 'Y', to_date('08-10-2021 15:56:15', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('11-07-2023 12:36:41', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'WIP.ForwardLotQuery', '未来到达批次查询', 'F', 'E', '20211008001', '605', '80', 'Future Arrived Lots Query', '未来到达批次查询', null, 'future_search', 'MES', 'com.glory.mes.wip.advance', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6210', '0', 'Y', to_date('24-04-2014', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:36:30', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.LotHisTrackOut', '生产加工历史查询', 'F', 'E', '6210', '605', '20', 'Lot Production History By EQP', '生产加工历史查询', null, 'production-history', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20210621001', '0', 'Y', to_date('21-06-2021 15:56:15', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('11-07-2023 12:36:53', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'WIP.ComponentHis', '批次组件事务历史', 'F', 'E', '20210621001', '605', '30', 'Lot Component History', '批次组件事务历史', null, 'comp-history', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('168', '0', 'Y', to_date('06-02-2009', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:37:28', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.WipBankQuery', 'Bank库查询', 'F', 'E', '288', '605', '70', 'Bank Query', 'Bank库查询', null, 'bank-query', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6370', '0', 'Y', to_date('19-07-2012', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:37:14', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'WipAdv.StartedTimer', '已开始QTime查询', 'F', 'E', '40008', '605', '60', 'Started Timer Query', '已开始QTime查询', null, 'timer_started', 'MES', 'com.glory.mes.wip.advance', null);




DELETE AD_AUTHORITY WHERE NAME IN ('WipAdv.FutureMultiHold','WipAdv.FutureNote','WipAdv.FutureHold','WipAdv.LotFutureActionQuery','WipAdv.ProcedureFutureActionQuery','WipAdv.ProcedureHold',
'WipAdv.ProcedureHoldQuery','WipAdv.FutureStepQuery','WipAdv.ProcedureFutureNote','WipAdv.FutureSkip','WipAdv.FutureActionHisQuery');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6375', '0', 'Y', to_date('03-08-2017', 'dd-mm-yyyy'), 'admin', to_date('03-08-2017', 'dd-mm-yyyy'), 'admin', '1', 'WipAdv.FutureMultiHold', '批次未来暂停(批量)', 'F', 'E', '40012', '606', '91', 'Lot Future Hold(Batch)', '批次未来暂停(批量)', null, 'hold-lot', 'MES', 'com.glory.mes.wip.advance', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6320', '0', 'Y', to_date('06-07-2010', 'dd-mm-yyyy'), 'admin', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', '1', 'WipAdv.FutureNote', '批次未来备注', 'F', 'E', '40002', '606', '80', 'Lot Future Note', '批次未来备注', null, 'note', 'MES', 'com.glory.mes.wip.advance', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6330', '0', 'Y', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', '1', 'WipAdv.FutureHold', '批次未来暂停', 'F', 'E', '40003', '606', '90', 'Lot Future Hold', '批次未来暂停', null, 'hold-lot', 'MES', 'com.glory.mes.wip.advance', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6360', '0', 'Y', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', '1', 'WipAdv.LotFutureActionQuery', '批次未来动作查询', 'F', 'E', '40007', '606', '97', 'Lot Future Action Query', '批次未来动作查询', null, 'future_search', 'MES', 'com.glory.mes.wip.advance', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6365', '0', 'Y', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', '1', 'WipAdv.ProcedureFutureActionQuery', '按模块查询批次未来动作', 'F', 'E', '40100', '606', '130', 'Procedure Future Lot Action Query', '按模块查询批次未来动作', null, 'future_search', 'MES', 'com.glory.mes.wip.advance', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6340', '0', 'Y', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', '1', 'WipAdv.ProcedureHold', '模块未来暂停', 'F', 'E', '40005', '606', '105', 'Procedure Future Hold', '模块未来暂停', null, 'procedure_hold', 'MES', 'com.glory.mes.wip.advance', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6342', '0', 'Y', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', '1', 'WipAdv.ProcedureHoldQuery', '模块未来暂停查询', 'F', 'E', '40105', '606', '110', 'Procedure Future Hold Query', '模块未来暂停查询', null, 'procedure_hold_query', 'MES', 'com.glory.mes.wip.advance', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6380', '0', 'Y', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', '1', 'WipAdv.FutureStepQuery', '批次未来流程查询', 'F', 'E', '6380', '606', '98', 'Lot Future Step Query', '批次未来流程查询', null, 'future_search', 'MES', 'com.glory.mes.wip.advance', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6335', '0', 'Y', to_date('13-02-2020', 'dd-mm-yyyy'), 'admin', to_date('13-02-2020', 'dd-mm-yyyy'), 'admin', '1', 'WipAdv.ProcedureFutureNote', '模块未来备注', 'F', 'E', '40020', '606', '100', 'Procedure Future Note', '模块未来备注', null, 'note', 'MES', 'com.glory.mes.wip.advance', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('292997131631', '0', 'Y', to_date('06-07-2010', 'dd-mm-yyyy'), 'admin', to_date('11-11-2008', 'dd-mm-yyyy'), 'admin', '1', 'WipAdv.FutureSkip', '批次未来跳步', 'F', 'E', '292997131631', '606', '79', 'Lot Future Skip', '批次未来跳步', null, 'note', 'MES', 'com.glory.mes.wip.advance', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202203151053', '0', 'Y', to_date('15-03-2022', 'dd-mm-yyyy'), 'admin', to_date('15-03-2022', 'dd-mm-yyyy'), 'admin', '1', 'WipAdv.FutureActionHisQuery', '未来动作设置历史查询', 'F', 'E', '202203151053', '606', '140', 'Future Action History Query', '未来动作历史查询', null, 'future_search', 'MES', 'com.glory.mes.wip.advance', null);



DELETE AD_AUTHORITY WHERE NAME IN ('Wip.LablePrint','TCard.Print','TCard.PrintBatch');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('4433', '0', 'Y', to_date('12-05-2017', 'dd-mm-yyyy'), null, to_date('12-05-2017', 'dd-mm-yyyy'), null, '0', 'Wip.LablePrint', '打印标签', 'F', 'E', '20170512', '680', '30', 'Print Label', '打印标签', null, 'print', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('7530', '0', 'Y', to_date('07-05-2011', 'dd-mm-yyyy'), 'admin', to_date('07-05-2011', 'dd-mm-yyyy'), 'admin', '1', 'TCard.Print', '打印流程卡', 'F', 'E', '7530', '680', '30', 'Print TCard', '打印流程卡', null, 'print', 'MES', 'com.glory.mes.tcard', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('7531', '0', 'Y', to_date('07-05-2011', 'dd-mm-yyyy'), 'admin', to_date('07-05-2011', 'dd-mm-yyyy'), 'admin', '1', 'TCard.PrintBatch', '批量打印', 'F', 'E', '7531', '680', '40', 'Print Tcard(Batch)', '批量打印', null, 'print', 'MES', 'com.glory.mes.tcard', null);



DELETE AD_AUTHORITY WHERE NAME IN ('Wip.Changhold','Wip.UnMergeLot','Wip.ScheduleStart','Wip.Schedule','Wip.MoveLocation','Wip.Identify','Wip.MaintainLot','Wip.NPWLotSchedule',
'Wip.ComponentChangeAlias','Wip.WipQueryNew','Wip.BatchManager','Wip.BatchSplitMergeManager','Wip.MyGroupLot','Wip.ComponentHold','Wip.ComponentRelease','Wip.ChangeLot');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6389', '0', 'Y', to_date('08-11-2011', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:42:25', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'Wip.Changhold', '转暂停', 'F', 'E', '400056', '601', '80', 'Lot Change Hold Owner', '转暂停', null, 'wip-query', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6180', '0', 'Y', to_date('05-12-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:40:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.UnMergeLot', '取消合批', 'F', 'E', '6180', '601', '110', 'Lot UnMerge', '取消合批', null, 'merge', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('7012', '0', 'Y', to_date('28-06-2011', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:40:06', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.ScheduleStart', '批次投料', 'F', 'E', '6012', '601', '40', 'Lot Start', '批次投料', null, 'newlot_start', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('7011', '0', 'Y', to_date('28-06-2011', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:39:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.Schedule', '批次创建', 'F', 'E', '6017', '601', '20', 'Lot Create', '批次创建', null, 'schedule', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('601335', '0', 'Y', to_date('24-03-2011', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:37:52', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.MoveLocation', '批次位置移动', 'F', 'E', '661', '601', '310', 'Lot Location Move', '批次位置移动', null, 'move_to_loc', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6005', '0', 'Y', to_date('14-07-2009', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:41:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.Identify', '晶圆号码生成', 'F', 'E', '605', '601', '50', 'Component Identify', '晶圆号码生成', null, 'identify', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('1', '0', 'Y', to_date('27-11-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:40:34', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.MaintainLot', '批次维修', 'F', 'E', '610', '601', '130', 'Maintain Lot', '批次维修', null, 'hold-lot', 'MES', 'com.glory.mes.pvm', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('7011112748123', '0', 'Y', to_date('28-06-2011', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:39:53', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.NPWLotSchedule', '批次批量创建', 'F', 'E', '6017123411234', '601', '30', 'Create NPW and Dummy Lot', '批量批次创建', null, 'schedule', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2022051877', '0', 'Y', to_date('18-05-2022', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:37:52', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.ComponentChangeAlias', '组件别名修改', 'F', 'E', '2022051877', '601', '300', 'Component Change Alias', '组件别名修改', null, 'change-lot-info', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202211171442', '0', 'Y', to_date('06-02-2009', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:38:47', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '6', 'Wip.WipQueryNew', '在制品', 'F', 'E', '202211171442', '601', '10', 'WIPLot', '在制品', null, 'wip-query', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2017090101', '0', 'Y', to_date('01-09-2017', 'dd-mm-yyyy'), null, to_date('11-07-2023 12:42:48', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'Wip.BatchManager', 'Batch组批', 'F', 'E', '2017090101', '601', '90', 'Batch', 'Batch组批', null, 'batch_merge', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2017090701', '0', 'Y', to_date('07-09-2017', 'dd-mm-yyyy'), null, to_date('11-07-2023 12:42:57', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'Wip.BatchSplitMergeManager', 'Batch分批合批', 'F', 'E', '2017090701', '601', '100', 'Batch Split Merge', 'Batch分批合批', null, 'batch_split', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20211215001', '0', 'Y', to_date('06-02-2009', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:40:45', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'Wip.MyGroupLot', 'MyLot管理', 'F', 'E', '20211215001', '601', '120', 'My Group Lot', 'MyGroupLot管理', null, 'mylot-query', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2019062701', '0', 'Y', to_date('25-04-2019', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:42:05', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.ComponentHold', 'Component暂停', 'F', 'E', '2019062701', '601', '60', 'Component Hold', 'Component暂停', null, 'hold-lot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2019070101', '0', 'Y', to_date('25-04-2019', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:42:15', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.ComponentRelease', 'Component放行', 'F', 'E', '2019070101', '601', '70', 'Component Release', 'Component放行', null, 'release_comp', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6007', '0', 'Y', to_date('19-11-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:37:52', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.ChangeLot', '批次号码修改', 'F', 'E', '607', '601', '315', 'Lot Change ID', '批次号码修改', null, 'adobject', 'MES', 'com.glory.mes.wip', null);



DELETE AD_AUTHORITY WHERE NAME IN ('Wip.Skip','Wip.Backup','Wip.NewPart','Wip.RecoverySplit','Wip.RecoverySplitSorting','Wip.LotManualMoveNext','Wip.ChangeProcess','Wip.LotProcedureChange','Wip.LotProcedureChangeSetup');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6036', '0', 'Y', to_date('06-07-2010', 'dd-mm-yyyy'), 'admin', to_date('19-07-2012', 'dd-mm-yyyy'), 'admin', '1', 'Wip.Skip', '跳步', 'F', 'E', '40000', '602', '10', 'Step Forward', '跳步', null, 'skip-lot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6037', '0', 'Y', to_date('06-07-2010', 'dd-mm-yyyy'), 'admin', to_date('19-07-2012', 'dd-mm-yyyy'), 'admin', '1', 'Wip.Backup', '退步', 'F', 'E', '40001', '602', '20', 'Step Backward', '退步', null, 'unskip-lot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6035', '0', 'Y', to_date('12-12-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:17:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.NewPart', '改产品', 'F', 'E', '635', '602', '50', 'Lot Change Product', '改产品', null, 'newpart', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6311', '0', 'Y', to_date('19-07-2017', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:15:02', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.RecoverySplit', '批次分批返工', 'F', 'E', '637', '602', '30', 'Lot Split Rework(AdHoc)', '批次分批返工', null, 'rework', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6314', '0', 'Y', to_date('19-07-2017', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:17:02', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.RecoverySplitSorting', '批次分批返工(Sorter)', 'F', 'E', '638', '602', '40', 'Lot Split Sorter Rework(AdHoc)', '批次分批返工(Sorter)', null, 'rework', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021111901', '0', 'Y', to_date('06-02-2009', 'dd-mm-yyyy'), 'admin', to_date('06-02-2009', 'dd-mm-yyyy'), 'admin', '1', 'Wip.LotManualMoveNext', '批次在线返工', 'F', 'E', '2021111901', '602', '70', 'Lot Rework Process', '批次在线返工', null, 'rework_lot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('1910211', '0', 'Y', to_date('12-12-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:17:23', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.ChangeProcess', '更改生产工步', 'F', 'E', '1910211', '602', '60', 'Lot Change Flow Step', '更改生产工步', null, 'step', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('298820638846619649', '0', 'Y', to_date('03-02-2023 14:06:32', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('11-07-2023 12:18:10', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.LotProcedureChange', '批次流程变更', 'F', 'E', '298820638846619648', '602', '90', 'Lot Procedure Change Query', '批次流程变更查询', null, 'production-history', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('298820092966342656', '0', 'Y', to_date('03-02-2023 14:04:21', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('11-07-2023 12:18:03', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.LotProcedureChangeSetup', '批次流程变更设置', 'F', 'E', '298820092962148352', '602', '80', 'Lot Procedure Change Setup', '批次流程变更设置', null, 'terminate-lot', 'MES', 'com.glory.mes.wip', null);



DELETE AD_AUTHORITY WHERE NAME IN ('Wip.WipReservedQuery','Wip.LotDispatchForbidden','Wip.LotTecn','Wip.RefreshCurrentStep','Wip.ComponentUnit.Reserved');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6059', '0', 'Y', to_date('06-02-2009', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.WipReservedQuery', '批次预留设备', 'F', 'E', '6059', '356048224961724416', '70', 'Lot Reserve EQP', '批次预留设备', null, 'reserve', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021121501', '0', 'Y', to_date('06-02-2009', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:14:23', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.LotDispatchForbidden', '批次禁止派工', 'F', 'E', '2021121501', '356048224961724416', '120', 'Lot Dispatch Forbidden', '批次禁止派工', null, 'terminate-lot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6190', '0', 'Y', to_date('05-12-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:10:19', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.LotTecn', 'Lot Tecn', 'F', 'E', '6190', '356048224961724416', '580', 'Lot Tecn', '批次临时工程变更', null, 'wip-query', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20220708077', '0', 'Y', to_date('06-02-2009', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:14:23', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.RefreshCurrentStep', '刷新批次当站', 'F', 'E', '20220708077', '356048224961724416', '210', 'Lot Refresh Current Step', '批次刷新当站', null, 'refresh', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('17111301', '0', 'Y', to_date('19-11-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:17:54', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.ComponentUnit.Reserved', '批次组件Recipe', 'F', 'E', '17111302', '356048224961724416', '100', 'Component Recipe', '批次组件Recipe', null, 'recipe_comp', 'MES', 'com.glory.mes.wip', null);


DELETE AD_AUTHORITY WHERE NAME IN ('WIP.Adv');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('356034141659840512', '0', 'Y', to_date('11-07-2023 11:12:34', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('12-07-2023 14:15:37', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '8', 'WIP.Adv', 'WIP Advance Management', 'M', null, null, null, '80', 'WIP Advance', '在制品高级', null, 'subapp_wip_adv', 'MES', null, null);



DELETE AD_AUTHORITY WHERE NAME IN ('DryRun','Pilot Manger','NpwManager','RunCard Manager','WIPSpecialManager');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2018041801', '0', 'N', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 11:18:41', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'DryRun', 'DryRun', 'M', null, null, '356034141659840512', '90', 'DryRun Management', '试跑管理', null, 'subapp_security', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('607', '0', 'Y', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 11:32:50', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'Pilot Manger', 'Pilot Management', 'M', null, null, '356034141659840512', '20', 'Pilot_Module', '先行批管理', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021012601', '0', 'Y', to_date('26-01-2021', 'dd-mm-yyyy'), null, to_date('11-07-2023 11:33:07', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'NpwManager', 'NPW Management', 'M', null, null, '356034141659840512', '30', 'NPW_Module', '控制批管理', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('63100', '0', 'Y', to_date('22-03-2021', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:33:18', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '5', 'RunCard Manager', 'RunCard Management', 'M', null, null, '356034141659840512', '40', 'RunCard_Module', 'RunCard管理', null, 'wip', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('356039136522256384', '0', 'Y', to_date('11-07-2023 11:32:25', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('11-07-2023 11:32:25', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'WIPSpecialManager', 'Special Operation Management', 'M', null, null, '356034141659840512', '10', 'Special_Operation', '在制品特殊管理', null, 'wip', 'MES', null, null);



DELETE AD_AUTHORITY WHERE NAME IN ('DryRun.Manager','SendMessageManager','DryRun.DryRunEquipmentManager','DryRunPolicyManager');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2018041802', '0', 'Y', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('23-09-2008', 'dd-mm-yyyy'), null, '0', 'DryRun.Manager', '试跑', 'F', 'E', '2018041802', '2018041801', '20', 'DryRun', '试跑', null, 'vertical', 'MES', 'com.glory.common.dryrun', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2018060701', '0', 'Y', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('23-09-2008', 'dd-mm-yyyy'), null, '0', 'SendMessageManager', '模拟消息', 'F', 'E', '2018060701', '2018041801', '30', 'SendMessage', '模拟消息', null, 'message', 'MES', 'com.glory.common.dryrun', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2018041804', '0', 'Y', to_date('09-10-2008', 'dd-mm-yyyy'), 'admin', to_date('09-10-2008', 'dd-mm-yyyy'), 'admin', '0', 'DryRun.DryRunEquipmentManager', '试跑设备', 'F', 'E', '2018041804', '2018041801', '30', 'DryRun Equipment', '试跑设备', null, 'step', 'MES', 'com.glory.common.dryrun', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2018041803', '0', 'Y', to_date('23-09-2008', 'dd-mm-yyyy'), null, to_date('23-09-2008', 'dd-mm-yyyy'), null, '0', 'DryRunPolicyManager', '试跑策略', 'F', 'E', '2018041803', '2018041801', '10', 'DryRunPolicy', '试跑策略', null, 'schedule', 'MES', 'com.glory.common.dryrun', null);


DELETE AD_AUTHORITY WHERE NAME IN ('WipAdv.PilotPlan','WipAdv.PilotFutureHold','WipAdv.PilotGroup','WipAdv.PilotByLot','Wip.PiLotQuery');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6887', '0', 'Y', to_date('08-12-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:37:57', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'WipAdv.PilotPlan', '先行批计划', 'F', 'E', '6887', '607', '10', 'Pilot Plan', '先行批计划', null, 'spec-template', 'MES', 'com.glory.mes.pilot', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6889', '0', 'Y', to_date('08-12-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:37:49', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'WipAdv.PilotFutureHold', '先行批模块关联', 'F', 'E', '6889', '607', '20', 'Pilot Procedure Relation', '先行批模块关联', null, 'hold-lot', 'MES', 'com.glory.mes.pilot', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021081201', '0', 'Y', to_date('08-12-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:38:06', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'WipAdv.PilotGroup', '先行批组(Group)', 'F', 'E', '2021081201', '607', '30', 'Pilot Group', '先行批组(Group)', null, 'split', 'MES', 'com.glory.mes.pilot', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202212151201', '0', 'Y', to_date('08-12-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:38:18', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'WipAdv.PilotByLot', '批次先行批计划', 'F', 'E', '202212151201', '607', '40', 'Pilot By Lot', '批次先行批计划', null, 'spec-template', 'MES', 'com.glory.mes.pilot', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2020092801', '0', 'Y', to_date('06-02-2009', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:38:41', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.PiLotQuery', 'WipPiLotQuery', 'F', 'E', '2020092801', '607', '50', 'PiLot Query', 'Pilot收集数据查询', null, 'wip-query', 'MES', 'com.glory.mes.pilot', null);




DELETE AD_AUTHORITY WHERE NAME IN ('Season.InstanceQuery','Npw.MonitorLotMerge','Npw.MonitorLot','Npw.MonitorLotQuery','Npw.MonitorLotDowngrade','Npw.MonitorLotUnCreat','Npw.MonitorLotRecyle','Npw.ControlContext','Npw.ControlContextOld'
,'Npw.MonitorLotEnd','Npw.Control','Npw.MonitorLotDowngradeBom','Npw.ControlInstanceQuery','Npw.DummyLot','Npw.SetupDummyGroup','Npw.DataTransferExcelImport','Season.RuleCreate');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20211125100', '0', 'Y', to_date('03-11-2021', 'dd-mm-yyyy'), 'admin', to_date('03-11-2021', 'dd-mm-yyyy'), 'admin', '1', 'Season.InstanceQuery', '控制批暖机查询', 'F', 'E', '20211125100', '2021012601', '1000', 'Season Instance Query', '控制批暖机查询', null, 'change-lot-info', 'MES', 'com.glory.common.season', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20201011401', '0', 'Y', to_date('01-01-2021', 'dd-mm-yyyy'), 'admin', to_date('01-01-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.MonitorLotMerge', '控制批合批', 'F', 'E', '2021011401', '2021012601', '580', 'Monitor Lot Merge', '控制批合批', null, 'merge-lot', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20201010601', '0', 'Y', to_date('01-01-2021', 'dd-mm-yyyy'), 'admin', to_date('01-01-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.MonitorLot', '控制批创建', 'F', 'E', '2021010601', '2021012601', '570', 'Lot NPW Use Start', '控制批创建', null, 'change-lot-info', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021012001', '0', 'Y', to_date('20-01-2021', 'dd-mm-yyyy'), 'admin', to_date('20-01-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.MonitorLotQuery', '控制批详情', 'F', 'E', '2021012001', '2021012601', '590', 'Monitor Lot Query', '控制批详情', null, 'change-lot-info', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021012101', '0', 'Y', to_date('21-01-2021', 'dd-mm-yyyy'), 'admin', to_date('21-01-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.MonitorLotDowngrade', '控制批降档', 'F', 'E', '2021012101', '2021012601', '600', 'Monitor Lot DownGrade', '控制批降档', null, 'newpart', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021020101', '0', 'N', to_date('01-02-2021', 'dd-mm-yyyy'), 'admin', to_date('01-02-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.MonitorLotUnCreat', '控制批取消创建', 'F', 'E', '2021020101', '2021012601', '575', 'Monitor Lot UnCreate', '控制批取消创建', null, 'merge', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021110302', '0', 'Y', to_date('03-11-2021', 'dd-mm-yyyy'), 'admin', to_date('03-11-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.MonitorLotRecyle', '控制批回收', 'F', 'E', '2021110302', '2021012601', '710', 'Lot NPW Recycle End', '控制批回收', null, 'monitorlotrecycle', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('21102902', '0', 'Y', to_date('26-05-2021', 'dd-mm-yyyy'), 'admin', to_date('26-05-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.ControlContext', 'Control Context', 'F', 'E', '21102902', '2021012601', '20', 'NPW ControlID Context', '控制项规则Context', null, 'basetable', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('21102901', '0', 'N', to_date('26-05-2021', 'dd-mm-yyyy'), 'admin', to_date('26-05-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.ControlContextOld', 'Control Context', 'F', 'E', '21102901', '2021012601', '20', 'NPW ControlID Context', '控制项规则Context', null, 'basetable', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20211011401', '0', 'Y', to_date('01-01-2021', 'dd-mm-yyyy'), 'admin', to_date('01-01-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.MonitorLotEnd', '控制批重用', 'F', 'E', '20211011401', '2021012601', '700', 'Lot NPW Use End', '控制批重用', null, 'monitorlotend', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021102801', '0', 'Y', to_date('01-01-2021', 'dd-mm-yyyy'), 'admin', to_date('01-01-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.Control', 'NPW Control', 'F', 'E', '2021102801', '2021012601', '10', 'NPW ControlID', '控制批规则设置', null, 'change-lot-info', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021110501', '0', 'Y', to_date('03-11-2021', 'dd-mm-yyyy'), 'admin', to_date('03-11-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.MonitorLotDowngradeBom', '控制批降档产品维护', 'F', 'E', '2021110501', '2021012601', '650', 'Monitor Lot Down Grade Part Maintenance', '控制批降档产品维护', null, 'bom', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021111701', '0', 'Y', to_date('03-11-2021', 'dd-mm-yyyy'), 'admin', to_date('03-11-2021', 'dd-mm-yyyy'), 'admin', '1', 'Npw.ControlInstanceQuery', '控制批实例查询', 'F', 'E', '2021111701', '2021012601', '800', 'NPW Control Instance Query', '控制批实例查询', null, 'change-lot-info', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2022040801', '0', 'Y', to_date('31-03-2022', 'dd-mm-yyyy'), 'admin', to_date('31-03-2022', 'dd-mm-yyyy'), 'admin', '1', 'Npw.DummyLot', '挡片批创建', 'F', 'E', '2022040801', '2021012601', '32', 'Dummy Lot Split', '挡片批创建', null, 'change-lot-info', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2022041201', '0', 'Y', to_date('31-03-2022', 'dd-mm-yyyy'), 'admin', to_date('31-03-2022', 'dd-mm-yyyy'), 'admin', '1', 'Npw.SetupDummyGroup', '挡片批组创建', 'F', 'E', '2022041201', '2021012601', '40', 'Setup Dummy Group', '挡片批组创建', null, 'change-lot-info', 'MES', 'com.glory.common.npw', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2022042501', '0', 'Y', to_date('09-10-2008', 'dd-mm-yyyy'), 'admin', to_date('09-10-2008', 'dd-mm-yyyy'), 'admin', '1', 'Npw.DataTransferExcelImport', 'Npw产品和流程数据导入', 'F', 'E', '2022042501', '2021012601', '850', 'NpwData Transfer Excel Import', '产品和流程数据导入', null, 'transation-history', 'MES', 'com.glory.mes.chjs', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202111250001', '0', 'Y', to_date('03-11-2021', 'dd-mm-yyyy'), 'admin', to_date('03-11-2021', 'dd-mm-yyyy'), 'admin', '1', 'Season.RuleCreate', '控制批暖机规则设置', 'F', 'E', '202111250001', '2021012601', '900', 'Season Control Create', '控制批暖机规则设置', null, 'basetable', 'MES', 'com.glory.common.season', null);



DELETE AD_AUTHORITY WHERE NAME IN ('Wip.RunCardSetup','Wip.RunCard','Wip.RunCardChangeFLowQuery','Wip.RunCardChangeFlow','Wip.RunCardChangeFlowByComp','Wip.RunCardMerge','Wip.RunCardSplit');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('631002', '0', 'Y', to_date('22-03-2021', 'dd-mm-yyyy'), 'admin', to_date('22-03-2021', 'dd-mm-yyyy'), 'admin', '1', 'Wip.RunCardSetup', 'EIN设置', 'F', 'E', '631002', '63100', '10', 'EIN Lot Setting', 'EIN设置', null, 'change-lot-info', 'MES', 'com.glory.common.runcard', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('631001', '0', 'Y', to_date('22-03-2021', 'dd-mm-yyyy'), 'admin', to_date('22-03-2021', 'dd-mm-yyyy'), 'admin', '1', 'Wip.RunCard', 'EIN查询', 'F', 'E', '631001', '63100', '20', 'EIN Query', 'EIN查询', null, 'schedule', 'MES', 'com.glory.common.runcard', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2021122210', '0', 'Y', to_date('22-12-2021', 'dd-mm-yyyy'), 'admin', to_date('22-12-2021', 'dd-mm-yyyy'), 'admin', '1', 'Wip.RunCardChangeFLowQuery', 'EIN查询（ChangeFLow）', 'F', 'E', '2021122210', '63100', '60', 'EIN ChangeFlow Query', 'EIN更改流程查询', null, 'schedule', 'MES', 'com.glory.common.runcard', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20211214001', '0', 'Y', to_date('22-03-2021', 'dd-mm-yyyy'), 'admin', to_date('22-03-2021', 'dd-mm-yyyy'), 'admin', '1', 'Wip.RunCardChangeFlow', 'EIN更改流程', 'F', 'E', '20211214001', '63100', '50', 'EIN ChangeFlow Setup', 'EIN更改流程', null, 'schedule', 'MES', 'com.glory.common.runcard', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('20211216001', '0', 'Y', to_date('16-03-2022', 'dd-mm-yyyy'), 'admin', to_date('16-03-2022', 'dd-mm-yyyy'), 'admin', '1', 'Wip.RunCardChangeFlowByComp', 'EIN按组件更改流程', 'F', 'E', '20211216001', '63100', '70', 'EIN ChangeFlow ByComp Setup', 'EIN按组件更改流程', null, 'bylot', 'MES', 'com.glory.common.runcard', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('631004', '0', 'Y', to_date('22-03-2021', 'dd-mm-yyyy'), 'admin', to_date('22-03-2021', 'dd-mm-yyyy'), 'admin', '1', 'Wip.RunCardMerge', 'EIN结束', 'F', 'E', '631004', '63100', '40', 'EIN Lot End', 'EIN结束', null, 'merge', 'MES', 'com.glory.common.runcard', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('631003', '0', 'Y', to_date('22-03-2021', 'dd-mm-yyyy'), 'admin', to_date('22-03-2021', 'dd-mm-yyyy'), 'admin', '1', 'Wip.RunCardSplit', 'EIN开始', 'F', 'E', '631003', '63100', '30', 'EIN Lot Start', 'EIN开始', null, 'split', 'MES', 'com.glory.common.runcard', null);



DELETE AD_AUTHORITY WHERE NAME IN ('Wip.OutSource','Wip.ScrapSupplement','Wip.LotComponentBondiong','Wip.UnScrapSorter','Wip.LotReplenish','Wip.EquipmentBufferCarrier','Wip.ComponentGrade');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('170706', '0', 'Y', to_date('10-09-2014', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:46:55', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.OutSource', '委外', 'F', 'E', '17706', '356039136522256384', '200', 'Lot OutSourcing', '委外', null, 'production_order', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('29001', '0', 'Y', to_date('09-10-2008', 'dd-mm-yyyy'), null, to_date('11-07-2023 11:36:32', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.ScrapSupplement', '按片报废补充', 'F', 'E', '29001', '356039136522256384', '30', 'Lot Component Replenish', '按片报废补充', null, 'supplement', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('18100', '0', 'Y', to_date('17-09-2020', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:41:10', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.LotComponentBondiong', '键合(绑定)', 'F', 'E', '18100', '356039136522256384', '530', 'Component Bonding', '键合(绑定)', null, 'bonding', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('63103', '0', 'Y', to_date('05-12-2008', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:36:16', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.UnScrapSorter', '取消报废(Sorter)', 'F', 'E', '63103', '356039136522256384', '10', 'Lot UnScrap By Sorter', '取消报废(Sorter)', null, 'unscrap', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2019042501', '0', 'Y', to_date('25-04-2019', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:36:25', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.LotReplenish', '报废补充', 'F', 'E', '2019042501', '356039136522256384', '20', 'Lot Replenish', '报废补充', null, 'hold-lot', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('60157', '0', 'Y', to_date('12-10-2021', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 12:41:10', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.EquipmentBufferCarrier', 'Equipment Buffer Carrier', 'F', 'E', '60157', '356039136522256384', '570', 'Equipment Buffer Carrier', '设备Buffer载具', null, 'trackmove', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6174674', '0', 'Y', to_date('03-11-2021', 'dd-mm-yyyy'), 'admin', to_date('11-07-2023 11:36:39', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.ComponentGrade', 'Component Grade', 'F', 'E', '20211026001', '356039136522256384', '40', 'Component Grade', '模组分档', null, 'material', 'MES', 'com.glory.mes.wip', null);

