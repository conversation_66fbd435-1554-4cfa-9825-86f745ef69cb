
delete from ad_message where <PERSON><PERSON><PERSON>_<PERSON> in ('wip.lot_grade');

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values (2020080501, 0, 'Y', 'wip.lot_grade', null, '等级', null);

delete from ad_reftable where NAME in ('WIPWorkOrderName');

insert into ad_reftable (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, KEY_FIELD, TEXT_FIELD, WHERE_CLAUSE, ORDER_BY_CLAUSE, IS_DISTIC)
values (2020080601, 0, 'Y', to_date('06-08-2020 13:11:00', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('06-08-2020 13:11:27', 'dd-mm-yyyy hh24:mi:ss'), 'admin', 2, 'WIPWorkOrderName', '工单-name', 125500, 'docId', 'docId', null, null, null);

DELETE FROM AD_FIELD WHERE TABLE_RRN IN (SELECT OBJECT_RRN FROM AD_TABLE WHERE NAME in ('WIPLotProcessorChangeWo'));
DELETE FROM AD_TABLE WHERE NAME IN  ('WIPLotProcessorChangeWo');

insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE)
values (17452, 0, 'Y', to_date('09-07-2020 09:51:39', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('09-07-2020 09:51:39', 'dd-mm-yyyy hh24:mi:ss'), 'admin', 1, 'WIPLotProcessorChangeWo', '批量修改工单', 'WIP_LOT', 'N', 'Lot', 'com.glory.mes.wip.model.Lot', null, null, null, 'N', null, null, 'WIPLotProcessorChangeWo', '批量修改工单', null, 0);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE)
values (17502, 0, 'Y', to_date('09-07-2020 09:54:27', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('06-08-2020 13:23:00', 'dd-mm-yyyy hh24:mi:ss'), 'admin', 4, 'woId', '工单', null, 17452, null, 10, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', null, null, null, null, null, 2020080601, null, null, null, null, 'woId', '工单', 'N', 'N', 'N', null, null, null, null, 'N', null, 0);

