delete AD_REFLIST where REFERENCE_NAME = 'ADVariableDataType';
insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('351759688042295296', '0', 'Y', to_date('29-06-2023 16:07:25', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-06-2023 16:07:25', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'ADVariableDataType', 'com.glory.mes.tcard.model.TCardStep', 'TCardStep', '160', 'TCardStep', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('334286220984180736', '0', 'Y', to_date('12-05-2023 10:54:05', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('25-06-2023 10:23:28', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'ADVariableDataType', 'java.lang.String', 'String', '10', 'String', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('334286220984180737', '0', 'Y', to_date('12-05-2023 10:54:05', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-06-2023 15:51:52', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'ADVariableDataType', 'java.lang.Integer', 'Integer', '20', 'Integer', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('334286220984180738', '0', 'Y', to_date('12-05-2023 10:54:05', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-06-2023 15:51:52', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'ADVariableDataType', 'java.lang.Boolean', 'Boolean', '30', 'Boolean', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('335720971993440256', '0', 'Y', to_date('16-05-2023 09:55:17', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-06-2023 15:57:04', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'ADVariableDataType', 'java.util.Date', 'Date', '40', 'Date', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('335720971993440258', '0', 'Y', to_date('16-05-2023 09:55:17', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-06-2023 15:52:06', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'ADVariableDataType', 'java.lang.Double', 'Double', '60', 'Double', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('335720971997634560', '0', 'Y', to_date('16-05-2023 09:55:17', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-06-2023 15:56:45', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'ADVariableDataType', 'org.apache.poi.hpsf.Decimal', 'Decimal', '70', 'Decimal', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('335720971997634561', '0', 'Y', to_date('16-05-2023 09:55:17', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-06-2023 15:53:37', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'ADVariableDataType', 'java.lang.Float', 'Float', '80', 'Float', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('335720971997634562', '0', 'Y', to_date('16-05-2023 09:55:17', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-06-2023 15:55:15', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'ADVariableDataType', 'java.lang.Long', 'Long', '90', 'Long', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('335720971997634564', '0', 'Y', to_date('16-05-2023 09:55:17', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-06-2023 15:55:31', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'ADVariableDataType', 'java.lang.Number', 'Number', '110', 'Number', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('335720971997634565', '0', 'Y', to_date('16-05-2023 09:55:17', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-06-2023 15:53:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'ADVariableDataType', 'java.lang.Short', 'Short', '120', 'Short', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('351750508089769984', '0', 'Y', to_date('29-06-2023 15:30:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('29-06-2023 15:30:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'ADVariableDataType', 'com.glory.mes.wip.model.Lot', 'Lot', '150', 'Lot', 'Y');
