
delete ad_message where OBJECT_RRN = '202403111755001';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755001', '0', 'Y', 'wip-1000: wip.lot_prepare_state_not_allow', 'The lot prepare state not allow!', '准备批次状态不允许!', null);

delete ad_message where OBJECT_RRN = '202403111755002';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755002', '0', 'Y', 'wip-1001: wip.lot_prepare_muilt_batch_not_allow', 'The lot prepare job muilt batch not allow!', '批次作业准备不允许多个Batch组合!', null);

delete ad_message where OBJECT_RRN = '202403111755003';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755003', '0', 'Y', 'wip-1002: wip.unsupport_evaluate_if_node', 'The unsupport evaluate if node!', '不支持评估节点！!', null);

delete ad_message where OBJECT_RRN = '202403111755004';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755004', '0', 'Y', 'wip-1003: wip.lot_unscrap_step_not_match', 'The scrap step does not match the current step!', '报废时的工步与当前工步不匹配！', null);

delete ad_message where OBJECT_RRN = '202403111755005';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755005', '0', 'Y', 'wip-1004: wip.lot_unscrap_state_not_match', 'The scrap status does not match the current status!', '报废时的状态与当前状态不匹配！', null);

delete ad_message where OBJECT_RRN = '202403111755006';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755006', '0', 'Y', 'wip-1005: wip.procedure_is_not_temp', 'The procedure is not temp!', '流程不是临时流程!', null);

delete ad_message where OBJECT_RRN = '202403111755007';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755007', '0', 'Y', 'wip-1006: wip.step_cannot_repeat', 'The step cannot repeat!', '这一步不能重复！', null);

delete ad_message where OBJECT_RRN = '202403111755008';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755008', '0', 'Y', 'wip-1007: wip.lot_not_trackout_hold', 'The lot not trackout hold!', '批次不能Hold出站!', null);

delete ad_message where OBJECT_RRN = '202403111755009';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755009', '0', 'Y', 'wip-1009: wip.ocap_hold_code_not_exist', 'The ocap hold code not exist!', 'Ocap Hold码不存在!', null);

delete ad_message where OBJECT_RRN = '202403111755010';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755010', '0', 'Y', 'wip-1010: wip.ocap_id_is_exist', 'The ocap id is exist!', 'Ocap ID 已存在!', null);

delete ad_message where OBJECT_RRN = '202403111755011';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755011', '0', 'Y', 'wip-1011: wip.component_process_state_not_allow', 'This operation is not allowed in component processing status!', '组件加工状态不允许此操作!', null);

delete ad_message where OBJECT_RRN = '202403111755012';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755012', '0', 'Y', 'wip-1012: wip.lot_hold_by_other_hold_code', 'The lot hold by other hold code!', '该批次由其他Hold代码Hold!', null);

delete ad_message where OBJECT_RRN = '202403111755013';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755013', '0', 'Y', 'wip-1013: wip.hold_code_is_repeat', 'Hold Code Is Repeat!', 'Hold代码重复!', null);

delete ad_message where OBJECT_RRN = '202403111755014';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755014', '0', 'Y', 'wip-1014: wip.hold_code_ocap_is_repeat', 'The hold code ocap is_repeat!', 'Ocap Hold代码重复!', null);

delete ad_message where OBJECT_RRN = '202403111755015';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755015', '0', 'Y', 'wip-1015: wip.ocap_diff_id_is_exist', 'The ocap diff id is exist', '存在不同的ocapId!', null);

delete ad_message where OBJECT_RRN = '202403111755016';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755016', '0', 'Y', 'wip-1016: wip.lot_state_is_not_wait', 'The lot state is not wait', '批次状态不是wait!', null);

delete ad_message where OBJECT_RRN = '202403111755017';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755017', '0', 'Y', 'wip-1017: wip.lot_mainmattype_is_different', 'Lot Mainmattype Is Different!', '批次主mat类型不同!', null);

delete ad_message where OBJECT_RRN = '202403111755018';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755018', '0', 'Y', 'wip-1018: wip.lot_is_holdstate', 'Lot Is Holdstate!', '当前批次处于暂停状态!', null);

delete ad_message where OBJECT_RRN = '202403111755019';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755019', '0', 'Y', 'wip-1020: wip.newpart_different_maintype', 'New Part Different Maintype!', '新产品主类型不同!', null);

delete ad_message where OBJECT_RRN = '202403111755020';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755020', '0', 'Y', 'wip-1021: wip.lots_muilt_batch_not_allow', 'Batch not Allow!', 'Batch不允许!', null);

delete ad_message where OBJECT_RRN = '202403111755021';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755021', '0', 'Y', 'wip-1022: wip.lots_batch_incomplete', 'Lot Batch Incomplete!', '批次Batch不完整!', null);

delete ad_message where OBJECT_RRN = '202403111755022';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755022', '0', 'Y', 'wip-1023: wip.batch_is_different_selected', 'The lots of the batch you selected is incomplete or there are multiple batches!', '您选择的批次的batch不完整或者存在多个batches!', null);

delete ad_message where OBJECT_RRN = '202403111755023';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755023', '0', 'Y', 'wip-1024: wip.batch_is_different_batch_job', 'The lots you selected does not match the batch job!!', '您选择的批次与batch job不匹配!', null);

delete ad_message where OBJECT_RRN = '202403111755024';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755024', '0', 'Y', 'wip-1025: wip.measure_lot_is_different_except', 'The lots<%2$s> you selected is not a measurement lot<%1$s>!', '您选择的批次<%2$s>不是量测批次<%1$s>!', null);

delete ad_message where OBJECT_RRN = '202403111755025';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755025', '0', 'Y', 'wip-1030: wip.lot_scrap_must_all_qty', 'Lot Scrap Must All Qty!', '批次必须报废全部数量!', null);

delete ad_message where OBJECT_RRN = '202403111755026';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755026', '0', 'Y', 'wip-1033: wip.lot_exist_when_delete_equipment', 'A lot is being processed. This equipment cannot be deleted!', '有批次正在加工，不能删除该设备!', null);

delete ad_message where OBJECT_RRN = '202403111755027';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755027', '0', 'Y', 'wip-1081: wip.lot_prepare_batch_not_complete#', 'The currently selected batch is not complete. Please check the missing lots <%1$s>!', '当前选择的Batch不完整，缺失批次<%1$s>，请检查!', null);

delete ad_message where OBJECT_RRN = '202403111755028';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755028', '0', 'Y', 'wip-1100: wip.unsupport_sorting_mode', 'Unsupport Sorting Mode!', '不支持Sorting模式!', null);

delete ad_message where OBJECT_RRN = '202403111755029';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755029', '0', 'Y', 'wip-1101: wip.sorting_component_not_match_lot_component', 'Sorting Component Not Match Lot Component!', 'Soring组件不匹配!', null);

delete ad_message where OBJECT_RRN = '202403111755030';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755030', '0', 'Y', 'wip-1102: wip.sorting_from_position_not_exist', 'Sorting From Position Not Exist!', 'Soring原位置不存在!', null);

delete ad_message where OBJECT_RRN = '202403111755031';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755031', '0', 'Y', 'wip-1103: wip.sorting_to_position_not_exist', 'Sorting To Position Not Exist!', 'Soring目标位置不存在!', null);

delete ad_message where OBJECT_RRN = '202403111755032';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755032', '0', 'Y', 'wip-1104: wip.sorting_component_in_multi_durable', 'Sorting Component In Multi Durable!', 'Soring组件在多个载具!', null);

delete ad_message where OBJECT_RRN = '202403111755033';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755033', '0', 'Y', 'wip-1105: wip.sorting_component_not_in_durable', 'Sorting Component Not In Durable!', 'Soring组件未绑定载具!', null);

delete ad_message where OBJECT_RRN = '202403111755034';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755034', '0', 'Y', 'wip-1106: wip.sorting_job_not_ready', 'Sorting Job Not Ready!', 'Sorting作业未准备就绪!', null);

delete ad_message where OBJECT_RRN = '202403111755035';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755035', '0', 'Y', 'wip-1107: wip.sorting_job_from_carrier_not_ready', 'Sorting Job From Carrier Not Ready!', 'Sorting原载具未准备就绪!', null);

delete ad_message where OBJECT_RRN = '202403111755036';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755036', '0', 'Y', 'wip-1108: wip.sorting_job_to_carrier_not_ready', 'Sorting Job To Carrier Not Ready!', 'Sorting目标载具未准备就绪!', null);

delete ad_message where OBJECT_RRN = '202403111755037';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755037', '0', 'Y', 'wip-1109: wip.sorting_job_can_not_reserve', 'Sorting Job Can Not Reserve!', '无法保留Sorting作业!', null);

delete ad_message where OBJECT_RRN = '202403111755038';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755038', '0', 'Y', 'wip-1110: wip.sorting_job_to_durable_is_exist', 'Sorting Job To Durable Is Exist!', 'Sorting作业目标载具已存在!', null);

delete ad_message where OBJECT_RRN = '202403111755039';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755039', '0', 'Y', 'wip-1111: wip.sorting_job_multi_batch_exist', 'Sorting Job Multi Batch Exist!', 'Sorting存在多个Batch作业!', null);

delete ad_message where OBJECT_RRN = '202403111755040';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755040', '0', 'Y', 'wip-1112: wip.sorting_job_info_error', 'Sorting Job Info Error<%1$s>!', 'Sorting 作业错误信息：<%1$s>！!', null);

delete ad_message where OBJECT_RRN = '202403111755041';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755041', '0', 'Y', 'wip-1113: wip.sorting_job_is_not_found', 'Sorting Job Is Not Found!', 'Sorting作业未找到!', null);

delete ad_message where OBJECT_RRN = '202403111755042';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755042', '0', 'Y', 'wip-1114: wip.split_sorting_job_size_over_limit', 'Split Sorting Job Size Over Limit!', 'Sorting作业分批大小超出限制!', null);

delete ad_message where OBJECT_RRN = '202403111755043';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755043', '0', 'Y', 'wip-2022: prd.jumpto_node_not_correct', 'Jumpto Node Not Correct!', '跳转节点不正确!', null);

delete ad_message where OBJECT_RRN = '202403111755044';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755044', '0', 'Y', 'wip-2023: prd.jumpto_node_not_in_procedure', 'Jumpto Node Not In Procedure!', '跳转节点不在流程中!', null);

delete ad_message where OBJECT_RRN = '202403111755045';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755045', '0', 'Y', 'wip-2110: prd.step_state_name_is_empty', 'Step State <%1$s> Name Is Empty!', '工步<%1$s>名称为空!', null);

delete ad_message where OBJECT_RRN = '202403111755046';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755046', '0', 'Y', 'wip-2111: prd.step_sstate_name_is_repeat', 'Step Sstate Name <%1$s> Is Repeat!', '工步<%1$s>名称重复!', null);

delete ad_message where OBJECT_RRN = '202403111755047';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755047', '0', 'Y', 'wip-2112: prd.procedure_state_name_is_empty', 'Procedure State <%1$s> Name Is Empty!', '流程<%1$s>名称为空!', null);

delete ad_message where OBJECT_RRN = '202403111755048';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111755048', '0', 'Y', 'wip-2113: prd.procedure_state_name_is_repeat', 'Procedure State <%1$s> Name Is Repeat!', '流程<%1$s>名称重复!', null);

DELETE AD_AUTHORITY WHERE NAME IN ('Wip.UnScrapNew','Wip.UnScrapSorter');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('63102', '0', 'Y', to_date('05-12-2008', 'dd-mm-yyyy'), 'admin', to_date('07-11-2023 15:27:59', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '5', 'Wip.UnScrapNew', '取消报废', 'F', 'E', '63102', '631', '50', 'Lot UnScrap', '取消报废', null, 'unscrap', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('63103', '0', 'Y', to_date('05-12-2008', 'dd-mm-yyyy'), 'admin', to_date('07-11-2023 15:30:09', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'Wip.UnScrapSorter', '取消报废(Sorter)', 'F', 'E', '63103', '631', '60', 'Lot UnScrap By Sorter', '取消报废(Sorter)', null, 'unscrap', 'MES', 'com.glory.mes.wip', null);

