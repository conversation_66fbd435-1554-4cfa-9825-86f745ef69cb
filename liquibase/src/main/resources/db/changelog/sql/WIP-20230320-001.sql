delete AD_MESSAGE where KEY_ID = 'wip-005004: wip.holdstate_is_not_the_latest_data';

INSERT INTO AD_MESSAGE
(OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
VALUES(20220504099, 0, 'Y', 'wip-005004: wip.holdstate_is_not_the_latest_data', 'Current hold status:<%1$s>,Actual hold status:<%2$s>. Please refresh the data!', '当前hold状态:<%1$s>,实际hold状态:<%2$s>。请刷新数据!', NULL);

delete AD_MESSAGE where KEY_ID = 'wip-005003: wip.step_material_main_mat_type_is_not_match';

INSERT INTO AD_MESSAGE
(OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
VALUES(20220428099, 0, 'Y', 'wip-005003: wip.step_material_main_mat_type_is_not_match', 'Step:<%1$s> material mat TYPE:<%2$s> is not MATCH', '工步:<%1$s> 工步物料类型:<%2$s>不匹配', NULL);

update ad_table t set t.label = 'Step Query#Select a step data', t.label_zh = '工步查询#选择一条工步数据' where t.object_rrn = 148365166688436224;

delete ad_message where KEY_ID = 'prd.change_part_contains_change_flow';

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20230320077', '0', 'Y', 'prd.change_part_contains_change_flow', 'The target procedure contains a lot change flow that will be deleted after product change. Are you sure you want to execute it?', '目标模块中，包含批次特殊流程，改产品后将会被删除，是否确认要执行？', null);
