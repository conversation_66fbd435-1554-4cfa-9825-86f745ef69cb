--包装规则message补充
DELETE AD_MESSAGE WHERE OBJECT_RRN = '202403290934001';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403290934001', '0', 'Y', 'mm-2178: mm.same_name_default_pack_rule_exist_only_one', 'Same Name Default Pack Rule Exist Only One', '相同名称的默认规则只能存在一个!', null);

--设备管理删除两个无用字段
DELETE AD_FORM_RELEASE where name = 'RASEquipmentManager';
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'RASEquipmentManager' NAME, TO_TIMESTAMP('2024/03/29 10:14:40', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('315876989033910359', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
--进出站配置栏位描述修改 
DELETE AD_FORM_RELEASE where name = 'WIPTrackConfigManager';
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPTrackConfigManager' NAME, TO_TIMESTAMP('2024/03/29 15:32:30', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('438357957005131790', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 		
--工单领料缺失参考表
DELETE AD_REFTABLE WHERE OBJECT_RRN = '444550964310347776';
insert into AD_REFTABLE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, KEY_FIELD, TEXT_FIELD, WHERE_CLAUSE, ORDER_BY_CLAUSE, IS_DISTIC, IS_QUERY_BY_FIELD)
values ('444550964310347776', '0', 'Y', to_date('11-03-2024 17:26:49', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('11-03-2024 17:27:11', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'MMWoRackAreaByWarehouse', '参考目标位置--name', '5282', 'name', 'name', 'warehouseRrn = :transTargetWarehouseRrn', 'name', null, 'N');

--工单投料页面message补充
DELETE AD_MESSAGE WHERE OBJECT_RRN = '202403290934002';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403290934002', '0', 'Y', 'mm.mlot_can_not_be_empty', 'MLot Can Not Be Empty', '物料批不能为空!', null);