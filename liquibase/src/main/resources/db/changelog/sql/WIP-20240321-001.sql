DELETE AD_EDITOR WHERE OBJECT_RRN = '6210';
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('6210', '0', 'Y', 'WIPTrackOutHistoryManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.history.trackout.WIPTrackOutHistoryManagerEditor', '6210', 'WIPTrackOutHistoryManager', null, 'production-history', null);

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPTrackOutHistoryManager' NAME, TO_TIMESTAMP('2024/03/19 11:20:13', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('447353263445438468', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');

DELETE AD_EDITOR WHERE OBJECT_RRN = '653';
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('653', '0', 'Y', 'WIPLotTransHisManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.history.WIPLotTransHisManagerEditor', '620', 'WIPLotTransHisManager', null, 'transation-history', null);

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPLotTransHisManager' NAME, TO_TIMESTAMP('2024/03/19 15:22:59', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('447401372649189377', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPScrapSBDHisViewDialog' NAME, TO_TIMESTAMP('2024/03/22 15:27:48', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('447418151643574274', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPSplitLotInfoDialog' NAME, TO_TIMESTAMP('2024/03/22 15:27:36', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('447419162957377541', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');

DELETE ad_field WHERE TABLE_RRN = '615';
DELETE ad_tab WHERE TABLE_RRN = '615';
DELETE ad_table WHERE OBJECT_RRN = '615';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 615, 0, 'Y', to_date('2008-12-01', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 157, 'WIPLotQuery', '批次查询', 'WIP_LOT', 'N', 'Lot', 'com.glory.mes.wip.model.Lot', null, 'stepName, stepVersion, lotId', '1!=1', 'N', 2, 3, 'LotQuery', '批次查询', null, 3); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 61510, 0, 'Y', to_date('2009-07-21 15:16:20', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 150, 'LotInfo', '批次基本信息', 615, 10, 3, 'Basic Info', '基本信息', null, null, null, 0); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 125693, 0, 'Y', to_date('2010-09-06 14:49:38', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 155, 'partName', '产品名称', null, 615, null, 10, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'Y', 'N', 'reftablecombo', null, null, null, null, null, 4191652, null, null, null, null, 'Product ID', '产品名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 4190223, 0, 'Y', to_date('2014-05-07 11:25:37', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 149, 'partVersion', '产品版本', null, 615, 61510, 12, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftablecombo', 'string', null, null, null, null, 4190244, null, null, null, null, 'Product Version', '产品版本', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61516, 0, 'Y', to_date('2008-11-21 09:24:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 169, 'woId', '工单号码', null, 615, 61510, 20, 28, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'WO ID', '工单号码', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61501, 0, 'Y', to_date('2008-11-20 19:24:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 176, 'lotId', '批次号码', null, 615, null, 25, 24, 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'Y', 'Y', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Lot ID', '批次号码', 'Y', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61512, 0, 'Y', to_date('2008-11-21 09:50:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 176, 'stepId', '工步名称', null, 615, 61510, 28, 22, 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Step ID', '工序号', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 207512631383572480, 0, 'Y', to_date('2022-05-27 15:01:06', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 16, 'attribute3', 'n2stock', null, 615, null, 29, null, 'N', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'N2Stock', 'N2Stock', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 183648668895690752, 0, 'Y', to_date('2022-03-22 18:34:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 22, 'attribute1', '设备能力', null, 615, 61510, 30, null, 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'WSG', 'WSG', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 156435, 0, 'Y', to_date('2010-10-08 15:27:43', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 167, 'stepDesc', '工步描述', null, 615, 61510, 32, 24, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Step Desc', '工步描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 178820434251915264, 0, 'Y', to_date('2022-03-09 10:48:33', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'lastArriveDurationHours', '当站时长', null, 615, 61510, 33, 20, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Last Arrive Duration(H)', '当站时长(H)', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 207522369676656640, 0, 'Y', to_date('2022-05-27 15:39:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 12, 'expireTime', '最大QTime到期时间', null, 615, null, 35, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'calendar', 'date', null, null, null, null, null, null, null, null, null, 'Remain QTime (Max)', '最大QTime到期时间', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 116239978345910272, 0, 'Y', to_date('2021-09-17 18:16:10', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 147, 'durable', '载具号码', null, 615, 61510, 40, 24, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Carrier ID', '载具号码', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61513, 0, 'Y', to_date('2008-11-21 09:29:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 158, 'comClass', '状态大类', null, 615, 61510, 50, 32, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'LotComClass', null, null, null, 'StateCategory', '状态大类', 'Y', 'N', 'Y', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61528, 0, 'Y', to_date('2008-11-21 09:18:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 159, 'startTime', '投产日期开始', null, 615, 61510, 54, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'datetime', 'time', null, null, null, null, null, null, null, null, null, 'Lot Start Time', '投产日期开始', 'Y', 'N', 'N', 'calendarfromto', null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61529, 0, 'Y', to_date('2008-11-21 09:20:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 151, 'endTime', '投产日期结束', null, 615, 61510, 56, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'datetime', 'time', null, null, null, null, null, null, null, null, null, 'Lot End Time', '投产日期结束', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61514, 0, 'Y', to_date('2008-11-24 14:19:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 46, 'cstate', '状态', null, 615, 61510, 60, 20, 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'State', '状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 178161261207482368, 0, 'Y', to_date('2022-03-07 15:09:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 17, 'remainTimeHours', '状态持续时长', null, 615, 61510, 61, 20, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'DurationTime(H)', '状态持续时长(H)', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61515, 0, 'Y', to_date('2008-11-24 14:20:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 150, 'subState', '子状态', null, 615, 61510, 62, 20, 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'SubState', '子状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 133138217219571712, 0, 'Y', to_date('2021-11-03 09:23:44', 'yyyy-MM-dd HH24:mi:ss'), 'yergoo', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 22, 'holdState', '暂停状态', null, 615, null, 70, 20, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'HoldState', null, null, null, 'Hold State', '暂停状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61505, 0, 'Y', to_date('2008-11-21 09:08:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 151, 'lotType', '批次类型', null, 615, 61510, 80, 20, 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'userreflist', 'string', null, null, null, null, 14479, null, 'LotType', null, null, 'Lot Type', '批次类型', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61508, 0, 'Y', to_date('2008-11-21 09:22:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 153, 'mainQty', '批次数量', null, 615, 61510, 90, 20, 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'Wafer Qty', '批次数量', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61504, 0, 'Y', to_date('2008-11-21 09:08:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 150, 'partType', '产品类型', null, 615, 61510, 100, 20, 'N', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Product Type', '产品类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61507, 0, 'Y', to_date('2008-11-21 09:40:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 161, 'priority', '优先级', null, 615, 61510, 110, 20, 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'userreflist', 'integer', null, null, null, null, 14479, null, 'LotPriority', null, null, 'Priority', '优先级', 'Y', 'N', 'N', 'reftablemulti', null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 135079251181985792, 0, 'Y', to_date('2021-11-08 17:56:43', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 27, 'processId', '工艺名称', null, 615, null, 120, null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Process Id', '工艺名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 280452591602032640, 0, 'Y', to_date('2020-11-11 14:43:36', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 180, 'processName', '工艺描述', null, 615, 61510, 130, null, 'N', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 280451294953922560, null, null, null, null, 'Process Desc', '工艺描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 2, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61511, 0, 'Y', to_date('2008-11-21 09:46:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 173, 'procedureId', '模块名称', null, 615, 61510, 140, 32, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Procedure ID', '模块名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 280449382716203008, 0, 'Y', to_date('2020-11-11 14:30:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 179, 'procedureName', '模块描述', null, 615, 61510, 150, null, 'N', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 280464030186287104, null, null, null, null, 'Procedure Desc', '模块描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 2, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 450496, 0, 'Y', to_date('2011-05-16 10:18:10', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 153, 'created', '生成时间', null, 615, 61510, 190, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'calendarfromto', 'date', null, null, null, null, null, null, null, null, null, 'Lot Start Time', '生成时间', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61530, 0, 'Y', to_date('2008-11-21 09:32:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 158, 'stateEntryTime', '状态修改时间', null, 615, 61510, 192, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'calendar', 'time', null, null, null, null, null, null, null, null, null, 'State Entry Time', '状态修改时间', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61535, 0, 'Y', to_date('2008-11-21 09:37:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 164, 'queueTime', '当站时间', null, 615, 61510, 194, null, 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'calendar', 'time', null, null, null, null, null, null, null, null, null, 'Last Arrive Time', '当站时间', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 184012557675180032, 0, 'Y', to_date('2022-03-23 18:40:12', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 12, 'attribute2', '批次物理状态', null, 615, 61510, 205, null, 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Physical Status', '批次物理状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 235814967111200768, 0, 'Y', to_date('2022-08-13 17:24:29', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 13, 'udf.runCardId', 'Run Card ID', null, 615, 61510, 206, 24, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Run Card ID', 'Run Card ID', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 193999, 0, 'Y', to_date('2011-03-11 17:36:16', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 183, 'location', '位置', null, 615, 61510, 210, 24, 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 1110, null, null, null, null, 'Location', '位置', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 161427109884059648, 0, 'Y', to_date('2022-01-20 10:53:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 13, 'subLocation', 'BAY', null, 615, 61510, 211, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 140054374482202624, null, null, null, null, 'BAY', 'BAY', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 127800297233846272, 0, 'Y', to_date('2021-10-19 15:52:45', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 144, 'stepName', '工步名称(查询)', null, 615, null, 212, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'Y', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Step ID', '工步名称(查询)', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 283706220823945216, 0, 'Y', to_date('2020-11-20 14:12:21', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 150, 'equipmentId', '设备号码', null, 615, null, 214, 26, 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Equipment ID', '设备号码', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61510, 0, 'Y', to_date('2008-11-21 09:45:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 166, 'equipmentId', '设备描述', null, 615, 61510, 216, 32, 'N', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 280448063347548160, null, null, null, null, 'Equipment Desc', '设备描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 2, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 156441, 0, 'Y', to_date('2010-10-08 15:31:59', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 152, 'recipeName', '程序菜单', null, 615, null, 218, 32, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Recipe Name', '程序菜单', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61509, 0, 'Y', to_date('2008-11-21 09:23:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 152, 'subQty', '子数量', null, 615, 61510, 220, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'Wafer Qty', '片数', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61522, 0, 'Y', to_date('2009-02-05 19:45:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 148, 'planStartDate', '计划开始日期', null, 615, 61510, 230, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'calendar', 'date', null, null, null, null, null, null, null, null, null, 'Plan Start Date', '计划开始日期', 'N', 'N', 'N', 'datetime', null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61523, 0, 'Y', to_date('2008-11-21 09:42:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 148, 'planEndDate', '计划完成日期', null, 615, 61510, 240, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'calendar', 'date', null, null, null, null, null, null, null, null, null, 'PlanEndDate', '计划完成日期', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61521, 0, 'Y', to_date('2008-11-21 09:41:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 150, 'requireDate', '交货日期', null, 615, 61510, 250, 32, 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'calendar', 'date', null, null, null, null, null, null, null, null, null, 'RequireDate', '交货日期', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 1945329, 0, 'Y', to_date('2013-05-28 13:30:24', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 148, 'deliveryDate', '交货日期', null, 615, null, 252, null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'calendar', 'time', null, null, null, null, null, null, null, null, null, 'deliveryDate', '交货日期', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61517, 0, 'Y', to_date('2008-11-21 09:27:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 150, 'customerOrder', '客户订单', null, 615, 61510, 260, 64, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Customer Order', '客户订单', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61518, 0, 'Y', to_date('2008-12-30 14:00:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 152, 'customerPartId', '客户品名', null, 615, 61510, 270, 32, 'Y', 'N', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Customer Product Name', '客户品名', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61519, 0, 'Y', to_date('2008-11-21 09:26:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 149, 'customerLotId', '客户批号', null, 615, 61510, 280, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Customer Lot ID', '客户批号', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61524, 0, 'Y', to_date('2008-11-21 09:14:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 149, 'startMainQty', 'StartMain QTY', null, 615, 61510, 310, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'Start Main Qty', '投料主数量', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61525, 0, 'Y', to_date('2008-11-21 09:15:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 149, 'startSubQty', 'StartSub QTY', null, 615, 61510, 320, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Start Card Qty', '投料片数', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61526, 0, 'Y', to_date('2008-11-21 09:16:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 150, 'endMainQty', 'End WaferQTY', null, 615, 61510, 330, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'Completed Main Qty', '完成主数量', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61527, 0, 'Y', to_date('2008-11-21 09:17:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 149, 'endSubQty', 'End DieQTY', null, 615, 61510, 340, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Completed Card Qty', '完成片数', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61531, 0, 'Y', to_date('2008-11-21 09:35:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 149, 'preComClass', '前状态大类', null, 615, 61510, 380, 32, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'PreStateCategory', '前状态大类', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61532, 0, 'Y', to_date('2008-11-21 09:35:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 149, 'preState', '前状态小类', null, 615, 61510, 390, 32, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'PreStateSubCategory', '前状态小类', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61533, 0, 'Y', to_date('2008-11-21 09:35:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 149, 'preSubState', '前状态', null, 615, 61510, 400, 32, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'PreState', '前状态', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61534, 0, 'Y', to_date('2008-11-21 09:37:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 149, 'preStateEntryTime', '前状态修改时间', null, 615, 61510, 410, null, 'Y', 'N', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'calendar', 'date', null, null, null, null, null, null, null, null, null, 'Pre State Modified Time', '前状态修改时间', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61536, 0, 'Y', to_date('2008-11-21 09:53:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 150, 'trackInTime', '进站时间', null, 615, 61510, 430, null, 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'datetime', 'time', null, null, null, null, null, null, null, null, 'List list = new ArrayList();Calendar from = Calendar.getInstance(); from.add(Calendar.DATE, -30);Calendar to = Calendar.getInstance(); to.add(Calendar.DATE, -1);list.add(from);list.add(to);return list;', 'TrackIn Time', '进站时间', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61537, 0, 'Y', to_date('2008-11-21 09:54:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 150, 'trackOutTime', '出站时间', null, 615, 61510, 440, null, 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'datetime', 'time', null, null, null, null, null, null, null, null, 'List list = new ArrayList();Calendar from = Calendar.getInstance(); from.add(Calendar.DATE, -30);Calendar to = Calendar.getInstance(); to.add(Calendar.DATE, -1);list.add(from);list.add(to);return list;', 'TrackOut Time', '出站时间', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61538, 0, 'Y', to_date('2008-11-21 09:55:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 154, 'operator1', '操作人1', null, 615, 61510, 450, 20, 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 168780080544231424, null, null, null, null, 'User Name', '操作人', 'N', 'N', 'N', null, null, null, null, 'N', null, 2, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61539, 0, 'Y', to_date('2008-11-21 09:55:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 150, 'operator2', '操作人2', null, 615, 61510, 460, null, 'N', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Operator2', '操作人2', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 157976, 0, 'Y', to_date('2010-10-11 15:13:14', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 153, 'owner', '责任人', null, 615, 61510, 500, 14, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Owner', '责任人', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61506, 0, 'Y', to_date('2008-11-21 09:09:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 148, 'engineer', '工程师', null, 615, 61510, 510, null, 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Engineer', '工程师', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 61555, 0, 'Y', to_date('2008-11-21 09:58:00', 'yyyy-MM-dd HH24:mi:ss'), null, to_date('2024-03-22 15:35:46', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 150, 'lotComment', '备注', null, 615, 61510, 560, 32, 'Y', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Comment', '备注', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
