delete AD_MESSAGE where OBJECT_RRN IN ('202403201708001','202403201708002','202403201708003','202403201708004','202403201708005');
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403201708001', '0', 'Y', 'wip.lot_his_no_trans_type', 'This Transaction Does Not Detail Information', '该事务没有对应的详细信息', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403201708002', '0', 'Y', 'wip.lot_his_split_detail', 'Split Detail Info', '分批详细信息', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAG<PERSON>, MESSAGE_ZH, MESSAGE_RES)
values ('202403201708003', '0', 'Y', 'wip.lot_his_merge_detail', 'Merge Detail Info', '合批详细信息', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403201708004', '0', 'Y', 'wip.lot_his_scrap_detail', 'Scrap Detail Info', '报废详细信息', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403201708005', '0', 'Y', 'wip.lot_his_un_scrap_detail', 'UnScrap Detail Info', '反报废详细信息', null);