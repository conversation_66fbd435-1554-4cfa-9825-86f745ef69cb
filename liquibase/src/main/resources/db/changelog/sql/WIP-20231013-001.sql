delete from AD_AUTHORITY t where t.OBJECT_RRN = 389057725528563712;
delete from AD_EDITOR t where t.OBJECT_RRN = 389057725524369408;

insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('389057725524369408', '0', 'Y', 'FlagStepEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.flag.step.WIPFlagStepEditor', null, 'WIPFlagStepManager', null, 'dcop', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('389057725528563712', '0', 'Y', to_date('10-10-2023 14:16:30', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('10-10-2023 14:25:49', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.FlagStep', 'Flag工步设置', 'F', 'E', '389057725524369408', '601', '410', 'Flag Step', 'Flag工步', null, 'dcop', 'MES', 'com.glory.mes.wip', null);

MERGE INTO AD_FORM_RELEASE a
			USING (SELECT 'WIPFlagStepManager' NAME, TO_TIMESTAMP('2023/10/12 18:08:07', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
			ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
			WHEN MATCHED THEN 
 			UPDATE SET a.IS_UPDATE = 'Y'
			WHEN NOT MATCHED THEN 
 			INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 			VALUES ('366461883109099614', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
