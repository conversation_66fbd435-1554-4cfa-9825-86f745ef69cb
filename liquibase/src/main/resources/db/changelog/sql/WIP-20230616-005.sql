
---------- Start Export ADMessage ----------
delete ad_message where KEY_ID IN ('wip-005026: wip.lot_return_super_start_node_not_exist', 'wip-005025: wip.lot_future_change_flow_exist_affect_others', 'wip-005024: wip.lot_future_change_flow_start_step_exist_multi', 'wip-005023: wip.lot_future_change_flow_step_cannot_same'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 346986064881139712, 0, 'Y', 'wip-005026: wip.lot_return_super_start_node_not_exist', 'Return to super start node is not exist!', '返回上级节点不存在!', null); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAG<PERSON>, MESSAGE_ZH, MESSAGE_RES) values  ( 343439979957043200, 0, 'Y', 'wip-005025: wip.lot_future_change_flow_exist_affect_others', 'Will conflict with other FutureChangeFlows.', '会与其他的FutureChangeFlow产生冲突!', null); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 343438993414799360, 0, 'Y', 'wip-005024: wip.lot_future_change_flow_start_step_exist_multi', 'Start step already exists future change flow.', '开始工步已存在FutureChangeFlow!', null); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 343440352360906752, 0, 'Y', 'wip-005023: wip.lot_future_change_flow_step_cannot_same', 'The start and end steps cannot be the same.', '开始工步与结束工步不能相同！', null); 
