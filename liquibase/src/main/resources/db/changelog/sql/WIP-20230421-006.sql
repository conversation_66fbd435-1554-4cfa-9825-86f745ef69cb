
---------- Start Export ADMessage ----------
delete ad_message where KEY_ID IN ('wip-005021: prd.procedure_state_used_procedure_is_null'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 326672143821643776, 0, 'Y', 'wip-005021: prd.procedure_state_used_procedure_is_null', 'Procedure state used procudure is null!', 'ProcedureState对象的usedProcedure属性为空！', null); 

delete ad_message where KEY_ID IN ('wip.lot_targetstep_not_in_rework_whether_to_continue'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 326312524641304576, 0, 'Y', 'wip.lot_targetstep_not_in_rework_whether_to_continue', 'Lot target step not in part procedure, whether to continue？', '选择的目标工步不在产品流程中，是否确认要进行变更？', null); 

delete ad_message where KEY_ID IN ('wip.lot_change_step_whether_to_continue'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 326312880960012288, 0, 'Y', 'wip.lot_change_step_whether_to_continue', 'Lot change step， whether to continue?', '批次变更工步，是否继续？', null); 
