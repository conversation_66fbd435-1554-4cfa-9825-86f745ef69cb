delete ad_message where OBJECT_RRN in (2024022977001,2024022977002,2024022977003,2024022977004,2024022977005,2024022977006,2024022977007,2024022977008,2024022977009,2024022977010,2024022977011,2024022977012);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977001', '0', 'Y', 'mm-1001: mm.bom_not_found', 'Mlot Bom Not Found', '物料BOM未找到！', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977002', '0', 'Y', 'mm-1002: mm.bom_line_is_not_found', 'Mlot Bom Line Not Found', '物料BOM清单信息未找到！', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977003', '0', 'Y', 'mm-1003: mm.bom_active_is_repeat', 'Mlot Bom Active Is Repeat', '存在多个激活BOM！', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977004', '0', 'Y', 'mm-1004: mm.material_is_not_found', 'Material Is Not Found', '物料未找到!', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977005', '0', 'Y', 'mm-1005: mm.bom_state_not_allow', 'Bom State Not,Actual:<%1$s>!,Expect:<%2$s>!', 'Bom状态不允许！实际：<%1$s>！,期望：<%2$s>！', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977006', '0', 'Y', 'mm-1006: mm.material_state_not_allow', 'Material State Not,Actual:<%1$s>!,Expect:<%2$s>!', '物料状态不允许！实际：<%1$s>！,期望：<%2$s>！', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977007', '0', 'Y', 'mm-1007: mm.material_is_used_by_bom,Bom_Info:', 'Material Is Used By Bom,Bom Info：<%1$s>', '物料已被Bom使用,Bom信息：<%1$s>', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977008', '0', 'Y', 'mm-1008: mm.material_is_used_by_mlot', 'Material Is Used By Mlot', '物料已被物料批使用', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977009', '0', 'Y', 'mm-1009: mm.warehouse_not_found', 'Warehouse Not Found', '仓库未找到', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977010', '0', 'Y', 'mm-1010: mm.storage_not_found', 'Storage Not Found', '储位未找到', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977011', '0', 'Y', 'mm-1011: mm.mlot_state_is_not_allow', 'Mlot State Is Not Allow：<%1$s>', '物料状态不允许：<%1$s>', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2024022977012', '0', 'Y', 'mm-1012: mm.mlot_storage_is_empty,warehouse_id:', 'There is no inventory information in [<%1$s>]', '[<%1$s>] 中没有库存信息', null);

update ad_field set label = null, label_zh = null where object_rrn = 93559135;