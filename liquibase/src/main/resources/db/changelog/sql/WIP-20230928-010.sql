
DELETE AD_AUTHORITY WHERE OBJECT_RRN IN (6310, 202112310009, 202112310129);
DELETE AD_EDITOR WHERE OBJECT_RRN = '636';

insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTI<PERSON>, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('636', '0', 'Y', 'RecoveryEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.recovery.glc.RecoveryEditor', '6130', 'WIPLotRecoveryManager', null, 'rework', null);


insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESC<PERSON>PTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('6310', '0', 'Y', to_date('12-12-2008', 'dd-mm-yyyy'), 'admin', to_date('26-09-2023 14:42:57', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'Wip.Recovery', '批次特殊返工', 'F', 'E', '636', '602', '25', 'Lot Special Rework', '批次特殊返工', null, 'rework', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202112310009', '0', 'Y', to_date('08-05-2017', 'dd-mm-yyyy'), 'admin', to_date('08-05-2017', 'dd-mm-yyyy'), 'admin', '1', 'Wip.Recovery.recovery', 'Wip.Recovery.recovery', 'B', null, null, '6310', '10', 'Recovery', '返工', null, 'rework', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202112310129', '0', 'Y', to_date('08-05-2017', 'dd-mm-yyyy'), 'admin', to_date('08-05-2017', 'dd-mm-yyyy'), 'admin', '1', 'Wip.Recovery.splitRecovery', 'Wip.Recovery.splitRecovery', 'B', null, null, '6310', '20', 'Split Recovery', '分批返工', null, 'rework', 'MES', null, null);

DELETE AD_REFTABLE WHERE OBJECT_RRN = '383937625507012608';
insert into AD_REFTABLE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, KEY_FIELD, TEXT_FIELD, WHERE_CLAUSE, ORDER_BY_CLAUSE, IS_DISTIC, IS_QUERY_BY_FIELD)
values ('383937625507012608', '0', 'Y', to_date('26-09-2023 11:11:03', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('26-09-2023 11:28:22', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'PRDReworkProcedureList(Active)', '激活返工流程列表', '403', 'objectRrn', 'name', 'status=''Active'' AND bitand(style,1) > 0', null, null, 'N');

delete ad_message where KEY_ID IN ('prd.lot_step_state_error'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 384718577879601152, 0, 'Y', 'prd.lot_step_state_error', '批次工步节点异常！', 'Lot stepState is error!', null); 

MERGE INTO AD_FORM_RELEASE a
			USING (SELECT 'WIPLotRecoveryManager' NAME, TO_TIMESTAMP('2023/09/28 14:47:27', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
			ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
			WHEN MATCHED THEN 
 			UPDATE SET a.IS_UPDATE = 'Y'
			WHEN NOT MATCHED THEN 
 			INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 			VALUES ('366971883109099614', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');

 MERGE INTO AD_FORM_RELEASE a
			USING (SELECT 'WIPLotComponentSplitRecovery' NAME, TO_TIMESTAMP('2023/09/27 17:43:50', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
			ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
			WHEN MATCHED THEN 
 			UPDATE SET a.IS_UPDATE = 'Y'
			WHEN NOT MATCHED THEN 
 			INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 			VALUES ('366971883109098894', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 			
 			