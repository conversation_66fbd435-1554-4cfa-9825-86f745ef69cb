delete ad_message where OBJECT_RRN = '202402021149275';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149275', '0', 'Y', 'edc-1208: edc.carrent_lot_not_edc', 'Carrent Lot Not Edc!', '当前批次没有数据采集!', null);

delete ad_message where OBJECT_RRN = '202402021149001';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149001', '0', 'Y', 'msgsend-1001: msgsend.message_handler_not_found', 'Message Handler Not Found!', '消息Handler没有找到!', null);

delete ad_message where OBJECT_RRN = '202402021149002';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149002', '0', 'Y', 'msgsend-1002: msgsend.request_message_is_null', 'Request Message Is Null!', 'Request消息为空!', null);

delete ad_message where OBJECT_RRN = '202402021149003';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149003', '0', 'Y', 'msgsend-1003: msgsend.response_message_is_null', 'Response Message Is Null!', 'Response消息为空!', null);

delete ad_message where OBJECT_RRN = '202402021149004';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149004', '0', 'Y', 'msgsend-1004: msgsend.org_is_not_found', 'Org Is Not Found!', '区域号未找到!', null);

delete ad_message where OBJECT_RRN = '202402021149005';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149005', '0', 'Y', 'mm-2166: mm.mlot_qty_more_then_recive_qty', 'Mlot Qty More Then Recive Qty!', '物料批次数量超过接收数量!', null);

delete ad_message where OBJECT_RRN = '202402021149006';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149006', '0', 'Y', 'mm-2167: mm.equipmentMaterial_not_found', 'EquipmentMaterial Not Found!', '未找到设备与物料的绑定关系！', null);

delete ad_message where OBJECT_RRN = '202402021149007';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149007', '0', 'Y', 'mm-2168: mm.unit_num_error', 'Unit Num Error!', 'Unit编号错误!', null);

delete ad_message where OBJECT_RRN = '202402021149008';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149008', '0', 'Y', 'mm-2169: mm.data_exception', 'Data Exception!', '数据异常!', null);

delete ad_message where OBJECT_RRN = '202402021149009';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149009', '0', 'Y', 'mm-2170: mm.iqc_mlot_state_not_allow', 'Incoming material type status does not allow for sheet number modification!', '来料类型状态不允许进行片号修改!', null);

delete ad_message where OBJECT_RRN = '202402021149010';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149010', '0', 'Y', 'mm-2171: mm.iqc_data_is_null', 'IQC Test data is null!', '检测数据为空!', null);

delete ad_message where OBJECT_RRN = '202402021149011';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149011', '0', 'Y', 'mm-2172: mm.iqc_all_reject_mlotiqc_not_exist', 'Batch reject operation cannot be performed due to incomplete detection!', '检测未完成，无法执行整批拒绝操作!', null);

delete ad_message where OBJECT_RRN = '202402021149012';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149012', '0', 'Y', 'mm-2173: mm.mlot_receive_must_more_than_zero', 'Material Lot receive wafer qty must more than zero!', '接收的圆片片数须大于0!', null);

delete ad_message where OBJECT_RRN = '202402021149013';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149013', '0', 'Y', 'ras-1029: ras.eqp_alarmId_is_null', 'Eqp AlarmId Is Null!', '设备警报ID为空!', null);

delete ad_message where OBJECT_RRN = '202402021149015';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149015', '0', 'Y', 'wip-8023: wip.lot_must_be_only_one', 'Lot Must Be Only One!', '批次必须唯一!', null);

delete ad_message where OBJECT_RRN = '202402021149016';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149016', '0', 'Y', 'wip-8024: wip.prd_start_before_end', 'Start node must exist before end node!', '开始节点必须结束节点之前！!', null);

delete ad_message where OBJECT_RRN = '202402021149017';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149017', '0', 'Y', 'wip-8025: wip.changestep_step_is_null', 'Changestep Step Is Null!', '工步不存在!', null);

delete ad_message where OBJECT_RRN = '202402021149018';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149018', '0', 'Y', 'wip-8026: wip.movelocation_location_not_null', 'Movelocation Location Not Null!', '移动的Location为空!', null);

delete ad_message where OBJECT_RRN = '202402021149019';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149019', '0', 'Y', 'wip-8027: wip.movelocation_location_not_equal', 'Movelocation Location Not Equal!', '不能移到当前Location，Location重复！', null);

delete ad_message where OBJECT_RRN = '202402021149020';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149020', '0', 'Y', 'wip-8028: wip.changestep_steppath_large_than_two', 'Steppath large than two!', '工步深度超过2!', null);

delete ad_message where OBJECT_RRN = '202402021149021';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149021', '0', 'Y', 'wip-7006: pp.workorder_bom_is_not_exist', 'Work order BOM is not exist!', '工单BOM不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149027';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149027', '0', 'Y', 'wip-2000: prd.rework_state_hisuper_must_return_to_end', 'Rework State Hisuper Must Return To End!', '返工状态Hisuper必须返回End!', null);

delete ad_message where OBJECT_RRN = '202401251149028';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149028', '0', 'Y', 'wip-2001: prd.rework_state_hisuper_node_not_exist', 'Rework State Hisuper Node Not Exist!', '返工状态Hisuper节点不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149029';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149029', '0', 'Y', 'wip-2002: prd.rework_state_hisuper_merge_node_not_exist', 'Rework State Hisuper Merge Node Not Exist!', '返工状态Hisuper合并节点不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149030';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149030', '0', 'Y', 'wip-2003: prd.unsupport_use_wfparameter', 'Unsupport Use Wfparameter!', '不支持使用参数!', null);

delete ad_message where OBJECT_RRN = '202401251149032';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149032', '0', 'Y', 'wip-2021: prd.jumpto_unsupport_diff_procedure_in_rework', 'Jumpto Unsupport Diff Procedure In Rework!', '跳转到不支持的不同返工流程中!', null);

delete ad_message where OBJECT_RRN = '202401251149033';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149033', '0', 'Y', 'wip-2021: prd.jumpto_node_not_correct', 'Jumpto Node Not Correct!', '跳转节点不正确!', null);

delete ad_message where OBJECT_RRN = '202401251149034';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149034', '0', 'Y', 'wip-2022: prd.jumpto_node_not_in_procedure', 'Jumpto Node Not In Procedure!', '跳转节点不在流程中!', null);

delete ad_message where OBJECT_RRN = '202401251149035';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149035', '0', 'Y', 'wip-2100: prd.node_unsupport_multi_leave_transition', 'Node Unsupport Multi Leave Transition!', '节点不支持多个层级转变!', null);

delete ad_message where OBJECT_RRN = '202401251149036';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149036', '0', 'Y', 'wip-2101: prd.procedure_state_path_is_null', 'Procedure State Path Is Null!', '流程Path为空!', null);

delete ad_message where OBJECT_RRN = '202401251149037';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149037', '0', 'Y', 'wip-2102: prd.lot_procedure_unsupport', 'Lot Procedure Unsupport!', '批次流程不支持!', null);

delete ad_message where OBJECT_RRN = '202401251149038';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149038', '0', 'Y', 'wip-2103: prd.lot_procedure_is_repeat', 'Lot Procedure Is Repeat!', '批次流程重复!', null);

delete ad_message where OBJECT_RRN = '202401251149039';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149039', '0', 'Y', 'wip-2104: prd.node_unsupport_multi_arriving_transition', 'Node Unsupport Multi Arriving Transition!', '节点不支持多个到达!', null);

delete ad_message where OBJECT_RRN = '202401251149068';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149068', '0', 'Y', 'wip-2006: prd._backup_step_not_in_current_procedure_or_not_exist', 'Back Up Step Not In Current Procedure Or Not Exist!', '退步工步不在当前流程或者不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149089';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149089', '0', 'Y', 'wip-2007: prd.procedure_state_used_procedure_is_null', 'Procedure State Used Procedure Is Null!', 'ProcedureState对象的usedProcedure属性为空!', null);

delete ad_message where OBJECT_RRN = '202401251149090';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149090', '0', 'Y', 'wip-2008: prd.lot_procedure_found_multiple', 'Found multiple lot procedures of lock version type!', '找到了多个LockVersion类型的Lot Procedure!', null);

delete ad_message where OBJECT_RRN = '202401251149098';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149098', '0', 'Y', 'wip-2009: prd.lot_step_state_error', 'Lot Step State Error!', '批次工步节点异常！!', null);

delete ad_message where OBJECT_RRN = '202401251149113';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149113', '0', 'Y', 'wip-2010: prd.procedure_is_not_exist', 'Procedure <%1$s> is not exist!', '流程<%1$s>不存在！', null);

delete ad_message where OBJECT_RRN = '202401251149114';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149114', '0', 'Y', 'wip-2011: prd.step_is_not_exist', 'Step <%1$s> is not exist!', '工序<%1$s>不存在！', null);

delete ad_message where OBJECT_RRN = '202401251149115';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149115', '0', 'Y', 'wip-2012: prd.step_is_used', 'Step Is Used!', '工步已经被使用!', null);

delete ad_message where OBJECT_RRN = '202401251149116';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149116', '0', 'Y', 'wip-2013: prd.procedure_is_used', 'Procedure Is Used!', '流程已经被使用!', null);

delete ad_message where OBJECT_RRN = '202401251149117';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149117', '0', 'Y', 'wip-2014: prd.process_is_used', 'Process Is Used!', '工艺已经被使用!', null);

delete ad_message where OBJECT_RRN = '202401251149118';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149118', '0', 'Y', 'wip-2015: prd.process_is_used_by_reworkprocess', 'Process Is Used By Reworkprocess!', '工艺已经被可选流程使用!', null);

delete ad_message where OBJECT_RRN = '202401251149119';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149119', '0', 'Y', 'wip-2016: prd.part_is_used', 'Part Is Used!', '产品已经被使用!', null);

delete ad_message where OBJECT_RRN = '202401251149120';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149120', '0', 'Y', 'wip-2017: prd.delete_only_unfrozen_or_inactive', 'Delete Only Unfrozen Or Inactive!', 'UnFrozen和InActive状态才能被删除!', null);

delete ad_message where OBJECT_RRN = '202401251149122';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149122', '0', 'Y', 'wip-2018: prd.future_step_is_not_exist', 'Future Step Is Not Exist!', '未来工步不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149123';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149123', '0', 'Y', 'wip-2019: prd.part_not_exist', 'Part Not Exist!', '产品不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149127';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149127', '0', 'Y', 'wip-2004: prd.node_is_not_exist', 'Node <%1$s> Is Not Exist!', '流程节点<%1$s>不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149128';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149128', '0', 'Y', 'wip-2005: prd.flow_nest_loop', 'Flow is nested loop or nest layer larger than 10!', '流程循环嵌套或嵌套层大于10层！', null);

delete ad_message where OBJECT_RRN = '202402021149022';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202402021149022', '0', 'Y', 'wip-2020: prd.flow_is_required', 'Flow is required!', '必须设置对应的流程!', null);

delete ad_message where OBJECT_RRN = '202401251149031';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149031', '0', 'Y', 'wip-8029: wip.lot_trackin_need_prepare_first', 'Equipment <%1$s> requires lots job prepare before track in!', '设备<%1$s>需要批次先做作业准备才能进站！', null);
