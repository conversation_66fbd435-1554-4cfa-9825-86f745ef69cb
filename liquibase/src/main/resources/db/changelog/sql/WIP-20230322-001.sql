
delete AD_AUTHORITY t where t.OBJECT_RRN in (800009,800010,800005,800017,800013,800016,800015,800014,800004,800006,800001,800002,800011,800012,800003,800007,800008);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800009', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionBankIn', '批次BankIn', 'B', null, null, '************', '90', 'Bank In', '批次BankIn', null, 'bankin-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800010', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionBankOut', '批次BankOut', 'B', null, null, '************', '100', 'Bank Out', '批次BankOut', null, 'bankout-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800005', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionHold', '暂停', 'B', null, null, '************', '50', 'Hold', '批次暂停', null, 'hold-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800017', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionLotCommentsModify', '批次备注修改', 'B', null, null, '************', '150', 'Lot Comments Modify', '批次备注修改', null, 'where', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800013', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionLotInfoModify', '修改批次信息', 'B', null, null, '************', '130', 'Lot Info Modify', '批次信息修改', null, 'edit', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800016', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionLotParametersModify', '批次参数修改', 'B', null, null, '************', '140', 'Lot Parameters Modify', '批次参数修改', null, 'edit', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800015', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionLotProductChange', '批次产品修改', 'B', null, null, '************', '170', 'Lot Product Change', '批次产品变更', null, 'newpart', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800014', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionLotWOChange', '批次工单修改', 'B', null, null, '************', '160', 'Lot WO Change', '批次工单变更', null, 'change-lot-info', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800004', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionMerge', '合批', 'B', null, null, '************', '40', 'Merge', '合批', null, 'merge-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800006', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionRelease', '放行', 'B', null, null, '************', '60', 'Release', '批次放行', null, 'release-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800001', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionScrap', '报废', 'B', null, null, '************', '10', 'Scrap', '报废', null, 'scrap-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800002', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionScrapCancel', '取消报废', 'B', null, null, '************', '20', 'Scrap Cancel', '报废取消', null, 'unscrap-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800011', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionShip', '完成品入库', 'B', null, null, '************', '110', 'Ship', '入库', null, 'ship-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800012', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionShipCancel', '取消入库', 'B', null, null, '************', '120', 'Ship Cancel', '入库取消', null, 'unship-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800003', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionSplit', '分批', 'B', null, null, '************', '30', 'Split', '分批', null, 'split-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800007', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionTerminate', '批次终止', 'B', null, null, '************', '70', 'Terminate', '批次终止', null, 'terminate-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('800008', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.WipQueryNew.LotActionTerminateCancel', '批次终止恢复', 'B', null, null, '************', '80', 'Terminate Cancel', '批次终止取消', null, 'unterminate-lot', 'MES', null, null);


delete AD_AUTHORITY t where t.OBJECT_RRN in (810009,810010,810005,810017,810013,810016,810015,810014,810004,810006,810001,810002,810011,810012,810003,810007,810008);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810009', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionBankIn', '批次BankIn', 'B', null, null, '9003', '90', 'Bank In', '批次BankIn', null, 'bankin-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810010', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionBankOut', '批次BankOut', 'B', null, null, '9003', '100', 'Bank Out', '批次BankOut', null, 'bankout-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810005', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionHold', '暂停', 'B', null, null, '9003', '50', 'Hold', '批次暂停', null, 'hold-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810017', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionLotCommentsModify', '批次备注修改', 'B', null, null, '9003', '150', 'Lot Comments Modify', '批次备注修改', null, 'where', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810013', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionLotInfoModify', '修改批次信息', 'B', null, null, '9003', '130', 'Lot Info Modify', '批次信息修改', null, 'edit', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810016', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionLotParametersModify', '批次参数修改', 'B', null, null, '9003', '140', 'Lot Parameters Modify', '批次参数修改', null, 'edit', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810015', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionLotProductChange', '批次产品修改', 'B', null, null, '9003', '170', 'Lot Product Change', '批次产品变更', null, 'newpart', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810014', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionLotWOChange', '批次工单修改', 'B', null, null, '9003', '160', 'Lot WO Change', '批次工单变更', null, 'change-lot-info', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810004', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionMerge', '合批', 'B', null, null, '9003', '40', 'Merge', '合批', null, 'merge-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810006', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionRelease', '放行', 'B', null, null, '9003', '60', 'Release', '批次放行', null, 'release-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810001', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionScrap', '报废', 'B', null, null, '9003', '10', 'Scrap', '报废', null, 'scrap-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810002', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionScrapCancel', '取消报废', 'B', null, null, '9003', '20', 'Scrap Cancel', '报废取消', null, 'unscrap-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810011', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionShip', '完成品入库', 'B', null, null, '9003', '110', 'Ship', '入库', null, 'ship-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810012', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionShipCancel', '取消入库', 'B', null, null, '9003', '120', 'Ship Cancel', '入库取消', null, 'unship-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810003', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionSplit', '分批', 'B', null, null, '9003', '30', 'Split', '分批', null, 'split-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810007', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionTerminate', '批次终止', 'B', null, null, '9003', '70', 'Terminate', '批次终止', null, 'terminate-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('810008', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByStep.LotActionTerminateCancel', '批次终止恢复', 'B', null, null, '9003', '80', 'Terminate Cancel', '批次终止取消', null, 'unterminate-lot', 'MES', null, null);


delete AD_AUTHORITY t where t.OBJECT_RRN in (820009,820010,820005,820017,820013,820016,820015,820014,820004,820006,820001,820002,820011,820012,820003,820007,820008);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820009', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionBankIn', '批次BankIn', 'B', null, null, '307580653749252097', '90', 'Bank In', '批次BankIn', null, 'bankin-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820010', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionBankOut', '批次BankOut', 'B', null, null, '307580653749252097', '100', 'Bank Out', '批次BankOut', null, 'bankout-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820005', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionHold', '暂停', 'B', null, null, '307580653749252097', '50', 'Hold', '批次暂停', null, 'hold-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820017', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionLotCommentsModify', '批次备注修改', 'B', null, null, '307580653749252097', '150', 'Lot Comments Modify', '批次备注修改', null, 'where', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820013', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionLotInfoModify', '修改批次信息', 'B', null, null, '307580653749252097', '130', 'Lot Info Modify', '批次信息修改', null, 'edit', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820016', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionLotParametersModify', '批次参数修改', 'B', null, null, '307580653749252097', '140', 'Lot Parameters Modify', '批次参数修改', null, 'edit', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820015', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionLotProductChange', '批次产品修改', 'B', null, null, '307580653749252097', '170', 'Lot Product Change', '批次产品变更', null, 'newpart', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820014', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionLotWOChange', '批次工单修改', 'B', null, null, '307580653749252097', '160', 'Lot WO Change', '批次工单变更', null, 'change-lot-info', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820004', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionMerge', '合批', 'B', null, null, '307580653749252097', '40', 'Merge', '合批', null, 'merge-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820006', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionRelease', '放行', 'B', null, null, '307580653749252097', '60', 'Release', '批次放行', null, 'release-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820001', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionScrap', '报废', 'B', null, null, '307580653749252097', '10', 'Scrap', '报废', null, 'scrap-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820002', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionScrapCancel', '取消报废', 'B', null, null, '307580653749252097', '20', 'Scrap Cancel', '报废取消', null, 'unscrap-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820011', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionShip', '完成品入库', 'B', null, null, '307580653749252097', '110', 'Ship', '入库', null, 'ship-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820012', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionShipCancel', '取消入库', 'B', null, null, '307580653749252097', '120', 'Ship Cancel', '入库取消', null, 'unship-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820003', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionSplit', '分批', 'B', null, null, '307580653749252097', '30', 'Split', '分批', null, 'split-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820007', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionTerminate', '批次终止', 'B', null, null, '307580653749252097', '70', 'Terminate', '批次终止', null, 'terminate-lot', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('820008', '0', 'Y', to_date('16-11-2020', 'dd-mm-yyyy'), null, to_date('16-11-2020', 'dd-mm-yyyy'), null, '0', 'Wip.ByEqp.LotActionTerminateCancel', '批次终止恢复', 'B', null, null, '307580653749252097', '80', 'Terminate Cancel', '批次终止取消', null, 'unterminate-lot', 'MES', null, null);
