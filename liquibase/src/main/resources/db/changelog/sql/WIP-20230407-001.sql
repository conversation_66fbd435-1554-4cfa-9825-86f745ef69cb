delete ad_message where KEY_ID = 'wip-005017: wip.procedure_change_is_started_operation_not_allowed';

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('3433321', '0', 'Y', 'wip-005017: wip.procedure_change_is_started_operation_not_allowed', 'Lot procedure change has been triggered, this operation is not allowed!', '已触发批次流程变更，此操作不允许！', null);

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPLotProcedureManager' NAME, TO_TIMESTAMP('2023/02/07 11:20:17', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('298047797549793282', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');