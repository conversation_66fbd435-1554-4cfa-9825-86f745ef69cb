DELETE AD_AUTHORITY WHERE PARENT_RRN = '2022061401101';
delete ad_authority where object_rrn = 2022061401101;
delete ad_editor where object_rrn = 20220614011;
insert into ad_authority (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2022061401101', '0', 'Y', to_date('24-03-2022', 'dd-mm-yyyy'), 'admin', to_date('10-04-2024 16:03:18', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.BatchJobMapping', 'BatchJob关系', 'F', 'E', '20220614011', '601', '200', 'Batch Job Mapping', 'BatchJob关系', null, 'basetable', 'MES', 'com.glory.mes.wip', null);
insert into ad_editor (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('20220614011', '0', 'Y', 'BatchJobMappingEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.batchjob.group.BatchJobMappingEditor', null, 'BatchJobMapping', null, 'basetable', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2022061401102', '0', 'Y', to_date('24-03-2022', 'dd-mm-yyyy'), 'admin', to_date('24-03-2022', 'dd-mm-yyyy'), 'admin', '1', 'Wip.BatchJobMapping.save', '保存', 'B', null, null, '2022061401101', '10', 'Save', '保存', null, 'production_order', 'MES', 'com.glory.mes.wip', null);
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2022061401103', '0', 'Y', to_date('24-03-2022', 'dd-mm-yyyy'), 'admin', to_date('24-03-2022', 'dd-mm-yyyy'), 'admin', '1', 'Wip.BatchJobMapping.delete', '移除', 'B', null, null, '2022061401101', '20', 'Delete', '移除', null, 'production_order', 'MES', 'com.glory.mes.wip', null);

delete ad_query where object_rrn = 20220614011;
insert into ad_query (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, QUERY_TEXT)
values ('20220614011', '0', 'Y', to_date('25-03-2022', 'dd-mm-yyyy'), 'admin', to_date('25-03-2022', 'dd-mm-yyyy'), 'admin', '1', 'BatchJobMappingGroup', 'BatchJob关系映射', 'select t.*
    from (select p.object_rrn     processRrn,
         m.object_rrn     measureRrn,
                 p.part_name      partName,
                 p.org_rrn        orgRrn,
                 p.is_active isActive,
                 p.group_id  groupId,
                 p.procedure_name procedureName,
                 m.procedure_name npwProcedureName,
                 p.process_name processName,
                 p.step_name stepName,
                 m.step_name npwStepName
            from EDC_PROCESS_GROUP p
            left join EDC_PROCESS_GROUP m
              on p.group_Id = m.group_id
           where p.category = ''BJ''
         and p.process_type = ''CREATEBATCH''
             and m.process_type = ''RESUMEBATCH'') t
   where (orgRrn = :orgRrn OR orgRrn = 0)');

DELETE ad_field WHERE OBJECT_RRN = 213962737284288512;
DELETE ad_field WHERE OBJECT_RRN = 213977042004217856;
DELETE ad_tab WHERE OBJECT_RRN = 213961216995889152;
DELETE ad_tab WHERE OBJECT_RRN = 213961216995889153;
DELETE ad_table WHERE OBJECT_RRN = 213959680068685824;
delete AD_REFTABLE where OBJECT_RRN = 213978539907616768;
delete AD_REFTABLE where OBJECT_RRN = 213978624062132224;
delete AD_BUTTON where TAB_RRN = 213961216995889153;
DELETE ad_field WHERE TABLE_RRN = 213967023225442304;
DELETE ad_table WHERE OBJECT_RRN = 213967023225442304;
DELETE ad_field WHERE TABLE_RRN = 213976277483261952;
DELETE ad_table WHERE OBJECT_RRN = 213976277483261952;
DELETE AD_FORM WHERE OBJECT_RRN = '214023707973128192';
DELETE FROM ad_form_attribute WHERE FORM_RRN = '214023707973128192';
DELETE FROM AD_EXPORT_TEMPLATE WHERE TABLE_RRN = '213967023225442304';

delete AD_IMPEXP_FIELD_MAP where parent_rrn =(select object_rrn from AD_IMPEXP where authority_name = 'Wip.BatchJobMapping');
delete AD_IMPEXP where authority_name = 'Wip.BatchJobMapping';
insert into AD_IMPEXP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_NAME, AUTHORITY_DESC, TYPE, AD_TABLE_NAME, BUTTON_NAME, DESCRIPTION)
values ('455705498409930752', '0', 'Y', to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'Wip.BatchJobMapping', 'BatchJob关系', 'ONE', 'BatchJobMapping_Table03', null, null);
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('455705498414125056', '0', 'Y', to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '455705498409930752', null, 'partName', '10', null, null);
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('455705498414125057', '0', 'Y', to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '455705498409930752', null, 'procedureName', '20', null, null);
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('455705498414125058', '0', 'Y', to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '455705498409930752', null, 'stepName', '30', null, null);
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('455705498414125059', '0', 'Y', to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '455705498409930752', null, 'npwProcedureName', '40', null, null);
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('455705498414125060', '0', 'Y', to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('11-04-2024 12:10:57', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '455705498409930752', null, 'npwStepName', '50', null, null);

DELETE AD_FORM_RELEASE WHERE NAME = 'BatchJobMapping';
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'BatchJobMapping' NAME, TO_TIMESTAMP('2024/04/11 17:48:11', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('455791672948379648', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, 'BatchJob关系映射', 'Active', b.release_timestamp, 'Y', 'Y');
