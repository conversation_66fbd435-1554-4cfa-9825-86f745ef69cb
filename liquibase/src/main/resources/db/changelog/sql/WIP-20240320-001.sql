delete ad_message where <PERSON><PERSON><PERSON><PERSON><PERSON> in ('wip.lot_new_part_whether_to_continue','wip.lot_recovery_whether_to_continue','wip.lot_split_recovery_whether_to_continue');

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('447439563318697984', '0', 'Y', 'wip.lot_split_recovery_whether_to_continue', 'Lot split recovery step, whether to continue? ', '批次分批返工，是否继续？', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('447435589848997888', '0', 'Y', 'wip.lot_new_part_whether_to_continue', 'Lot new Part, whether to continue?', '批次改产品，是否继续？', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('447438908017418240', '0', 'Y', 'wip.lot_recovery_whether_to_continue', 'Lot recovery, whether to continue?', '批次返工，是否继续？', null);

delete AD_BUTTON_AUTHORITY where OBJECT_RRN in ('20240319001','20240319002','20240319003');
insert into AD_BUTTON_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_KEY, AUTHORITY_DESC, AUTHORITY_ACTION_ID, IS_ENABLE)
values ('20240319001', '0', 'Y', to_date('19-03-2024', 'dd-mm-yyyy'), 'admin', to_date('19-03-2024', 'dd-mm-yyyy'), 'admin', '0', 'Wip.NewPart.newPart', 'Wip.NewPart.newPart', 'UserAuth', 'Y');

insert into AD_BUTTON_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_KEY, AUTHORITY_DESC, AUTHORITY_ACTION_ID, IS_ENABLE)
values ('20240319002', '0', 'Y', to_date('19-03-2024', 'dd-mm-yyyy'), 'admin', to_date('19-03-2024', 'dd-mm-yyyy'), 'admin', '0', 'Wip.Recovery.recovery', 'Wip.Recovery.recovery', 'UserAuth', 'Y');

insert into AD_BUTTON_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_KEY, AUTHORITY_DESC, AUTHORITY_ACTION_ID, IS_ENABLE)
values ('20240319003', '0', 'Y', to_date('19-03-2024', 'dd-mm-yyyy'), 'admin', to_date('19-03-2024', 'dd-mm-yyyy'), 'admin', '0', 'Wip.Recovery.splitRecovery', 'Wip.Recovery.splitRecovery', 'UserAuth', 'Y');