delete from AD_SYS_PARAMETER_VALUE where NAME = 'mes_auto_error_hold_lot';
delete from AD_SYS_PARAMETER where NAME = 'mes_auto_error_hold_lot';
insert into AD_SYS_PARAMETER (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, DEFAULT_VALUE, IS_STATIC, IS_GLOBAL, IS_MODIFIABLE)
values ('182161', '0', 'Y', to_date('06-11-2017', 'dd-mm-yyyy'), 'admin', to_date('06-11-2017', 'dd-mm-yyyy'), 'admin', '0', 'mes_auto_error_hold_lot', '自动化逻辑中，出现可控异常时，是HoldLot还是抛出异常', null, 'N', 'Y', 'Y');

insert into AD_SYS_PARAMETER_VALUE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, VALUE, DEFAULT_VALUE, IS_STATIC, IS_SYSTEM, IS_GLOBAL, IS_MODIFIABLE, COMMENTS)
values ('182161', '0', 'Y', to_date('19-03-2019 00:01:00', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('19-03-2019 12:38:00', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'mes_auto_error_hold_lot', '自动化逻辑中，出现可控异常时，是HoldLot还是抛出异常', 'Y', 'Y', 'N', 'Y', 'Y', 'Y', null);


delete from BAS_ID_GENERATOR_RULE_LINE where RULE_RRN = (select object_rrn from BAS_ID_GENERATOR_RULE where NAME = 'CreateMComponentUnit');
delete from BAS_ID_GENERATOR_RULE where NAME = 'CreateMComponentUnit';
insert into BAS_ID_GENERATOR_RULE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, VERSION, STATUS, ACTIVE_TIME, ACTIVE_USER, RULE_TYPE)
values ('5413151', '10', 'Y', to_date('03-11-2020 15:51:28', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('03-11-2020 15:52:47', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '8', 'CreateMComponentUnit', '生成物料片号', null, 'Active', to_date('03-11-2020 15:52:47', 'dd-mm-yyyy hh24:mi:ss'), 'admin', 'ComponentUnit');

insert into BAS_ID_GENERATOR_RULE_LINE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, RULE_RRN, RULE_ID, SEQ_NO, DS_TYPE, FIXED_STRING, DATE_TYPE, SPECIFIC_DATE, CALENDAR, DATE_FORMAT, SEQUENCE_TYPE, SEQUENCE_DIRECTION, EXCLUDE_TYPE, MIN, MAX, BASE_TYPE, BASE_ON, BASE_TABLE, BASE_COLUMN, VARIABLE_TYPE, PARAMETER, WHERE_CLAUSE, VARIABLE_DIRECTION, START_POSITION, LENGTH, FORMAT_CODE, CODE_LEN, TABLEE, COLUMNN, SIZEE, STRATEGY, EXCLUDE)
values ('5413201', '10', 'Y', '5413151', null, '1', 'F', 'M', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);

insert into BAS_ID_GENERATOR_RULE_LINE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, RULE_RRN, RULE_ID, SEQ_NO, DS_TYPE, FIXED_STRING, DATE_TYPE, SPECIFIC_DATE, CALENDAR, DATE_FORMAT, SEQUENCE_TYPE, SEQUENCE_DIRECTION, EXCLUDE_TYPE, MIN, MAX, BASE_TYPE, BASE_ON, BASE_TABLE, BASE_COLUMN, VARIABLE_TYPE, PARAMETER, WHERE_CLAUSE, VARIABLE_DIRECTION, START_POSITION, LENGTH, FORMAT_CODE, CODE_LEN, TABLEE, COLUMNN, SIZEE, STRATEGY, EXCLUDE)
values ('5413202', '10', 'Y', '5413151', null, '2', 'D', null, 'SYSTEM', null, null, 'yyyy', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '1', null, null, null, null, null);

insert into BAS_ID_GENERATOR_RULE_LINE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, RULE_RRN, RULE_ID, SEQ_NO, DS_TYPE, FIXED_STRING, DATE_TYPE, SPECIFIC_DATE, CALENDAR, DATE_FORMAT, SEQUENCE_TYPE, SEQUENCE_DIRECTION, EXCLUDE_TYPE, MIN, MAX, BASE_TYPE, BASE_ON, BASE_TABLE, BASE_COLUMN, VARIABLE_TYPE, PARAMETER, WHERE_CLAUSE, VARIABLE_DIRECTION, START_POSITION, LENGTH, FORMAT_CODE, CODE_LEN, TABLEE, COLUMNN, SIZEE, STRATEGY, EXCLUDE)
values ('5413203', '10', 'Y', '5413151', null, '3', 'D', null, 'SYSTEM', null, null, 'MM', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '1', null, null, null, null, null);

insert into BAS_ID_GENERATOR_RULE_LINE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, RULE_RRN, RULE_ID, SEQ_NO, DS_TYPE, FIXED_STRING, DATE_TYPE, SPECIFIC_DATE, CALENDAR, DATE_FORMAT, SEQUENCE_TYPE, SEQUENCE_DIRECTION, EXCLUDE_TYPE, MIN, MAX, BASE_TYPE, BASE_ON, BASE_TABLE, BASE_COLUMN, VARIABLE_TYPE, PARAMETER, WHERE_CLAUSE, VARIABLE_DIRECTION, START_POSITION, LENGTH, FORMAT_CODE, CODE_LEN, TABLEE, COLUMNN, SIZEE, STRATEGY, EXCLUDE)
values ('5413204', '10', 'Y', '5413151', null, '4', 'D', null, 'SYSTEM', null, null, 'dd', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '1', null, null, null, null, null);

insert into BAS_ID_GENERATOR_RULE_LINE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, RULE_RRN, RULE_ID, SEQ_NO, DS_TYPE, FIXED_STRING, DATE_TYPE, SPECIFIC_DATE, CALENDAR, DATE_FORMAT, SEQUENCE_TYPE, SEQUENCE_DIRECTION, EXCLUDE_TYPE, MIN, MAX, BASE_TYPE, BASE_ON, BASE_TABLE, BASE_COLUMN, VARIABLE_TYPE, PARAMETER, WHERE_CLAUSE, VARIABLE_DIRECTION, START_POSITION, LENGTH, FORMAT_CODE, CODE_LEN, TABLEE, COLUMNN, SIZEE, STRATEGY, EXCLUDE)
values ('5413205', '10', 'Y', '5413151', null, '5', 'D', null, 'SYSTEM', null, null, 'hh', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '1', null, null, null, null, null);

insert into BAS_ID_GENERATOR_RULE_LINE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, RULE_RRN, RULE_ID, SEQ_NO, DS_TYPE, FIXED_STRING, DATE_TYPE, SPECIFIC_DATE, CALENDAR, DATE_FORMAT, SEQUENCE_TYPE, SEQUENCE_DIRECTION, EXCLUDE_TYPE, MIN, MAX, BASE_TYPE, BASE_ON, BASE_TABLE, BASE_COLUMN, VARIABLE_TYPE, PARAMETER, WHERE_CLAUSE, VARIABLE_DIRECTION, START_POSITION, LENGTH, FORMAT_CODE, CODE_LEN, TABLEE, COLUMNN, SIZEE, STRATEGY, EXCLUDE)
values ('5413206', '10', 'Y', '5413151', null, '6', 'S', null, null, null, null, null, '1', '1', null, '00001', '99999', null, null, null, null, null, null, null, null, null, null, null, null, null, null, '5', null, null);

delete from AD_UREFLIST where OBJECT_RRN in ('277569947037626368', '277570045222088704');
insert into AD_UREFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, PARENT_RRN, IS_AVAILABLE, RESERVED01, RESERVED02, RESERVED03, RESERVED04, RESERVED05)
values ('277569947037626368', '10', 'Y', to_date('03-11-2020 15:49:00', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('03-11-2020 15:49:00', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'TransactionType', 'CreateMComponentUnit', 'CreateMComponentUnit', '260', '生成物料片号', null, 'Y', null, null, null, null, null);

insert into AD_UREFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, PARENT_RRN, IS_AVAILABLE, RESERVED01, RESERVED02, RESERVED03, RESERVED04, RESERVED05)
values ('277570045222088704', '10', 'Y', to_date('03-11-2020 15:49:23', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('03-11-2020 15:49:23', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'ObjectType', 'MComponentUnit', 'MComponentUnit', '8', '物料片号', null, 'Y', null, null, null, null, null);

