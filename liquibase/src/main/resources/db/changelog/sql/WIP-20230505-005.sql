
---------- Start Export ADMessage ----------
delete ad_message where KEY_ID IN ('prd.change_part_contains_change_flow', 'prd.change_part_contains_change_lock_flow', 'prd.change_part_contains_lock_flow'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 20230320077, 0, 'Y', 'prd.change_part_contains_change_flow', 'The target procedure contains a lot change flow that will be deleted after new part. Are you sure you want to execute it?', '目标模块中，包含批次特殊流程，改产品后将会被删除，是否确认要执行？', null); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 331850899716661248, 0, 'Y', 'prd.change_part_contains_change_lock_flow', 'The target procedure contains a lot change procedure, and there is a lock procedure version action in the lot. After new part, it will be deleted. Are you sure you want to execute it?', '在目标模块中包含批次特殊流程，同时批次存在锁流程版本动作，改产品后将会被删除，是否确认要执行？', null); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 331849757314080768, 0, 'Y', 'prd.change_part_contains_lock_flow', 'There is a lock procedure version action in the lot. After new part, it will be deleted. Are you sure you want to execute it?', '批次存在锁定流程版本动作，改产品后将会被删除，是否确认要执行？', null); 
