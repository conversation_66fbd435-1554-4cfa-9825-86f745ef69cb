delete AD_AUTHORITY where NAME = 'Wip.FlagDef';
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('389021556251049985', '0', 'Y', to_date('10-10-2023 11:52:46', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('11-10-2023 09:55:34', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'Wip.FlagDef', 'Flag定义', 'F', 'E', '389021556251049984', '601', '400', 'Flag Define', 'Flag定义', null, 'dcop', 'MES', 'com.glory.mes.wip', null);

delete ad_editor where OBJECT_RRN = 389021556251049984;
insert into ad_editor (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('389021556251049984', '0', 'Y', 'WipFlagDefManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.flag.def.WipFlagDefManagerEditor', null, 'WipFlagDefManager', null, 'dcop', null);

 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WipFlagDefManager' NAME, TO_TIMESTAMP('2023/10/11 10:28:55', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('389362843080777728', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
delete ad_message t where key_id in ('wip.flagdef_name','wip.flagdef_desc','wip.please_select_category');
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('389377777638367232', '0', 'Y', 'wip.please_select_category', 'Please select Category!', '请选择Category！', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('389380324491059200', '0', 'Y', 'wip.flagdef_name', 'FlagBit%1$s Name', '位%1$s名称', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('389380512945332224', '0', 'Y', 'wip.flagdef_desc', 'FlagBit%1$s Desc', '位%1$s描述', null);
