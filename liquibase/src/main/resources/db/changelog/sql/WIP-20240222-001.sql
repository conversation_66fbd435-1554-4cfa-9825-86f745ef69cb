delete ad_message where OBJECT_RRN in (438005938419515392, 438005743485042688, 438005498701266944, 438004469062549504, 438004469062549505, 438005208551899136, 438004469062549506, 438004756888272896, 17112361);
delete ad_message where KEY_ID IN ('wip-2303: wip.component_position_is_null', 'wip-2304: wip.component_position_is_invalid', 'wip-2305: wip.component_position_is_repeate'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 438005938419515392, 0, 'Y', 'wip-2303: wip.component_position_is_null', 'Component position is null!', '组件位置为空！', null); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, <PERSON><PERSON><PERSON>_<PERSON>, ME<PERSON><PERSON><PERSON>, MESSAGE_ZH, MESSAGE_RES) values  ( 438005743485042688, 0, 'Y', 'wip-2304: wip.component_position_is_invalid', 'Component position is invalid!', '组件位置无效！', null); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 438005498701266944, 0, 'Y', 'wip-2305: wip.component_position_is_repeate', 'Component position is repeat!', '组件位置重复！', null); 

delete ad_message where KEY_ID IN ('wip-2105: prd.unsupport_processdefinition'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 438004469062549504, 0, 'Y', 'wip-2105: prd.unsupport_processdefinition', 'UnSuport ProcessDefinition。', '不支持的ProcessDefinition。', null); 

delete ad_message where KEY_ID IN ('wip-2106: prd.unsupport_process_type#{0}'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 438004469062549505, 0, 'Y', 'wip-2106: prd.unsupport_process_type#{0}', 'UnSuport processType %1$s 。', '不支持的ProcessType %1$s 。', null); 

delete ad_message where KEY_ID IN ('wip-2306: wip.lot_can_not_lot_identity'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 438005208551899136, 0, 'Y', 'wip-2306: wip.lot_can_not_lot_identity', 'Lot can not identity!', '批次不能打标！', null); 

delete ad_message where KEY_ID IN ('ras-1031: ras.state_not_found'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 438004469062549506, 0, 'Y', 'ras-1031: ras.state_not_found', 'State not found!', '状态没发现！', null); 

delete ad_message where KEY_ID IN ('ras-1032: ras.eqp_state_cascade_invalid'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 438004756888272896, 0, 'Y', 'ras-1032: ras.eqp_state_cascade_invalid', 'EQP state Cascade Condition Illegal !', '设备状态级联条件不合法！', null); 

delete ad_message where KEY_ID IN ('mm-2177: mm.pack_unsupport_qty_type'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 17112361, 0, 'Y', 'mm-2177: mm.pack_unsupport_qty_type', 'Unsupport qty type of this package type', '不支持此数量类型', null); 

