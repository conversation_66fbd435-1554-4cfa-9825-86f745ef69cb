delete from AD_EDITOR where OBJECT_RRN in (select editor_rrn from AD_AUTHORITY where name in ('Wip.DeassignCarrier'));
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('2016082402', '0', 'Y', 'LotDeassignManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.carrier.glc.deassign.LotDeassignManagerEditor', null, 'LotDeassignManager', null, 'deassign', null);

DELETE AD_FORM_RELEASE WHERE NAME = 'LotDeassignManager';
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'LotDeassignManager' NAME, TO_TIMESTAMP('2024/1/3 18:47:08', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('419930740860358656', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次解绑载具', 'Active', b.release_timestamp, 'Y', 'Y');
  
-----------------------------------------------------
delete from AD_EDITOR where OBJECT_RRN in (select editor_rrn from AD_AUTHORITY where name in ('Wip.ChangeCarrier'));
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('2019081401', '0', 'Y', 'LotChangeCarrierManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.carrier.glc.changecarrier.LotChangeCarrierManagerEditor', null, 'LotChangeCarrierManager', null, 'carrier-change', null);

DELETE AD_FORM_RELEASE WHERE NAME = 'LotChangeCarrierManager';
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'LotChangeCarrierManager' NAME, TO_TIMESTAMP('2024/1/4 18:47:08', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('420242594576977920', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次更改载具', 'Active', b.release_timestamp, 'Y', 'Y');
  
  
