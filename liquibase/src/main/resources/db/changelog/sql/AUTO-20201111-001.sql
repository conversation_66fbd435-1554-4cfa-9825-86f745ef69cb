delete from AD_MESSAGE where KEY_<PERSON> in ('auto.unload_no_component_units','auto.unload_type_unsupported','auto.unload_qty_not_match');

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('122434', '0', 'Y', 'auto.unload_no_component_units', 'When exiting the port, there is no Component Unit information, but Lot information is found!', '退Port时，没有片信息，但是有查到批次信息！', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('122436', '0', 'Y', 'auto.unload_type_unsupported', 'Unsupported exit port type!', '不支持的退Port类型！', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAG<PERSON>, MESSAGE_ZH, MESSAGE_RES)
values ('122437', '0', 'Y', 'auto.unload_qty_not_match', 'The Lot quantity does not match the Component Unit quantity!', '批次数量与片数量不匹配！', null);
