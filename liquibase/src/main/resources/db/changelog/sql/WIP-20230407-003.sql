delete ad_message where <PERSON><PERSON><PERSON><PERSON><PERSON> in ('wip.target_step_selection','wip.select_a_step_as_the_target_step','wip.procedure_preview','wip.procedure_after_lot_procedure_change');

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2023050707702', '0', 'Y', 'wip.target_step_selection', 'Target step selection', '目标工步选择', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2023050707703', '0', 'Y', 'wip.select_a_step_as_the_target_step', 'Select a step as the target step', '激活时流程变更会立即生效，请选择目标工步', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, ME<PERSON>AG<PERSON>, MESSAGE_ZH, MESSAGE_RES)
values ('2023050707704', '0', 'Y', 'wip.procedure_preview', 'Procedure Preview', '流程预览', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2023050707705', '0', 'Y', 'wip.procedure_after_lot_procedure_change', 'Procedure after lot procedure change', '批次流程变更后的流程', null);

delete ad_message where KEY_ID = 'wip.lot_procedure_change_effective_immediately';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('2023050707701', '0', 'Y', 'wip.lot_procedure_change_effective_immediately', 'The process change will take effect immediately upon activation, with step% 1 $s. Do you want to continue?', '激活时流程变更会立即生效，工步为%1$s。是否继续?', null);
