delete from AD_EDITOR where OBJECT_RRN in (select editor_rrn from AD_AUTHORITY where name in ('Wip.AssignCarrier','Wip.SortingSplit'));
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('2016082401', '0', 'Y', 'LotAssignManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.carrier.glc.LotAssignManagerEditor', null, 'LotAssignManager', null, 'assign', null);
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('2017082901', '0', 'Y', 'CarrierLotSplitManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.carrier.glc.split.CarrierLotSplitManagerEditor', null, 'CarrierLotSplitManager', null, 'carrier-split', null);

delete from AD_AUTHORITY where name in ('Wip.AssignCarrier','Wip.SortingSplit');
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2016092901', '0', 'Y', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', to_date('22-12-2023 16:47:15', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.SortingSplit', '载具分批', 'F', 'E', '2017082901', '603', '40', 'Lot Split(Sorter)', '载具分批', null, 'carrier-split', 'MES', 'com.glory.mes.wip', null);
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('2016082401', '0', 'Y', to_date('14-09-2011', 'dd-mm-yyyy'), 'admin', to_date('21-12-2023 12:00:35', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Wip.AssignCarrier', '绑定载具', 'F', 'E', '2016082401', '603', '10', 'Lot Assign Carrier', '绑定载具', null, 'assign', 'MES', 'com.glory.mes.wip', null);

delete ad_form_attribute where form_rrn = (select object_rrn from ad_form where name = 'WIPLotFutureStep');
delete ad_form where name = 'WIPLotFutureStep';

DELETE AD_FIELD where TABLE_RRN IN (SELECT OBJECT_RRN FROM AD_TABLE WHERE NAME IN ('WIPLotFutureStep'));
DELETE AD_TAB where TABLE_RRN IN (SELECT OBJECT_RRN FROM AD_TABLE WHERE NAME IN ('WIPLotFutureStep'));
DELETE AD_TABLE WHERE NAME IN ('WIPLotFutureStep');

delete AD_MESSAGE where KEY_ID in ('ras.source_port','ras.target_port','wip.select_node_is_incorrect');
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('416650668119154688', '0', 'Y', 'ras.source_port', 'Source Port ID', '源Port', null);
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('416651019882848256', '0', 'Y', 'ras.target_port', 'Target Port ID', '目标Port', null);
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('417007804388995072', '0', 'Y', 'wip.select_node_is_incorrect', 'Select node is incorrect,Please select correct node!', '选择的节点不正确，请选择正确的工步节点!', null);

DELETE AD_FORM_RELEASE WHERE NAME = 'LotAssignManager';
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'LotAssignManager' NAME, TO_TIMESTAMP('2023/12/21 18:47:08', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('419519696273612800', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次绑定载具', 'Active', b.release_timestamp, 'Y', 'Y');
	
DELETE AD_FORM_RELEASE WHERE NAME = 'CarrierLotSplitManager';
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'CarrierLotSplitManager' NAME, TO_TIMESTAMP('2023/12/25 17:20:07', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('419519696273612801', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '载具批次分批管理', 'Active', b.release_timestamp, 'Y', 'Y');

DELETE AD_FORM_RELEASE WHERE NAME = 'SelectFutureMergeStepDialog';
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'SelectFutureMergeStepDialog' NAME, TO_TIMESTAMP('2023/12/26 17:07:20', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('419519696273612802', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '载具未来合批工步', 'Active', b.release_timestamp, 'Y', 'Y');
	