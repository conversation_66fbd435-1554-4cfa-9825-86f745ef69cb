
UPDATE ad_table SET init_where_clause = 'name not in (''REC<PERSON><PERSON>'',''OCAPTYPE'',''RETICLE'',''LOTTECN'',''EDCTECN'',''FPQ'')', where_clause = 'name not in (''RECIPE'',''OCAPTYPE'',''RETIC<PERSON>'',''LOTTECN'',''EDCTECN'',''FPQ'')' where name = 'COMContextName';

delete COM_CONTEXT_RULE where CONTEXT_RRN = 145;
delete COM_CONTEXT where OBJECT_RRN = 145;
delete ad_field where TABLE_RRN = 76151;
delete ad_table where OBJECT_RRN = 76151;
insert into ad_table (OBJECT_RRN , ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, M<PERSON>EL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE, IS_DESIGNER)
values ('76151', '0', 'Y', to_date('21-12-2023', 'dd-mm-yyyy'), 'admin', to_date('21-12-2023 15:10:21', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'MMPackageRuleContextValue', 'PackeageRuleContext查询', 'COM_CONTEXT_VALUE', 'N', 'ContextValue', 'com.glory.common.context.model.ContextValue', ' status = ''Active'' AND contextRrn = 145', null, ' status = ''Active''', 'N', '1', '1', 'PackeageRule Context Query', 'PackeageRule Context查询', null, '1', null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('3100111', '0', 'Y', to_date('29-09-2011 17:23:10', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('21-12-2023 15:10:21', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '11', 'effectDateFrom', '生效时间从', null, '76151', null, '10', null, 'N', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'datetime', 'date', null, null, null, null, null, null, null, null, null, 'Effective Date From', '生效时间从', 'N', 'N', 'N', null, null, null, null, null, null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('3100121', '0', 'Y', to_date('29-09-2011 17:26:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('21-12-2023 15:10:21', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '12', 'contextFieldValue2', '产品名称', null, '76151', null, '40', null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'search', 'string', null, null, null, null, '6164324', null, null, null, null, 'Product Name', '产品名称', 'Y', 'N', 'N', null, null, null, null, null, null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('3100131', '0', 'Y', to_date('29-09-2011 17:30:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('21-12-2023 15:10:21', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '23', 'resultValue1', '包装规则名称', null, '76151', null, '60', null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', 'string', null, null, null, null, '126255', null, null, null, null, 'PackageRuleName', '包装规则名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('3100151', '0', 'Y', to_date('29-09-2011 17:24:10', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('21-12-2023 15:10:21', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '10', 'effectDateTo', '生效时间到', null, '76151', null, '20', null, 'N', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'datetime', 'date', null, null, null, null, null, null, null, null, null, 'Effective Date To', '生效时间到', 'N', 'N', 'N', null, null, null, null, null, null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('3100161', '0', 'Y', to_date('29-09-2011 17:25:17', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('21-12-2023 15:10:21', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '18', 'contextFieldValue1', '包装类型', null, '76151', null, '30', null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', 'string', null, null, null, null, '126255', null, null, null, null, 'Package Type', '包装类型', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('210653', '0', 'Y', to_date('23-06-2020 10:38:51', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('21-12-2023 15:10:21', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '9', 'contextFieldValue3', '客户代码', null, '76151', null, '50', null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, '186542', null, null, null, null, 'Customer Code', '客户代码', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into COM_CONTEXT (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, TABLE_RRN, IS_REPEATABLE, CONTEXT_FIELD_RRN1, CONTEXT_FIELD_RRN2, CONTEXT_FIELD_RRN3, CONTEXT_FIELD_RRN4, CONTEXT_FIELD_RRN5, CONTEXT_FIELD_RRN6, CONTEXT_FIELD_RRN7, CONTEXT_FIELD_RRN8, CONTEXT_FIELD_RRN9, CONTEXT_FIELD_RRN10, CONTEXT_FIELD_ID1, CONTEXT_FIELD_ID2, CONTEXT_FIELD_ID3, CONTEXT_FIELD_ID4, CONTEXT_FIELD_ID5, CONTEXT_FIELD_ID6, CONTEXT_FIELD_ID7, CONTEXT_FIELD_ID8, CONTEXT_FIELD_ID9, CONTEXT_FIELD_ID10, RESULT_FIELD_RRN1, RESULT_FIELD_RRN2, RESULT_FIELD_RRN3, RESULT_FIELD_RRN4, RESULT_FIELD_RRN5, RESULT_FIELD_RRN6, RESULT_FIELD_RRN7, RESULT_FIELD_RRN8, RESULT_FIELD_RRN9, RESULT_FIELD_RRN10, RESULT_FIELD_ID1, RESULT_FIELD_ID2, RESULT_FIELD_ID3, RESULT_FIELD_ID4, RESULT_FIELD_ID5, RESULT_FIELD_ID6, RESULT_FIELD_ID7, RESULT_FIELD_ID8, RESULT_FIELD_ID9, RESULT_FIELD_ID10, DESCRIPTION)
values ('145', '0', 'Y', to_date('21-12-2023', 'dd-mm-yyyy'), 'admin', to_date('21-12-2023', 'dd-mm-yyyy'), 'admin', '1', 'PACKAGERULE', '76151', 'Y', '3100161', '3100121', '210653', null, null, null, null, null, null, null, 'packageType', 'partName', 'customerCode', null, null, null, null, null, null, null, '3100131', null, null, null, null, null, null, null, null, null, 'packageRuleName', null, null, null, null, null, null, null, null, null, null);

insert into COM_CONTEXT_RULE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, CONTEXT_RRN, SEQ_NO, CONTEXT_FIELD_FLAG1, CONTEXT_FIELD_FLAG2, CONTEXT_FIELD_FLAG3, CONTEXT_FIELD_FLAG4, CONTEXT_FIELD_FLAG5, CONTEXT_FIELD_FLAG6, CONTEXT_FIELD_FLAG7, CONTEXT_FIELD_FLAG8, CONTEXT_FIELD_FLAG9, CONTEXT_FIELD_FLAG10)
values ('2023122201', '0', 'Y', to_date('22-12-2023', 'dd-mm-yyyy'), 'admin', to_date('22-12-2023', 'dd-mm-yyyy'), 'admin', '1', '145', '1', null, null, null, null, null, null, null, null, null, null);