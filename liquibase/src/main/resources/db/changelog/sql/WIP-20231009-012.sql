
DELETE AD_EDITOR WHERE OBJECT_RRN = '40001';
DELETE AD_BUTTON_AUTHORITY WHERE OBJECT_RRN = '2022021102';
DELETE AD_AUTHORITY WHERE OBJECT_RRN = '202112310007';
insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('202112310007', '0', 'Y', to_date('08-05-2017', 'dd-mm-yyyy'), 'admin', to_date('08-05-2017', 'dd-mm-yyyy'), 'admin', '1', 'Wip.Backup.backUp', 'Wip.Backup.backUp', 'B', null, null, '6037', '10', 'Backup', '退步', null, 'production_order', 'MES', null, null);

insert into AD_BUTTON_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_KEY, AUTHORITY_DESC, AUTHORITY_ACTION_ID, IS_ENABLE)
values ('2022021102', '0', 'Y', to_date('11-02-2022', 'dd-mm-yyyy'), 'admin', to_date('11-02-2022', 'dd-mm-yyyy'), 'admin', '0', 'Wip.Backup.backUp', 'Wip.Backup.backUp', 'UserAuth', 'Y');

insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('40001', '0', 'Y', 'BackUpManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.backup.BackUpManagerEditor', '6035', 'BackUpManager', null, 'unskip-lot', null);

 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'BackUpManager' NAME, TO_TIMESTAMP('2023/10/09 12:00:33', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('387984676331376642', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 