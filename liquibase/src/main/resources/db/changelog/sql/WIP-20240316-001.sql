delete ad_message where KEY_ID = 'wip.no_abnormal_eqp_info';

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('446255534819962880', '0', 'Y', 'wip.no_abnormal_eqp_info', 'No abnormal Equipment Info', '无异常设备信息', null);

MERGE INTO AD_FORM_RELEASE a
		 USING (SELECT 'TrackInMultiSubEquipmentPage' NAME, TO_TIMESTAMP('2024/3/16 10:27:53', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
		 ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
		 WHEN MATCHED THEN 
 		 UPDATE SET a.IS_UPDATE = 'Y'
		 WHEN NOT MATCHED THEN 
 		 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 		 VALUES ('446257474874306560', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');