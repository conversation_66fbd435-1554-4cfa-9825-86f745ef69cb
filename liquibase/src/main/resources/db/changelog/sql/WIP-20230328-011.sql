delete ad_message where KEY_ID = 'wip.select_step_exist_rework_procedure';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('3433313', '0', 'Y', 'wip.select_step_exist_rework_procedure', 'Select step exist rework procedure!', '选择的工步存在返工流程！', null);

delete ad_impexp_field_map t where t.sub_rrn = 308549029620453376 and t.field_name = 'attributeName';

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('317667015321014272', '0', 'Y', to_date('27-03-2023 14:15:18', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('27-03-2023 14:15:18', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308549029620453376', 'attributeName', '60', 'Y', 'LOTATTRIBUTENAME');