DELETE AD_MESSAGE WHERE OBJECT_RRN = '202401081104001';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401081104001', '0', 'Y', 'mm.tray_bar_action', 'Tray Bar Action', '托盘动作', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN in ('202401111104001','202401111104002');
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401111104001', '0', 'Y', 'wip.component_his', 'Component His', '组件事务历史', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401111104002', '0', 'Y', 'wip.component_other_detail', 'Component Other Detail', '组件其他信息', null);

UPDATE AD_TABLE
SET WHERE_CLAUSE='state = ''TRACKOUT''', INIT_WHERE_CLAUSE='state = ''TRACKOUT'''
WHERE OBJECT_RRN=139051484045238272;
