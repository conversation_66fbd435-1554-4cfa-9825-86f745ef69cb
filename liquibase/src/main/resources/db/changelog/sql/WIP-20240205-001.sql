delete ad_message where OBJECT_RRN = '20244402040001';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040001', '0', 'Y', 'context-1000: common.context_object_not_found', 'Context Object Not Found!', '找不到上下文对象！', null);

delete ad_message where OBJECT_RRN = '20244402040002';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040002', '0', 'Y', 'context-1001: common.context_name_not_found', 'Context Name Not Found!', '找不到上下文名称！', null);

delete ad_message where OBJECT_RRN = '20244402040003';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040003', '0', 'Y', 'context-1002: common.context_rule_not_found', 'Context Rule Not Found!', '找不到上下文规则！', null);

delete ad_message where OBJECT_RRN = '20244402040004';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040004', '0', 'Y', 'context-1003: common.context_field_not_found', 'Context Field Not Found!', '找不到上下文字段！', null);

delete ad_message where OBJECT_RRN = '20244402040005';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040005', '0', 'Y', 'context-1004: common.context_value_repeate', 'Context Value Repeate!', '上下文值重复！', null);

delete ad_message where OBJECT_RRN = '20244402040006';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040006', '0', 'Y', 'excel-1000: common.field_not_found_in_header', 'Field %1$s is not found in header!', '在表头中找不到栏位%1$s！', null);

delete ad_message where OBJECT_RRN = '20244402040007';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040007', '0', 'Y', 'excel-1001: commom.import_template_field_size_zero', 'The import field is not checked in the dynamic table.', '动态表没有勾选导入栏位！', null);

delete ad_message where OBJECT_RRN = '20244402040008';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040008', '0', 'Y', 'edc-1209: edc.sampling_unsupport_sample_type', 'SamplingPlan Unsupport Sample Type', '抽检类型不支持！', null);

delete ad_message where OBJECT_RRN = '20244402040009';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040009', '0', 'Y', 'edc-1210: edc.interval_lines_is_null', 'SamplingPlan Line is null.', '抽检详情为空！', null);

delete ad_message where OBJECT_RRN = '20244402040010';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040010', '0', 'Y', 'edc-1211: edc.samping_total_size_is_null', 'SamplingPlan Total size is null.', '抽检总数为空！', null);

delete ad_message where OBJECT_RRN = '20244402040011';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040011', '0', 'Y', 'edc-1212: edc.samping_sample_size_is_null', 'SamplingPlan  size is null.', '抽检数为空！', null);

delete ad_message where OBJECT_RRN = '20244402040012';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040012', '0', 'Y', 'edc-1213: edc.samping_sample_first_is_null', 'First SamplingPlan is null.', '首检为空！', null);

delete ad_message where OBJECT_RRN = '20244402040013';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040013', '0', 'Y', 'edc-1214: edc.samping_unsupport_sample_rule', 'SamplingPlan Unsupport Sample Rule', '抽检规则不支持！', null);

delete ad_message where OBJECT_RRN = '20244402040014';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040014', '0', 'Y', 'edc-1215: edc.sampling_current_sampling_is_not_exist', 'Current SamplingPlan is not Exist', '当前抽检不存在！', null);

delete ad_message where OBJECT_RRN = '20244402040015';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040015', '0', 'Y', 'msgsend-1005: msgsend.message_have_no_body', 'Message hava no body', '消息没有正文！', null);

delete ad_message where OBJECT_RRN = '20244402040016';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('20244402040016', '0', 'Y', 'mm-2175: mm.mlot_is_in_durable', 'MLot %1$s is in durable', '物料批%1$s在载具中！', null);
