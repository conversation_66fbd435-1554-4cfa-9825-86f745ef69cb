delete ad_message where OBJECT_RRN = 202401251149288;

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149288', '0', 'Y', 'wip-8015: wip.no_step_permission', 'User no step permission!', '用户没有工步权限！', null);

update ad_editor set param2 = 'PPWorkOrdeLotStartManager' where object_rrn = 21091501;

 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'PPWorkOrdeLotStartManager' NAME, TO_TIMESTAMP('2024/2/20 17:08:24', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('437298573358514176', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 