delete ad_editor t where t.object_rrn = 6190;
insert into ad_editor (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('6190', '0', 'Y', 'LotTecnEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.tecn.LotTecnEditor', null, 'LotTecnManager', null, 'wip-query', null);

DELETE FROM AD_FORM_ATTRIBUTE t WHERE t.FORM_RRN IN (SELECT w.object_rrn FROM AD_FORM w WHERE w.name IN ('LotTecnForm', 'LotTecnDownForm'));
DELETE FROM AD_FORM t WHERE t.name IN ('LotTecnForm', 'LotTecnDownForm');

delete AD_BUTTON where TABLE_RRN in (select OBJECT_RRN from ad_table where name in ('LotTecnDown','LotTecn'));
delete ad_field where TABLE_RRN in (select OBJECT_RRN from ad_table where name in ('LotTecnDown','LotTecn'));
delete ad_tab where TABLE_RRN in (select OBJECT_RRN from ad_table where name in ('LotTecnDown','LotTecn'));
delete ad_table where name in ('LotTecnDown','LotTecn');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'LotTecnManager' NAME, TO_TIMESTAMP('2023/7/3 11:28:19', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
  UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
  INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
  VALUES ('353144813067689984', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次临时工程变更', 'Active', b.release_timestamp, 'Y', 'Y');
