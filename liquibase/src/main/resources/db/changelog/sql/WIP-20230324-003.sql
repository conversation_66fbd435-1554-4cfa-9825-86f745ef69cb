--导入导出问题
--设备能力管理管理
DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN IN ('305013593551085571','305013593551085569','305013593551085570');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305013593551085571', '0', 'Y', to_date('20-02-2023 16:15:07', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 17:47:08', 'dd-mm-yyyy hh24:mi:ss'), null, '3', '305013593551085568', null, 'isAvailable', '20', 'N', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305013593551085569', '0', 'Y', to_date('20-02-2023 16:15:07', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 17:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305013593551085568', null, 'capaRrn', '15', 'Y', 'CAPANAME');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305013593551085570', '0', 'Y', to_date('20-02-2023 16:15:07', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 17:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305013593551085568', null, 'equipmentRrn', '5', 'Y', 'EQUIPMENTID');

--设备管理
DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN IN ('305006917540323343','305006917540323342','305006917540323341','305006917540323331','305006917540323332','305006917540323346',
'305006917540323340','305006917540323336','305006917540323335','305006917540323334','305006917540323339','305006917540323333','305006917540323337','305013593551085571','305013593551085569','305013593551085570');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323343', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'subState', '120', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323342', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'state', '110', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323341', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'comClass', '100', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323331', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'location', '30', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323332', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'subLocation', '31', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323346', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'controlQtyType', '160', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323340', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'subEqpType', '90', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323336', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'positionSetRrn', '60', 'Y', 'POSITIONSETNAME');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323335', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'eqpGroup', '50', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323334', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'eqpType', '40', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323339', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'parentEqpRrn', '80', 'Y', 'PARENTEQPID');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323333', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'category', '35', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305006917540323337', '0', 'Y', to_date('20-02-2023 15:48:35', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 18:07:52', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305006917540323328', null, 'statusModelRrn', '70', 'Y', 'STATUSMODELNAME');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305013593551085571', '0', 'Y', to_date('20-02-2023 16:15:07', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 17:47:08', 'dd-mm-yyyy hh24:mi:ss'), null, '3', '305013593551085568', null, 'isAvailable', '20', 'N', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305013593551085569', '0', 'Y', to_date('20-02-2023 16:15:07', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 17:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305013593551085568', null, 'capaRrn', '15', 'Y', 'CAPANAME');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305013593551085570', '0', 'Y', to_date('20-02-2023 16:15:07', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('23-03-2023 17:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305013593551085568', null, 'equipmentRrn', '5', 'Y', 'EQUIPMENTID');


--端口管理
DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN IN ('316496518410911746','316496518410911745','305030784728113158','305030784728113157','305030784728113156','316496518410911744','305030784728113160','305030784728113159');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316496518410911746', '0', 'Y', to_date('24-03-2023 08:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 08:51:30', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305030784728113152', null, 'transferState', '140', 'N', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316496518410911745', '0', 'Y', to_date('24-03-2023 08:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 08:44:53', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305030784728113152', null, 'mainMatType', '150', 'N', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305030784728113158', '0', 'Y', to_date('20-02-2023 17:23:26', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 08:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305030784728113152', null, 'statusModelRrn', '70', 'Y', 'STATUSMODEL');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305030784728113157', '0', 'Y', to_date('20-02-2023 17:23:26', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 08:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305030784728113152', null, 'parentEqpRrn', '60', 'Y', 'PARENTEQPID');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305030784728113156', '0', 'Y', to_date('20-02-2023 17:23:26', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 08:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, '3', '305030784728113152', null, 'portType', '50', 'Y', 'PORTTYPE');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316496518410911744', '0', 'Y', to_date('24-03-2023 08:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 08:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '305030784728113152', null, 'holdState', '40', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305030784728113160', '0', 'Y', to_date('20-02-2023 17:23:26', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 08:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, '3', '305030784728113152', null, 'accessState', '120', 'Y', 'ACCESSSTATE');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305030784728113159', '0', 'Y', to_date('20-02-2023 17:23:26', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 08:44:10', 'dd-mm-yyyy hh24:mi:ss'), null, '3', '305030784728113152', null, 'durableType', '110', 'Y', 'DURABLETYPE');

--设备权限
DELETE AD_FIELD WHERE OBJECT_RRN IN ('50410');
insert into AD_FIELD (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('50410', '0', 'Y', to_date('16-07-2009 21:42:00', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 09:15:18', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '23', 'equipmentId', '设备号', 'EQUIPMENTID', '504', '50410', '20', null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'hidden', 'string', null, null, null, null, null, null, null, 'equipmentRrn.equipmentId', null, 'EquipmentId', '设备号', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

--治具规格
DELETE AD_MESSAGE WHERE KEY_ID = 'mm.toolspec_in_use';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('316508870850101248', '0', 'Y', 'mm.toolspec_in_use', 'Tool Spec In Use', '治具规格已被使用', null);

DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN IN ('306104627257331716','306104627257331715');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('306104627257331716', '0', 'Y', to_date('23-02-2023 16:30:30', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 09:56:37', 'dd-mm-yyyy hh24:mi:ss'), null, '3', '306104627253137408', null, 'statusModelRrn', '50', 'Y', 'STATUSMODEL');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('306104627257331715', '0', 'Y', to_date('23-02-2023 16:30:30', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 09:56:37', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '306104627253137408', null, 'materialType', '40', 'Y', null);

--治具接收
DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN IN ('305014732011020292','305014732011020293','305014732011020295','316517739630419968','305014732011020291');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305014732011020292', '0', 'Y', to_date('20-02-2023 16:19:39', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:33:56', 'dd-mm-yyyy hh24:mi:ss'), null, '4', '305014732011020288', null, 'holdState', '60', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305014732011020293', '0', 'Y', to_date('20-02-2023 16:19:39', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:30:26', 'dd-mm-yyyy hh24:mi:ss'), null, '4', '305014732011020288', null, 'cleanState', '70', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305014732011020295', '0', 'Y', to_date('20-02-2023 16:19:39', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:30:26', 'dd-mm-yyyy hh24:mi:ss'), null, '4', '305014732011020288', null, 'partnerCode', '120', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316517739630419968', '0', 'Y', to_date('24-03-2023 10:08:29', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:30:26', 'dd-mm-yyyy hh24:mi:ss'), null, '5', '305014732011020288', null, 'transWarehouseRrn', '10', 'Y', 'TRANSWAREHOUSEID');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305014732011020291', '0', 'Y', to_date('20-02-2023 16:19:39', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:06:56', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305014732011020288', null, 'materialName', '30', 'Y', null);

--物料信息
DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN IN ('316525462648512513','316525529623158784','316525529623158785','304939061066801156','304939061066801155'，
'316525462648512512','304939061066801157');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316525462648512513', '0', 'Y', to_date('24-03-2023 10:39:11', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:40:28', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304939061062606848', null, 'mainMatType', '57', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316525529623158784', '0', 'Y', to_date('24-03-2023 10:39:27', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:39:27', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '304939061062606848', null, 'spec1', '40', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316525529623158785', '0', 'Y', to_date('24-03-2023 10:39:27', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:39:27', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '304939061062606848', null, 'spec2', '45', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('304939061066801156', '0', 'Y', to_date('20-02-2023 11:18:57', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:39:11', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304939061062606848', null, 'statusModelRrn', '38', 'Y', 'STATEMODEL');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('304939061066801155', '0', 'Y', to_date('20-02-2023 11:18:57', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:39:11', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304939061062606848', null, 'materialType', '27', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316525462648512512', '0', 'Y', to_date('24-03-2023 10:39:11', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:39:11', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '304939061062606848', null, 'category', '36', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('304939061066801157', '0', 'Y', to_date('20-02-2023 11:18:57', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 10:39:11', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304939061062606848', null, 'batchType', '37', 'Y', null);

DELETE AD_FIELD WHERE OBJECT_RRN IN ('4875127');
insert into AD_FIELD (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('4875127', '0', 'Y', to_date('17-03-2014 10:00:30', 'dd-mm-yyyy hh24:mi:ss'), '1', to_date('24-03-2023 10:45:33', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '17', 'name', '物料名称', 'NAME', '70351', '4875125', '10', null, 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', '^$|^[A-Za-z0-9-.-_]+$', null, null, null, null, null, null, null, null, 'Name', '物料名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

--BOM设置
DELETE AD_IMPEXP_FIELD_MAP WHERE SUB_RRN = '308282245239005184' AND PARENT_RRN IS NULL;
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844485140480', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'isCritical', '90', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844510306304', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'flushType', '100', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844535472128', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'isOptional', '110', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844464168960', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'isMain', '80', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844413837312', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'unitQty', '70', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844397060096', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'stepName', '30', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844615163904', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'alternateStrategy', '140', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844552249344', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'isAlternate', '120', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844652912640', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'isAssembly', '170', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844631941120', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'alternatePriority', '150', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844569026560', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'isProduction', '125', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844594192384', '0', 'Y', to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'alternateGroup', '130', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844338339840', '0', 'Y', to_date('24-03-2023 11:08:30', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:30', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'materialName', '10', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844317368320', '0', 'Y', to_date('24-03-2023 11:08:30', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:30', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'seqNo', '5', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844380282880', '0', 'Y', to_date('24-03-2023 11:08:30', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:30', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'uomId', '25', null, null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316532844355117056', '0', 'Y', to_date('24-03-2023 11:08:30', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:08:30', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308282245239005184', 'itemCategory', '25', 'Y', null);

DELETE AD_FIELD WHERE OBJECT_RRN IN ('308279945103663109','308279945103663108');
insert into AD_FIELD (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('308279945103663109', '0', 'Y', to_date('01-03-2023 16:34:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('24-03-2023 11:06:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '13', 'stepName', '工序名称', 'STEPNAME', '308279945103663104', null, '30', '20', 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'search', 'string', null, null, null, null, '10512', null, null, null, null, 'Step Name', '工序名称', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

insert into AD_FIELD (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE, IS_DESIGNER)
values ('308279945103663108', '0', 'Y', to_date('01-03-2023 16:34:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('24-03-2023 11:06:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '21', 'itemCategory', '物料类别', 'ITEMCATEGORY', '308279945103663104', null, '25', null, 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, '11962', null, null, null, null, 'Material Type', '物料类别', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null, null);

--载具信息
DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN IN ('304945111828471811','304945111828471810','304945111828471809','304945111828471816','304945111828471815');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('304945111828471811', '0', 'Y', to_date('20-02-2023 11:43:00', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:32:02', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304945111824277504', null, 'cleanState', '80', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('304945111828471810', '0', 'Y', to_date('20-02-2023 11:43:00', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:32:02', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304945111824277504', null, 'holdState', '65', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('304945111828471809', '0', 'Y', to_date('20-02-2023 11:43:00', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:32:02', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304945111824277504', null, 'durableSpecName', '20', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('304945111828471816', '0', 'Y', to_date('20-02-2023 11:43:00', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:32:02', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304945111824277504', null, 'locatorId', '140', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('304945111828471815', '0', 'Y', to_date('20-02-2023 11:43:00', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 11:32:02', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304945111824277504', null, 'warehouseId', '135', 'Y', null);

--载具清洗限制
DELETE AD_BUTTON WHERE OBJECT_RRN = '307573720690970624';
insert into AD_BUTTON (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, STYLE, FORM_RRN, TAB_RRN, SEQ_NO, IMAGE, IS_AUTHORITY, IS_SEPERAOTR, IS_USE_DEFAULT, DEFAULT_HANDLER, BPM_EVENT, LABEL, LABEL_ZH, LABEL_RES, CATEGORY, FIELD_RRN, TABLE_RRN, DATA_FROM)
values ('307573720690970624', '0', 'Y', to_date('27-02-2023 17:48:09', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-02-2023 17:48:09', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'export', '0', null, '183952738223255553', '3', 'export', 'N', 'Y', 'N', null, null, 'Export', '导出', null, null, null, '183952738219061248', 'equipmentList');

--晶圆来料接收
---------- Start Export ADTable [MMMComponentUnit] ----------
DELETE AD_FIELD WHERE TABLE_RRN = '93594094';
DELETE ad_table WHERE OBJECT_RRN = '93594094';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 93594094, 0, 'Y', to_date('2017-10-30 13:58:26', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:00:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 19, 'MMMComponentUnit', '晶圆原料表', 'MM_COMPONENTUNIT', 'N', 'MComponentUnit', 'com.glory.mes.mm.lot.model.MComponentUnit', null, null, 'state = ''IN''', 'N', 1, 2, 'Wafer List', '晶圆原料表', null, 1); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93967985, 0, 'Y', to_date('2018-11-28 11:05:10', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:00:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 17, 'position', '位置', 'POSITION', 93594094, null, 5, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Position', '位置', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93594095, 0, 'Y', to_date('2017-10-30 13:59:37', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:00:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 26, 'mComponentId', '晶圆片号', 'COMPONETID', 93594094, null, 10, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, 'IQCBadCode', null, null, 'Wafer ID', '晶圆片号', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93594096, 0, 'Y', to_date('2017-10-30 14:00:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:00:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 18, 'materialName', '晶圆料号', 'MATERIALNAME', 93594094, null, 20, null, 'Y', 'N', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', 'string', null, null, null, null, 21444, null, null, null, null, 'Wafer Material Name', '晶圆料号', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 93594097, 0, 'Y', to_date('2017-10-30 14:01:44', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:00:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 21, 'subQty', 'Die数量', null, 93594094, null, 30, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Die Qty', 'Die数量', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 305036557319348226, 0, 'Y', to_date('2023-02-20 17:46:22', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:00:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 16, 'partnerLotId', '客户名称', 'CUSTOMERLOTID', 93594094, null, 40, null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'PartnerLotId', 'PartnerLotId', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 305036557319348224, 0, 'Y', to_date('2023-02-20 17:46:22', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:00:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 16, 'durable', '载具号', 'CARRIERID', 93594094, null, 50, null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 29828955, null, null, null, null, 'Durable', 'Durable', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 305036557319348225, 0, 'Y', to_date('2023-02-20 17:46:22', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:00:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 16, 'partnerCode', '客户码', 'CUSTOMERCODE', 93594094, null, 60, null, 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 2161, null, null, null, null, 'PartnerCode', 'PartnerCode', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308254923437891584, 0, 'Y', to_date('2023-03-01 14:55:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:00:48', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 12, 'lotComment', '备注', 'LOTCOMMENT', 93594094, null, 70, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'LotComment', 'LotComment', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 

DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN IN ('305037201941929987','305037201941929991','305037201941929990');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305037201941929987', '0', 'Y', to_date('20-02-2023 17:48:56', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 15:01:07', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305037201941929984', null, 'materialName', '20', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305037201941929991', '0', 'Y', to_date('20-02-2023 17:48:56', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 14:56:26', 'dd-mm-yyyy hh24:mi:ss'), null, '3', '305037201941929984', null, 'partnerCode', '60', 'Y', 'CUSTOMERCODE');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305037201941929990', '0', 'Y', to_date('20-02-2023 17:48:56', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 14:56:26', 'dd-mm-yyyy hh24:mi:ss'), null, '3', '305037201941929984', null, 'durable', '50', 'Y', 'CARRIERID');

--数据采集项集
DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN = '304936288057237519';
DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN IN ('304936288057237521','304936288057237520','304936288057237522');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('304936288057237521', '0', 'Y', to_date('20-02-2023 11:07:56', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 15:39:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304936288053043200', null, 'sampleLevel', '200', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('304936288057237520', '0', 'Y', to_date('20-02-2023 11:07:56', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 15:39:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304936288053043200', null, 'specType', '190', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('304936288057237522', '0', 'Y', to_date('20-02-2023 11:07:56', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 15:39:46', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304936288053043200', null, 'defectCode', '210', 'Y', null);

DELETE AD_FIELD WHERE TABLE_RRN = '104001';
DELETE ad_table WHERE OBJECT_RRN = '104001';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 104001, 0, 'Y', to_date('2019-02-14 14:34:20', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'EDCItemSetUploadTemp', '数据采集项集导入临时表', null, 'N', 'EdcItemSetUploadTemp', 'com.glory.edc.model.EdcItemSetUploadTemp', null, null, null, 'N', null, null, null, null, null, 3); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191005278212, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 9, 'itemSetName', '采集项集名称', 'ITEMSETNAME', 104001, null, 10, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'ItemSetName', '采集项集名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191005278211, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 9, 'itemSetDescription', '采集项集描述', 'ITEMSETDESCRIPTION', 104001, null, 20, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'ItemSetDescription', '采集项集描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191005278210, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 9, 'itemName', '采集项名称', 'ITEMNAME', 104001, null, 25, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'ItemName', '采集项名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191005278209, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 8, 'edcItem.description', '采集项描述', null, 104001, null, 27, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'itemNameDescription', '采集项描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191005278217, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 8, 'edcItem.unit', '采集项单位', null, 104001, null, 29, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'userreflist', 'string', null, null, null, null, 14479, null, 'EDCItemUnit', null, null, 'ItemUnit', '采集项', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191009472512, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 8, 'edcItem.digits', '采集项小数点', null, 104001, null, 30, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, '0', '10', null, null, null, null, null, 'ItemDigits', '采集项小数点', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191009472513, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 8, 'edcItem.dataType', '采集项数据类型', null, 104001, null, 32, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'radio', 'string', null, null, null, null, null, 'EDCDataType', null, null, null, 'ItemDataType', '采集项数据类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191009472514, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 8, 'edcItem.sampleType', '采集项采样类型', null, 104001, null, 34, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'EDCSampleType', null, null, null, 'ItemSampleType', '采集项采样类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191009472515, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 8, 'edcItem.comments', '采集项备注', null, 104001, null, 36, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'ItemComments', '采集项备注', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104054, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 16, 'isMandatory', 'isMandatory', 'ISMANDATORY', 104001, null, 40, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'isMandatory', '必须输入', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104055, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'isJudgeByManual', 'isJudgeByManual', 'ISJUDGEBYMANUAL', 104001, null, 50, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'isJudgeByManual', '人为判断', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104056, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'isAutoDc', 'isAutoDc', 'ISAUTODC', 104001, null, 60, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'isAutoDc', '自动收集', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104057, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'isShowEquipment', 'isShowEquipment', 'ISSHOWEQUIPMENT', 104001, null, 70, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'isShowEquipment', '显示设备', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 159245810518077440, 0, 'Y', to_date('2022-01-14 10:25:59', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'isHoldLot', 'isHoldLot', 'ISHOLDLOT', 104001, null, 75, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'isHoldLot', '是否暂停批次', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104058, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'usl', 'usl', 'USL', 104001, null, 80, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'usl', '规范上限', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104059, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'sl', 'sl', 'SL', 104001, null, 90, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'sl', '规范限', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104060, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'lsl', 'lsl', 'LSL', 104001, null, 100, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'lsl', '规范下限', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104061, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'item', 'item', 'ITEM', 104001, null, 110, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'item', '片数', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104062, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'itemDesc', 'itemDesc', 'ITEMDESC', 104001, null, 120, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'itemDesc', '片描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104063, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'comp', 'comp', 'COMP', 104001, null, 130, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'comp', '位置数', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104064, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'compDesc', 'compDesc', 'COMPDESC', 104001, null, 140, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'compDesc', '位置描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104065, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'site', 'site', 'SITE', 104001, null, 150, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'site', '点数', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104066, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'siteDesc', 'siteDesc', 'SITEDESC', 104001, null, 160, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'siteDesc', '点描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 104067, 0, 'Y', to_date('2019-02-14 14:35:27', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'compPosition', 'compPosition', 'COMPPOSITION', 104001, null, 170, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'compPosition', '位置号码', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191005278208, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 9, 'componentSamplingPlan', '组件抽样计划', 'COMPONENTSAMPLINGPLAN', 104001, null, 180, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 384125789507567616, null, null, null, null, 'ComponentSamplingPlan', '组件抽样计划', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191005278214, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 9, 'specType', '规格类型', 'SPECTYPE', 104001, null, 190, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'SpecType', null, null, null, 'SpecType', '规格类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191005278213, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 9, 'sampleLevel', '样本类型', 'SAMPLELEVEL', 104001, null, 200, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'SampleLevel', null, null, null, 'SampleLevel', '样本类型', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191005278215, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 9, 'defectCode', '缺陷码', 'DEFECTCODE', 104001, null, 210, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'userreflist', 'string', null, null, null, null, 14479, null, 'DefectCode', null, null, 'DefectCode', '缺陷码', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 194844191005278216, 0, 'Y', to_date('2022-04-22 16:01:15', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:44:14', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 9, 'attribute', '样本大小', 'ATTRIBUTE', 104001, null, 220, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Attribute', '样本大小', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 

--bin数据采集项集
DELETE AD_IMPEXP_FIELD_MAP WHERE SUB_RRN = '308190889581731840' AND PARENT_RRN IS NULL;
DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN in('308184365157310469','308184365153116166');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612231348224', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'holdReason', '130', null, 'BIN_HOLD_REASON');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612260708352', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'holdOwner', '140', 'Y', 'BIN_HOLD_OWNER');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612323622912', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'comments', '150', null, 'BIN_COMMENTS');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612348788736', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'isUsePartSpec', '160', null, 'IS_USE_PART_SPEC');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612369760256', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'isUseParameter', '170', null, 'IS_USE_PARAMETER');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609611971301376', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'seqNo', '5', null, 'SEQ_NO');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609611988078592', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'name', '10', null, 'BIN_NAME');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612004855808', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'description', '20', null, 'BIN_DESC');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612021633024', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'binType', '30', 'Y', 'BIN_TYPE');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612042604544', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'binGroup', '40', null, 'BIN_GROUP');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612059381760', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'binAction', '50', 'Y', 'BIN_ACTION');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612076158976', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'isActionFixed', '60', null, 'IS_ACTION_FIXED');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612101324800', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'reworkTransition', '70', 'Y', 'REWORK_TRANSITION');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612118102016', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'uslString', '80', null, 'BIN_USL');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612143267840', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'lslString', '90', null, 'BIN_LSL');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612172627968', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'isHoldLot', '100', null, 'BIN_IS_HOLD_LOT');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612189405184', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'isHoldEqp', '110', null, 'BIN_IS_HOLD_EQP');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('316609612214571008', '0', 'Y', to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:13:33', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308190889581731840', 'holdCode', '120', 'Y', 'BIN_HOLD_CODE');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('308184365157310469', '0', 'Y', to_date('01-03-2023 10:14:38', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:12:53', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304934616421572608', null, 'holdOwner', '150', 'Y', 'HOLD_OWNER');

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('308184365153116166', '0', 'Y', to_date('01-03-2023 10:14:38', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:12:53', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '304934616421572608', null, 'owner', '80', 'Y', 'OWNER');

DELETE AD_FIELD WHERE TABLE_RRN = '308176601794207744';
DELETE ad_tab WHERE TABLE_RRN = '308176601794207744';
DELETE ad_table WHERE OBJECT_RRN = '308176601794207744';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 308176601794207744, 0, 'Y', to_date('2023-03-01 09:43:47', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 25, 'EDCBinSetUpload', 'Bin数据采集项集', 'EDC_SET', 'N', 'EdcBinSet', 'com.glory.edc.model.EdcBinSet', null, null, null, 'N', 2, null, 'EdcBinSet', 'Bin数据采集项集', null, 3); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 308176601794207745, 0, 'Y', to_date('2023-03-01 09:43:47', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 23, 'EdcBinSetBase', 'BIN 分组信息', 308176601794207744, null, 2, 'EdcBinGroupInfo', 'BIN 分组信息', null, null, null, 0); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308177429070340100, 0, 'Y', to_date('2023-03-01 09:47:04', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 19, 'name', null, 'BIN_SET_NAME', 308176601794207744, null, 10, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Name', 'Name', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308177429070340096, 0, 'Y', to_date('2023-03-01 09:47:04', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 19, 'description', null, 'BIN_SET_DESC', 308176601794207744, null, 20, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', 'Description', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308177429070340099, 0, 'Y', to_date('2023-03-01 09:47:04', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 19, 'isRepeatable', null, 'IS_REPEATABLE', 308176601794207744, null, 30, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'IsRepeatable', 'IsRepeatable', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308177429070340098, 0, 'Y', to_date('2023-03-01 09:47:04', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 19, 'isCheckTotal', null, 'IS_CHECK_TOTAL', 308176601794207744, null, 40, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'IsCheckTotal', 'IsCheckTotal', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308177429070340097, 0, 'Y', to_date('2023-03-01 09:47:04', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 19, 'isByComponent', null, 'IS_BY_COMPONENT', 308176601794207744, null, 50, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'IsByComponent', 'IsByComponent', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308178488807383040, 0, 'Y', to_date('2023-03-01 09:51:17', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 18, 'isByComponentEdc', null, 'IS_BY_COMPONENT_EDC', 308176601794207744, null, 60, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, null, null, 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308178607569100800, 0, 'Y', to_date('2023-03-01 09:51:45', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 17, 'isBinActionEdit', null, 'IS_BIN_ACTION_EDIT', 308176601794207744, null, 70, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, null, null, 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308179095324712966, 0, 'Y', to_date('2023-03-01 09:53:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'owner', null, 'OWNER', 308176601794207744, null, 80, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'search', 'string', null, null, null, null, 105001, null, null, null, null, 'Owner', 'Owner', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308179095324712967, 0, 'Y', to_date('2023-03-01 09:53:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'uslString', null, 'USL', 308176601794207744, null, 90, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'UslString', 'UslString', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308179095324712965, 0, 'Y', to_date('2023-03-01 09:53:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'lslString', null, 'LSL', 308176601794207744, null, 100, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'LslString', 'LslString', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308179095324712964, 0, 'Y', to_date('2023-03-01 09:53:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'isHoldLot', null, 'IS_HOLD_LOT', 308176601794207744, null, 110, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'IsHoldLot', 'IsHoldLot', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308179095324712963, 0, 'Y', to_date('2023-03-01 09:53:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'isHoldEqp', null, 'IS_HOLD_EQP', 308176601794207744, null, 120, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'IsHoldEqp', 'IsHoldEqp', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308179095324712960, 0, 'Y', to_date('2023-03-01 09:53:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'holdCode', null, 'HOLD_CODE', 308176601794207744, null, 130, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'userreflist', 'string', null, null, null, null, 14479, null, 'HoldCode', null, null, 'HoldCode', 'HoldCode', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308179095324712962, 0, 'Y', to_date('2023-03-01 09:53:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'holdReason', null, 'HOLD_REASON', 308176601794207744, null, 140, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'HoldReason', 'HoldReason', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308179095324712961, 0, 'Y', to_date('2023-03-01 09:53:42', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'holdOwner', null, 'HOLD_OWNER', 308176601794207744, null, 150, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 105001, null, null, null, null, 'HoldOwner', 'HoldOwner', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308179179810578432, 0, 'Y', to_date('2023-03-01 09:54:02', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'isJudgeByManual', null, 'IS_JUDGE_BY_MANUAL', 308176601794207744, null, 160, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, null, null, 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308179786252410880, 0, 'Y', to_date('2023-03-01 09:56:26', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 15:56:00', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'binSetLines', null, null, 308176601794207744, null, 170, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'tablelist', null, null, null, null, null, 308183273086377984, null, null, null, null, null, null, 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 


DELETE AD_FIELD WHERE TABLE_RRN = '308180170677149696';
DELETE ad_tab WHERE TABLE_RRN = '308180170677149696';
DELETE ad_table WHERE OBJECT_RRN = '308180170677149696';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 308180170677149696, 0, 'Y', to_date('2023-03-01 09:57:58', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 18, 'EDCBinSetLineUpload', '数据采集BIN分组信息', 'EDC_SET_LINE', 'N', 'EdcBinSetLine', 'com.glory.edc.model.EdcBinSetLine', null, null, null, 'N', 1, null, 'EDCBinSetLine', '数据采集BIN分组信息', null, 3); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 308180170677149697, 0, 'Y', to_date('2023-03-01 09:57:58', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 17, 'EdcBinSetLineInfo', 'Bin分组采集列', 308180170677149696, 10, 2, 'EdcBinSetLineInfo', 'Bin分组采集列', null, null, null, 0); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308185585263566848, 0, 'Y', to_date('2023-03-01 10:19:29', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 7, 'seqNo', null, 'SEQ_NO', 308180170677149696, null, 5, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'SeqNo', 'SeqNo', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072332, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'name', null, 'BIN_NAME', 308180170677149696, null, 10, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Name', 'Name', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072324, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'description', null, 'BIN_DESC', 308180170677149696, null, 20, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', 'Description', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072322, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'binType', null, 'BIN_TYPE', 308180170677149696, null, 30, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'EDCBinBinType', null, null, null, 'BinType', 'BinType', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072321, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'binGroup', null, 'BIN_GROUP', 308180170677149696, null, 40, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'BinGroup', 'BinGroup', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072320, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'binAction', null, 'BIN_ACTION', 308180170677149696, null, 50, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'sysreflist', 'string', null, null, null, null, 14480, 'EDCBinAction', null, null, null, 'BinAction', 'BinAction', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072328, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'isActionFixed', null, 'IS_ACTION_FIXED', 308180170677149696, null, 60, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'IsActionFixed', 'IsActionFixed', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072333, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'reworkTransition', null, 'REWORK_TRANSITION', 308180170677149696, null, 70, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'userreflist', 'string', null, null, null, null, 14479, null, 'BinReworkTransition', null, null, 'ReworkTransition', 'ReworkTransition', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072334, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'uslString', null, 'BIN_USL', 308180170677149696, null, 80, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'UslString', 'UslString', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072331, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'lslString', null, 'BIN_LSL', 308180170677149696, null, 90, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'double', null, null, null, null, null, null, null, null, null, 'LslString', 'LslString', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072330, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'isHoldLot', null, 'BIN_IS_HOLD_LOT', 308180170677149696, null, 100, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'IsHoldLot', 'IsHoldLot', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072329, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'isHoldEqp', null, 'BIN_IS_HOLD_EQP', 308180170677149696, null, 110, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, 'IsHoldEqp', 'IsHoldEqp', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072325, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'holdCode', null, 'BIN_HOLD_CODE', 308180170677149696, null, 120, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'userreflist', 'string', null, null, null, null, 14479, null, 'HoldCode', null, null, 'HoldCode', 'HoldCode', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072327, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'holdReason', null, 'BIN_HOLD_REASON', 308180170677149696, null, 130, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'HoldReason', 'HoldReason', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072326, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'holdOwner', null, 'BIN_HOLD_OWNER', 308180170677149696, null, 140, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'search', 'string', null, null, null, null, 105001, null, null, null, null, 'HoldOwner', 'HoldOwner', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308180966886072323, 0, 'Y', to_date('2023-03-01 10:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 14, 'comments', null, 'BIN_COMMENTS', 308180170677149696, null, 150, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Comments', 'Comments', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308181079075315712, 0, 'Y', to_date('2023-03-01 10:01:34', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 13, 'isUsePartSpec', null, 'IS_USE_PART_SPEC', 308180170677149696, null, 160, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, null, null, 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 308181229441114112, 0, 'Y', to_date('2023-03-01 10:02:10', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-27 11:21:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 11, 'isUseParameter', null, 'IS_USE_PARAMETER', 308180170677149696, null, 170, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'boolean', 'string', null, null, null, null, null, null, null, null, null, null, null, 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 

DELETE AD_REFLIST where REFERENCE_NAME in ('EDCBinType','EDCBinBinType','EDCBinAction');
DELETE AD_REFNAME where NAME in ('EDCBinType','EDCBinBinType','EDCBinAction');
insert into AD_REFNAME (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, CATEGORY, NAME, DESCRIPTION, REFERENCE_TYPE, LIST_LEVEL)
values ('317616811497885696', '0', 'Y', to_date('27-03-2023 10:55:48', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:55:48', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'SYS', 'EDCBinType', 'EDCBin类型', null, null);

insert into AD_REFNAME (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, CATEGORY, NAME, DESCRIPTION, REFERENCE_TYPE, LIST_LEVEL)
values ('317616901218242560', '0', 'Y', to_date('27-03-2023 10:56:10', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:56:10', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'SYS', 'EDCBinBinType', 'EDCBin Bin数据类型', null, null);

insert into AD_REFNAME (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, CATEGORY, NAME, DESCRIPTION, REFERENCE_TYPE, LIST_LEVEL)
values ('317616980859686912', '0', 'Y', to_date('27-03-2023 10:56:29', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:56:29', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'SYS', 'EDCBinAction', 'EDCBin动作类型', null, null);

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('317617145356095488', '0', 'Y', to_date('27-03-2023 10:57:08', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:57:08', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'EDCBinType', 'NUMBER', 'NUMBER', '10', 'NUMBER', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('317617145356095489', '0', 'Y', to_date('27-03-2023 10:57:08', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:57:08', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'EDCBinType', 'PERCENT', 'PERCENT', '20', 'PERCENT', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('317617471123492864', '0', 'Y', to_date('27-03-2023 10:58:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:58:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'EDCBinBinType', 'P', 'P', '10', 'PASS', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('317617471123492865', '0', 'Y', to_date('27-03-2023 10:58:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:58:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'EDCBinBinType', 'F', 'F', '20', 'FAIL', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('317617471123492866', '0', 'Y', to_date('27-03-2023 10:58:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:58:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'EDCBinBinType', 'G', 'G', '30', 'GROUP', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('317617471123492867', '0', 'Y', to_date('27-03-2023 10:58:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:58:26', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'EDCBinBinType', 'O', 'O', '40', 'OTHER', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('317617674886975488', '0', 'Y', to_date('27-03-2023 10:59:14', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:59:14', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'EDCBinAction', 'Scrap', 'Scrap', '10', '报废', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('317617674886975489', '0', 'Y', to_date('27-03-2023 10:59:14', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:59:14', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'EDCBinAction', 'Split', 'Split', '20', '分批', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('317617674886975490', '0', 'Y', to_date('27-03-2023 10:59:14', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:59:14', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'EDCBinAction', 'Rework', 'Rework', '30', '返工', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('317617674886975491', '0', 'Y', to_date('27-03-2023 10:59:14', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 10:59:14', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'EDCBinAction', 'None', 'None', '40', 'None', 'Y');


DELETE AD_REFNAME where OBJECT_RRN = '317622523980550144';
DELETE AD_UREFLIST WHERE OBJECT_RRN IN ('108301','108302');
insert into AD_REFNAME (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, CATEGORY, NAME, DESCRIPTION, REFERENCE_TYPE, LIST_LEVEL)
values ('317622523980550144', '1', 'Y', to_date('27-03-2023 11:18:30', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('27-03-2023 11:18:30', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'USER', 'BinReworkTransition', 'Bin返工流程', null, null);

insert into AD_UREFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, PARENT_RRN, IS_AVAILABLE, RESERVED01, RESERVED02, RESERVED03, RESERVED04, RESERVED05)
values ('108301', '1', 'Y', to_date('04-03-2019 14:08:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('04-03-2019 14:08:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'BinReworkTransition', 'Rework01', 'Rework01', '1', 'Rework01', null, 'Y', null, null, null, null, null);

insert into AD_UREFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, PARENT_RRN, IS_AVAILABLE, RESERVED01, RESERVED02, RESERVED03, RESERVED04, RESERVED05)
values ('108302', '1', 'Y', to_date('04-03-2019 14:08:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('04-03-2019 14:08:13', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'BinReworkTransition', 'Rework02', 'Rework02', '2', 'Rework02', null, 'Y', null, null, null, null, null);



--量测工步和清洗工步
DELETE AD_FIELD WHERE TABLE_RRN = '184330132467744768';
DELETE ad_table WHERE OBJECT_RRN = '184330132467744768';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 184330132467744768, 0, 'Y', to_date('2022-03-24 15:42:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 16:21:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 35, 'EDCProcessGroupQuery', '制程工步和量测工步关联查询', 'EDC_PROCESS_GROUP', 'N', 'ProcessGroup', 'com.glory.edc.model.calculation.ProcessGroup', null, null, null, 'N', 1, null, 'EDCProcessGroupQUERY', '制程工步和量测工步关联查询', null, 3); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 184330132471939072, 0, 'Y', to_date('2022-03-24 15:42:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 16:21:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 34, 'partName', '产品', 'PARTNAME', 184330132467744768, null, 10, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 6164324, null, null, null, null, 'PartName', '产品名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 184330132471939073, 0, 'Y', to_date('2022-03-24 15:42:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 16:21:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 29, 'procedureName', '流程', 'PROCEDURENAME', 184330132467744768, null, 20, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 10513, null, null, null, null, 'ProcedureName', '流程名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 184330132471939074, 0, 'Y', to_date('2022-03-24 15:42:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 16:21:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 23, 'processStepName', '制程工步', 'PROCESSSTEPNAME', 184330132467744768, null, 30, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'search', 'string', null, null, null, null, 10512, null, null, null, null, 'Process Step', '制程工步', 'Y', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 184330460592340992, 0, 'Y', to_date('2022-03-24 15:43:26', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 16:21:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 21, 'measureStepName', '量测工步', 'MEASURESTEPNAME', 184330132467744768, null, 40, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'search', 'string', null, null, null, null, 10512, null, null, null, null, 'Measure Step', '量测工步', 'Y', 'N', 'N', null, null, null, null, 'N', null, 12, null); 

DELETE AD_FIELD WHERE TABLE_RRN = '1843301324677447681';
DELETE ad_table WHERE OBJECT_RRN = '1843301324677447681';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 1843301324677447681, 0, 'Y', to_date('2022-03-24 15:42:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 16:23:29', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 36, 'EDCMeasureCleanQuery', '量测工步和清洗工步关联查询', 'EDC_PROCESS_GROUP', 'N', 'ProcessGroup', 'com.glory.edc.model.calculation.ProcessGroup', null, null, null, 'N', 1, null, 'EDCMeasureCleanQUERY', '量测工步和清洗工步关联查询', null, 3); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 1843301324719390721, 0, 'Y', to_date('2022-03-24 15:42:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 16:23:29', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 35, 'partName', '产品', 'PARTNAME', 1843301324677447681, null, 10, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 6164324, null, null, null, null, 'PartName', '产品名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 1843301324719390731, 0, 'Y', to_date('2022-03-24 15:42:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 16:23:29', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 30, 'procedureName', '流程', 'PROCEDURENAME', 1843301324677447681, null, 20, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 10513, null, null, null, null, 'ProcedureName', '流程名称', 'N', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 1843304605923409921, 0, 'Y', to_date('2022-03-24 15:43:26', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 16:23:29', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 22, 'measureStepName', '量测工步', 'MEASURESTEPNAME', 1843301324677447681, null, 30, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'search', 'string', null, null, null, null, 10512, null, null, null, null, 'Measure Step', '量测工步', 'Y', 'N', 'N', null, null, null, null, 'N', null, 12, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 1843301324719390741, 0, 'Y', to_date('2022-03-24 15:42:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-24 16:23:29', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 24, 'processStepName', '清洗工步', 'PROCESSSTEPNAME', 1843301324677447681, null, 40, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'search', 'string', null, null, null, null, 10512, null, null, null, null, 'Clean Step', '清洗工步', 'Y', 'N', 'N', null, null, null, null, 'N', null, 12, null); 

DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN IN ('305719482981490689','305719482981490692','305719482981490691'，'305719482981490690','305719697755021313',
'305719697755021314'，'305719697755021315','305719697755021316');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305719482981490689', '0', 'Y', to_date('22-02-2023 15:00:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:25:42', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305719482981490688', null, 'partName', '10', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305719482981490692', '0', 'Y', to_date('22-02-2023 15:00:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:25:42', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305719482981490688', null, 'processStepName', '40', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305719482981490691', '0', 'Y', to_date('22-02-2023 15:00:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:25:42', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305719482981490688', null, 'measureStepName', '30', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305719482981490690', '0', 'Y', to_date('22-02-2023 15:00:04', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:25:42', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305719482981490688', null, 'procedureName', '20', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305719697755021313', '0', 'Y', to_date('22-02-2023 15:00:55', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:25:27', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305719697755021312', null, 'partName', '10', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305719697755021314', '0', 'Y', to_date('22-02-2023 15:00:55', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:25:27', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305719697755021312', null, 'procedureName', '20', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305719697755021315', '0', 'Y', to_date('22-02-2023 15:00:55', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:25:27', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305719697755021312', null, 'processStepName', '30', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('305719697755021316', '0', 'Y', to_date('22-02-2023 15:00:55', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('24-03-2023 16:25:27', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '305719697755021312', null, 'measureStepName', '40', 'Y', null);


--工艺限制添加栏位参考值校验
DELETE AD_IMPEXP_FIELD_MAP WHERE OBJECT_RRN IN ('308615213724307456','308615213678170112','308615213703335936','308615213749473280','308615213770444800','308615213791416320');
insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('308615213724307456', '0', 'Y', to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308610945554419712', 'processId', '30', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('308615213678170112', '0', 'Y', to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308610945554419712', 'checkFlag', '10', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('308615213703335936', '0', 'Y', to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308610945554419712', 'partId', '20', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('308615213749473280', '0', 'Y', to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308610945554419712', 'stepId', '40', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('308615213770444800', '0', 'Y', to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308610945554419712', 'stageId', '50', 'Y', null);

insert into AD_IMPEXP_FIELD_MAP (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('308615213791416320', '0', 'Y', to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('02-03-2023 14:46:40', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '308610945554419712', 'customerCode', '60', 'Y', null);



