MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'ChangeShiftTo' NAME, TO_TIMESTAMP('2023/10/24 14:41:41', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('394137495187496960', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
  MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'ChangeShiftRefer' NAME, TO_TIMESTAMP('2023/10/24 14:42:08', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('394137606760177664', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
   MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'ChangeShiftQuery' NAME, TO_TIMESTAMP('2023/10/24 14:42:35', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('394137721814130688', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
 update Ad_Editor set editor_id = 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.changeshift.refer.ChangeShiftReferEditor' where object_rrn = 201810121;
update Ad_Editor set editor_id = 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.changeshift.to.ChangeShiftToEditor' where object_rrn = 201810122;
update Ad_Editor set editor_id = 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.changeshift.query.ChangeShiftQueryEditor' where object_rrn = 201810123;

delete ad_message where key_id in ('wip.change_shift_info','wip.common_info','wip.workorder_info','wip.lot_info','wip.equipment_info');

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('394458857332658176', '0', 'Y', 'wip.lot_info', 'Lot Info', '批次信息', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('394459666623614976', '0', 'Y', 'wip.equipment_info', 'Equipment Info', '设备信息', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('394457108370804736', '0', 'Y', 'wip.change_shift_info', 'Change Shift Info', '交接班详细信息!', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('394457764078931968', '0', 'Y', 'wip.common_info', 'Common Info', '备注信息', null);

insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('394458478230491136', '0', 'Y', 'wip.workorder_info', 'WorkOder Info', '工单信息', null);
