
--按工步作业工步不显示
delete AD_TREE WHERE CATEGORY = 'ByStep';
insert into AD_TREE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, CATEGORY, NAME, DESCRIPTION, LEVEL_NO, MODEL_NAME, TEXT, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, PARENT_RRN, IMAGE, IS_ACCESS_AUTHORITY)
values ('8111', '0', 'Y', to_date('16-06-2014', 'dd-mm-yyyy'), '1', to_date('16-06-2014', 'dd-mm-yyyy'), '1', '1', 'ByStep', 'L2', '第二层', '1', 'Step', ':name (:description)', 'com.glory.mes.prd.model.Step', 'stageId = :name', 'name', '8110', 'step', null);

insert into AD_TREE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, CATEGORY, NAME, DESCRIPTION, LEVEL_NO, MODEL_NAME, TEXT, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, PARENT_RRN, IMAGE, IS_ACCESS_AUTHORITY)
values ('8110', '0', 'Y', to_date('16-06-2014', 'dd-mm-yyyy'), '1', to_date('16-06-2014', 'dd-mm-yyyy'), '1', '1', 'ByStep', 'L1', '第一层', '0', 'Stage', ':name (:description)', 'com.glory.mes.prd.model.Stage', null, 'name', null, 'step', null);
--按设备清洗载具进出站提示
delete ad_message where KEY_ID IN ('ras.durable_clean_start_succeed', 'ras.durable_clean_end_succeed'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 310054820675809280, 0, 'Y', 'ras.durable_clean_start_succeed', 'Durable Clean Start Succeed!', '载具清洗开始成功！', null); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 310055906249764864, 0, 'Y', 'ras.durable_clean_end_succeed', 'Durable Clean End Succeed！', '载具清洗结束！', null); 
--工程连接限制FIELD修改（liquibase已修改）

--批次作业准备不存在
delete AD_MESSAGE where OBJECT_RRN = '202303231428001';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202303231428001', '0', 'Y', 'wip-001202: wip.lot_prepare_not_exist', 'Wip Lot Prepare Not Exist<%1$s>', '批次作业准备不存在<%1$s>', null);

--设备详细信息按钮弹框显示修改
delete ad_field where TABLE_RRN = '328745187775991808';
delete ad_tab where TABLE_RRN = '328745187775991808';
delete ad_table where OBJECT_RRN = '328745187775991808';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 328745187775991808, 0, 'Y', to_date('2021-03-24 21:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 14:47:07', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'RASEquipmentInfoOther', '设备详细信息其它信息', 'RAS_EQP', 'N', 'Equipment', 'com.glory.mes.ras.eqp.Equipment', null, null, null, 'N', 2, null, 'Equipment Info Other', '设备详细信息其它信息', null, 1); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 328745187775991809, 0, 'Y', to_date('2021-03-24 21:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 14:47:07', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'SubEquipments', '子设备列表', 328745187775991808, 10, 1, 'Sub Equpment List', '子设备列表', null, 'Section', null, 320); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 328745187775991810, 0, 'Y', to_date('2021-03-24 21:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 14:47:07', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 5, 'Ports', '端口列表', 328745187775991808, 20, 2, 'Port List', '端口列表', null, 'Section', null, 320); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 328745187775991811, 0, 'Y', to_date('2021-03-24 21:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 14:47:07', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 13, 'subEquipments', '子设备列表', null, 328745187775991808, 328745187775991809, 10, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'listTable', null, null, null, null, null, 328742861845680128, null, null, null, null, 'subEquipments', null, 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 328745187775991812, 0, 'Y', to_date('2021-03-24 21:01:08', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 14:47:07', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 8, 'ports', '端口列表', null, 328745187775991808, 328745187775991810, 20, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'listTable', null, null, null, null, null, 328742636863213568, null, null, null, null, 'ports', null, 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 

delete ad_field where TABLE_RRN = '328743038958555136';
delete ad_tab where TABLE_RRN = '328743038958555136';
delete ad_table where OBJECT_RRN = '328743038958555136';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 328743038958555136, 0, 'Y', to_date('2021-03-24 20:52:36', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 14:46:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 8, 'RASEquipmentInfoParent', '设备详细信息母表', 'RAS_EQP', 'N', 'Equipment', 'com.glory.mes.ras.eqp.Equipment', null, null, null, 'N', 2, null, 'Equipment Info Parent#Equipment Info and Sub Equipment Info', '设备详细信息#设备和子设备详细信息', null, 1); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 328743038958555137, 0, 'Y', to_date('2021-03-24 20:52:36', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 14:46:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 9, 'EqupmentInfo', '设备详细信息', 328743038958555136, 10, 2, 'Equpment Info', '设备详细信息', null, 'Section', null, 320); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 328743038958555138, 0, 'Y', to_date('2021-03-24 20:52:36', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 14:46:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 2, 'OtherInfo', '其它信息', 328743038958555136, 20, 2, 'Other Info', '其它信息', null, 'Form', null, 320); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 328743038958555157, 0, 'Y', to_date('2021-03-24 20:52:36', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 14:46:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 10, 'equipmentInfo', '设备信息', null, 328743038958555136, 328743038958555137, 10, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'entityform', null, null, null, null, null, 328742861845680128, null, null, null, null, 'equipmentInfo', null, 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 328743038958555158, 0, 'Y', to_date('2021-03-24 20:52:36', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 14:46:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 8, 'otherInfo', '其它信息', null, 328743038958555136, 328743038958555138, 20, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'glcform', null, null, null, null, null, null, null, null, 'RASEquipmentInfoOther', null, 'otherInfo', null, 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 

--载具清洗作业准备
delete ad_field where TABLE_RRN = '187151582536044544';
delete ad_tab where TABLE_RRN = '187151582536044544';
delete ad_table where OBJECT_RRN = '187151582536044544';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 187151582536044544, 0, 'Y', to_date('2022-04-01 10:33:34', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 15:08:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 23, 'ByEQPCarrierPrepare', '作业准备父界面', 'MM_CARRIER', 'N', 'Carrier', 'com.glory.mes.mm.durable.model.Carrier', null, null, null, 'N', 2, null, 'Carrier Prepare#Carrier Clean Prepare', '作业准备#载具清洗作业准备', null, 1); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 187151582536044545, 0, 'Y', to_date('2022-04-01 10:33:34', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 15:08:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 20, 'LeftForm', '左边表单', 187151582536044544, 10, 1, 'Left Form', '左边表单', null, 'Form', null, 320); 
insert into ad_tab (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, SEQ_NO, GRID_Y, LABEL, LABEL_ZH, LABEL_RES, TAB_TYPE, HEIGHT_HINT, STYLE) values  ( 187151582536044546, 0, 'Y', to_date('2022-04-01 10:33:34', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 15:08:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 16, 'PreparedList', '作业准备列表', 187151582536044544, 20, 1, 'Prepared List', '作业准备列表', null, 'Group', null, 320); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 187151582536044548, 0, 'Y', to_date('2022-04-01 10:33:34', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 15:08:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 22, 'leftFrom', '左边表单', null, 187151582536044544, 187151582536044545, 10, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'glcform', null, null, null, null, null, null, null, null, 'ByEQPCarrierPrepareLeft', null, 'leftFrom', null, 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 187151582536044549, 0, 'Y', to_date('2022-04-01 10:33:34', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 15:08:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 19, 'prepareList', '作业准备列表', null, 187151582536044544, 187151582536044546, 20, null, 'Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'listTable', null, null, null, null, null, 187234758868504576, null, null, null, null, 'prepareList', null, 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 187151582536044552, 0, 'Y', to_date('2022-04-01 10:33:34', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-03-23 15:08:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 13, 'jobId', '任务号', null, 187151582536044544, 187151582536044546, 30, null, 'Y', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'integer', null, null, null, null, null, null, null, null, null, 'Job ID', '任务号', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 

--按设备作业，作业准备页面，右键菜单，buff page右键菜单修改
MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPLotByEqpPrepareDialog' NAME, TO_TIMESTAMP('2023/03/23 14:59:27', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('310462169122664454', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPLotByEqpDefaultPage' NAME, TO_TIMESTAMP('2023/03/23 16:01:41', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('310367044723834885', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
 DELETE AD_FORM_RELEASE WHERE OBJECT_RRN = '310001127788081165';
 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPLotRunByEqpBufferPage' NAME, TO_TIMESTAMP('2023/03/23 16:08:42', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('310001127788081165', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
 --按工步作业RUN批次颜色显示
delete AD_MESSAGE where OBJECT_RRN = '202303231728001';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202303231728001', '0', 'Y', 'wip.run_lot', 'Run Lot', '作业批次', null);
