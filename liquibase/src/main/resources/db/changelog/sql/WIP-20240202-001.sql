delete ad_message where OBJECT_RRN = '202401270001';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270001', '0', 'Y', 'mbas-1000: mbas.id_not_generate', 'Id Not Generate!', 'Id未生成!', null);

delete ad_message where OBJECT_RRN = '202401270002';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270002', '0', 'Y', 'mbas-1001: mbas.generate_rule_not_exist', '%1$s Generate Rule Not Exist!', '%1$s生成规则不存在!', null);

delete ad_message where OBJECT_RRN = '202401270003';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270003', '0', 'Y', 'mbas-1002: mbas.generator_rule_is_used', 'Generator Rule Is Used!', 'ID生成规则已使用!', null);

delete ad_message where OBJECT_RRN = '202401270004';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270004', '0', 'Y', 'mbas-1003: mbas.idgenerator_rule_not_found', 'Id generator Rule Not Found!', 'ID生成规则未找到!', null);

delete ad_message where OBJECT_RRN = '202401270005';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270005', '0', 'Y', 'mbas-1004: mbas.idgenerator_unsupport_paramlist_in_batch', 'Id generator Unsupport Paramlist In Batch!', 'Id生成规则在Batch中不支持参数列表!', null);

delete ad_message where OBJECT_RRN = '202401270006';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270006', '0', 'Y', 'mbas-1005: mbas.idgenerator_unsupport_multiseq_in_batch', 'ID generator unsupport multiseq in batch!', 'ID生成规则, Batch生成时不支持多个Sequence!', null);

delete ad_message where OBJECT_RRN = '202401270007';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270007', '0', 'Y', 'mbas-1006: mbas.idgenerator_context_not_found', 'Id generator Context Not Found!', '未找到ID生成规则的Context记录!', null);

delete ad_message where OBJECT_RRN = '202401270008';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270008', '0', 'Y', 'mbas-1007: mbas.merge_rule_is_used', 'Merge Rule Is Used!', '合批规则已使用!', null);

delete ad_message where OBJECT_RRN = '202401270009';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270009', '0', 'Y', 'mbas-1008: mbas.idgenerator_size_rule', 'Type S, Sequence type 4, the Size should be larger than 1!', '类型S，序列类型4，大小应大于1！', null);

delete ad_message where OBJECT_RRN = '202401270010';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270010', '0', 'Y', 'mbas-1009: mbas.idgenerator_has_been_used_cannot_delete', 'Idgenerator Has Been Used Cannot Delete!', 'ID生成规则已经被使用,不能删除!', null);

delete ad_message where OBJECT_RRN = '202401270011';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270011', '0', 'Y', 'mbas-1100: mbas.shift_is_exist', 'Shift Is Exist!', '班次已存在!', null);

delete ad_message where OBJECT_RRN = '202401270012';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270012', '0', 'Y', 'mbas-1101: mbas.team_is_exist', 'Team Is Exist!', '班组已存在!', null);

delete ad_message where OBJECT_RRN = '202401270013';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270013', '0', 'Y', 'mbas-1102: mbas.idgenerator_rule_not_found', 'Id generator rule <%1$s> not found!', 'ID生成规则<%1$s>不存在!', null);

delete ad_message where OBJECT_RRN = '202401270014';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270014', '0', 'Y', 'mbas-1103: mbas.merge_rule_not_found', 'Rule is not found!', '规则不存在!', null);

delete ad_message where OBJECT_RRN = '202401270015';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270015', '0', 'Y', 'mbas-1104: mbas.team_not_found', 'Team is Not Found!', '班组未找到!', null);

delete ad_message where OBJECT_RRN = '202401270016';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270016', '0', 'Y', 'mbas-1105: mbas.idgenerator_rule_date_rule_line_error', '<%1$s> Id generator rule seqNo <%2$s> dateRuleLine error,error info:%3$s!', '<%1$s>生成规则第%2$s位dateRule错误，错误信息:%3$s!', null);

delete ad_message where OBJECT_RRN = '202401270017';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270017', '0', 'Y', 'mbas-1106: mbas.cycle_rule_can_not_generate_id', 'Cycle Rule Can Not Generate Id!', '周期规则无法生成Id!', null);

delete ad_message where OBJECT_RRN = '202401270018';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270018', '0', 'Y', 'mbas-1107: mbas.id_cycle_rule_illegal', 'Id Cycle Rule Illegal!', 'Id循环规则非法!', null);

delete ad_message where OBJECT_RRN = '202401270019';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270019', '0', 'Y', 'mbas-1108: mbas.idgenerator_rule_sequence_rule_line_error', '<%1$s> Id generator rule seqNo <%2$s> sequenceRuleLine error,error info:%3$s!', '<%1$s>生成规则第%2$s位sequenceRule错误，错误信息:%3$s!', null);

delete ad_message where OBJECT_RRN = '202401270020';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270020', '0', 'Y', 'mbas-1109: mbas.idgenerator_rule_XSequence_rule_line_error', '<%1$s> Id generator rule seqNo <%2$s> XSequenceLine error,error info:%3$s!', '<%1$s>生成规则第%2$s位XSequenceLine错误，错误信息:%3$s!', null);

delete ad_message where OBJECT_RRN = '202401270021';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270021', '0', 'Y', 'mbas-1110: mbas.idgenerator_rule_variable_rule_line_error', '<%1$s> Id generator rule seqNo <%2$s> variableRuleLine error,error info:%3$s!', '<%1$s>生成规则第%2$s位variableRule错误，错误信息:%3$s!', null);

delete ad_message where OBJECT_RRN = '202401270022';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270022', '0', 'Y', 'mbas-1111: mbas.id_more_than_size', 'Id More Than Size!', 'ID超过位数限制!', null);

delete ad_message where OBJECT_RRN = '202401270023';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270023', '0', 'Y', 'mbas-1112: mbas.generator_xseq_xparse', 'Bas Generator Xseq Xparse!', 'Xseq Xparse基本生成器!', null);

delete ad_message where OBJECT_RRN = '202401270024';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270024', '0', 'Y', 'mbas-1113: mbas.idgenerator_dbvalue_error', 'Idgenerator Dbvalue Error!', 'Id生成器Dbvalue错误!', null);

delete ad_message where OBJECT_RRN = '202401270025';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270025', '0', 'Y', 'mbas-1114: mbas.versioncontrol_state_not_allow', 'Versioncontrol State Not Allow!', '版本控制状态不允许！', null);

delete ad_message where OBJECT_RRN = '202401270026';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270026', '0', 'Y', 'mbas-1115: mbas.location_has_children', 'Location Has Children!', '位置有子记录!', null);

delete ad_message where OBJECT_RRN = '202401270027';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270027', '0', 'Y', 'mbas-1116: mbas.parameter_is_used', 'This parameter has been used, cannot be deleted!', '该参数已被使用,无法删除!', null);

delete ad_message where OBJECT_RRN = '202401270028';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401270028', '0', 'Y', 'mbas-2001: mbas.get_ad_sequence', 'Get Ad Sequence Error!', '获取adSequence异常!', null);

