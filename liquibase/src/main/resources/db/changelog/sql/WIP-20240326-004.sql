
delete AD_SYS_PARAMETER t where t.NAME = 'mes_only_cancel_cur_proc_lock';
delete AD_SYS_PARAMETER_VALUE t where t.NAME = 'mes_only_cancel_cur_proc_lock';
delete AD_SYS_PARAMETER t where t.OBJECT_RRN = 8212103;
delete AD_SYS_PARAMETER_VALUE t where t.OBJECT_RRN = 8212104;

insert into AD_SYS_PARAMETER (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, DEFAULT_VALUE, IS_STATIC, IS_GLOBAL, IS_MODIFIABLE)
values ('8212103', '0', 'Y', to_date('05-05-2023', 'dd-mm-yyyy'), 'admin', to_date('05-05-2023', 'dd-mm-yyyy'), 'admin', '0', 'mes_only_cancel_target_proc_lock', '批次NewPart时，如果存在LotProcedureLock，为true时仅取消目标流程的LotProcedureLock，否则取消批次所有的！', null, 'N', 'Y', 'Y');

insert into AD_SYS_PARAMETER_VALUE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, VALUE, DEFAULT_VALUE, IS_STATIC, IS_SYSTEM, IS_GLOBAL, IS_MODIFIABLE, COMMENTS)
values ('8212104', '0', 'Y', to_date('05-05-2023 17:08:45', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('05-05-2023 20:21:41', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '4', 'mes_only_cancel_target_proc_lock', '批次NewPart时，如果存在LotProcedureLock，为true时仅取消目标流程的LotProcedureLock，否则取消批次所有的！', 'N', null, 'N', 'Y', 'Y', 'Y', null);

