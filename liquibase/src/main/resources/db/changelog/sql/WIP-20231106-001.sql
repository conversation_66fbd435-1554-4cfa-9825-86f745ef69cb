delete ad_editor t where t.object_rrn IN (select OBJECT_RRN FROM AD_AUTHORITY WHERE NAME = 'Wip.LotDispatchForbidden');
delete AD_AUTHORITY t WHERE T.NAME like 'Wip.LotDispatchForbidden%';

DELETE FROM AD_FORM_ATTRIBUTE t WHERE t.FORM_RRN IN (SELECT w.object_rrn FROM AD_FORM w WHERE w.name IN ('WIPLotDispatchForbiddenAddGLC','WIPLotDispatchForbiddenGLC'));
DELETE FROM AD_FORM t WHERE t.name IN ('WIPLotDispatchForbiddenAddGLC','WIPLotDispatchForbiddenGLC');

delete ad_reftable where name in ('WIPLotDispatchForbiddenAdd','WIPLotDispatchForbiddenQuery');
delete AD_BUTTON where TABLE_RRN in (select OBJECT_RRN from ad_table where name in ('WIPLotDispatchForbiddenAddQuery','WIPLotDispatchForbiddenAdd','WIPLotDispatchForbidden','WIPLotDispatchForbiddenQuery'));
delete ad_field where TABLE_RRN in (select OBJECT_RRN from ad_table where name in ('WIPLotDispatchForbiddenAddQuery','WIPLotDispatchForbiddenAdd','WIPLotDispatchForbidden','WIPLotDispatchForbiddenQuery'));
delete ad_tab where TABLE_RRN in (select OBJECT_RRN from ad_table where name in ('WIPLotDispatchForbiddenAddQuery','WIPLotDispatchForbiddenAdd','WIPLotDispatchForbidden','WIPLotDispatchForbiddenQuery'));
delete ad_table where name in ('WIPLotDispatchForbiddenAddQuery','WIPLotDispatchForbiddenAdd','WIPLotDispatchForbidden','WIPLotDispatchForbiddenQuery');

