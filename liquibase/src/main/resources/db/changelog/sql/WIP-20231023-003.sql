DELETE AD_EDITOR WHERE OBJECT_RRN = '1910211';
insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('1910211', '0', 'Y', 'ChangeProcessEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.changeprocess.ChangeProcessManagerEditor', '6005', 'ChangeProcessManager', null, 'step', null);


MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'ChangeProcessManager' NAME, TO_TIMESTAMP('2023/10/11 15:59:43', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('388735273192009729', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 