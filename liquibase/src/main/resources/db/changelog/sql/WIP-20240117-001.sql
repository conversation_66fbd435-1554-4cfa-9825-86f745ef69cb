MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'MMLotPackManager' NAME, TO_TIMESTAMP('2024/01/19 15:30:20', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('418086350781181995', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次合批功能', 'Active', b.release_timestamp, 'Y', 'Y');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'SplitUnPackManager' NAME, TO_TIMESTAMP('2024/01/19 15:37:11', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('417619698835185666', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次合批功能', 'Active', b.release_timestamp, 'Y', 'Y');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'SplitPackManager' NAME, TO_TIMESTAMP('2024/01/19 15:36:02', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('417331368797069317', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次合批功能', 'Active', b.release_timestamp, 'Y', 'Y');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'MMLotPackActionManager' NAME, TO_TIMESTAMP('2024/01/19 15:33:44', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('424144334783717373', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次合批功能', 'Active', b.release_timestamp, 'Y', 'Y');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'PackManager' NAME, TO_TIMESTAMP('2024/01/19 15:34:35', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('416967969932238843', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次合批功能', 'Active', b.release_timestamp, 'Y', 'Y');

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'PackedActionManager' NAME, TO_TIMESTAMP('2024/01/19 15:35:10', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('417616803377111042', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次合批功能', 'Active', b.release_timestamp, 'Y', 'Y');
