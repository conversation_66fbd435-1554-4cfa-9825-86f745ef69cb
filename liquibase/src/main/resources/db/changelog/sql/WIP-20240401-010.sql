delete ad_field t where t.TABLE_RRN = 368238155;
delete ad_table t where t.OBJECT_RRN = 368238155;
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 368238155, 0, 'Y', to_date('2019-08-02 15:30:51', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2019-08-02 16:25:29', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 3, 'WIPShipAction', '入库信息', null, 'N', 'LotAction', 'com.glory.mes.wip.action.LotAction', null, null, null, 'N', 2, null, 'Ship Action', '入库信息', null, 1); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 368238232, 0, 'Y', to_date('2019-08-02 15:42:40', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2019-08-05 12:17:16', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 15, 'actionCode', '入库码', null, 368238155, null, 10, null, 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'Y', 'N', 'N', 'reftable', 'string', null, null, null, null, 93500952, null, null, null, null, 'Action Code', '入库码', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 368238233, 0, 'Y', to_date('2019-08-02 15:44:26', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2019-08-02 15:44:26', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 1, 'actionComment', '备注', null, 368238155, null, 20, null, 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Action Comment', '备注', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 

delete ad_field t where t.TABLE_RRN = 300654865233125376;
delete ad_table t where t.OBJECT_RRN = 300654865233125376;

update AD_REFTABLE t set t.name = 'PMSSheetListByName'  where t.object_rrn = 356022414325768192;

delete AD_SEQUENCE t where upper(t.name) = 'XXX_TABLE';

delete AD_REFLIST t where t.ORG_RRN = 0 and t.reference_name = 'objectType';
delete AD_REFNAME t where t.NAME = 'objectType' and t.ORG_RRN = 0 and t.CATEGORY = 'SYS';


delete AD_AUTHORITY t where t.OBJECT_RRN = 701254866;
delete AD_AUTHORITY t where t.OBJECT_RRN = 202112310027;