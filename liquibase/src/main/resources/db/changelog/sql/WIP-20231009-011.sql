delete ad_editor t where t.object_rrn = 40000;

insert into ad_editor (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('40000', '0', 'Y', 'SkipManagerEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.skip.SkipManagerEditor', '6033', 'SkipManager', null, 'skip-lot', null);

  MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'SkipManager' NAME, TO_TIMESTAMP('2023/10/9 11:11:10', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('388648697691500544', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 