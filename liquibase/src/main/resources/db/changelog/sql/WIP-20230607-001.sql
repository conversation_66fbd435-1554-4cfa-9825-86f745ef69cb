DELETE FROM AD_REFTABLE where NAME = 'RASEquipmentListTable';

DELETE FROM AD_REFLIST where REFERENCE_NAME = 'ADVariableCategory';

DELETE FROM AD_REFNAME where NAME = 'ADVariableCategory';

insert into AD_REFTABLE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, KEY_FIELD, TEXT_FIELD, WHERE_CLAUSE, ORDER_BY_CLAUSE, IS_DISTIC, IS_QUERY_BY_FIELD)
values ('332924035406196736', '0', 'Y', to_date('08-05-2023 16:41:15', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('08-05-2023 16:41:15', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'RASEquipmentListTable', '设备列表', '501', 'equipmentId', 'equipmentId', null, null, null, 'N');

insert into AD_REFNAME (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, CATEGORY, NAME, DESCRIPTION, REFERENCE_TYPE, LIST_LEVEL)
values ('333199063733350400', '0', 'Y', to_date('09-05-2023 10:54:07', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('09-05-2023 10:54:07', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'SYS', 'ADVariableCategory', '变量类别', null, null);

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('336174617079132160', '0', 'Y', to_date('17-05-2023 15:57:54', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('17-05-2023 15:57:54', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'ADVariableCategory', 'TransitionCondition', 'TransitionCondition', '40', 'TransitionCondition', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('333199271330426880', '0', 'Y', to_date('09-05-2023 10:54:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('09-05-2023 10:54:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'ADVariableCategory', 'Common', 'Common', '10', 'Common', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('333199271330426881', '0', 'Y', to_date('09-05-2023 10:54:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('09-05-2023 10:54:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'ADVariableCategory', 'TCard', 'TCard', '20', 'TCard', 'Y');

insert into AD_REFLIST (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, REFERENCE_NAME, KEY_ID, TEXT, SEQ_NO, DESCRIPTION, IS_AVAILABLE)
values ('333199271330426882', '0', 'Y', to_date('09-05-2023 10:54:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('09-05-2023 10:54:56', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'ADVariableCategory', 'Label', 'Label', '30', 'Label', 'Y');
