delete ad_message where <PERSON>EY_ID IN ('wip-2106: prd.unsupport_process_type#{0}'); 
delete ad_message where KEY_ID IN ('wip-2106: prd.unsupport_process_type'); 
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES) values  ( 438004469062549505, 0, 'Y', 'wip-2106: prd.unsupport_process_type', 'UnSuport processType %1$s 。', '不支持的ProcessType %1$s 。', null); 

DELETE ad_message where OBJECT_RRN = '202403111809001';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111809001', '0', 'Y', 'mm.please_input_mlotid', 'Please Input MLot Id!', '请输入物料批号！', null);

DELETE ad_message where OBJECT_RRN = '202403111809002';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403111809002', '0', 'Y', 'mm.material_lot_not_required_mlotid', 'Material Lot Not Required Mlot Id!', 'Material类型批次不需要填写物料批号！', null);

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WoMaterialRequestManager' NAME, TO_TIMESTAMP('2024/03/14 18:56:00', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
VALUES ('444502726412771330', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');

delete ad_message where OBJECT_RRN = '202403131128001';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403131128001', '0', 'Y', 'mm.box_already_exists', 'Box already exists!', '列表中已存在该批次！', null);

delete ad_message where OBJECT_RRN = '202403131128002';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403131128002', '0', 'Y', 'wip.batch_lot_track_in', 'Batch Lot Track In', 'Batch批次进站', null);

delete ad_message where OBJECT_RRN = '202403131128003';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202403131128003', '0', 'Y', 'wip.batch_lot_track_in_info', 'Batch Lot Track In List', 'Batch批次进站同Batch下的所有批次', null);
  