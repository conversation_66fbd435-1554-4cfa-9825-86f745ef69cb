delete ad_impexp_field_map where PARENT_RRN = 326033393757290496;

delete ad_impexp where OBJECT_RRN = 326033393757290496;

insert into ad_impexp (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_NAME, AUTHORITY_DESC, TYPE, AD_TABLE_NAME, BUTTON_NAME)
values ('326033393757290496', '1', 'Y', to_date('19-04-2023 16:20:18', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('19-04-2023 16:20:18', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'Ras.EquipmentComClass', '设备状态大类', 'ONE', 'RASEqpComClass', null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326033393757290497', '0', 'Y', to_date('19-04-2023 16:20:18', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('19-04-2023 16:20:18', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326033393757290496', null, 'description', '11', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326033393757290498', '0', 'Y', to_date('19-04-2023 16:20:18', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('19-04-2023 16:20:18', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326033393757290496', null, 'comClass', '1', null, null);

delete ad_impexp_field_map where PARENT_RRN = 326035509615886336;

delete ad_impexp where OBJECT_RRN = 326035509615886336;

insert into ad_impexp (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_NAME, AUTHORITY_DESC, TYPE, AD_TABLE_NAME, BUTTON_NAME)
values ('326035509615886336', '1', 'Y', to_date('19-04-2023 16:28:42', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('19-04-2023 16:30:46', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Ras.EquipmentState', '设备状态小类', 'ONE', 'RASEqpState', null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326035509615886337', '0', 'Y', to_date('19-04-2023 16:28:42', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('19-04-2023 16:28:42', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326035509615886336', null, 'comClass', '1', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326035509615886338', '0', 'Y', to_date('19-04-2023 16:28:42', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('19-04-2023 16:28:42', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326035509615886336', null, 'state', '11', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326035509615886339', '0', 'Y', to_date('19-04-2023 16:28:42', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('19-04-2023 16:28:42', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326035509615886336', null, 'description', '21', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326036027360772096', '0', 'Y', to_date('19-04-2023 16:30:46', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('19-04-2023 16:30:46', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326035509615886336', null, 'isAvailable', '24', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326036027360772097', '0', 'Y', to_date('19-04-2023 16:30:46', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('19-04-2023 16:30:46', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326035509615886336', null, 'availableLotType', '26', null, null);

delete ad_impexp_field_map where PARENT_RRN = 326037980035776512;

delete ad_impexp where OBJECT_RRN = 326037980035776512;

insert into ad_impexp (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_NAME, AUTHORITY_DESC, TYPE, AD_TABLE_NAME, BUTTON_NAME)
values ('326037980035776512', '1', 'Y', to_date('19-04-2023 16:38:31', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('19-04-2023 16:38:31', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '1', 'Ras.EquipmentSubState', '设备状态', 'ONE', 'RASEqpSubState', null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326037980035776514', '0', 'Y', to_date('19-04-2023 16:38:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('19-04-2023 16:38:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326037980035776512', null, 'subState', '21', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326037980035776515', '0', 'Y', to_date('19-04-2023 16:38:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('19-04-2023 16:38:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326037980035776512', null, 'state', '11', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326037980035776516', '0', 'Y', to_date('19-04-2023 16:38:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('19-04-2023 16:38:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326037980035776512', null, 'comClass', '1', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326037980035776513', '0', 'Y', to_date('19-04-2023 16:38:31', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('19-04-2023 16:38:31', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326037980035776512', null, 'description', '31', null, null);

delete ad_impexp_field_map where PARENT_RRN = 326294370285371392;

delete ad_impexp where OBJECT_RRN = 326294370285371392;

delete ad_impexp_field_map where SUB_RRN = 326295918361034752;

delete ad_impexp_sub where OBJECT_RRN = 326295918361034752;

insert into ad_impexp (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_NAME, AUTHORITY_DESC, TYPE, AD_TABLE_NAME, BUTTON_NAME)
values ('326294370285371392', '1', 'Y', to_date('20-04-2023 09:37:20', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Ras.EquipmentEvent', '设备事件', 'ONE2MANY', 'RASEquipmentEvent', null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326294370285371393', '0', 'Y', to_date('20-04-2023 09:37:20', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:37:20', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326294370285371392', null, 'eventId', '1', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326294370285371394', '0', 'Y', to_date('20-04-2023 09:37:20', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:37:20', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326294370285371392', null, 'eventType', '11', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326294370285371395', '0', 'Y', to_date('20-04-2023 09:37:20', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:37:20', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326294370285371392', null, 'description', '21', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326294370285371396', '0', 'Y', to_date('20-04-2023 09:37:20', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:37:20', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326294370285371392', '326295918361034752', 'eventStatus', '25', null, null);

insert into ad_impexp_sub (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, PARENT_FIELD_NAME, AD_TABLE_NAME)
values ('326295918361034752', '0', 'Y', to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '326294370285371392', 'eventStatus', 'RASEventStatus');

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326295918361034753', '0', 'Y', to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326295918361034752', 'checkFlag', '10', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326295918361034754', '0', 'Y', to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326295918361034752', 'sourceComClass', '20', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326295918361034755', '0', 'Y', to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326295918361034752', 'sourceState', '30', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326295918361034756', '0', 'Y', to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326295918361034752', 'sourceSubState', '40', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326295918361034757', '0', 'Y', to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326295918361034752', 'targetComClass', '50', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326295918361034758', '0', 'Y', to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326295918361034752', 'targetState', '60', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326295918361034759', '0', 'Y', to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:43:29', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326295918361034752', 'targetSubState', '70', null, null);

delete ad_impexp_field_map where PARENT_RRN = 326299388090630144;

delete ad_impexp where OBJECT_RRN = 326299388090630144;

delete ad_impexp_field_map where SUB_RRN = 326299608375476224;

delete ad_impexp_sub where OBJECT_RRN = 326299608375476224;


insert into ad_impexp (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_NAME, AUTHORITY_DESC, TYPE, AD_TABLE_NAME, BUTTON_NAME)
values ('326299388090630144', '1', 'Y', to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'Ras.StatusModel', '设备状态模型', 'ONE2MANY', 'RASStatusModel', null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299388090630145', '0', 'Y', to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326299388090630144', '326299608375476224', 'modelEvents', '70', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299388090630146', '0', 'Y', to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326299388090630144', null, 'description', '60', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299388090630147', '0', 'Y', to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326299388090630144', null, 'initialSubState', '50', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299388090630148', '0', 'Y', to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326299388090630144', null, 'initialState', '40', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299388094824448', '0', 'Y', to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326299388090630144', null, 'initialComClass', '30', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299388094824449', '0', 'Y', to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326299388090630144', null, 'modelType', '20', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299388094824450', '0', 'Y', to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:57:16', 'dd-mm-yyyy hh24:mi:ss'), null, '1', '326299388090630144', null, 'modelId', '10', null, null);

insert into ad_impexp_sub (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, PARENT_FIELD_NAME, AD_TABLE_NAME)
values ('326299608375476224', '0', 'Y', to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, '2', '326299388090630144', 'modelEvents', 'StatusModelEventList');

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299608375476225', '0', 'Y', to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326299608375476224', 'eventId', '30', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299608375476226', '0', 'Y', to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326299608375476224', 'eventDescription', '40', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299608379670528', '0', 'Y', to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326299608375476224', 'eventType', '50', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299608379670529', '0', 'Y', to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326299608375476224', 'userGroupId', '60', null, null);

insert into ad_impexp_field_map (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, PARENT_RRN, SUB_RRN, FIELD_NAME, SEQ_NO, IS_VALIDATE_REF, MAP_NAME)
values ('326299608379670530', '0', 'Y', to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('20-04-2023 09:58:08', 'dd-mm-yyyy hh24:mi:ss'), null, '1', null, '326299608375476224', 'limitCount', '70', null, null);

