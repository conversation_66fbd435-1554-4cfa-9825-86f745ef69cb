update AD_EDITOR t
   set t.editor_id = 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.detail.WIPLotDetailManagerEditor',
       t.param2    = 'WIPLotDetailManager'
 where t.object_rrn = 650
   and name = 'DetailLotEditor';

MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPLotDetailManager' NAME, TO_TIMESTAMP('2023/08/04 17:16:19', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('364709408361558021', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '批次详细信息', 'Active', b.release_timestamp, 'Y', 'Y');
 