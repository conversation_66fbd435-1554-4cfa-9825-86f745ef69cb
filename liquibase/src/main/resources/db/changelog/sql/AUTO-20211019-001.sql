delete from <PERSON>_FIELD t where name = 'isDispatch' and TABLE_RRN IN (SELECT OBJECT_RRN FROM AD_TABLE WHERE NAME = 'MMCarrier');
insert into AD_FIELD (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, <PERSON><PERSON><PERSON><PERSON><PERSON>ON, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE)
values ('127688255858974720', '0', 'Y', to_date('19-10-2021 08:27:32', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('19-10-2021 08:30:43', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '2', 'isDispatch', '是否派工', null, '107356', null, '125', null, 'Y', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'boolean', null, null, null, null, null, null, null, null, null, null, 'Is Dispatch', '是否派工', 'N', 'N', 'N', null, null, null, null, 'N', null, '0', null);


