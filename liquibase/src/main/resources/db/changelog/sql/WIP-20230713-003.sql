--仓库产品绑定弹框添加查询条件
DELETE ad_field WHERE TABLE_RRN = '117704';
DELETE ad_table WHERE OBJECT_RRN = '117704';
insert into ad_table (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_NAME, IS_VIEW, MODEL_NAME, MODEL_CLASS, WHERE_CLAUSE, ORDER_BY_CLAUSE, INIT_WHERE_CLAUSE, IS_VERTICAL, GRID_Y_BASIC, GRID_Y_QUERY, LABEL, LABEL_ZH, LABEL_RES, STYLE) values  ( 117704, 0, 'Y', to_date('2014-12-12 21:00:43', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-12 10:27:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 6, 'MMWareHouseActivePart', '仓库绑定激活机种', 'MM_MATERIAL', 'N', 'Part', 'com.glory.mes.prd.model.Part', 'status = ''Active''', 'name', null, 'N', 1, null, 'ActivePartForWareHouse', '仓库绑定激活机种', null, 1); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 117705, 0, 'Y', to_date('2014-12-12 21:03:30', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-12 10:27:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 4, 'name', '机种名称', null, 117704, null, 1, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'reftable', 'string', null, null, null, null, 6164324, null, null, null, null, 'Product Name', '产品名称', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 117706, 0, 'Y', to_date('2014-12-12 21:04:23', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-12 10:27:31', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 4, 'description', '描述', null, 117704, null, 10, null, 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'text', 'string', null, null, null, null, null, null, null, null, null, 'Description', '描述', 'N', 'N', 'N', null, null, null, null, 'N', null, 0, null); 


--BOM模板设置导入BOM模板弹框添加message
DELETE AD_MESSAGE WHERE OBJECT_RRN = '356391738992021504';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('356391738992021504', '0', 'Y', 'mm.please_select_bom_template', 'Please Select Bom Template', '请选择BOM模板', null);

 
 --数据采集项集添加公式下一步缺失message
DELETE AD_MESSAGE WHERE OBJECT_RRN = '356451778809614336';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('356451778809614336', '0', 'Y', 'edc.item_set_line_formula_must', 'Item Set Line Formula Must', '采集项集必须输入公式', null);
	
--批次未来到达查询指定工步栏位下拉动态表修改
DELETE ad_field where OBJECT_RRN = '124904331546091520';
insert into ad_field (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, COLUMN_NAME, TABLE_RRN, TAB_RRN, SEQ_NO, DISPLAY_LENGTH, IS_DISPLAY, IS_BASIC, IS_MAIN, IS_PERSIST, IS_READONLY, IS_EDITABLE, IS_SAMELINE, IS_MANDATORY, IS_UPPER, IS_FROM_PARENT, DISPLAY_TYPE, DATA_TYPE, NAMING_RULE, KEY_NUMBER, MIN_VALUE, MAX_VALUE, REFTABLE_RRN, REFLIST_NAME, UREFLIST_NAME, REFERENCE_RULE, DEFAULT_VALUE, LABEL, LABEL_ZH, IS_QUERY, IS_QUERY_ADVANCE, IS_QUERY_MANDATORY, DISPLAY_TYPE_QUERY, SEQ_NO_QUERY, REPEAT_NUMBER, LABEL_RES, IS_ATTRIBUTE, ATTRIBUTE_NAME, STYLE, CUSTOM_COMPOSITE) values  ( 124904331546091520, 0, 'Y', to_date('2021-10-11 16:05:13', 'yyyy-MM-dd HH24:mi:ss'), 'admin', to_date('2023-07-14 11:13:01', 'yyyy-MM-dd HH24:mi:ss'), 'admin', 26, 'attribute2', '指定工步', null, 124086608960020480, null, 13, null, 'Y', 'N', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'reftablemulti', 'string', null, null, null, null, 310361354353917952, null, null, null, null, 'Future Arrive Step', '指定工步', 'Y', 'N', 'N', null, null, null, null, 'N', null, 0, null); 
	