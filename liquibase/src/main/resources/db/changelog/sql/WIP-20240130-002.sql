DELETE AD_MESSAGE WHERE OBJECT_RRN = '202112300094561';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202112300094561', '0', 'Y', 'wip-1008: wip.sampling_action_not_exist', 'The sampling action not exist!', '抽样检测不存在！', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN = '202401251149001';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149001', '0', 'Y', 'wip-1019: wip.lots_is_running', '<%1$s> Lots Is Running!', '<%1$s> 批次正在作业！', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN = '202401251149002';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149002', '0', 'Y', 'wip-1026: wip.monitor_lot_control_id_null', 'Monitor lot control ID is null !', '监控批Control ID为空！', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN = '202401251149003';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149003', '0', 'Y', 'wip-1027: wip.lot_resume_batch_id_null', 'Batch resume need batch ID !', 'Batch恢复需要Batch ID！', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN = '202401251149004';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149004', '0', 'Y', 'wip-1028: wip.merge_rule_unsupport_rule', 'Merge Rule <%1$s> UnSupport Rule', '合批规则<%1$s>不支持！', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN = '202401251149005';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149005', '0', 'Y', 'wip-1029: wip.merge_rule_unsupport_comparison', 'Merge Rule <%1$s> UnSupport Comparison', '合批规则<%1$s>不支持比较！', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN = '202401251149006';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149006', '0', 'Y', 'wip-1031: wip.last_process_step_not_exist', 'Last Process Step Not Exist !', '最后一个工艺工步不存在！', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN = '202401251149007';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149007', '0', 'Y', 'wip-1032: wip.current_step_not_exist', 'Current Step Not Exist', '当前工步不存在！', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN = '202401251149008';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149008', '0', 'Y', 'wip-1115: wip.sorting_job_state_cannot_release', 'Sorting Job State Cannot Release', '该Sorting任务状态不支持放行动作！', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN = '202401251149009';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149009', '0', 'Y', 'wip-1116: wip.carrier_state_not_allow', 'Carrier State Not Allow, <%1$s>From<%2$s>', '载具状态不允许，<%1$s>至<%2$s>', null);

DELETE AD_MESSAGE WHERE OBJECT_RRN = '202401251149010';
insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149010', '0', 'Y', 'wip-1117: wip.carrier_is_not_empty', 'Carrier Is Not Empty', '载具不存在！', null);

delete ad_message where OBJECT_RRN = '202401251149011';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149011', '0', 'Y', 'wip-1118: wip.carrier_port_mat_type_mismatch', 'Carrier Port Mat Type Mismatch, MatType1:<%1$s>,MatType2:<%2$s>!', '载具端口主物料类型不一致,类型1：<%1$s>，类型2：<%2$s>', null);

delete ad_message where OBJECT_RRN = '202401251149012';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149012', '0', 'Y', 'wip-1119: wip.carrier_port_durable_type_mismatch', 'Carrier Port Durable Type Mismatch,Type1:<%1$s>,Type2:<%2$s>!', '载具端口载具类型不一致,类型1：<%1$s>，类型2：<%2$s>', null);

delete ad_message where OBJECT_RRN = '202401251149013';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149013', '0', 'Y', 'wip-1150: wip.combine_comp_lot_id_miss', 'Combine Comp Lot Id Miss!', '合并组件的批次Id未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149014';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149014', '0', 'Y', 'wip-1151: wip.combine_comp_from_different_lot', 'Combine Comp From Different Lot!', '合并的组件属于不同的批次!', null);

delete ad_message where OBJECT_RRN = '202401251149015';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149015', '0', 'Y', 'wip-1159: wip.combine_comp_unsupport_carrier_combine_type', 'Combine Comp Unsupport Carrier Combine Type!', '合并的组件不支持载具合并类型!', null);

delete ad_message where OBJECT_RRN = '202401251149016';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149016', '0', 'Y', 'wip-1200: wip.lot_prepare_multi_same_eqipment_lot', 'Lot Prepare Multi Same Eqipment Lot!', '批次准备在多个相同的设备!', null);

delete ad_message where OBJECT_RRN = '202401251149017';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149017', '0', 'Y', 'wip-1201: wip.lot_prepare_before_trackin', 'Lot Prepare Before Trackin!', '批次准备应该在进站之前!', null);

delete ad_message where OBJECT_RRN = '202401251149018';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149018', '0', 'Y', 'wip-1202: wip.lot_prepare_not_exist', 'Lot <%1$s> Prepare Not Exist!', '批次<%1$s>准备不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149019';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149019', '0', 'Y', 'wip-1203: wip.lot_attribute_not_found', 'Lot Attribute Not Found!', '批次属性未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149020';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149020', '0', 'Y', 'wip-1300: wip.equipment_unit_exceed_capacity', 'Equipment Unit Exceed Capacity!', '设备Unit超出容量!', null);

delete ad_message where OBJECT_RRN = '202401251149021';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149021', '0', 'Y', 'wip-1301: wip.equipment_unit_carrier_or_lot_already_in', 'Equipment Unit Carrier Or Lot Already In!', '载具或批次已经在设备中！!', null);

delete ad_message where OBJECT_RRN = '202401251149022';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149022', '0', 'Y', 'wip-1302: wip.equipment_unit_carrier_or_lot_not_in', 'Equipment Unit Carrier Or Lot Not In!', '载具或批次不在设备中！!', null);

delete ad_message where OBJECT_RRN = '202401251149023';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149023', '0', 'Y', 'wip-1400: wip.future_component_timer_unsupport_futurehold', 'Future Component Timer Unsupport Futurehold!', 'Component定时器不支持未来暂停！!', null);

delete ad_message where OBJECT_RRN = '202401251149024';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149024', '0', 'Y', 'wip-1401: wip.future_merge_step_not_exist', 'Future Merge Step Not Exist!', '未来合批工步不存在！!', null);

delete ad_message where OBJECT_RRN = '202401251149025';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149025', '0', 'Y', 'wip-1402: wip.future_merge_step_must_current_procedure', 'Future Merge Step Must Current Procedure!', '未来合并工步必须是当前流程下!', null);

delete ad_message where OBJECT_RRN = '202401251149026';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149026', '0', 'Y', 'wip-1403: wip.future_hold_step_not_allow', 'Future Hold Step Not Allow!', '未来暂停工步不能自动跳站!', null);

delete ad_message where OBJECT_RRN = '202401251149027';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149027', '0', 'Y', 'wip-2000: prd.rework_state_hisuper_must_return_to_end', 'Rework State Hisuper Must Return To End!', '返工状态Hisuper必须返回End!', null);

delete ad_message where OBJECT_RRN = '202401251149028';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149028', '0', 'Y', 'wip-2001: prd.rework_state_hisuper_node_not_exist', 'Rework State Hisuper Node Not Exist!', '返工状态Hisuper节点不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149029';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149029', '0', 'Y', 'wip-2002: prd.rework_state_hisuper_merge_node_not_exist', 'Rework State Hisuper Merge Node Not Exist!', '返工状态Hisuper合并节点不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149030';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149030', '0', 'Y', 'wip-2003: prd.unsupport_use_wfparameter', 'Unsupport Use Wfparameter!', '不支持使用参数!', null);

delete ad_message where OBJECT_RRN = '202401251149031';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149031', '0', 'Y', 'wip-2013: wip.lot_trackin_need_prepare_first', 'Equipment <%1$s> requires lots job prepare before track in!', '设备<%1$s>需要批次先做作业准备才能进站！', null);

delete ad_message where OBJECT_RRN = '202401251149032';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149032', '0', 'Y', 'wip-2020: prd.jumpto_unsupport_diff_procedure_in_rework', 'Jumpto Unsupport Diff Procedure In Rework!', '跳转到不支持的不同返工流程中!', null);

delete ad_message where OBJECT_RRN = '202401251149033';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149033', '0', 'Y', 'wip-2021: prd.jumpto_node_not_correct', 'Jumpto Node Not Correct!', '跳转节点不正确!', null);

delete ad_message where OBJECT_RRN = '202401251149034';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149034', '0', 'Y', 'wip-2022: prd.jumpto_node_not_in_procedure', 'Jumpto Node Not In Procedure!', '跳转节点不在流程中!', null);

delete ad_message where OBJECT_RRN = '202401251149035';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149035', '0', 'Y', 'wip-2100: prd.node_unsupport_multi_leave_transition', 'Node Unsupport Multi Leave Transition!', '节点不支持多个层级转变!', null);

delete ad_message where OBJECT_RRN = '202401251149036';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149036', '0', 'Y', 'wip-2101: prd.procedure_state_path_is_null', 'Procedure State Path Is Null!', '流程Path为空!', null);

delete ad_message where OBJECT_RRN = '202401251149037';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149037', '0', 'Y', 'wip-2102: prd.lot_procedure_unsupport', 'Lot Procedure Unsupport!', '批次流程不支持!', null);

delete ad_message where OBJECT_RRN = '202401251149038';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149038', '0', 'Y', 'wip-2103: prd.lot_procedure_is_repeat', 'Lot Procedure Is Repeat!', '批次流程重复!', null);

delete ad_message where OBJECT_RRN = '202401251149039';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149039', '0', 'Y', 'wip-2104: prd.node_unsupport_multi_arriving_transition', 'Node Unsupport Multi Arriving Transition!', '节点不支持多个到达!', null);

delete ad_message where OBJECT_RRN = '202401251149040';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149040', '0', 'Y', 'wip-2200: wip.eqp_recipe_not_exist_or_invalid', 'The PPID of lot <%1$s> does not exist or is incorrect!', '批次<%1$s>的PPID不存在或者不正确！!', null);

delete ad_message where OBJECT_RRN = '202401251149041';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149041', '0', 'Y', 'wip-2201: wip.eqp_recipe_isdifferent_in_batch', 'Lots in this batch are matched to multiple different ppids <%1$s>!!', 'Batch中的批次匹配到了多个不同的PPID<%1$s>!!', null);

delete ad_message where OBJECT_RRN = '202401251149042';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149042', '0', 'Y', 'wip-2202: wip.lot_future_timer_minimal_now', 'Lot Future Timer Minimal Now!', '当前时间未满足批次Q-Time计时器最小值!', null);

delete ad_message where OBJECT_RRN = '202401251149043';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149043', '0', 'Y', 'wip-2203: wip.lot_prepare_eqp_not_allow_batch', 'Lot Prepare Eqp Not Allow Batch!', '设备不支持多批次加工！!', null);

delete ad_message where OBJECT_RRN = '202401251149044';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149044', '0', 'Y', 'wip-2204: wip.batch_rule_not_found', 'Batch Rule Not Found!', 'Batch规则不存在！!', null);

delete ad_message where OBJECT_RRN = '202401251149045';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149045', '0', 'Y', 'wip-2205: wip.this_lot_type_not_allowd_in_the_position', 'This Lot <%1$s> Type Not Allowd In The Position!', '该位置不允许<%1$s>批次类型!', null);

delete ad_message where OBJECT_RRN = '202401251149046';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149046', '0', 'Y', 'wip-2206: wip.equipment_not_match', 'Equipment Not Match!', '设备不匹配！!', null);

delete ad_message where OBJECT_RRN = '202401251149047';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149047', '0', 'Y', 'wip-2207: wip.unit_position_prohibited', 'Unit Position <%1$s> Prohibited!', 'Unit Position被禁止!', null);

delete ad_message where OBJECT_RRN = '202401251149048';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149048', '0', 'Y', 'wip-2208: wip.lot_future_timer_maximal_now', 'Lot Future Timer Maximal Now!', '批次未来定时器未达到最大时!', null);

delete ad_message where OBJECT_RRN = '202401251149049';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149049', '0', 'Y', 'wip-2209: wip.lot_pre_sortingjob_is_startassign_type_not_allow', 'Lot Pre Sortingjob Is Startassign Type Not Allow!', '批次存在一个未完成的TRATAASIGN类型的SortingJob，不允许创建！!', null);

delete ad_message where OBJECT_RRN = '202401251149050';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149050', '0', 'Y', 'wip-2210: wip.lot_pre_sortingjob_is_split_type_not_allow', 'Lot Pre Sortingjob Is Split Type Not Allow!', '批次存在一个未完成的ESPLIT类型的SortingJob，不允许创建！!', null);

delete ad_message where OBJECT_RRN = '202401251149051';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149051', '0', 'Y', 'wip-2211: wip.lot_pre_sortingjob_is_merge_type_not_allow', 'Lot Pre Sortingjob Is Merge Type Not Allow!', '批次存在一个未完成的MERGE类型的SortingJob，不允许创建！!', null);

delete ad_message where OBJECT_RRN = '202401251149052';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149052', '0', 'Y', 'wip-2212: wip.lot_pre_sortingjob_is_exchange_type_not_allow', 'Lot Pre Sortingjob Is Exchange Type Not Allow!', '批次存在一个未完成的EXCHANGE类型的SortingJob，不允许创建！!', null);

delete ad_message where OBJECT_RRN = '202401251149053';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149053', '0', 'Y', 'wip-2213: wip.lot_pre_sortingjob_is_logic_split_type_not_allow', 'Lot Pre Sortingjob Is Logic Split Type Not Allow!', '批次存在一个未完成的LOGICSPLIT类型的SortingJob，不允许创建！!', null);

delete ad_message where OBJECT_RRN = '202401251149054';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149054', '0', 'Y', 'wip-2214: wip.lot_sortingjob_is_exist_not_allow', 'Lot Sortingjob Is Exist Not Allow!', '批次存在一个未完成的Sorting任务!', null);

delete ad_message where OBJECT_RRN = '202401251149055';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149055', '0', 'Y', 'wip-2215: wip.no_lot_in_carrier', 'No Lot In Carrier!', '载具中不存在批次!', null);

delete ad_message where OBJECT_RRN = '202401251149056';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149056', '0', 'Y', 'wip-2216: wip.lotId_or_carrierId_is_required', 'LotId Or CarrierId Is Required!', '需要批次ID的或者载具ID!', null);

delete ad_message where OBJECT_RRN = '202401251149057';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149057', '0', 'Y', 'wip-2217: wip.lot_state_is_not_allow_trackin', 'Lot State Is Not Allow Trackin!', '批次状态不允许进站!', null);

delete ad_message where OBJECT_RRN = '202401251149058';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149058', '0', 'Y', 'wip-2218: wip.lot_state_is_not_allow_trackout', 'Lot State Is Not Allow Trackout!', '批次状态不允许出站!', null);

delete ad_message where OBJECT_RRN = '202401251149059';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149059', '0', 'Y', 'wip-2300: wip.lot_track_in_muilt_batch_not_allow', 'Lot Track In Muilt Batch Not Allow!', 'MES不允许多个Batch同时进站!', null);

delete ad_message where OBJECT_RRN = '202401251149060';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149060', '0', 'Y', 'wip-2301: wip.lot_track_in_batch_not_complete', 'MES detected that batch is incomplete and missing <%1$s>, please check!', 'MES检测到Batch不完整，缺失<%1$s>，请检查!', null);

delete ad_message where OBJECT_RRN = '202401251149061';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149061', '0', 'Y', 'wip-2302: wip.lot_track_in_batch_not_allow', 'Lot Track In Batch Not Allow!', '设备不允许Batch作业！!', null);

delete ad_message where OBJECT_RRN = '202401251149062';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149062', '0', 'Y', 'wip-4000: wip.comp_reserved_exist', 'Comp <%1$s> Reserved Position <%2$s> Exist!', '组件<%1$s>预留位置<%2$s>已存在 !', null);

delete ad_message where OBJECT_RRN = '202401251149063';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149063', '0', 'Y', 'wip-4001: wip.comp_not_in_lot', 'Comp <%1$s> Not In Lot!', '组件<%1$s>不在批次中!', null);

delete ad_message where OBJECT_RRN = '202401251149064';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149064', '0', 'Y', 'wip-4002: wip.comp_not_in_batch', 'Comp Not In Batch!', '组件不在Batch中!', null);

delete ad_message where OBJECT_RRN = '202401251149065';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149065', '0', 'Y', 'wip-4003: wip.measure_lot_not_resume_batch', 'Measure Lot Not Resume Batch!', '控制批在恢复站点，如果没有恢复batch，则不能prepare！!', null);

delete ad_message where OBJECT_RRN = '202401251149066';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149066', '0', 'Y', 'wip-4004: wip.product_lot_not_resume_batch', 'Product Lot Not Resume Batch!', '生产批次在恢复站点，不能prepare！!', null);

delete ad_message where OBJECT_RRN = '202401251149067';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149067', '0', 'Y', 'wip-4005: wip.resume_step_can_not_change_flow', 'Resume Step Can Not Change Flow!', '恢复站点所有的批次都不能变更流程，如果需要变更，需要把batch job信息删除！', null);

delete ad_message where OBJECT_RRN = '202401251149068';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149068', '0', 'Y', 'wip-5000: prd._backup_step_not_in_current_procedure_or_not_exist', 'Back Up Step Not In Current Procedure Or Not Exist!', '退步工步不在当前流程或者不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149069';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149069', '0', 'Y', 'wip-5001: wip.reach_step_not_found', 'Reach Step Not Found!', '到达工步未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149070';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149070', '0', 'Y', 'wip-5002: wip.reach_step_different_inmaintype', 'Reach Step Different Inmaintype!', '到达工步进站类型不同!!', null);

delete ad_message where OBJECT_RRN = '202401251149071';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149071', '0', 'Y', 'wip-5003: wip.step_material_main_mat_type_is_not_match', 'Step:<%1$s> material mat TYPE:<%2$s> is not MATCH!', '工步:<%1$s> 工步物料类型:<%2$s>不匹配!', null);

delete ad_message where OBJECT_RRN = '202401251149072';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149072', '0', 'Y', 'wip-5004: wip.holdstate_is_not_the_latest_data', 'Current hold status:<%1$s>,Actual hold status:<%2$s>. Please refresh the data!', '当前hold状态:<%1$s>,实际hold状态:<%2$s>。请刷新数据!!', null);

delete ad_message where OBJECT_RRN = '202401251149073';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149073', '0', 'Y', 'wip-5005: wip.sorting_lot_carrier_mainmattype_not_match', 'Sorting Lot Material Type:<%1$s>  is not MATCH Foup Material Type:<%2$s>!!', '需要Sorting批次的物料类型:<%1$s>  与载具的物料类型:<%2$s>不一致!!', null);

delete ad_message where OBJECT_RRN = '202401251149074';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149074', '0', 'Y', 'wip-5006: wip.sorting_batch_id_cannot_generate', 'Sorting Batch Id  generation rule does not exist!', 'Sorter Batch ID生成规则不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149075';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149075', '0', 'Y', 'wip-5007: wip.sorting_job_child_lot_exist', 'Sorting Job Child Lot <%1$s> Exist!', 'Sorting任务子批 <%1$s> 已存在!', null);

delete ad_message where OBJECT_RRN = '202401251149076';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149076', '0', 'Y', 'wip-5008: wip.change_lot_id_state_not_allow', 'Change Lot Id State Not Allow!', '只有等待状态才能更改ID！!', null);

delete ad_message where OBJECT_RRN = '202401251149077';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149077', '0', 'Y', 'wip-5009: wip.lot_state_is_not_allow_carrier_out', 'Lot State <%1$s> Is Not Allow Carrier Out!', '批次状态<%1$s>不允许载具Out!', null);

delete ad_message where OBJECT_RRN = '202401251149078';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149078', '0', 'Y', 'wip-5010: wip.lot_exist_prepare_does_not_allow_carrier_out', 'Lot Exist Prepare Does Not Allow Carrier Out!', '批次不存在准备任务不允许载具Out!', null);

delete ad_message where OBJECT_RRN = '202401251149079';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149079', '0', 'Y', 'wip-5011: wip.generate_job_id_error:', 'Generate Job Id Error:<%1$s>!', '生成任务ID失败:<%1$s>!', null);

delete ad_message where OBJECT_RRN = '202401251149080';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149080', '0', 'Y', 'wip-5012: wip.lot_procedure_change_already_exist', 'Lot Procedure Change Already Exist!', '批次流程变更已经存在!', null);

delete ad_message where OBJECT_RRN = '202401251149081';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149081', '0', 'Y', 'wip-5013: wip.lot_procedure_change_state_not_allow', 'Lot Procedure Change State Not Allow!', '批次流程变更状态不允许!', null);

delete ad_message where OBJECT_RRN = '202401251149082';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149082', '0', 'Y', 'wip-5014: wip.lot_procedure_change_removestep_not_exist', 'Lot Procedure Change Removestep Not Exist!', '批次流程变更删除工步不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149083';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149083', '0', 'Y', 'wip-5015: wip.lot_procedure_change_not_immediate_active', 'Lot Procedure Change Not Immediate Active!', '批次流程变更不允许立即激活！', null);

delete ad_message where OBJECT_RRN = '202401251149084';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149084', '0', 'Y', 'wip-5016: wip.lot_procedure_change_immediatestep_not_exist', 'Lot Procedure Change Immediatestep Not Exist!', '批次流程变更要变更的工步不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149085';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149085', '0', 'Y', 'wip-5017: wip.procedure_change_is_started_operation_not_allowed', 'Procedure Change Is Started Operation Not Allowed!', '已触发批次流程变更，此操作不允许！!', null);

delete ad_message where OBJECT_RRN = '202401251149086';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149086', '0', 'Y', 'wip-5018: wip.material_requisition_is_not_found', 'Material Requisition Is Not Found!', '领料单未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149087';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149087', '0', 'Y', 'wip-5019: wip.lot_step_logic_recipe_data_exception', 'Lot Step Logic Recipe Data Exception!', '批次工步LogicRecipe数据异常!', null);

delete ad_message where OBJECT_RRN = '202401251149088';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149088', '0', 'Y', 'wip-5020: wip.lot_step_reticle_data_exception', 'Lot Step Reticle Data Exception!', '批次工步Reticle数据异常!', null);

delete ad_message where OBJECT_RRN = '202401251149089';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149089', '0', 'Y', 'wip-5021: prd.procedure_state_used_procedure_is_null', 'Procedure State Used Procedure Is Null!', 'ProcedureState对象的usedProcedure属性为空!', null);

delete ad_message where OBJECT_RRN = '202401251149090';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149090', '0', 'Y', 'wip-5022: prd.lot_procedure_found_multiple', 'Found multiple lot procedures of lock version type!', '找到了多个LockVersion类型的Lot Procedure!', null);

delete ad_message where OBJECT_RRN = '202401251149091';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149091', '0', 'Y', 'wip-5023: wip.lot_future_change_flow_step_cannot_same', 'Lot Future Change Flow Step Cannot Same!', '开始工步与结束工步不能相同！!', null);

delete ad_message where OBJECT_RRN = '202401251149092';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149092', '0', 'Y', 'wip-5024: wip.lot_future_change_flow_start_step_exist_multi', 'Lot Future Change Flow Start Step Exist Multi!', '开始工步已存在FutureChangeFlow!', null);

delete ad_message where OBJECT_RRN = '202401251149093';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149093', '0', 'Y', 'wip-5025: wip.lot_future_change_flow_exist_affect_others', 'Lot Future Change Flow Exist Affect Others!', '会与其他的FutureChangeFlow产生冲突!', null);

delete ad_message where OBJECT_RRN = '202401251149094';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149094', '0', 'Y', 'wip-5026: wip.lot_return_super_start_node_not_exist', 'Lot Return Super Start Node Not Exist!', '返回上级节点不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149095';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149095', '0', 'Y', 'wip-6002: wip.mot_material_not_kitting', 'Mot Material <%1$s> Not Kitting!', '对应物料<%1$s>的Kitting物料批没有找到!', null);

delete ad_message where OBJECT_RRN = '202401251149096';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149096', '0', 'Y', 'wip-6003: wip.mot_not_kitting', 'Mot <%1$s> Not Kitting!', '物料批<%1$s>未Kitting!', null);

delete ad_message where OBJECT_RRN = '202401251149097';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149097', '0', 'Y', 'wip-6004: wip.mot_append_material_diff', 'Mot Append Material <%1$s> Diff!', 'Append的物料批物料<%1$s>不一致!', null);

delete ad_message where OBJECT_RRN = '202401251149098';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149098', '0', 'Y', 'wip-6005: prd.lot_step_state_error', 'Lot Step State Error!', '批次工步节点异常！!', null);

delete ad_message where OBJECT_RRN = '202401251149099';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149099', '0', 'Y', 'wip-6007: wip.wo_id_cannot_generate', 'Wo Id Cannot Generate!', '工单Id生成规则未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149100';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149100', '0', 'Y', 'wip-6008: wip.wo_repeat', 'Wo Repeat!', '工单名称重复！!', null);

delete ad_message where OBJECT_RRN = '202401251149101';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149101', '0', 'Y', 'wip-6009: wip.wo_lot_qty_component_qty_not_equal', 'Wo Lot Qty Component Qty Not Equal!', '工单批次数量和批次组件数量不相等!', null);

delete ad_message where OBJECT_RRN = '202401251149102';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149102', '0', 'Y', 'wip-6010: wip.wo_is_null', 'Wo Is Null!', '工单为空!', null);

delete ad_message where OBJECT_RRN = '202401251149103';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149103', '0', 'Y', 'wip-6011: wip.wo_have_no_process', 'Work order have no process!', '工单没有指定流程!', null);

delete ad_message where OBJECT_RRN = '202401251149104';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149104', '0', 'Y', 'wip-6012: wip.changemo_part_is_not_same', 'Changemo Part Is Not Same!', '更改批次的工单产品与批次产品不相同!', null);

delete ad_message where OBJECT_RRN = '202401251149105';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149105', '0', 'Y', 'wip-6013: wip.wo_not_is_approved_state', 'Wo state not is approved!', '工单必须为审核状态!', null);

delete ad_message where OBJECT_RRN = '202401251149106';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149106', '0', 'Y', 'wip-6014: wip.wo_have_started', 'This workorder have started!', '该工单已开始生产!', null);

delete ad_message where OBJECT_RRN = '202401251149107';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149107', '0', 'Y', 'wip-6015: wip.lot_part_not_found', 'Part Not Found!', '产品未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149108';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149108', '0', 'Y', 'wip-6016: wip.lot_started_process', 'Lot Started Process!', '批次已开始作业!', null);

delete ad_message where OBJECT_RRN = '202401251149109';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149109', '0', 'Y', 'wip-6017: wip.wo_no_approved', 'Wo Not Approved!', '工单未审核！!', null);

delete ad_message where OBJECT_RRN = '202401251149110';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149110', '0', 'Y', 'wip-6018: wip.wo_state_not_allowed', 'Wo State Not Allowed!', '工单状态不允许当前操作!', null);

delete ad_message where OBJECT_RRN = '202401251149111';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149111', '0', 'Y', 'wip-6019: wip.wo_can_not_exist_multi_started_work_order', 'Wo Can Not Exist Multi Started Work Order!', '不能存在多个正在运行的工单!', null);

delete ad_message where OBJECT_RRN = '202401251149112';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149112', '0', 'Y', 'wip-6020: wip.lot_future_timer_minimal_now', 'Lot Future Timer Minimal Now!', '当前时间未满足批次Q-Time计时器最小值!', null);

delete ad_message where OBJECT_RRN = '202401251149113';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149113', '0', 'Y', 'wip-6021: prd.procedure_is_not_exist', 'Procedure <%1$s> is not exist!', '流程<%1$s>不存在！', null);

delete ad_message where OBJECT_RRN = '202401251149114';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149114', '0', 'Y', 'wip-6022: prd.step_is_not_exist', 'Step <%1$s> is not exist!', '工序<%1$s>不存在！', null);

delete ad_message where OBJECT_RRN = '202401251149115';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149115', '0', 'Y', 'wip-6023: prd.step_is_used', 'Step Is Used!', '工步已经被使用!', null);

delete ad_message where OBJECT_RRN = '202401251149116';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149116', '0', 'Y', 'wip-6024: prd.procedure_is_used', 'Procedure Is Used!', '流程已经被使用!', null);

delete ad_message where OBJECT_RRN = '202401251149117';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149117', '0', 'Y', 'wip-6025: prd.process_is_used', 'Process Is Used!', '工艺已经被使用!', null);

delete ad_message where OBJECT_RRN = '202401251149118';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149118', '0', 'Y', 'wip-6026: prd.process_is_used_by_reworkprocess', 'Process Is Used By Reworkprocess!', '工艺已经被可选流程使用!', null);

delete ad_message where OBJECT_RRN = '202401251149119';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149119', '0', 'Y', 'wip-6027: prd.part_is_used', 'Part Is Used!', '产品已经被使用!', null);

delete ad_message where OBJECT_RRN = '202401251149120';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149120', '0', 'Y', 'wip-6028: prd.delete_only_unfrozen_or_inactive', 'Delete Only Unfrozen Or Inactive!', 'UnFrozen和InActive状态才能被删除!', null);

delete ad_message where OBJECT_RRN = '202401251149121';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149121', '0', 'Y', 'wip-6029: wip.part_used_by_workorder', 'The product is being used by the work order!', '该产品正在被工单使用!', null);

delete ad_message where OBJECT_RRN = '202401251149122';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149122', '0', 'Y', 'wip-6030: prd.future_step_is_not_exist', 'Future Step Is Not Exist!', '未来工步不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149123';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149123', '0', 'Y', 'wip-6031: prd.part_not_exist', 'Part Not Exist!', '产品不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149124';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149124', '0', 'Y', 'wip-6032: wip.first_step_cannot_backup', 'Current step is first step of this procedure,cannot BackUp!', '批次位于流程第一步，不能BackUp!', null);

delete ad_message where OBJECT_RRN = '202401251149125';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149125', '0', 'Y', 'wip-6033: wip.endif_step_cannot_backup', 'Not support backward after ENDIF!', '批次流程数据不支持在EndIf退步!', null);

delete ad_message where OBJECT_RRN = '202401251149126';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149126', '0', 'Y', 'wip-6034: wip.process_node_not_found', 'The child node is not be found, the flow  maybe modified or update!', '没有找到对应的子节点，该流程可能已被修改或更新!', null);

delete ad_message where OBJECT_RRN = '202401251149127';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149127', '0', 'Y', 'wip-6035: prd.node_is_not_exist', 'Node <%1$s> Is Not Exist!', '流程节点<%1$s>不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149128';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149128', '0', 'Y', 'wip-6036: prd.flow_nest_loop', 'Flow is nested loop or nest layer larger than 10!', '流程循环嵌套或嵌套层大于10层！', null);

delete ad_message where OBJECT_RRN = '202401251149129';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149129', '0', 'Y', 'wip-6037: wip.process_not_found', 'Process Not Found!', '工艺未找到！', null);

delete ad_message where OBJECT_RRN = '202401251149130';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149130', '0', 'Y', 'wip-6038: wip.procedure_not_found', 'Procedure Not Found!', '流程未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149131';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149131', '0', 'Y', 'wip-6039: wip.step_name_and_step_category_is_all_empty', 'Step name and step category cannot be all empty!', '工步类别和工步名称不能全为空!', null);

delete ad_message where OBJECT_RRN = '202401251149132';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149132', '0', 'Y', 'wip-6040: wip.durable_exist_multi_lot', 'Durable Exist Multi Lot!', '载具存在多个批次!', null);

delete ad_message where OBJECT_RRN = '202401251149133';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149133', '0', 'Y', 'wip-6041: wip.durable_cannot_contains_multi_lot', 'Carrier cannot be bound to multiple master lot!', '载具不能绑定多个主批次!', null);

delete ad_message where OBJECT_RRN = '202401251149134';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149134', '0', 'Y', 'wip-6042: wip.component_is_not_all_in_lot', 'Component Is Not All In Lot!', '不能只绑定部分组件!', null);

delete ad_message where OBJECT_RRN = '202401251149135';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149135', '0', 'Y', 'wip-6043: wip.lot_is_in_durable', 'Lot Is In Durable!', '批次已在载具中!', null);

delete ad_message where OBJECT_RRN = '202401251149136';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149136', '0', 'Y', 'wip-6044: wip.durable_used_in_other_org', 'Durable Used In Other Org!', '载具区域与当前系统使用区域不一致!', null);

delete ad_message where OBJECT_RRN = '202401251149137';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149137', '0', 'Y', 'wip-6045: wip.lot_is_not_in_durable', 'Lot Is Not In Durable!', '批次没有在载具中!', null);

delete ad_message where OBJECT_RRN = '202401251149138';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149138', '0', 'Y', 'wip-6046: wip.component_is_in_durable', 'Component Is In Durable!', '组件已在载具中!', null);

delete ad_message where OBJECT_RRN = '202401251149139';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149139', '0', 'Y', 'wip-6047: wip.durable_sublot_cannot_assign_component', 'Durable Lot Type Is Sublot Cannot Assign Component!', '载具下的批次为SubLot类型时不能绑定组件!', null);

delete ad_message where OBJECT_RRN = '202401251149140';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149140', '0', 'Y', 'wip-6048: wip.durable_source_must_be_sublot', 'Durable Source Must Be Sublot!', '载具下SUBLOT类型时mainLot不能直接关联主Lot!', null);

delete ad_message where OBJECT_RRN = '202401251149141';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149141', '0', 'Y', 'wip-6049: wip.is_not_allow_lot', 'Is Not Allow Lot!', '不允许改批次信息!', null);

delete ad_message where OBJECT_RRN = '202401251149142';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149142', '0', 'Y', 'wip-6050: wip.lot_unsupport_unit_type', 'Lot Unsupport Unit Type!', '批次UnitType类型不支持!', null);

delete ad_message where OBJECT_RRN = '202401251149143';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149143', '0', 'Y', 'wip-6051: wip.mainlot_is_in_other_durable', 'Current lot is in other Carrier', '当前批次已经在其他的载具中!', null);

delete ad_message where OBJECT_RRN = '202401251149144';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149144', '0', 'Y', 'wip-6052: wip.component_is_not_exist', 'Component Is Not Exist!', '组件不存在！', null);

delete ad_message where OBJECT_RRN = '202401251149145';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149145', '0', 'Y', 'wip-6053: wip.component_not_in_source', 'Component Not In Source!', '组件在载具中不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149146';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149146', '0', 'Y', 'wip-6054: wip.qty_can_not_less_zero', 'Quantity can not less than 0!', '数量不能小于0!', null);

delete ad_message where OBJECT_RRN = '202401251149147';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149147', '0', 'Y', 'wip-6055: wip.lot_assign_one_carrier', 'Lot should assign in one carrier!', '批次只能绑定在一个载具上!', null);

delete ad_message where OBJECT_RRN = '202401251149148';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149148', '0', 'Y', 'wip-6056: wip.sourcelot_targetlot_is_same', 'Assign carrier source lot and target lot is same! ', '原载具上的批次与目标载具批次相同!', null);

delete ad_message where OBJECT_RRN = '202401251149149';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149149', '0', 'Y', 'wip-6057: wip.durable_source_lot_is_null', 'Durable Source Lot Is Null!', '载具源批次为空！', null);

delete ad_message where OBJECT_RRN = '202401251149150';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149150', '0', 'Y', 'wip-6058: wip.durable_lot_is_not_same_unittype', 'Durable Lot Is Not Same Unittype!', '载具批次unitType类型不一致!', null);

delete ad_message where OBJECT_RRN = '202401251149151';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149151', '0', 'Y', 'wip-6059: wip.carrier_mainlot_is_not_found', 'Lot not found in carrier!', '载具里不存在批次!', null);

delete ad_message where OBJECT_RRN = '202401251149152';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149152', '0', 'Y', 'wip-6060: wip.sourcecarrier_targetcarrier_is_same', 'Sourcecarrier Targetcarrier Is Same!', '源载具和目标载具相同！', null);

delete ad_message where OBJECT_RRN = '202401251149153';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149153', '0', 'Y', 'wip-6061: wip.lot_is_exist', 'Lot Is Exist!', '批次已经存在!', null);

delete ad_message where OBJECT_RRN = '202401251149154';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149154', '0', 'Y', 'wip-6062: wip.lot_unpakage_not_in_one_source', 'Lot Unpakage Not In One Source!', '废品拆包信息不在一个源批次里!', null);

delete ad_message where OBJECT_RRN = '202401251149155';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149155', '0', 'Y', 'wip-6063: wip.lot_is_not_exist', 'Lot Is Not Exist!', '批次不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149156';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149156', '0', 'Y', 'wip-6064: wip.schedule_source_lot_mainqty', 'Qty cant greater than lots actual Qty!', '数量不能大于批次的实际数量!', null);

delete ad_message where OBJECT_RRN = '202401251149157';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149157', '0', 'Y', 'wip-6065: wip.durable_assign_component_repeat', 'Durable Assign Component Repeat!', '载具绑定的组件重复!', null);

delete ad_message where OBJECT_RRN = '202401251149158';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149158', '0', 'Y', 'wip-6066: wip.component_cell_not_found', 'Component Cell Not Found!', '组件Cell未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149159';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149159', '0', 'Y', 'wip-6067: wip.cell_id_rule_is_null', 'Cell Id Rule Is Null!', 'Cell Id生成规则为空!', null);

delete ad_message where OBJECT_RRN = '202401251149160';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149160', '0', 'Y', 'wip-6068: wip.cell_id_size_less_than_count', 'Cell Id Size Less Than Count!', '生成的Cell Id数量小于Id生成规则的YCount!', null);

delete ad_message where OBJECT_RRN = '202401251149161';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149161', '0', 'Y', 'wip-6069: wip.cell_unsupport_id_generator', 'Cell Unsupport Id Generator!', '暂不支持全部通过IDGenerator来生成!', null);

delete ad_message where OBJECT_RRN = '202401251149162';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149162', '0', 'Y', 'wip-6070: wip.component_id_cannot_generate', 'Component Id Cannot Generate!', '组件Id生成失败!', null);

delete ad_message where OBJECT_RRN = '202401251149163';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149163', '0', 'Y', 'wip-6071: wip.lot_qty_must_larger_zero', 'Lot Qty Must Larger Zero!', '批次数量必须大于0!', null);

delete ad_message where OBJECT_RRN = '202401251149164';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149164', '0', 'Y', 'wip-6072: wip.track_out_identify_nocomp', 'Track Out Identify Nocomp!', '组件没有找到，不能出站!', null);

delete ad_message where OBJECT_RRN = '202401251149165';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149165', '0', 'Y', 'wip-6073: wip.bonding_parent_lot_not_split', 'Bonding Parent Lot Not Split!', '需键合的批次不能分批!', null);

delete ad_message where OBJECT_RRN = '202401251149166';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149166', '0', 'Y', 'wip-6074: wip.bonding_lot_component_mismatch', 'Bonding lot component qty not match!', '键合的批次组件数量不匹配!', null);

delete ad_message where OBJECT_RRN = '202401251149167';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149167', '0', 'Y', 'wip-6075: wip.component_state_not_allow', 'Component State Not Allow!', '组件状态不允许!', null);

delete ad_message where OBJECT_RRN = '202401251149168';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149168', '0', 'Y', 'wip-6076: wip.scrap_component_different_parent_rrn', 'Scrap Component Different Parent Rrn!', '报废组件的Parent Rrn和批次Rrn不一致!', null);

delete ad_message where OBJECT_RRN = '202401251149169';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149169', '0', 'Y', 'wip-6077: wip.scrap_lot_mainqty_exceed', 'Scrap Lot Mainqty Exceed!', '报废数量超过批次数量!', null);

delete ad_message where OBJECT_RRN = '202401251149170';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149170', '0', 'Y', 'wip-6078: wip.scrap_lot_subqty_exceed', 'Scrap Lot Subqty Exceed!', '报废子数量超过批次子数量!', null);

delete ad_message where OBJECT_RRN = '202401251149171';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149171', '0', 'Y', 'wip-6079: wip.scrap_comp_mainqty_exceed', 'Scrap Comp Mainqty Exceed!', '报废组件数量超过组件数量!', null);

delete ad_message where OBJECT_RRN = '202401251149172';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149172', '0', 'Y', 'wip-6080: wip.scrap_comp_subqty_exceed', 'Scrap Comp Subqty Exceed!', '报废组件子数量超过组件子数量!', null);

delete ad_message where OBJECT_RRN = '202401251149173';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149173', '0', 'Y', 'wip-6081: wip.unscrap_comp_lot_scrap_not_exist', 'Unscrap Comp Lot Scrap Not Exist!', '批次组件的报废记录不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149174';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149174', '0', 'Y', 'wip-6082: wip.unscrap_comp_mainqty_is_repeat', 'Unscrap Comp Mainqty Is Repeat!', '组件反报废找到多条报废记录!', null);

delete ad_message where OBJECT_RRN = '202401251149175';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149175', '0', 'Y', 'wip-6083: wip.unscrap_comp_mainqty_is_null', 'Unscrap Comp Mainqty Is Null!', '组件报废记录主数量为空!', null);

delete ad_message where OBJECT_RRN = '202401251149176';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149176', '0', 'Y', 'wip-6084: wip.hold_code_is_repeat', 'Hold Code Is Repeat!', 'Hold代码重复!', null);

delete ad_message where OBJECT_RRN = '202401251149177';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149177', '0', 'Y', 'wip-6085: wip.component_holdstate_not_allow', 'Component Holdstate Not Allow!', '当前状态不可操作!', null);

delete ad_message where OBJECT_RRN = '202401251149178';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149178', '0', 'Y', 'wip-6086: wip.release_pwd_error', 'Release Pwd Error!', '放行密码错误!', null);

delete ad_message where OBJECT_RRN = '202401251149179';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149179', '0', 'Y', 'wip-6087: wip.release_owner_error', 'Release Owner Error!', '当前用户不属于暂停暂停人!', null);

delete ad_message where OBJECT_RRN = '202401251149180';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149180', '0', 'Y', 'wip-6088: wip.parentunitrrn_is_null', 'Parentunitrrn Is Null!', '组件的ParentUnitRrn为Null!', null);

delete ad_message where OBJECT_RRN = '202401251149181';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149181', '0', 'Y', 'wip-6089: wip.lot_vialote_location', 'Lot <%1$s> Location Is Not Same With <%2$s> ', '批次<%1$s>与设备<%2$s>不在同一位置上!', null);

delete ad_message where OBJECT_RRN = '202401251149182';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149182', '0', 'Y', 'wip-6090: wip.lot_no_reserved_select_eqp', 'Lot %1$s no reserved select eqp!', '批次 %1$s 没有预约选中的设备！', null);

delete ad_message where OBJECT_RRN = '202401251149183';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149183', '0', 'Y', 'wip-6091: wip.trackin_less_than_min_batch', 'The %1$s can not less than %2$s minimum quantity!', '%1$s数不能小于规定的最小数量%2$s !', null);

delete ad_message where OBJECT_RRN = '202401251149184';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149184', '0', 'Y', 'wip-6092: wip.trackin_large_than_max_batch', 'The %1$s can not greater than %2$s !', '%1$s数不能大于规定的最大数量%2$s !', null);

delete ad_message where OBJECT_RRN = '202401251149185';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149185', '0', 'Y', 'wip-6093: wip.convert_qty_must_specify', 'Convert Qty Must Specify!', '必须指定装换后的数量!', null);

delete ad_message where OBJECT_RRN = '202401251149186';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149186', '0', 'Y', 'wip-6094: wip.cell_not_found', 'Cell Not Found!', '没有找到Cell配置!', null);

delete ad_message where OBJECT_RRN = '202401251149187';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149187', '0', 'Y', 'wip-6095: wip.lot_id_cannot_generate', 'Lot Id Cannot Generate!', '批次ID不能生成!', null);

delete ad_message where OBJECT_RRN = '202401251149188';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149188', '0', 'Y', 'wip-6096: wip.lot_mcomponent_not_found', 'Mcomponent Not Found!', '物料批组件未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149189';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149189', '0', 'Y', 'wip-6097: wip.batch_lot_is_different', 'Batch Lot Is Different!', 'Batch中的Lot不一致!', null);

delete ad_message where OBJECT_RRN = '202401251149190';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149190', '0', 'Y', 'wip-6098: wip.identify_qty_is_not_equal', 'Identify Qty Is Not Equal!', '组件数量和批次数量不相等!', null);

delete ad_message where OBJECT_RRN = '202401251149191';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149191', '0', 'Y', 'wip-6099: wip.batch_lot_different_batch', 'Batch Lot Different Batch!', 'Batch中的Lot BatchId不一致!', null);

delete ad_message where OBJECT_RRN = '202401251149192';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149192', '0', 'Y', 'wip-6100: wip.trackin_no_lot', 'Trackin No Lot!', '进站没有批次!', null);

delete ad_message where OBJECT_RRN = '202401251149193';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149193', '0', 'Y', 'wip-6101: wip.trackin_lot_repeat', 'Trackin Lot Repeat!', '进站批次重复!', null);

delete ad_message where OBJECT_RRN = '202401251149194';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149194', '0', 'Y', 'wip-6102: wip.multi_eqp_in_qty_error', 'The QTY of track in cannot be than the QTY of lot!', '进站数量不能大于批次数量!', null);

delete ad_message where OBJECT_RRN = '202401251149195';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149195', '0', 'Y', 'wip-6103: wip.multi_eqp_select_eqp', 'Please select the equipment!', '请选择设备!', null);

delete ad_message where OBJECT_RRN = '202401251149196';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149196', '0', 'Y', 'wip-6104: wip.multi_eqp_component_hold_state_error', 'The component hold state does not allow this operation!', '组件暂停状态不允许此操作！', null);

delete ad_message where OBJECT_RRN = '202401251149197';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149197', '0', 'Y', 'wip-6105: wip.trackout_no_lot', 'Trackout No Lot!', '出站没有批次!', null);

delete ad_message where OBJECT_RRN = '202401251149198';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149198', '0', 'Y', 'wip-6106: wip.trackout_lot_repeat', 'Trackout Lot Repeat!', '出站批次重复!', null);

delete ad_message where OBJECT_RRN = '202401251149199';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149199', '0', 'Y', 'wip-6107: wip.multi_eqp_out_qty_error', 'The QTY of track out cannot be than the QTY of lot!', '出站数量不能大于批次数量!', null);

delete ad_message where OBJECT_RRN = '202401251149200';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149200', '0', 'Y', 'wip-6108: wip.trackout_not_lot', 'Trackout Not Lot!', '出站没有批次!', null);

delete ad_message where OBJECT_RRN = '202401251149201';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149201', '0', 'Y', 'wip-6109: wip.abort_no_lot', 'Abort No Lot!', 'Abort没有批次!', null);

delete ad_message where OBJECT_RRN = '202401251149202';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149202', '0', 'Y', 'wip-6110: wip.trackmove_step_is_not_exist', 'Trackmove Step <%1$s> Is Not Exist!', '扫描站点不存在待过站的中!', null);

delete ad_message where OBJECT_RRN = '202401251149203';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149203', '0', 'Y', 'wip-6111: wip.trackthold_multi_hold', 'Lot is mulit held, can not process this operation!', '批次被多重保留，不能执行此操作!', null);

delete ad_message where OBJECT_RRN = '202401251149204';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149204', '0', 'Y', 'wip-6112: wip.trackthold_no_thold', 'Lot is not track out held, can not process this operation!', '批次没有被出站保留，不能执行此操作！', null);

delete ad_message where OBJECT_RRN = '202401251149205';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149205', '0', 'Y', 'wip-6113: wip.edclotdata_edcset_is_null', 'Edclotdata Edcset Is Null!', '数据采集对应的当前EdcSet为空!', null);

delete ad_message where OBJECT_RRN = '202401251149206';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149206', '0', 'Y', 'wip-6114: wip.component_not_in_lot', 'Component Not In Lot!', '组件没有在批次中!', null);

delete ad_message where OBJECT_RRN = '202401251149207';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149207', '0', 'Y', 'wip-6115: wip.release_lothold_not_found', 'Release Lothold Not Found!', '未找到暂停的批次!', null);

delete ad_message where OBJECT_RRN = '202401251149208';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149208', '0', 'Y', 'wip-6116: wip.release_ocap_error', 'Release Ocap Error!', '批次处于OCAP流程中无法放行批次!', null);

delete ad_message where OBJECT_RRN = '202401251149209';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149209', '0', 'Y', 'wip-6117: wip.lot_state_not_allow', 'Lot State Not Allow!', '批次状态不允许!', null);

delete ad_message where OBJECT_RRN = '202401251149210';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149210', '0', 'Y', 'wip-6118: wip.merge_unit_different', 'Merge Unit Different!', '取消合批批次与原始批次unit类型不一致!', null);

delete ad_message where OBJECT_RRN = '202401251149211';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149211', '0', 'Y', 'wip-6119: wip.unmerge_pre_transtype_no_merge', 'Previous transaction is not merge!', '前事务不是合批!', null);

delete ad_message where OBJECT_RRN = '202401251149212';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149212', '0', 'Y', 'wip-6120: wip.unmerge_not_same_step', 'Child Lot and parent lot step no same！', '被合批批次与母批不在同一工步上!', null);

delete ad_message where OBJECT_RRN = '202401251149213';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149213', '0', 'Y', 'wip-6121: wip.unmerge_not_same_state', 'The pre-batch status is different from the current status of the parent batch', '被合批批次前状态与母批当前状态不相同!', null);

delete ad_message where OBJECT_RRN = '202401251149214';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149214', '0', 'Y', 'wip-6122: wip.unmerge_parent_qty_less_zero', 'Parent lot main qty less zero！', '母批主数量小于0!', null);

delete ad_message where OBJECT_RRN = '202401251149215';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149215', '0', 'Y', 'wip-6123: wip.unmerge_parent_sub_qty_less_zero', 'Parent lot sub qty less zero！', '母批子数量小于0!', null);

delete ad_message where OBJECT_RRN = '202401251149216';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149216', '0', 'Y', 'wip-6124: wip.lot_step_no_use_category_bonding', 'Lot Step No Use Category Bonding!', '批次工步类别不为Bonding工步!', null);

delete ad_message where OBJECT_RRN = '202401251149217';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149217', '0', 'Y', 'wip-6125: wip.error.no_step_found', 'No Step Found!', '没有找到对应的工步!', null);

delete ad_message where OBJECT_RRN = '202401251149218';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149218', '0', 'Y', 'wip-6126: wip.uncombine_lot_not_combine', 'The component is not bonded and cannot be unbound！', '组件没有键合不能取消键合!', null);

delete ad_message where OBJECT_RRN = '202401251149219';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149219', '0', 'Y', 'wip-6127: wip.lot_scrap_not_found', 'Lot Scrap Not Found!', '没有找到批次报废信息!', null);

delete ad_message where OBJECT_RRN = '202401251149220';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149220', '0', 'Y', 'wip-6128: wip.wrong_unit_type', 'Wrong Unit Type!', '不支持的批次单位类型！', null);

delete ad_message where OBJECT_RRN = '202401251149221';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149221', '0', 'Y', 'wip-6129: wip.lot_skip_step_can_not_skip', 'Skip Step Can Not Skip!', '当前工步不允许跳站！', null);

delete ad_message where OBJECT_RRN = '202401251149222';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149222', '0', 'Y', 'wip-6130: wip.lot_skip_must_be_stepstate', 'Skip Must Be Stepstate!', '跳步节点必须是工步节点!', null);

delete ad_message where OBJECT_RRN = '202401251149223';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149223', '0', 'Y', 'wip-6131: wip.lot_process_not_found', 'Lot Process Not Found!', '批次工艺没有发现!', null);

delete ad_message where OBJECT_RRN = '202401251149224';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149224', '0', 'Y', 'wip-6132: wip.recovery_qty_err', 'Rework quantity is greater than batch size!', '返工数量大于批次数量!', null);

delete ad_message where OBJECT_RRN = '202401251149225';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149225', '0', 'Y', 'wip-6133: wip.rework_transition_is_not_exist', 'Rework Transition Is Not Exist!', '返工Transition不存在', null);

delete ad_message where OBJECT_RRN = '202401251149226';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149226', '0', 'Y', 'wip-6134: wip.rework_transition_mulit_is_exist', 'Rework Transition Mulit Is Exist!', '存在多个返工Transition!', null);

delete ad_message where OBJECT_RRN = '202401251149227';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149227', '0', 'Y', 'wip-6135: wip.rework_count_error', 'The lot exceeds the current number of rework times!', '批次超出当前返工次数限制!', null);

delete ad_message where OBJECT_RRN = '202401251149228';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149228', '0', 'Y', 'wip-6136: wip.rework_total_count_error', 'The lot exceeds the total number of rework times!', '批次超出总返工次数限制!', null);

delete ad_message where OBJECT_RRN = '202401251149229';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149229', '0', 'Y', 'wip-6137: wip.futurenote_not_before_current', 'Future note can not place before current step!', '未来备注不能加在当前步之前!', null);

delete ad_message where OBJECT_RRN = '202401251149230';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149230', '0', 'Y', 'wip-6138: wip.futurehold_not_before_current', 'Future hold can not place before current step!', '未来暂停不能加在当前步之前!', null);

delete ad_message where OBJECT_RRN = '202401251149231';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149231', '0', 'Y', 'wip-6139: wip.futurehold_current_only_trackout', 'The current hold can only set in TrackOut!', '当前步只能设置在TrackOut上!', null);

delete ad_message where OBJECT_RRN = '202401251149232';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149232', '0', 'Y', 'wip-6140: wip.future_flow_action_not_before_current', 'Future flow action cannot be defined before the current process step!', '未来流程变更不能定义在当前工步之前!', null);

delete ad_message where OBJECT_RRN = '202401251149233';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149233', '0', 'Y', 'wip-6141: wip.future_flow_action_not_current', 'Queue type future flow action cannot be defined in the current process step!', 'Queue类型的未来流程变更不能定义在当前工步!', null);

delete ad_message where OBJECT_RRN = '202401251149234';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149234', '0', 'Y', 'wip-6142: wip.auto_merge_durable_different', 'Lot durable different, Please manually complete lot merge!', '批次载具不相同，请手动完成合批!', null);

delete ad_message where OBJECT_RRN = '202401251149235';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149235', '0', 'Y', 'wip-6143: wip.merege_unsupport_object_type', 'Merege Unsupport Object Type!', '该合批规则不支持这个合批规则类型', null);

delete ad_message where OBJECT_RRN = '202401251149236';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149236', '0', 'Y', 'wip-6144: wip.change_hold_error', 'Hold record does not exist!', '保留记录不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149237';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149237', '0', 'Y', 'wip-6145: wip.lot_bin_data_qty_mismatch', 'Lot Bin Data Qty Mismatch!', '批次Bin数据与批次数量不匹配!', null);

delete ad_message where OBJECT_RRN = '202401251149238';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149238', '0', 'Y', 'wip-6146: wip.lot_state_is_run', 'Lot State Is Run!', '批次状态为Run!', null);

delete ad_message where OBJECT_RRN = '202401251149239';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149239', '0', 'Y', 'wip-6147: wip.warehouse_is_not_found', 'Warehouse Is Not Found!', '仓位未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149240';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149240', '0', 'Y', 'wip-6148: wip.lot_not_exist', 'Lot Not Exist!', '批次不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149241';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149241', '0', 'Y', 'wip-6149: wip.wo_not_exist_or_no_approved', 'Wo Not Exist Or No Approved!', '工单不存在或者未审核!', null);

delete ad_message where OBJECT_RRN = '202401251149242';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149242', '0', 'Y', 'wip-6150: wip.multi_wip_flag', 'Multi Wip Flag!', '存在多条WipFlag数据，只支持更新一条WipFlag!', null);

delete ad_message where OBJECT_RRN = '202401251149243';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149243', '0', 'Y', 'wip-6151: wip.lot_is_rejected', 'Lot Is Rejected!', '批次被拒绝使用该设备或者Mask和Recipe!', null);

delete ad_message where OBJECT_RRN = '202401251149244';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149244', '0', 'Y', 'wip-6152: wip.lot_is_reserved', 'Lot Is Reserved!', '批次已被预留!', null);

delete ad_message where OBJECT_RRN = '202401251149245';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149245', '0', 'Y', 'wip-6153: wip.lot_in_different_step', 'Lots in different steps or states. It cannot enter the step at the same time. Please check!', '批次处在不同的工步或者状态上面，不能同时进站, 请检查！', null);

delete ad_message where OBJECT_RRN = '202401251149246';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149246', '0', 'Y', 'wip-6154: wip.component_is_null', 'Component Is Null!', '组件为空!', null);

delete ad_message where OBJECT_RRN = '202401251149247';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149247', '0', 'Y', 'wip-6155: wip.lot_target_org_is_exist', 'Lot Target Org Is Exist!', '目标区域已经有对应的批次!', null);

delete ad_message where OBJECT_RRN = '202401251149248';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149248', '0', 'Y', 'wip-6156: wip.component_target_org_is_exist', 'Component Target Org Is Exist!', '目标区域已经有对应的组件!', null);

delete ad_message where OBJECT_RRN = '202401251149249';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149249', '0', 'Y', 'wip-6157: wip.flow_condition_is_not_exist', 'Flow Condition Is Not Exist!', 'FlowCondition不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149250';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149250', '0', 'Y', 'wip-6158: wip.replenish_lot_subunittype_is_not_qtyunit', 'Replenish Lot Subunittype Is Not Qtyunit!', '报废或者补充批次的SubUnit类型不是Qty!', null);

delete ad_message where OBJECT_RRN = '202401251149251';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149251', '0', 'Y', 'wip-6159: wip.scrap_lot_mainqty_all', 'Scrap Lot Mainqty All!', '批次主数量被全部报废!', null);

delete ad_message where OBJECT_RRN = '202401251149252';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149252', '0', 'Y', 'wip-6160: wip.scrap_lot_subqty_all', 'Scrap Lot Subqty All!', '批次子数量被全部报废!', null);

delete ad_message where OBJECT_RRN = '202401251149253';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149253', '0', 'Y', 'wip-6161: wip.lot_ocapid_is_null', 'Lot Ocapid Is Null!', '批次Ocap Id为空!', null);

delete ad_message where OBJECT_RRN = '202401251149254';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149254', '0', 'Y', 'wip-6162: wip.lot_not_assign_durable', 'Lot Not Assign Durable!', '批次没有绑定载具!', null);

delete ad_message where OBJECT_RRN = '202401251149255';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149255', '0', 'Y', 'wip-6163: wip.lottecn_name_is_exist', 'Lottecn Name Is Exist!', 'Lot Tecn名称已经存在!', null);

delete ad_message where OBJECT_RRN = '202401251149256';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149256', '0', 'Y', 'wip-6164: wip.lot_material_not_match', 'Lot Material Not Match!', '批次物料信息不匹配!', null);

delete ad_message where OBJECT_RRN = '202401251149257';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149257', '0', 'Y', 'wip-6165: wip.outsourcing_procedure_is_null', 'Outsourcing Procedure Is Null!', '委外流程未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149258';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149258', '0', 'Y', 'wip-6166: wip.sorting_job_state_is_not_allow', 'Sorting Job State Is Not Allow!', 'Sorting任务状态不允许!', null);

delete ad_message where OBJECT_RRN = '202401251149259';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149259', '0', 'Y', 'wip-6167: wip.sorting_job_is_not_auto', 'Sorting Job Type Is Not Auto!', 'Sorting任务不是Auto类型!', null);

delete ad_message where OBJECT_RRN = '202401251149260';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149260', '0', 'Y', 'wip-6168: wip.sorting_job_is_not_start', 'Sorting Job Is Not Start!', 'Sorting任务没有开始!', null);

delete ad_message where OBJECT_RRN = '202401251149261';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149261', '0', 'Y', 'wip-6169: wip.sorting_job_state_not_allow', 'Sorting Job State Not Allow!', 'Sorting任务状态不允许!', null);

delete ad_message where OBJECT_RRN = '202401251149262';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149262', '0', 'Y', 'wip-6170: wip.sort_rule_is_not_exist', 'Sort Rule Is Not Exist!', 'Sort规则不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149263';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149263', '0', 'Y', 'wip-6171: wip.lot_not_allow_skip_step', 'Lot Not Allow Skip Step!', '批次不允许跳步!', null);

delete ad_message where OBJECT_RRN = '202401251149264';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149264', '0', 'Y', 'wip-6172: wip.lot_assigned_carrier', 'lot was assigned by carrier!', '批次已经绑定载具!', null);

delete ad_message where OBJECT_RRN = '202401251149265';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149265', '0', 'Y', 'wip-6173: wip.multi_eqp_operation_info_incomplete', 'Multi Eqp Operation Info Incomplete!', '多设备操作信息不完整!', null);

delete ad_message where OBJECT_RRN = '202401251149266';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149266', '0', 'Y', 'wip-6174: wip.aps_mr_id_cannot_generate', 'Mr Id Cannot Generate!', '领料单ID不能生成，请检查ID生成规则配置!', null);

delete ad_message where OBJECT_RRN = '202401251149267';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149267', '0', 'Y', 'wip-6175: wip.mlot_unsupport_disassemable_type', 'Mlot Unsupport Disassemable Type!', '解除批次组装不支持该Disassemable类型!', null);

delete ad_message where OBJECT_RRN = '202401251149268';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149268', '0', 'Y', 'wip-6176: wip.merege_base_object_not_match_type', 'Merege Base Object Not Match Type!', '合批对象类型不匹配!', null);

delete ad_message where OBJECT_RRN = '202401251149269';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149269', '0', 'Y', 'wip-7001: pp.deassign_lot_not_in_wo', 'Deassign lot not in wo!', '待解绑批次不在工单中!', null);

delete ad_message where OBJECT_RRN = '202401251149270';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149270', '0', 'Y', 'wip-7002: pp.split_num_not_more_than_mother_num', 'Split num not more than mother num', '分批数量不能大于母批数量!', null);

delete ad_message where OBJECT_RRN = '202401251149271';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149271', '0', 'Y', 'wip-7003: pp.wo_is_not_exist', 'Wo Is Not Exist!', '工单不存在!', null);

delete ad_message where OBJECT_RRN = '202401251149272';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149272', '0', 'Y', 'wip-7004: pp.schedule_wo_is_approve', 'Schedule Wo <%1$s> Is Approve!', '工单<%1$s>已经被审核!', null);

delete ad_message where OBJECT_RRN = '202401251149273';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149273', '0', 'Y', 'wip-7005: pp.schedule_wo_is_exist', 'Schedule Wo <%1$s> Is Exist!', '工单已存在!', null);

delete ad_message where OBJECT_RRN = '202401251149274';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149274', '0', 'Y', 'wip-8001: wip.workorder_bound_equipment', 'Workorder Not Bound Equipment!', '工单未绑定设备!', null);

delete ad_message where OBJECT_RRN = '202401251149275';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149275', '0', 'Y', 'wip-8002: wip.status_can_not_cancel', 'Status Can Not Cancel!', '工单该状态不能取消!', null);

delete ad_message where OBJECT_RRN = '202401251149276';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149276', '0', 'Y', 'wip-8003: wip.workorder_not_bound_equipment', 'Workorder Not Bound Equipment!', '工单未绑定设备!', null);

delete ad_message where OBJECT_RRN = '202401251149277';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149277', '0', 'Y', 'wip-8004: wip.the_schedule_should_not_be_supplement', 'The Schedule Should Not Be Supplement!', '工单不应该为补投工单!', null);

delete ad_message where OBJECT_RRN = '202401251149278';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149278', '0', 'Y', 'wip-8005: wip.the_schedule_status_must_be_complete', 'The Schedule Status Must Be Complete!', '工单必须为完成状态!', null);

delete ad_message where OBJECT_RRN = '202401251149279';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149279', '0', 'Y', 'wip-8006: wip.the_schedule_should_not_be_0', 'The Schedule Should Not Be 0!', '工单补充数量不能为0!', null);

delete ad_message where OBJECT_RRN = '202401251149280';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149280', '0', 'Y', 'wip-8007: wip.object_duplicate', '%1$s Already Exists!', '%1$s已经存在!', null);

delete ad_message where OBJECT_RRN = '202401251149281';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149281', '0', 'Y', 'wip-8008: wip.unsupport_this_operation', 'Unsupport This Operation!', '不支持此类型操作!', null);

delete ad_message where OBJECT_RRN = '202401251149282';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149282', '0', 'Y', 'wip-8009: wip.no_process_found', 'No Process Found!', '工艺未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149283';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149283', '0', 'Y', 'wip-8010: wip.step_attribute_not_found', 'Step Attribute Not Found!', '工步属性未找到!', null);

delete ad_message where OBJECT_RRN = '202401251149284';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149284', '0', 'Y', 'wip-8011: wip.lot_transferstate_not_allow', 'Lot Transferstate Not Allow!', '批次搬运状态不允许!', null);

delete ad_message where OBJECT_RRN = '202401251149285';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149285', '0', 'Y', 'wip-8012: wip.lot_state_not_allow', 'Lot State Not Allow!', '批次状态不允许！', null);

delete ad_message where OBJECT_RRN = '202401251149286';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149286', '0', 'Y', 'wip-8013: wip.carriers_is_mix', 'Carriers Is Mix!', '载具为混批，不支持混批操作!', null);

delete ad_message where OBJECT_RRN = '202401251149287';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149287', '0', 'Y', 'wip-8014: wip.lot_already_identity', 'Lot Already Identity!', '批次已经完成Identify!', null);

delete ad_message where OBJECT_RRN = '202401251149288';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149288', '0', 'Y', 'wip-8015: wip.no_step_permission', 'No Step Permission!', '用没有工步权限!', null);

delete ad_message where OBJECT_RRN = '202401251149289';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149289', '0', 'Y', 'wip-8016: wip.batch_different_stepid', 'Different stepid can not make up a Batch!', '不同的工步不能组成一个Batch!', null);

delete ad_message where OBJECT_RRN = '202401251149290';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149290', '0', 'Y', 'wip-8017: wip.component_holdstate_not_allow', 'Component has been held, this operation is not allowed', '该批次中存在被暂停的组件，不允许此操作!', null);

delete ad_message where OBJECT_RRN = '202401251149291';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149291', '0', 'Y', 'wip-8018: wip.eqp_not_allow_lot', 'Function of equipment does not allow the batch operation', '设备功能不允许该批次作业!', null);

delete ad_message where OBJECT_RRN = '202401251149292';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149292', '0', 'Y', 'wip-8019: wip.lot_material_check_failed', 'Lot Material Check Failed!', '批次物料检查失败!', null);

delete ad_message where OBJECT_RRN = '202401251149293';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149293', '0', 'Y', 'wip-8020: wip.lot_holdstate_not_allow', 'Lot Holdstate Not Allow!', '批次暂停状态不允许!', null);

delete ad_message where OBJECT_RRN = '202401251149294';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149294', '0', 'Y', 'wip-8021: wip.pre_trans_error', 'This operation does not allow in Previous transaction !', '前一事务不允许此操作!', null);

delete ad_message where OBJECT_RRN = '202401251149275';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149295', '0', 'Y', 'edc-1205: edc.check_edc_spec_failed', 'Check Edc Spec Failed!', '检查EDC规格失败!', null);

delete ad_message where OBJECT_RRN = '202401251149276';
insert into ad_message (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('202401251149296', '0', 'Y', 'ras-1028: ras.eqp_state_not_allow', 'Equipment current state is not allow this operation!', '设备的当前状态不允许此操作!', null);



