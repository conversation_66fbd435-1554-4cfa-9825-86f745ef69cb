delete from AD_AUTHORITY t where t.OBJECT_RRN in (397430273807056811, 397430273807056812, 397430273807056896);
delete from AD_EDITOR t where t.OBJECT_RRN in (397430273798668288);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('397430273807056896', '0', 'Y', to_date('02-11-2023 16:46:01', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('07-11-2023 10:48:46', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '5', 'Wip.UpdateProcedureVersion', '批次更新流程版本', 'F', 'E', '397430273798668288', '602', '100', 'Lot Update Procedure Version', '批次更新流程版本', null, 'procedure', 'MES', 'com.glory.mes.wip', null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('397430273807056811', '0', 'Y', to_date('07-11-2023', 'dd-mm-yyyy'), 'admin', to_date('07-11-2023 11:04:17', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.UpdateProcedureVersion.updateProcedure', '更新流程', 'B', null, null, '397430273807056896', '20', 'Update Procedure', '更新流程', null, 'procedure', 'MES', null, null);

insert into AD_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, AUTHORITY_TYPE, ACTION, EDITOR_RRN, PARENT_RRN, SEQ_NO, LABEL, LABEL_ZH, LABEL_RES, IMAGE, AUTHORITY_CATEGORY, MODULE, HELP_URL)
values ('397430273807056812', '0', 'Y', to_date('07-11-2023', 'dd-mm-yyyy'), 'admin', to_date('07-11-2023 11:04:05', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '3', 'Wip.UpdateProcedureVersion.futureHold', '未来暂停', 'B', null, null, '397430273807056896', '10', 'Future Hold', '未来暂停', null, 'hold', 'MES', null, null);

insert into AD_EDITOR (OBJECT_RRN, ORG_RRN, IS_ACTIVE, NAME, DESCRIPTION, EDITOR_ID, PARAM1, PARAM2, PARAM3, PARAM4, PARAM5)
values ('397430273798668288', '0', 'Y', 'UpdateProcedureVersionEditor', null, 'bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.update.procedureversion.UpdateProcedureVersionEditor', null, 'WIPUpdateProcVerManager', null, 'procedure', null);

delete from AD_BUTTON_AUTHORITY t where t.OBJECT_RRN in (20231107001, 20231107002);

insert into AD_BUTTON_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_KEY, AUTHORITY_DESC, AUTHORITY_ACTION_ID, IS_ENABLE)
values ('20231107001', '0', 'Y', to_date('07-11-2023', 'dd-mm-yyyy'), 'admin', to_date('07-11-2023', 'dd-mm-yyyy'), 'admin', '0', 'Wip.UpdateProcedureVersion.futureHold', 'Wip.UpdateProcedureVersion.futureHold', 'UserAuth', 'Y');

insert into AD_BUTTON_AUTHORITY (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, AUTHORITY_KEY, AUTHORITY_DESC, AUTHORITY_ACTION_ID, IS_ENABLE)
values ('20231107002', '0', 'Y', to_date('07-11-2023', 'dd-mm-yyyy'), 'admin', to_date('07-11-2023', 'dd-mm-yyyy'), 'admin', '0', 'Wip.UpdateProcedureVersion.updateProcedure', 'Wip.UpdateProcedureVersion.updateProcedure', 'UserAuth', 'Y');

delete from AD_REFTABLE t where t.OBJECT_RRN in (397717071363903488);

insert into AD_REFTABLE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, TABLE_RRN, KEY_FIELD, TEXT_FIELD, WHERE_CLAUSE, ORDER_BY_CLAUSE, IS_DISTIC, IS_QUERY_BY_FIELD)
values ('397717071363903488', '0', 'Y', to_date('03-11-2023 11:45:39', 'dd-mm-yyyy hh24:mi:ss'), 'admin', to_date('06-11-2023 17:50:46', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '11', 'PRDProcedureEffectiveList', '有效的流程', '403', 'objectRrn', 'id', '(status=''Active'' OR status=''InActive'')', 'name, version', null, 'N');

delete from AD_MESSAGE t where t.KEY_ID in ('wip.lot_procedure_is_not_same', 'wip.select_porcedure_and_lot_procedure_not_same');

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('399149673421492224', '0', 'Y', 'wip.lot_procedure_is_not_same', 'The selected lot procedure is different', '选择的批次流程不相同!', null);

insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
values ('399149673421492111', '0', 'Y', 'wip.select_porcedure_and_lot_procedure_not_same', 'The selected procedure is different from the lot process.', '选择的流程与批次流程不相同!', null);

 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPUpdateProcVerManager' NAME, TO_TIMESTAMP('2023/11/07 10:53:01', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('394503481334517669', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 
 MERGE INTO AD_FORM_RELEASE a
USING (SELECT 'WIPLotFutureHoldDialog' NAME, TO_TIMESTAMP('2023/11/03 17:04:29', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
WHEN MATCHED THEN 
 UPDATE SET a.IS_UPDATE = 'Y'
WHEN NOT MATCHED THEN 
 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 VALUES ('3945065342176691', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 