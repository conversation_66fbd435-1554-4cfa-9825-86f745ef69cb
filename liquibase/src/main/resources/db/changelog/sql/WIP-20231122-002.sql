delete from AD_SYS_PARAMETER_VALUE where name = 'mes_use_is_lock_version';
delete from AD_SYS_PARAMETER where name = 'mes_use_is_lock_version';

delete from AD_SYS_PARAMETER_VALUE where name = 'mes_wo_use_is_lock_version';
delete from AD_SYS_PARAMETER where name = 'mes_wo_use_is_lock_version';

insert into AD_SYS_PARAMETER (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, DEFAULT_VALUE, IS_STATIC, IS_GLOBAL, IS_MODIFIABLE)
values ('210761', '0', 'Y', to_date('22-11-2023', 'dd-mm-yyyy'), 'admin', to_date('22-11-2023', 'dd-mm-yyyy'), 'admin', '0', 'mes_wo_use_is_lock_version', '工单管理显示锁定模块版本栏位', null, 'N', 'Y', 'Y');

insert into AD_SYS_PARAMETER_VALUE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, CREATED, CREATED_BY, UPDATED, UPDATED_BY, LOCK_VERSION, NAME, DESCRIPTION, VALUE, DEFAULT_VALUE, IS_STATIC, IS_SYSTEM, IS_GLOBAL, IS_MODIFIABLE, COMMENTS)
values ('210762', '0', 'Y', to_date('22-11-2023 17:26:23', 'dd-mm-yyyy hh24:mi:ss'), null, to_date('22-11-2023 17:26:23', 'dd-mm-yyyy hh24:mi:ss'), 'admin', '5', 'mes_wo_use_is_lock_version', '工单管理显示锁定模块版本栏位', 'Y', 'Y', 'N', 'Y', 'Y', 'Y', null);
