<?xml version="1.1" encoding="GBK" standalone="no"?>
<databaseChangeLog
	xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
	xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
	xmlns:pro="http://www.liquibase.org/xml/ns/pro"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.9.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.9.xsd">

	<changeSet id="MM-20231219-001_CREATE_TABLE_PACKAGE_RULE" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="MM_PACKAGE_RULE"/>
			</not>
		</preConditions>
		
    	<createTable remarks="包装规则表" tableName="MM_PACKAGE_RULE" tablespace="TS_MES_DAT">
        	<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_MM_PACKAGE_RULE"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/> 
            
            <column name="NAME" remarks="规则名称" type="VARCHAR2(32 BYTE)"/>
            <column name="DESCRIPTION" remarks="规则描述" type="VARCHAR2(32 BYTE)"/>
            
            <column name="PACKAGE_TYPE_NAME" remarks="包装类型名称" type="VARCHAR2(32 BYTE)"/>
            <column name="IS_DEFAULT" remarks="是否默认" type="VARCHAR2(1 BYTE)"/>
            <column name="DEFAULT_LABEL_NAME" remarks="默认标签收名称" type="VARCHAR2(32 BYTE)"/>
            <column name="MERGE_RULE_NAME" remarks="合批规则名称" type="VARCHAR2(32 BYTE)"/>
            <column name="SOURCE_QTY_TYPE" remarks="包装数量类型" type="VARCHAR2(32 BYTE)"/>
            <column name="SOURCE_MAIN_QTY" remarks="包装数量" type="NUMBER(19, 0)"/>
            <column name="STYLE" remarks="Style" type="NUMBER"/>
    	</createTable>
	</changeSet>
	
	<changeSet id="MM-20231221-001-ADDCOLOUMN_PARENT_RRN" author="DaiWenBin">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="MM_PACKAGE_HIERARCHY" columnName="PARENT_RRN"/>
			</not>
		</preConditions>

		<addColumn tableName="MM_PACKAGE_HIERARCHY">
			<column name="PARENT_RRN" remarks="父层级Rrn" type="NUMBER(19, 0)"/>
		</addColumn>
				
		<comment>MM_PACKAGE_HIERARCHY增加PARENT_RRN栏位</comment>
	</changeSet>
	
	<changeSet id="MM-20231221-001-PACKAGE_TYPE" author="DaiWenBin">
		<sqlFile path="sql/MM-20231221-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>包装类型功能提交</comment>
	</changeSet>
	
	<changeSet id="MM-20231222-001_CREATETABLE_PACKAGE_RULE_HIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="MM_PACKAGE_RULE_HIS"/>
			</not>
		</preConditions>
		
    	<createTable remarks="包装规则历史表" tableName="MM_PACKAGE_RULE_HIS" tablespace="TS_MES_HIS_DAT">
        	<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_MM_PACKAGE_RULE_HIS"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>        
            <column name="TRANS_TIME" remarks="事物时间" type="date"/>         
            <column name="TRANS_TYPE" remarks="事物类型" type="VARCHAR2(32 BYTE)"/>
            <column name="HIS_SEQ" remarks="事物号" type="VARCHAR2(32 BYTE)"/>
            <column name="HIS_SEQ_NO" remarks="事物序号" type="NUMBER(19, 0)"/>		
            
            <column name="PACKAGE_RULE_RRN" remarks="规则主键" type="NUMBER(19, 0)"/>
            <column name="NAME" remarks="规则名称" type="VARCHAR2(32 BYTE)"/>
            <column name="DESCRIPTION" remarks="规则描述" type="VARCHAR2(32 BYTE)"/>
            
            <column name="PACKAGE_TYPE_NAME" remarks="包装类型名称" type="VARCHAR2(32 BYTE)"/>
            <column name="IS_DEFAULT" remarks="是否默认" type="VARCHAR2(1 BYTE)"/>
            <column name="DEFAULT_LABEL_NAME" remarks="默认标签收名称" type="VARCHAR2(32 BYTE)"/>
            <column name="MERGE_RULE_NAME" remarks="合批规则名称" type="VARCHAR2(32 BYTE)"/>
            <column name="SOURCE_QTY_TYPE" remarks="包装数量类型" type="VARCHAR2(32 BYTE)"/>
            <column name="SOURCE_MAIN_QTY" remarks="包装数量" type="NUMBER(19, 0)"/>
            <column name="STYLE" remarks="Style" type="NUMBER"/>
    	</createTable>
	</changeSet>
	
	<changeSet id="MM-20231222-001_CREATETABLE_PACKAGE_TYPE_HIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="MM_PACKAGE_TYPE_HIS"/>
			</not>
		</preConditions>
		
    	<createTable remarks="包装类型历史表" tableName="MM_PACKAGE_TYPE_HIS" tablespace="TS_MES_HIS_DAT">
        	<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_MM_PACKAGE_TYPE_HIS"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>        
            <column name="TRANS_TIME" remarks="事物时间" type="date"/>         
            <column name="TRANS_TYPE" remarks="事物类型" type="VARCHAR2(32 BYTE)"/>
            <column name="HIS_SEQ" remarks="事物号" type="VARCHAR2(32 BYTE)"/>
            <column name="HIS_SEQ_NO" remarks="事物序号" type="NUMBER(19, 0)"/>		
            
            <column name="PACKAGE_TYPE_RRN" remarks="类型主键" type="NUMBER(19, 0)"/>		
            <column name="NAME" remarks="类型名称" type="VARCHAR2(32 BYTE)"/>
            <column name="DESCRIPTION" remarks="描述" type="VARCHAR2(32 BYTE)"/>
            
            <column name="OBJECT_TYPE" remarks="对象类型" type="VARCHAR2(32 BYTE)"/>
            <column name="PACK_STYLE" remarks="" type="NUMBER"/>
            <column name="PACK_MAIN_MAT_TYPE" remarks="包装后主物料类型" type="VARCHAR2(32 BYTE)"/>
            <column name="PACK_SUB_MAT_TYPE" remarks="包装后子物料类型" type="VARCHAR2(32 BYTE)"/>
            <column name="PACK_MAIN_QTY_TYPE" remarks="包装后主数量类型" type="VARCHAR2(32 BYTE)"/>
            <column name="PACK_SUB_QTY_TYPE" remarks="包装后子数量类型" type="VARCHAR2(32 BYTE)"/>
            <column name="PACK_MERGE_RULE" remarks="包装合批规则" type="VARCHAR2(32 BYTE)"/>
            <column name="PACK_ID_RULE" remarks="包装ID生成规则" type="VARCHAR2(32 BYTE)"/>
            <column name="SOURCE_MAIN_MAT_TYPE" remarks="包装前主物料类型" type="VARCHAR2(32 BYTE)"/>
            <column name="SOURCE_SUB_MAT_TYPE" remarks="包装前子物料类型" type="VARCHAR2(32 BYTE)"/>
            <column name="SOURCE_QTY_TYPE" remarks="包装前主数量类型" type="VARCHAR2(32 BYTE)"/>
            <column name="SOURCE_MAIN_QTY" remarks="包装前子数量类型" type="VARCHAR2(32 BYTE)"/>
            <column name="SOURCE_LOT_STATE" remarks="包装前批次状态" type="VARCHAR2(32 BYTE)"/>
            <column name="REMAINDER_TYPE" remarks="尾批规则" type="VARCHAR2(32 BYTE)"/>
            
            <column name="RESERVED01" remarks="预留栏位1" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED02" remarks="预留栏位2" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED03" remarks="预留栏位3" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED04" remarks="预留栏位4" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED05" remarks="预留栏位5" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED06" remarks="预留栏位6" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED07" remarks="预留栏位7" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED08" remarks="预留栏位8" type="VARCHAR2(32 BYTE)"/>
    	</createTable>
	</changeSet>
	
	<changeSet id="MM-20231222-001_CREATETABLE_PACKAGE_HIERARCHY_HIS" author="Clark">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="MM_PACKAGE_HIERARCHY_HIS"/>
			</not>
		</preConditions>
		
    	<createTable remarks="包装层次历史表" tableName="MM_PACKAGE_HIERARCHY_HIS" tablespace="TS_MES_HIS_DAT">
        	<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_MM_PACKAGE_HIERARCHY_HIS"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>        
            <column name="TRANS_TIME" remarks="事物时间" type="date"/>         
            <column name="TRANS_TYPE" remarks="事物类型" type="VARCHAR2(32 BYTE)"/>
            <column name="HIS_SEQ" remarks="事物号" type="VARCHAR2(32 BYTE)"/>
            <column name="HIS_SEQ_NO" remarks="事物序号" type="NUMBER(19, 0)"/>		
            
            <column name="PACKAGE_HIERARCHY_RRN" remarks="层次主键" type="NUMBER(19, 0)"/>
            <column name="NAME" remarks="层次名称" type="VARCHAR2(32 BYTE)"/>
            <column name="DESCRIPTION" remarks="描述" type="VARCHAR2(32 BYTE)"/>
            
            <column name="PACKAGE_LEVEL" remarks="包装层级" type="NUMBER(19, 0)"/>
            <column name="RELATION_TYPE" remarks="包装上下级关系" type="VARCHAR2(32 BYTE)"/>
            <column name="DEFAULT_PACKAGE_TYPE" remarks="默认包装名称" type="VARCHAR2(32 BYTE)"/>
            <column name="OBJECT_TYPE" remarks="对象类型" type="VARCHAR2(32 BYTE)"/>
            <column name="MAIN_MAT_TYPE" remarks="主物料类型" type="VARCHAR2(32 BYTE)"/>
            <column name="IS_OPTIONAL" remarks="是否可选" type="VARCHAR2(1 BYTE)"/>
            <column name="PARENT_RRN" remarks="父级主键" type="NUMBER(19, 0)"/>
    	</createTable>
	</changeSet>
	
	<changeSet id="MM-20231226-002-PACKAGE_TYPE" author="DaiWenBin">
		<sqlFile path="sql/MM-20231226-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>包装类型功能重提交</comment>
	</changeSet>
	
	<changeSet id="MM-20231227-001-PACKAGE_HIER" author="Clark">
		<sqlFile path="sql/MM-20231227-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>删除包装层次功能</comment>
	</changeSet>
	
	<changeSet id="MM-20231227-002-PACKAGE_RULE" author="Clark">
		<sqlFile path="sql/MM-20231227-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>包装规则功能SQL</comment>
	</changeSet>
	
	<changeSet id="MM-20231228-002-PACKAGE-SPLITPACKAGE-GLC" author="tangjiacheng">
		<sqlFile path="sql/MM-20231228-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>批次包装，分包包装功能改GLC</comment>
	</changeSet>
	
	<changeSet id="MM-20231229-001-UNPACKAGE-SPLITPACKAGE-GLC" author="tangjiacheng">
		<sqlFile path="sql/MM-20231229-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>批次拆包，分包拆包功能改GLC</comment>
	</changeSet>

	<changeSet id="MM-20231229-002-PACK-GLC" author="Clark">
		<sqlFile path="sql/MM-20231229-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料批次包装，分包动作功能改GLC</comment>
	</changeSet>
	
	<changeSet id="MM-20231229-010-PACKAGE_TYPE" author="DaiWenBin">
		<sqlFile path="sql/MM-20231229-010.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>包装类型功能重提交</comment>
	</changeSet>
	
	<changeSet id="WIP-20240102-CARRIERLOT-SPLIT" author="FanChengMing">
		<sqlFile path="sql/WIP-20240102-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>载具绑定和分批功能改成GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20240103-CARRIERLOT-DEASSIGN" author="WangLuoPeng">
		<sqlFile path="sql/WIP-20240103-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>载具解绑功能改成GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20240103-CARRIERLOT-CHANGE" author="WangLuoPeng">
		<sqlFile path="sql/WIP-20240104-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>载具更改功能改成GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20240104-PACKED-MANAGER-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240104-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>包装管理的一些admessage添加</comment>
	</changeSet>
	
	<changeSet id="WIP-20240105-TRACK-CONFIG" author="Clark">
		<sqlFile path="sql/WIP-20240105-005.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>进出站控制面板功能SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20240108-CARRIERLOT-MERGE" author="WangLuoPeng">
		<sqlFile path="sql/WIP-20240108-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>载具合批功能改成GLC</comment>
	</changeSet>
		
	<changeSet id="WIP-20240111-001-ADD-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240111-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	  
		<comment>物料组托，组件事务历史查询缺失的message补充，批次在线返工添加查询条件</comment>
	</changeSet>
	
	<changeSet id="WIP-20240116-CARRIERLOT-CHANGE" author="WangLuoPeng">
		<sqlFile path="sql/WIP-20240116-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>载具Change和解绑调整</comment>
	</changeSet>
	
	<changeSet id="EDC-20240116-001_CREATETABLE_EDC_PROCESS_DATA_CONFIG" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="EDC_PROCESS_DATA_CONFIG"/>
			</not>
		</preConditions>
		
    	<createTable remarks="数据采集配置表" tableName="EDC_PROCESS_DATA_CONFIG" tablespace="TS_MES_DAT">
        	<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_EDC_PROCESS_DATA_CONFIG"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/> 
            
            <column name="CONFIG_NAME" remarks="配置名称" type="VARCHAR2(32 BYTE)"/>
            <column name="SEQ_NO" remarks="序列号" type="NUMBER(19, 0)"/>
            <column name="EQP_TYPES" remarks="设备类型列表" type="VARCHAR2(256 BYTE)"/>
            <column name="EQP_IDS" remarks="设备号列表" type="VARCHAR2(256 BYTE)"/>
            <column name="STEP_NAMES" remarks="工步名称列表" type="VARCHAR2(256 BYTE)"/>
            <column name="ITEM_NAMES" remarks="待处理变量列表" type="VARCHAR2(256 BYTE)"/>
    	</createTable>
	</changeSet>
	
	<changeSet id="EDC-20240116-001_CREATETABLE_EDC_PROCESS_DATA_LOG" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="EDC_PROCESS_DATA_LOG"/>
			</not>
		</preConditions>
		
    	<createTable remarks="数据采集发送历史表" tableName="EDC_PROCESS_DATA_LOG" tablespace="TS_MES_HIS_DAT">
        	<column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_EDC_PROCESS_DATA_LOG"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            
            <column name="UPDATED_BY" remarks="更新者" type="VARCHAR2(32 BYTE)"/>        
            <column name="TRANS_TIME" remarks="事物时间" type="date"/>         
            <column name="TRANS_TYPE" remarks="事物类型" type="VARCHAR2(32 BYTE)"/>
            
            <column name="PROCESS_DATA_RRN" remarks="上报数据主键" type="NUMBER(19, 0)"/>
            <column name="LOT_ID" remarks="批次号" type="VARCHAR2(32 BYTE)"/>
            <column name="EQUIPMENT_ID" remarks="设备号" type="VARCHAR2(32 BYTE)"/>
            <column name="STEP_NAME" remarks="工步名称" type="VARCHAR2(32 BYTE)"/>
            <column name="RETURN_CODE" remarks="返回代码" type="VARCHAR2(64 BYTE)"/>
            <column name="RETURN_MESSAGE" remarks="返回消息" type="BLOB"/>
    	</createTable>
	</changeSet>
	
	<changeSet id="WIP-20240117-LOT-PACKED-VIEW-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240117-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料和批次包装功能列表新增Json属性后页面修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20240118-TRACKCONFIG" author="Clark">
		<sqlFile path="sql/WIP-20240118-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>进出站控制面板修改</comment>
	</changeSet>
	
	<changeSet id="EDC-20240118-001_PROCESSDATACONFIGMANAGER" author="FanChengMing">
		<sqlFile path="sql/EDC-20240118-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>数据收集配置管理</comment>
	</changeSet>
	
	<changeSet id="WIP-20240123-001_UPDATE-AD_AUTHORITY" author="DaiWenBin">
		<sql>update ad_authority set is_active = 'N' where name = 'Wip.StepFlowCdiPoint';</sql>	   
		<comment>隐藏批次流程动作注入功能</comment>
	</changeSet>
	
	<changeSet id="WIP-20240123-TRACK_AD_FORM" author="DaiWenBin">
		<sqlFile path="sql/WIP-20240123-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>进出站作业AD_Form</comment>
	</changeSet>
	
	<changeSet id="MM-20240130-001-DROPINDEX" author="zhougelong">
		<preConditions onFail="MARK_RAN">
		    <indexExists indexName="MM_STORAGE" tableName="MM_STORAGE"/>
		</preConditions>
		
		<dropIndex tableName="MM_STORAGE" indexName="MM_STORAGE"/>
		<comment>库位删除无用索引</comment>
	</changeSet>
	
	<changeSet id="MM-20240130-001-KEY" author="zhougelong">
		<preConditions onFail="MARK_RAN">
			<not>
				<primaryKeyExists primaryKeyName="MM_STORAGE"/>
			</not>
		</preConditions>
		
		<addPrimaryKey columnNames="OBJECT_RRN" tableName="MM_STORAGE" tablespace="TS_MES_IDX" constraintName="PK_MM_STORAGE"></addPrimaryKey>
		<comment>库位增加主键</comment>
	</changeSet>
	
	<changeSet id="MM-20240130-001-INDEX" author="zhougelong">
		<preConditions onFail="MARK_RAN">
		   <not>
			<indexExists indexName="UK_MM_STORAGE_NAME_CATEGORY" tableName="MM_STORAGE"/>
		   </not>
		</preConditions>
		
		<createIndex indexName="UK_MM_STORAGE_NAME_CATEGORY" tableName="MM_STORAGE" tablespace="TS_MES_IDX" unique="true">
			<column name="NAME"/>
			<column name="CATEGORY"/>
		</createIndex>
		
		<addUniqueConstraint columnNames="NAME, CATEGORY" constraintName="UK_MM_STORAGE_NAME_CATEGORY" forIndexName="UK_MM_STORAGE_NAME_CATEGORY" tableName="MM_STORAGE" tablespace="TS_MES_IDX"/>
		<comment>库位索引修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20240130-WIP-EXCEPTION-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240130-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>wip模块的后台异常提示SQL</comment>
	</changeSet>
	
	<changeSet id="MM-20240201-MM-EXCEPTION-MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/MM-20240201-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>mm模块的后台异常提示SQL</comment>
	</changeSet>
	
	<changeSet id="EDC-20240202-EDC-EXCEPTION-MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/EDC-20240202-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>edc模块的后台异常提示SQL</comment>
	</changeSet>
	
	<changeSet id="RAS-20240202-RAS-EXCEPTION-MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/RAS-20240202-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>ras模块的后台异常提示SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20240202-MBAS-EXCEPTION-MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20240202-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>mbas模块的后台异常提示SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20240201-TRAY-GLC-XML" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240201-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>组托，托盘动作转GLC XML发布</comment>
	</changeSet>
	
	<changeSet id="MM-20240202-001-STORAGE" author="Clark">
		<sqlFile path="sql/MM-20240202-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>STORAGE转GLC XML发布</comment>
	</changeSet>
	
	<changeSet id="BAS-20240202-001-VENDOR-IMPORT" author="zhougelong">
		<sqlFile path="sql/BAS-20240202-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>供应商导入导出</comment>
	</changeSet>
	
	<changeSet id="WIP-20240202-EXCEPTION-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240202-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>后台异常提示调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20240202-PACK—GLC-XML" author="DaiWenBin">
		<sqlFile path="sql/WIP-20240202-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>包装查询和废品包装GLC XML发布</comment>
	</changeSet>
	
	<changeSet id="WIP-20240203-VALIDATELOG-EXCEPTION-MESSAGE" author="FanChengMing">
		<sqlFile path="sql/WIP-20240203-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>ValidateLog异常提示SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20240204-EXCEPTION-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240204-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>后台异常提示新增</comment>
	</changeSet>
	
	<changeSet id="EDC-20240207-001-EDC_PROCESS_DATA_LOG_CLOUMN" author="FanChengMing">
		<preConditions onFail="MARK_RAN">
			<columnExists tableName="EDC_PROCESS_DATA_LOG" columnName="RETURN_MESSAGE"/>
		</preConditions> 
		
    	<dropColumn tableName="EDC_PROCESS_DATA_LOG">  
        	<column name="RETURN_MESSAGE" />
    	</dropColumn>  
		<addColumn tableName="EDC_PROCESS_DATA_LOG">
			<column name="RETURN_MESSAGE" remarks="返回消息" type="CLOB"/>
		</addColumn>
		<comment>修改RETURN_MESSAGE字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20240205-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20240205-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>ADMessage SQL</comment>
	</changeSet>
	
	<changeSet id="WIP-20240221-AD_FORM_RELEASE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20240221-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>工单批次投料转新版GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20240221-WO-CHANGE-LOT-GLC-MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240221-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>工单转投，工单配料转GLC</comment>
	</changeSet>
	
	<changeSet id="MM-20240222-MM-MESSAGE" author="zhougelong">
		<sqlFile path="sql/MM-20240222-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料Message补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20240222-001-MESSAGE" author="Clark">
		<sqlFile path="sql/WIP-20240222-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>Message补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20240223-001-VIEW-SQL" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240223-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>一些缺失的editor和页面调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20240226-001-MY-LOT-LIST-UPDATE-EDITOR" author="DaiWenBin">
		<sql>update ad_editor t set t.param2 = 'LotMyGroupManager' where t.object_rrn = 20211215001;</sql>
		<comment>更新MyLotList Editor</comment>
	</changeSet>
	
	<changeSet id="WIP-20240229-001-UNSTART" author="Clark">
		<sqlFile path="sql/WIP-20240229-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新取消投料页面</comment>
	</changeSet>
	
	<changeSet id="WIP-20240229-002-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20240229-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充一些英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20240229-MODIFYDATATYPE" author="DaiWenBin">
		<modifyDataType tableName="BAS_MERGE_RULE" columnName="NAME" newDataType="VARCHAR2(128 BYTE)"/>
		<modifyDataType tableName="MM_PACKAGE_RULE" columnName="MERGE_RULE_NAME" newDataType="VARCHAR2(128 BYTE)"/>
		<modifyDataType tableName="MM_PACKAGE_RULE_HIS" columnName="MERGE_RULE_NAME" newDataType="VARCHAR2(128 BYTE)"/>
		<comment>修改BAS_MERGE_RULE、MM_PACKAGE_RULE、MM_PACKAGE_RULE_HIS字段的长度</comment>
	</changeSet>
	
	<changeSet id="WIP-20240229-001-VIEW-SQL" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240229-003.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>一些缺失的editor和页面调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20240301-001-VIEW-SQL" author="tangjiacheng">
		<sql>
		MERGE INTO AD_FORM_RELEASE a
		USING (SELECT 'PpReworkStartManager' NAME, TO_TIMESTAMP('2024/03/01 16:24:20', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
		ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
		WHEN MATCHED THEN 
		UPDATE SET a.IS_UPDATE = 'Y'
		WHEN NOT MATCHED THEN 
 		INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
		VALUES ('395182995617726505', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
		</sql>
		<comment>返工工单页面调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20240301-010-LOTPROCEDURE" author="Clark">
		<sqlFile path="sql/WIP-20240301-010.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新LOTPROCEDURE页面</comment>
	</changeSet>
	
	<changeSet id="WIP-20240304-001-UPDATE-SCRAP-PACK" author="DaiWenBin">
		<sql>
		MERGE INTO AD_FORM_RELEASE a
		USING (SELECT 'ScrapPackManager' NAME, TO_TIMESTAMP('2024/2/2 22:23:47', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
		ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
		WHEN MATCHED THEN 
		 UPDATE SET a.IS_UPDATE = 'Y'
		WHEN NOT MATCHED THEN 
		 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
		 VALUES ('430854960369983488', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
		</sql>
		<comment>废品包装页面调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20240304-002-UPDATE_CONTEXT" author="DaiWenBin">
		<sqlFile path="sql/WIP-20240304-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新Context</comment>
	</changeSet>
	
	<changeSet id="WIP-20240306-001-UPDATE_AD_AUTHORITY" author="Clark">
		<sqlFile path="sql/WIP-20240306-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新AD_AUTHORITY记录</comment>
	</changeSet>
	
	<changeSet id="MM-20240305-010-MM-LOT-CLASS-COLUMN-ADD" author="hetao">
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="MM_LOT" columnName="MATERIAL_CLASS"/>
			</not>
		</preConditions> 
		
		<addColumn tableName="MM_LOT">
			<column name="MATERIAL_CLASS" remarks="物料大类" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<addColumn tableName="MM_LOT_HIS">
			<column name="MATERIAL_CLASS" remarks="物料大类" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
		<comment>MLOT增加物料大类字段</comment>
	</changeSet>
	
	<changeSet id="WIP-20240307-005-UPDATE_WO" author="Clark">
		<sqlFile path="sql/WIP-20240307-005.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新工单与投料页面</comment>
	</changeSet>

	<changeSet id="WIP-20240307-001-UPDATE-MLOTSTART-ADD" author="tangjiacheng">
		<sql>
		 MERGE INTO AD_FORM_RELEASE a
		 USING (SELECT 'WoMLotQueryDialog' NAME, TO_TIMESTAMP('2024/03/06 18:44:42', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
		 ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
		 WHEN MATCHED THEN 
 		 UPDATE SET a.IS_UPDATE = 'Y'
		 WHEN NOT MATCHED THEN 
 		 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
 		 VALUES ('442739385214345223', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
		</sql>
		<comment>工单投料添加弹框重新设计</comment>
	</changeSet>
	
	<changeSet id="WIP-20240308-010-AD_TABLE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20240308-010.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新动态表</comment>
	</changeSet>
	
	<changeSet id="WIP-20240307-001-UPDATE-TRACKIN_SELECT_EQUIPMENTPAGE" author="DaiWenBin">
		<sql>
		   MERGE INTO AD_FORM_RELEASE a
			USING (SELECT 'TrackInSelectEquipmentPage' NAME, TO_TIMESTAMP('2024/1/23 14:41:52', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
			ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
			WHEN MATCHED THEN 
			 UPDATE SET a.IS_UPDATE = 'Y'
			WHEN NOT MATCHED THEN 
			 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
			 VALUES ('427114838658940928', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
		</sql>
		<comment>进站设备重新设计</comment>
	</changeSet>
	
	<changeSet id="WIP-20240308-001-ADMESSAGE-ADD" author="tangjiacheng">
		<sql>
		DELETE AD_MESSAGE WHERE OBJECT_RRN = '202403081011001';
		insert into AD_MESSAGE (OBJECT_RRN, ORG_RRN, IS_ACTIVE, KEY_ID, MESSAGE, MESSAGE_ZH, MESSAGE_RES)
		values ('202403081011001', '0', 'Y', 'error.name_repeat', 'Name Repeat', '名称重复!', null);
		</sql>
		<comment>补充缺失的message</comment>
	</changeSet>
	
	<changeSet id="WIP-20240312-001-WO-REQUEST-MATERIAL-UPDATED" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240312-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>工单领料功能页面重构</comment>
	</changeSet>
	
	<changeSet id="WIP-20240315-011-AD_MESSAGE" author="Clark">
		<sqlFile path="sql/WIP-20240315-011.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>Message补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20240315-012-WIP_LOTDEASSIGN" author="Clark">
		<sqlFile path="sql/WIP-20240315-012.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>载具解绑页面调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20240315-002-AD_MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240315-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>异常提示Message补充</comment>
	</changeSet>
	
	<changeSet id="WIP-20240316-001-MULTI_SUB_EQUIPMENT_PAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20240316-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>父子设备进站界面发布</comment>
	</changeSet>
	
	<changeSet id="WIP-20240317-002-AD_MESSAGE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20240320-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20240320-002-AD_MESSAGE" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240320-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充英译中</comment>
	</changeSet>
	
	<changeSet id="WIP-20240321-002-AD_MESSAGE-LOTHIS-QUERY" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240321-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>批次事务历史，批次加工历史查询转GLC</comment>
	</changeSet>
	
	<changeSet id="BAS-20240326-001-WEB-LOCATION-EXP" author="tangjiacheng">
		<sqlFile path="sql/BAS-20240326-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>WEB生产区域导入修改</comment>
	</changeSet>
	
	<changeSet id="WIP-20240326-004-AD_SYS_PARAMETER" author="Clark">
		<sqlFile path="sql/WIP-20240326-004.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>修改一个系统参数名称</comment>
	</changeSet>
	
	<changeSet id="WIP-20240328-001-BY-LOT-RUN-OPERATOR" author="tangjiacheng">
		<sql>
		     MERGE INTO AD_FORM_RELEASE a
     		 USING (SELECT 'WIPLotTrackManager' NAME, TO_TIMESTAMP('2024/03/28 17:02:30', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
     		 ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
     		 WHEN MATCHED THEN 
     		 UPDATE SET a.IS_UPDATE = 'Y'
     		 WHEN NOT MATCHED THEN 
      		 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
      		 VALUES ('450705288799420449', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 		 </sql>
		<comment>按批次作业添加通过系统参数控制是否显示操作人栏位</comment>
	</changeSet>
	
	<changeSet id="WIP-20240328-001-QTIME_IMPORT" author="Clark">
		<sqlFile path="sql/WIP-20240328-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>QTime后台导入</comment>
	</changeSet>
	
	<changeSet id="WIP-20240329-001-BUG-UPDATED-TWO" author="tangjiacheng">
		<sqlFile path="sql/WIP-20240329-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>1.包装规则保存添加默认规则判断。2.设备管理删除两个无用字段。3.进出站配置栏位描述修改。4.工单领料缺失参考表。5.工单投料页面添加物料批bug，补充message</comment>
	</changeSet>
	
	<changeSet id="WIP-20240401-001-EDC_ITEM_GLC" author="DaiWenBin">
		<sql>
		     MERGE INTO AD_FORM_RELEASE a
     		 USING (SELECT 'EdcItemManager' NAME, TO_TIMESTAMP('2024/4/1 9:35:11', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
     		 ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
     		 WHEN MATCHED THEN 
     		 UPDATE SET a.IS_UPDATE = 'Y'
     		 WHEN NOT MATCHED THEN 
      		 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
      		 VALUES ('452042421498281984', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 		 </sql>
		<comment>EDC ITEM转GLC</comment>
	</changeSet>
	
	<changeSet id="WIP-20240401-010-SAME_DATA" author="Clark">
		<sqlFile path="sql/WIP-20240401-010.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>处理Oracle相同的数据因为大小写而存在的问题</comment>
	</changeSet>
	
	<changeSet id="WIP-20240401-011-WIP_WO_BOM_LINE" author="Clark">
		<modifyDataType tableName="WIP_WO_BOM_LINE" columnName="OBJECT_RRN" newDataType="NUMBER(19)"/>
		<modifyDataType tableName="WIP_WO_BOM_LINE" columnName="ORG_RRN" newDataType="NUMBER(19)"/>
		<modifyDataType tableName="WIP_WO_BOM_LINE" columnName="STEP_VERSION" newDataType="NUMBER(19)"/>
		<modifyDataType tableName="WIP_WO_BOM_LINE" columnName="MATERIAL_RRN" newDataType="NUMBER(19)"/>
		<comment>修改WIP_WO_BOM_LINE表4个字段长度</comment>
	</changeSet>
	
	<changeSet id="WIP-20240401-002-AD_TABLE" author="DaiWenBin">
		<sqlFile path="sql/WIP-20240401-002.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>更新动态表</comment>
	</changeSet>
	
	<changeSet id="BAS-20240402-001-TEAMUSER-IMP" author="zhougelong">
		<sqlFile path="sql/BAS-20240402-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>修改班组动态导入导出名称</comment>
	</changeSet>
	
	<changeSet id="WIP-20240403-001-TRACK-CONFIG-XML-UPDATE" author="tangjiacheng">
		<sql>
			delete AD_FORM_RELEASE where name = 'WIPTrackConfigManager';
		    MERGE INTO AD_FORM_RELEASE a
			USING (SELECT 'WIPTrackConfigManager' NAME, TO_TIMESTAMP('2024/04/03 14:44:06', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
			ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
			WHEN MATCHED THEN 
			UPDATE SET a.IS_UPDATE = 'Y'
			WHEN NOT MATCHED THEN 
			INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE, a.IS_PRODUCT_RELEASE) 
			VALUES ('438357957005131790', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y', 'Y');
 		</sql>
		<comment>调整进出站控制面板页面栏位</comment>
	</changeSet>
	
	<changeSet id="BAS-20240402-001-TEAMUSER-IMP-UPDATE" author="zhougelong">
		<sqlFile path="sql/BAS-20240403-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>修改班组动态导入导出关键字</comment>
	</changeSet>
	
	<changeSet id="MM-20240407-001-MATERIAL-STEPCHANGEINFO-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/MM-20240407-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>物料管理,工步修改信息栏位调整</comment>
	</changeSet>
	
	<changeSet id="PRD-20240412-001-STEP-UPDATE" author="tangjiacheng">
		<sqlFile path="sql/PRD-20240408-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>工步管理栏位调整</comment>
	</changeSet>
	
	<changeSet id="WIP-20240403-001-MLOT_QUERY_XML" author="DaiWenBin">
		<sql>
		    MERGE INTO AD_FORM_RELEASE a
			USING (SELECT 'MMLotQueryManager' NAME, TO_TIMESTAMP('2022/11/28 17:32:48', 'yyyy-mm-dd hh24:mi:ss') RELEASE_TIMESTAMP FROM dual) b
			ON (a.NAME = b.NAME AND a.RELEASE_TIMESTAMP = b.RELEASE_TIMESTAMP)       
			WHEN MATCHED THEN 
			 UPDATE SET a.IS_UPDATE = 'Y'
			WHEN NOT MATCHED THEN 
			 INSERT(a.OBJECT_RRN, a.ORG_RRN, a.IS_ACTIVE, a.CREATED, a.CREATED_BY, a.UPDATED, a.UPDATED_BY, a.LOCK_VERSION, a.NAME, a.DESCRIPTION, a.STATUS, a.RELEASE_TIMESTAMP, a.IS_UPDATE) 
			 VALUES ('273501549428666375', '0', 'Y', sysdate, 'admin', sysdate, 'admin', '1', b.name, '', 'Active', b.release_timestamp, 'Y');
 		</sql>
		<comment>物料批管理更新</comment>
	</changeSet>
	
	<changeSet id="WIP-20240407-001-BATCHJOBMAPPING" author="FanChengMing">
		<sqlFile path="sql/WIP-20240410-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>增加BatchJob关系维护界面</comment>
	</changeSet>
	
	<changeSet id="EDC-20240716-002-EDC_ITME_EDITOR" author="DaiWenBin">
		<sql>update AD_EDITOR set editor_id = 'bundleclass://com.glory.edc/com.glory.edc.edcitem.EdcItemManagerEditor', PARAM2 = 'EdcItemManager'  where object_Rrn = 701</sql>	   
		<comment>修改EdcItem功能的Editor</comment>
	</changeSet>
	
	<changeSet id="EDC-20240823-010-ITEM_AD_FORM" author="DaiWenBin">
		<sqlFile path="sql/EDC-20240823-010.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>补充数据采集项页面发布sql</comment>
	</changeSet>
    
    <changeSet id="***********-002-STORAGE_HIS" author="ShiJianPing">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="MM_STORAGE_HIS"/>
            </not>
        </preConditions>
        <createTable tableName="MM_STORAGE_HIS" tablespace="TS_MES_DAT" remarks="Storage历史表">
            <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_MM_STORAGE_HIS"/>
            </column>
            <column name="ORG_RRN" remarks="区域号" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="是否有效" type="VARCHAR2(1 BYTE)"/>
            <column name="UPDATED_BY" remarks="更新人" type="VARCHAR2(32 BYTE)"/>
            <column name="HISTORY_SEQ" remarks="事务号" type="VARCHAR2(32 BYTE)"/>
            <column name="HISTORY_SEQ_NO" remarks="事务号序列" type="NUMBER(19, 0)"/>
            <column name="TRANS_TYPE" remarks="历史类型" type="VARCHAR2(32 BYTE)"/>
            <column name="TRANS_TIME" remarks="历史时间" type="TIMESTAMP(6)"/>
            
            <column name="STORAGE_RRN" remarks="库位主键" type="NUMBER(19, 0)"/>
            <column name="NAME" remarks="名称" type="VARCHAR2(32 BYTE)"/>
            <column name="DESCRIPTION" remarks="描述" type="VARCHAR2(128 BYTE)"/>
            <column name="CATEGORY" remarks="类别" type="VARCHAR2(32 BYTE)"/>
            <column name="MAX_QTY" remarks="最大数量" type="NUMBER(6, 0)"/>
            <column name="QTY" remarks="当前数量" type="NUMBER(6, 0)"/>
            <column name="WAREHOUSE_RRN" remarks="仓库主键" type="NUMBER(19, 0)"/>
            <column name="WAREHOUSE_ID" remarks="仓库编号" type="VARCHAR2(32 BYTE)"/>
            <column name="STATUS" remarks="状态" type="VARCHAR2(32 BYTE)"/>
            <column name="PARENT_RRN" remarks="父对象主键" type="NUMBER(19, 0)"/>
            <column name="GROUP2" remarks="组别1" type="VARCHAR2(32 BYTE)"/>
            <column name="GROUP1" remarks="组别2" type="VARCHAR2(32 BYTE)"/>
            <column name="TIMER_TYPE" remarks="定时器类型" type="VARCHAR2(32 BYTE)"/>
            <column name="TIMER_DURATION" remarks="定时器时间间隔" type="NUMBER(19, 0)"/>
            <column name="EARLY_PERIOD" remarks="提前警告时间" type="NUMBER(19, 0)"/>
            <column name="TIMER_ACTION" remarks="定时器动作" type="VARCHAR2(32 BYTE)"/>
            <column name="IS_SUSPEND_TIMER" remarks="是否暂停Timer" type="VARCHAR2(1 BYTE)"/>
            <column name="RESERVED1" remarks="预留" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED2" remarks="预留" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED3" remarks="预留" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED4" remarks="预留" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED5" remarks="预留" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED6" remarks="预留" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED7" remarks="预留" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED8" remarks="预留" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED9" remarks="预留" type="VARCHAR2(32 BYTE)"/>
            <column name="RESERVED10" remarks="预留" type="VARCHAR2(32 BYTE)"/>
            <column name="STYLE" remarks="STYLE" type="NUMBER(6, 0)"/>
        </createTable>
        <comment>增加MM_STORAGE的历史表</comment>
    </changeSet>
    
    <changeSet id="MM-20240826-001-STORAGE_HIS" author="ShiJianPing">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="MM_STORAGE_HIS"/>
        </preConditions>
        <dropTable tableName="MM_STORAGE_HIS"/>
        <comment>回滚提交的功能，删除MM_STORAGE的历史表</comment>
    </changeSet>
    
    <changeSet id="EDC-20241107-001-EDCITEM_IMPORT" author="Yanchao">
        <sqlFile path="sql/EDC-20241107-001.sql" relativeToChangelogFile="true" encoding="GBK"/>	   
		<comment>数据采集项设置支持导入</comment>
    </changeSet>
    
</databaseChangeLog	> 