<?xml version="1.1" encoding="GBK" standalone="no"?>
<databaseChangeLog
	xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
	xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
	xmlns:pro="http://www.liquibase.org/xml/ns/pro"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.9.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.9.xsd">
	
	 <!-- 
     tablespaces:表空间
     TS_MES_DAT:MES普通数据的表空间
     TS_MES_IDX:MES索引表空间
     TS_MES_HIS_DAT:MES历史数据的表空间
     TS_MES_HIS_IDX:历史索引表空间
    -->
	
	<!-- Create Table -->	
	<!-- Oracle 11g支持最长字符30 -->
	 <changeSet author="admin" id="WIP-20220623-Create-table">
    	<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="TABLE"/>
			</not>
		</preConditions>
        <createTable remarks="描述" tableName="TABLE" tablespace="TS_MES_DAT">
            <column name="OBJECT_RRN" remarks="对象序列号" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_NAME" primaryKeyTablespace="TS_MES_IDX"/>
            </column>
            <column name="ORG_RRN" remarks="区域ID" type="NUMBER(19, 0)"/>
            <column name="IS_ACTIVE" remarks="对象是否可用" type="VARCHAR2(1 BYTE)"/>
            <column name="CREATED" remarks="创建时间" type="date"/>
            <column name="CREATED_BY" remarks="创建者ID" type="VARCHAR2(32 BYTE)"/>
            <column name="UPDATED" remarks="更新时间" type="date"/>
            <column name="UPDATED_BY" remarks="更新者ID" type="VARCHAR2(32 BYTE)"/>
            <column name="LOCK_VERSION" remarks="锁定版本" type="NUMBER(19, 0)"/>
            
            <column name="NAME" remarks="描述1" type="VARCHAR2(32 BYTE)"/>
            <column name="STYLE" remarks="描述2" type="NUMBER(19, 0)"/>
        </createTable>
	</changeSet>
	
	<!-- Add Column -->
	<changeSet author="admin" id="WIP-20220623-Add-Column" >
		<preConditions onFail="MARK_RAN">
			<not>
				<columnExists tableName="TABLE" columnName="EQUIPMENT_ID"/>	
			</not>		
		</preConditions>
				
		<addColumn tableName="TABLE">
			<column name="EQUIPMENT_ID" remarks="描述" type="VARCHAR2(32 BYTE)"/>
		</addColumn>
	</changeSet>
	
	
	<!-- Create Index -->
	<!-- Oracle 11g支持最长字符30 -->
	<changeSet author="admin" id="WIP-20220623-Create-Index">
		<preConditions onFail="MARK_RAN">
			<not>
				<indexExists indexName="IDX_WF_PROCESSDEF_HIS_NAME" tableName="WF_PROCESSDEFINITION_HIS"/>
			</not>
		</preConditions>
   		<createIndex indexName="IDX_WF_PROCESSDEF_HIS_NAME" tableName="WF_PROCESSDEFINITION_HIS" tablespace="TS_MES_HIS_DAT" unique="false">
        	<column name="NAME"/>
    	</createIndex>
	</changeSet>
	
	<!-- SqlFile -->
	<changeSet id="WIP-20220623-SqlFile" author="admin">
		<sqlFile path="sql/WIP-20211119-002.sql" relativeToChangelogFile="true" encoding="GBK" />
		<comment>工单按钮英文修改</comment>
	</changeSet>
	
	<!-- Sql -->
	<changeSet id="WIP-20220623-Sql" author="admin">
       <sql>UPDATE AD_FIELD T
			   SET T.DESCRIPTION = 'WSG', T.LABEL = 'WSG', T.LABEL_ZH = 'WSG'
			 WHERE NAME = 'comments'
			   AND TABLE_RRN = (SELECT OBJECT_RRN FROM AD_TABLE WHERE NAME = 'PRDStep')
		</sql>
       <comment>Step Add WSG</comment>
	</changeSet>
	
	<!-- ModifyDataType -->
	<changeSet id="WIP-20220623-modifyDataType" author="admin">
		<modifyDataType tableName="WIP_COMPONENTUNIT_EQP_UNIT_HIS" columnName="UNIT_ID" newDataType="VARCHAR2(64 BYTE)"/>
		<comment>修改WIP_COMPONENTUNIT_EQP_UNIT_HIS字段的长度</comment>
	</changeSet>
	
	<!-- dropTable -->
	<changeSet id="WIP-20220623-dropTable" author="admin">
		<preConditions onFail="MARK_RAN">
			<tableExists  tableName="WIP_LOT_EQUIPMENT_BUFFER"/>
		</preConditions>
		
		<dropTable cascadeConstraints="true"  tableName="WIP_LOT_EQUIPMENT_BUFFER"/>  
	</changeSet>
	
	<!-- dropColumn -->
	<changeSet id="WIP-20220623-dropColumn" author="admin">
    	<preConditions onFail="MARK_RAN">
			<columnExists tableName="WIP_LOT_EQUIPMENT_UNIT_HIS" columnName="LOT_RRN"/>
		</preConditions> 
    	
    	<dropColumn tableName="WIP_LOT_EQUIPMENT_UNIT_HIS">  
        	<column name="LOT_RRN"/>  
    	</dropColumn>  
	</changeSet>
	
	<!-- dropIndex -->
	<changeSet author="admin" id="WIP-20220623-dropIndex">
		<preConditions onFail="MARK_RAN">
			<indexExists indexName="IDX_EDC_PROCESS_GROUP_PART" tableName="EDC_PROCESS_GROUP"/>
		</preConditions>
		
		<dropIndex tableName="EDC_PROCESS_GROUP" indexName="IDX_EDC_PROCESS_GROUP_PART"/>
	</changeSet>
	
</databaseChangeLog> 