/**
 *
 * 保存或者删除  adOwnerRefList
 * 
 * <h2>消息名称: SYS.ADOWNERREFLIST</h2>
 * 
 * <h2>JSON格式：</h2>
 * 
 * <ul>
 * <li><b>Request</b></li>
 * </ul>
 * 
 * <pre>
* {@code {
      "request": {
          "header": {
              "messageName": "SYS.ADOWNERREFLIST",
              "transactionId": "8c512aa1-dbeb-43fc-aa08-03fd988c6fcd",
              "orgName": "",
              "username": "",
              "token": "",
              "language": "",
              "orgRrn": 1
          },
          "body": {
              "actionType": "SAVE",
              "xadOwnerRefLists": [
                  {
                      "referenceName": "Test005",
                      "key": "Key1",
                      "text": "Value1",
                      "seqNo": "1",
                      "description": "Test",
                      "owner": "Admin",
                      "department": "",
                      "isAvailable": true,
                      "parentRrn": "",
                      "reserved01": "",
                      "reserved02": "",
                      "reserved03": "",
                      "reserved04": "",
                      "reserved05": ""
                  }
              ],
              "deleteXAdOwnerRefLists": [
                  {
                      "objectRrn": 404669769557323776
                  }
              ]
          }
      }
  }
}
* </pre>
 * 
 * <ul>
 * <li><b>Response</b></li>
 * </ul>
 * 
 * <pre>
* {@code {
      "response": {
          "header": {
              "transactionId": "8c512aa1-dbeb-43fc-aa08-03fd988c6fcd",
              "result": "SUCCESS"
          }
      }
  }
}
* </pre>
 * 
 * <h2>XML格式</h2>
 * 
 * <ul>
 * <li><b>Request</b></li>
 * </ul>
 * 
 * <pre>
* {@code <Request>
      <Header>
          <MESSAGENAME>SYS.ADOWNERREFLIST</MESSAGENAME>
          <TRANSACTIONID>8c512aa1-dbeb-43fc-aa08-03fd988c6fcd
          </TRANSACTIONID>
          <ORGNAME></ORGNAME>
          <USERNAME></USERNAME>
          <TOKEN></TOKEN>
          <LANGUAGE></LANGUAGE>
          <ORGRRN>1</ORGRRN>
      </Header>
      <Body>
          <ACTIONTYPE>SAVE</ACTIONTYPE>
          <ADOWNERREFLIST>
              <ADOWNERREFLIST>
                  <REFERENCENAME>TEST006</REFERENCENAME>
                  <KEYID>KEY1</KEYID>
                  <TEXT>VALUE1</TEXT>
                  <SEQNO>1</SEQNO>
                  <DESCRIPTION>TEST</DESCRIPTION>
                  <OWNER>ADMIN</OWNER>
                  <DEPARTMENT></DEPARTMENT>
                  <ISAVAILABLE>true</ISAVAILABLE>
                  <PARENTRRN></PARENTRRN>
                  <RESERVED01></RESERVED01>
                  <RESERVED02></RESERVED02>
                  <RESERVED03></RESERVED03>
                  <RESERVED04></RESERVED04>
                  <RESERVED05></RESERVED05>
              </ADOWNERREFLIST>
          </ADOWNERREFLIST>
          <DELETEADOWNERREFLIST>
              <ADOWNERREFLIST>
                  <OBJECTRRN>404671669874831360</OBJECTRRN>
              </ADOWNERREFLIST>
          </DELETEADOWNERREFLIST>
      </Body>
  </Request>
}
* </pre>
 * 
 * <ul>
 * <li><b>Response</b>
 * </ul>
 * 
 * <pre>
* {@code
  <Response>
      <Header>
          <TRANSACTIONID>8c512aa1-dbeb-43fc-aa08-03fd988c6fcd
          </TRANSACTIONID>
          <RESULT>SUCCESS</RESULT>
      </Header>
  </Response>
}
* </pre>
 */
package com.glory.msg.adownerreflist;
