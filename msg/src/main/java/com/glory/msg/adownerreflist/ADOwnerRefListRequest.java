package com.glory.msg.adownerreflist;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
public class ADOwnerRefListRequest extends Request {
	private static final long serialVersionUID = 1L;

	public static final String MESSAGE_NAME = "ADOWNERREFLIST";
	
	public static final String ACTION_SAVE = "SAVE";
	public static final String ACTION_DELETE = "DELETE";
	
	@XmlElement(name="Body")
	private ADOwnerRefListRequestBody body;

	public ADOwnerRefListRequestBody getBody() {
		return body;
	}
	
	public void setBody(ADOwnerRefListRequestBody body) {
		this.body = body;
	}
}
