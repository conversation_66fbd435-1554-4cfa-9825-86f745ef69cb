package com.glory.msg.adownerreflist;

import com.glory.framework.activeentity.model.ADOwnerRefList;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreMessage;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserModel;
import com.glory.msg.Request;
import com.glory.msg.Response;
import com.glory.msg.model.XADOwnerRefList;
import com.glory.msg.trans.MsgTransHandler;
import com.glory.msg.trans.TransContext;

import java.util.ArrayList;
import java.util.List;

@CoreMessage(serviceGroup = "MESCore", serviceModule = "ACTIVEENTITY", messageName = ADOwnerRefListRequest.MESSAGE_NAME)
public class ADOwnerRefList<PERSON>andler extends MsgTransHandler {

	@Override
	protected TransContext internalExecute(TransContext context) throws Exception {
		// 获得对应的消息解析器
		MessageParser parser = getMessageParser(ADOwnerRefListRequest.MESSAGE_NAME);
		ADOwnerRefListRequest request = (ADOwnerRefListRequest) parser.readerRequest(context.getRequest(), context.isJson());
		ADOwnerRefListResponse response = (ADOwnerRefListResponse) executeRequest(request, context);
		context.setResponse(parser.writerResponse(response, context.isJson()));
		return context;
	}

	public Response executeRequest(Request request, TransContext context) throws ClientException {
		SessionContext sc = getSessionContext(request, context);
		context.setTransactionId(request.getHeader().getTransactionId());

		ADOwnerRefListResponse response = new ADOwnerRefListResponse();
		response.getHeader().setTransactionId(request.getHeader().getTransactionId());
		ADOwnerRefListRequestBody requestBody = (ADOwnerRefListRequestBody) request.getBody();
		String actionType = requestBody.getActionType();
		List<XADOwnerRefList> xadOwnerRefLists = requestBody.getXadOwnerRefLists();
		List<XADOwnerRefList> deleteXAdOwnerRefLists = requestBody.getDeleteXAdOwnerRefLists();
		
		List<ADOwnerRefList> refLists = new ArrayList<ADOwnerRefList>();
		List<ADOwnerRefList> deleteRefLists = new ArrayList<ADOwnerRefList>();
		
		if(xadOwnerRefLists != null && !xadOwnerRefLists.isEmpty()) {
			for(XADOwnerRefList xadOwnerRefList : xadOwnerRefLists) {
				ADOwnerRefList refList = new ADOwnerRefList();
				refList.setOrgRrn(sc.getOrgRrn());
				if(xadOwnerRefList.getObjectRrn() != null) {
					refList.setObjectRrn(xadOwnerRefList.getObjectRrn());
					refList = (ADOwnerRefList) context.getAdManager().getEntity(refList);
				}
				PropertyUtil.copyProperties(refList, xadOwnerRefList);
				refList.setIsAvailable(Boolean.TRUE);
				refLists.add(refList);
			}
		}
		
		if(deleteXAdOwnerRefLists != null && !deleteXAdOwnerRefLists.isEmpty()) {
			for(XADOwnerRefList xadOwnerRefList : deleteXAdOwnerRefLists) {
				ADOwnerRefList refList = new ADOwnerRefList();	
				refList.setOrgRrn(sc.getOrgRrn());
				if(xadOwnerRefList.getObjectRrn() != null) {
					refList.setObjectRrn(xadOwnerRefList.getObjectRrn());
					refList = (ADOwnerRefList) context.getAdManager().getEntity(refList);
				}
				PropertyUtil.copyProperties(refList, xadOwnerRefList);
				deleteRefLists.add(refList);
			}
		}
		
		if(ADOwnerRefListRequest.ACTION_SAVE.equals(actionType)) {
			context.getAdManager().cudEntityList(refLists, deleteRefLists, sc);
		}
		
		
		return response;
	}

	public void initMessageParser() {
		// 注册LOTINFO方法
		MessageParserModel model = new MessageParserModel();
		model.setMessageName(ADOwnerRefListRequest.MESSAGE_NAME);
		model.setRequestClass(ADOwnerRefListRequest.class);
		model.setResponseClass(ADOwnerRefListResponse.class);
		registerMessageParser(ADOwnerRefListRequest.MESSAGE_NAME, model);
	}

	public MessageParser getMessageParser() {
		return getMessageParser(ADOwnerRefListRequest.MESSAGE_NAME);
	}

}
