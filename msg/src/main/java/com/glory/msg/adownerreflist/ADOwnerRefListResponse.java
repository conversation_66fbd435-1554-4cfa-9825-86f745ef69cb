package com.glory.msg.adownerreflist;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
public class ADOwnerRefListResponse extends Response {
	
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="Body")
	private ADOwnerRefListResponseBody body;

	public ADOwnerRefListResponseBody getBody() {
		return body;
	}
	
	public void setBody(ADOwnerRefListResponseBody body) {
		this.body = body;
	}
}
