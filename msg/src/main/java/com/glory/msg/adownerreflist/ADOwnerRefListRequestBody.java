package com.glory.msg.adownerreflist;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;

import com.glory.msg.RequestBody;
import com.glory.msg.model.XADOwnerRefList;

@XmlAccessorType(XmlAccessType.NONE)
public class ADOwnerRefListRequestBody extends RequestBody {
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name="ACTIONTYPE")
	private String actionType;
	
	@XmlElementWrapper(name="ADOWNERREFLIST")
	@XmlElementRef
	private List<XADOwnerRefList> xadOwnerRefLists;
	
	@XmlElementWrapper(name="DELETEADOWNERREFLIST")
	@XmlElementRef
	private List<XADOwnerRefList> deleteXAdOwnerRefLists;
	
	/**
	 * 描述：动作类型，映射为：ACTIONTYPE；
	 * @return actionType
	 */
	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public List<XADOwnerRefList> getXadOwnerRefLists() {
		return xadOwnerRefLists;
	}

	public void setXadOwnerRefLists(List<XADOwnerRefList> xadOwnerRefLists) {
		this.xadOwnerRefLists = xadOwnerRefLists;
	}

	public List<XADOwnerRefList> getDeleteXAdOwnerRefLists() {
		return deleteXAdOwnerRefLists;
	}

	public void setDeleteXAdOwnerRefLists(List<XADOwnerRefList> deleteXAdOwnerRefLists) {
		this.deleteXAdOwnerRefLists = deleteXAdOwnerRefLists;
	}

}
