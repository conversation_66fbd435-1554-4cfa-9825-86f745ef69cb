package com.glory.msg.entitymanager.adimpexp;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import com.glory.msg.RequestBody;
import com.glory.msg.model.XADImpExp;
import com.glory.msg.model.XADImpExpFieldMap;
import com.glory.msg.model.XADImpExpSub;

@XmlAccessorType(XmlAccessType.NONE)
public class ADImpExpRequestBody extends RequestBody {
	
	private static final long serialVersionUID = -4851355353875498122L;

	//动作类型
	@XmlElement(name="ACTIONTYPE")
	private String actionType;
	
	/**
	 * 描述：动作类型，映射为：ACTIONTYPE；<br>
	 * 1.当值为QUERY时，查询动态表导入导出信息<br>
	 * 2.当值为UPDATE时，更新用户信息<br>
	 * 3.当值为DELETE时，删除用户<br>
	 * 4.当值为CHANGEPASSWORD时，更新用户信息<br>
	 * 5.当值为BYRRN时，获取用户信息<br>
	 * 6.当值为UPDATESHOP时，更新用户区域权限<br>
	 * 7.当值为GETPASSWORD时，获取用户并密码明文<br>
	 * @return String
	 */
	
	//导入表信息
	@XmlElement(name="ADIMPEXP")
	private XADImpExp adImpExp;
	
	//导入表信息
	@XmlElement(name="XADIMPEXPSUB")
	private XADImpExpSub adImpExpSub;
	
	//导入字段信息
	@XmlElement(name="ADIMPEXPFIELDMAP")
	private XADImpExpFieldMap adImpExpFieldMap;
	
	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public XADImpExp getAdImpExp() {
		return adImpExp;
	}

	public void setAdImpExp(XADImpExp adImpExp) {
		this.adImpExp = adImpExp;
	}

	public XADImpExpFieldMap getAdImpExpFieldMap() {
		return adImpExpFieldMap;
	}

	public void setAdImpExpFieldMap(XADImpExpFieldMap adImpExpFieldMap) {
		this.adImpExpFieldMap = adImpExpFieldMap;
	}

	public XADImpExpSub getAdImpExpSub() {
		return adImpExpSub;
	}

	public void setAdImpExpSub(XADImpExpSub adImpExpSub) {
		this.adImpExpSub = adImpExpSub;
	}
}
