/**
*
* 动态表导入导出查询.
*  
* <h2>消息名称: SYS.ADIMPEXP</h2> 
* 
* <h2>JSON格式：</h2>
* 
* <ul>
* <li><b>Request</b></li>
* </ul>
* <pre>
* {@code {
    "request": {
		"header": {
			"messageName": "SYS.ADIMPEXP",
			"transactionId": "fbfe967030224c9fa0641f3391cef954",
			"orgName": "",
			"userName": "admin",
			"orgRrn": 1,
			"language": "ZH"
		},
		"body": {
			"authorityName": "authorityName",
			"buttonName": "buttonName",
			"actionType": "QUERY"
		}
	}
  }
}
* </pre>
* 
* <ul>
* <li><b>Response</b></li>
* </ul>
* <pre>
* {@code {
      "response": {
		"header": {
			"transactionId": "fbfe967030224c9fa0641f3391cef954",
			"result": "SUCCESS"
		},
		"body": {
			"adImpExps": [{
				"objectRrn": "432229101533085728",
				"orgRrn": "0",
				"authorityName": "Web.PMS.DailyCheckSetup",
				"type": "ONE2MANY",
				"adTableName": "SPCJobUploadTST",
				"subs": null,
				"filedMaps": null,
				"adTable": {
					"objectRrn": "432229101533085696",
					"name": "SPCJobUploadTST",
					"description": "SPC任务",
					"style": 0,
					"tableName": "SPC_JOB",
					"isView": false,
					"modelName": "SpcJob",
					"modelClass": "com.glory.spc.model.SpcJob",
					"isVertical": false,
					"gridYBasic": "1",
					"gridYQuery": "3",
					"label": "SPC Job",
					"label_zh": "SPC任务管理",
					"isFilter": true
				}
			}]
		}
	}
  }
}
* </pre>
* 
* <h2>XML格式</h2>
* 
* <ul>
* 	<li><b>Request</b></li>
*</ul>
* <pre>
* {@code 
	<request>
    <header>
        <MESSAGENAME>SYS.ADIMPEXP</MESSAGENAME>
        <TRANSACTIONID>fbfe967030224c9fa0641f3391cef954</TRANSACTIONID>
        <ORGNAME>
        </ORGNAME>
        <USERNAME>admin</USERNAME>
        <ORGRRN>1</ORGRRN>
        <LANGUAGE>ZH</LANGUAGE>
    </header>
    <body>
        <AUTHORITYNAME>authorityName</AUTHORITYNAME>
        <BUTTONNAME>buttonName</BUTTONNAME>
        <ACTIONTYPE>QUERY</ACTIONTYPE>
    </body>
</request>
}
* </pre>
* 
*<ul>
*	<li><b>Response</b>
*</ul>
* <pre>
* {@code 
	<RESPONSE>
    <HEADER>
        <TRANSACTIONID>FBFE967030224C9FA0641F3391CEF954</TRANSACTIONID>
        <RESULT>SUCCESS</RESULT>
    </HEADER>
    <BODY>
        <ADIMPEXPS>
            <OBJECTRRN>432229101533085728</OBJECTRRN>
            <ORGRRN>0</ORGRRN>
            <AUTHORITYNAME>WEB.PMS.DAILYCHECKSETUP</AUTHORITYNAME>
            <TYPE>ONE2MANY</TYPE>
            <ADTABLENAME>SpcJobUploadtst</ADTABLENAME>
            <SUBS  />
            <FILEDMAPS  />
            <ADTABLE>
                <OBJECTRRN>432229101533085696</OBJECTRRN>
                <NAME>SpcJobUploadtst</NAME>
                <DESCRIPTION>SPC任务</DESCRIPTION>
                <STYLE>0</STYLE>
                <TABLENAME>SPC_JOB</TABLENAME>
                <ISVIEW>false</ISVIEW>
                <MODELNAME>SPCJOB</MODELNAME>
                <MODELCLASS>com.glory.spc.model.spcjob</MODELCLASS>
                <ISVERTICAL>false</ISVERTICAL>
                <GRIDYBASIC>1</GRIDYBASIC>
                <GRIDYQUERY>3</GRIDYQUERY>
                <LABEL>SPC JOB</LABEL>
                <LABEL_ZH>SPC任务管理</LABEL_ZH>
                <ISFILTER>TRUE</ISFILTER>
            </ADTABLE>
        </ADIMPEXPS>
    </BODY>
</RESPONSE>
}
* </pre>
* 动态表获取子字段.
*  
* <h2>消息名称: SYS.ADIMPEXP</h2> 
* 
* <h2>JSON格式：</h2>
* 
* <ul>
* <li><b>Request</b></li>
* </ul>
* <pre>
* {@code {
    "request": {
		"header": {
			"messageName": "SYS.ADIMPEXP",
			"transactionId": "f3dbb146e23147cca664939aba55b2b3",
			"orgName": "",
			"userName": "admin",
			"orgRrn": 1,
			"language": "ZH"
		},
		"body": {
			"adImpExp": {
				"objectRrn": "432229101533085728",
				"adTable": {
					"objectRrn": "432229101533085696",
					"name": "SPCJobUploadTST",
					"description": "SPC任务",
					"tableName": "SPC_JOB"
				},
				"modelName": "SpcJob",
				"modelClass": "com.glory.spc.model.SpcJob",
				"tableName": "SPC_JOB"
			},
			"actionType": "SELECTFIELD"
		}
	}
  }
}
* </pre>
* 
* <ul>
* <li><b>Response</b></li>
* </ul>
* <pre>
* {@code {
      "response": {
		"header": {
			"transactionId": "f3dbb146e23147cca664939aba55b2b3",
			"result": "SUCCESS"
		},
		"body": {
			"adImpExps": [{
				"objectRrn": "432229101533085728",
				"orgRrn": "0",
				"authorityName": "Web.PMS.DailyCheckSetup",
				"type": "ONE2MANY",
				"adTableName": "SPCJobUploadTST",
				"filedMaps": [{
					"objectRrn": "432229101537280000",
					"orgRrn": "0",
					"parentRrn": "432229101533085728",
					"subRrn": "432229162115612672",
					"fieldName": "jobFormulaVariables",
					"seqNo": "230",
					"isValidateRef": false,
					"field": {
						"objectRrn": "432229101533085720",
						"name": "jobFormulaVariables",
						"description": "FormulaVariables",
						"style": 0,
						"tableRrn": "432229101533085696",
						"seqNo": "230",
						"displayType": "tablelist",
						"reftableRrn": "312983869336264704",
						"label": "JobFormulaVariables",
						"label_zh": "JobFormulaVariables",
						"isQuery": false,
						"isQueryAdvance": false,
						"isQueryMandatory": false,
						"validateLevel": "3",
						"isTableShowRef": false,
						"isAutoLoad": false
					},
					"isKey": false
				}]
			}]
		}
	}
  }
}
* </pre>
* 
* <h2>XML格式</h2>
* 
* <ul>
* 	<li><b>Request</b></li>
*</ul>
* <pre>
* {@code 
	<request>
    <header>
        <messageName>SYS.ADIMPEXP</messageName>
        <transactionId>f3dbb146e23147cca664939aba55b2b3</transactionId>
        <orgName>
        </orgName>
        <userName>admin</userName>
        <orgRrn>1</orgRrn>
        <language>ZH</language>
    </header>
    <body>
        <adImpExp>
            <objectRrn>432229101533085728</objectRrn>
            <adTable>
                <objectRrn>432229101533085696</objectRrn>
                <name>SPCJobUploadTST</name>
                <description>SPC任务</description>
                <tableName>SPC_JOB</tableName>
            </adTable>
            <modelName>SpcJob</modelName>
            <modelClass>com.glory.spc.model.SpcJob</modelClass>
            <tableName>SPC_JOB</tableName>
        </adImpExp>
        <actionType>SELECTFIELD</actionType>
    </body>
</request>
}
* </pre>
* 
*<ul>
*	<li><b>Response</b>
*</ul>
* <pre>
* {@code 
	<response>
    <header>
        <transactionId>f3dbb146e23147cca664939aba55b2b3</transactionId>
        <result>SUCCESS</result>
    </header>
    <body>
        <adImpExps>
            <objectRrn>432229101533085728</objectRrn>
            <orgRrn>0</orgRrn>
            <authorityName>Web.PMS.DailyCheckSetup</authorityName>
            <type>ONE2MANY</type>
            <adTableName>SPCJobUploadTST</adTableName>
            <filedMaps>
                <objectRrn>432229101537280000</objectRrn>
                <orgRrn>0</orgRrn>
                <parentRrn>432229101533085728</parentRrn>
                <subRrn>432229162115612672</subRrn>
                <fieldName>jobFormulaVariables</fieldName>
                <seqNo>230</seqNo>
                <isValidateRef>false</isValidateRef>
                <field>
                    <objectRrn>432229101533085720</objectRrn>
                    <name>jobFormulaVariables</name>
                    <description>FormulaVariables</description>
                    <style>0</style>
                    <tableRrn>432229101533085696</tableRrn>
                    <seqNo>230</seqNo>
                    <displayType>tablelist</displayType>
                    <reftableRrn>312983869336264704</reftableRrn>
                    <label>JobFormulaVariables</label>
                    <label_zh>JobFormulaVariables</label_zh>
                    <isQuery>false</isQuery>
                    <isQueryAdvance>false</isQueryAdvance>
                    <isQueryMandatory>false</isQueryMandatory>
                    <validateLevel>3</validateLevel>
                    <isTableShowRef>false</isTableShowRef>
                    <isAutoLoad>false</isAutoLoad>
                </field>
                <isKey>false</isKey>
            </filedMaps>
        </adImpExps>
    </body>
</response>
}
* </pre>
* 
* * 动态表获取Sub子字段.
*  
* <h2>消息名称: SYS.ADIMPEXP</h2> 
* 
* <h2>JSON格式：</h2>
* 
* <ul>
* <li><b>Request</b></li>
* </ul>
* <pre>
* {@code {
    "request": {
		"header": {
			"messageName": "SYS.ADIMPEXP",
			"transactionId": "fbe19a0dee364f579201a93b2d51285f",
			"orgName": "",
			"userName": "admin",
			"orgRrn": 1,
			"language": "ZH"
		},
		"body": {
			"adImpExpFieldMap": {
				"objectRrn": "432229101537280000",
				"orgRrn": "0",
				"parentRrn": "432229101533085728",
				"subRrn": "432229162115612672",
				"fieldName": "jobFormulaVariables",
				"seqNo": "230",
				"isValidateRef": false,
				"isKey": false,
				"displayType": "tablelist",
				"reftableRrn": "312983869336264704"
			},
			"adImpExp": {
				"objectRrn": "432229101533085728",
				"orgRrn": "0",
				"authorityName": "Web.PMS.DailyCheckSetup",
				"type": "ONE2MANY",
				"adTableName": "SPCJobUploadTST",
				"subs": null,
				"filedMaps": null
			}
		},
		"actionType": "SELECTSUBFIELD"
	}
  }
}
* </pre>
* 
* <ul>
* <li><b>Response</b></li>
* </ul>
* <pre>
* {@code {
     "response": {
		"header": {
			"transactionId": "fbe19a0dee364f579201a93b2d51285f",
			"result": "SUCCESS"
		},
		"body": {
			"adImpExpFieldMaps": [{
				"objectRrn": "432229162115612673",
				"orgRrn": "0",
				"subRrn": "432229162115612672",
				"fieldName": "variableName",
				"seqNo": "5",
				"isValidateRef": false,
				"field": {
					"objectRrn": "432229162082058241",
					"name": "variableName",
					"columnName": "VARIABLENAME",
					"description": "参数名称",
					"tableRrn": "432229162082058240",
					"seqNo": "5",
					"isMandatory": false,
					"isUpper": false,
					"isAttribute": false,
					"isFromParent": false,
					"displayType": "text",
					"dataType": "string",
					"label": "Variable Name",
					"label_zh": "参数名称",
					"isQuery": false,
					"isQueryAdvance": false,
					"isQueryMandatory": false,
					"validateLevel": "3",
					"isTableShowRef": false,
					"isAutoLoad": false
				},
				"isKey": false
			}]
		}
	}
  }
}
* </pre>
* 
* <h2>XML格式</h2>
* 
* <ul>
* 	<li><b>Request</b></li>
*</ul>
* <pre>
* {@code 
	<request>
    <header>
        <messageName>SYS.ADIMPEXP</messageName>
        <transactionId>fbe19a0dee364f579201a93b2d51285f</transactionId>
        <orgName>
        </orgName>
        <userName>admin</userName>
        <orgRrn>1</orgRrn>
        <language>ZH</language>
    </header>
    <body>
        <adImpExpFieldMap>
            <objectRrn>432229101537280000</objectRrn>
            <orgRrn>0</orgRrn>
            <parentRrn>432229101533085728</parentRrn>
            <subRrn>432229162115612672</subRrn>
            <fieldName>jobFormulaVariables</fieldName>
            <seqNo>230</seqNo>
            <isValidateRef>false</isValidateRef>
            <isKey>false</isKey>
            <displayType>tablelist</displayType>
            <reftableRrn>312983869336264704</reftableRrn>
        </adImpExpFieldMap>
        <adImpExp>
            <objectRrn>432229101533085728</objectRrn>
            <orgRrn>0</orgRrn>
            <authorityName>Web.PMS.DailyCheckSetup</authorityName>
            <type>ONE2MANY</type>
            <adTableName>SPCJobUploadTST</adTableName>
            <subs  />
            <filedMaps  />
        </adImpExp>
    </body>
    <actionType>SELECTSUBFIELD</actionType>
</request>
}
* </pre>
* 
*<ul>
*	<li><b>Response</b>
*</ul>
* <pre>
* {@code 
	<response>
    <header>
        <transactionId>fbe19a0dee364f579201a93b2d51285f</transactionId>
        <result>SUCCESS</result>
    </header>
    <body>
        <adImpExpFieldMaps>
            <objectRrn>432229162115612673</objectRrn>
            <orgRrn>0</orgRrn>
            <subRrn>432229162115612672</subRrn>
            <fieldName>variableName</fieldName>
            <seqNo>5</seqNo>
            <isValidateRef>false</isValidateRef>
            <field>
                <objectRrn>432229162082058241</objectRrn>
                <name>variableName</name>
                <columnName>VARIABLENAME</columnName>
                <description>参数名称</description>
                <tableRrn>432229162082058240</tableRrn>
                <seqNo>5</seqNo>
                <isMandatory>false</isMandatory>
                <isUpper>false</isUpper>
                <isAttribute>false</isAttribute>
                <isFromParent>false</isFromParent>
                <displayType>text</displayType>
                <dataType>string</dataType>
                <label>Variable Name</label>
                <label_zh>参数名称</label_zh>
                <isQuery>false</isQuery>
                <isQueryAdvance>false</isQueryAdvance>
                <isQueryMandatory>false</isQueryMandatory>
                <validateLevel>3</validateLevel>
                <isTableShowRef>false</isTableShowRef>
                <isAutoLoad>false</isAutoLoad>
            </field>
            <isKey>false</isKey>
        </adImpExpFieldMaps>
    </body>
</response>
}

* 动态表导入信息adimpexp保存.
*  
* <h2>消息名称: SYS.ADIMPEXP</h2> 
* 
* <h2>JSON格式：</h2>
* 
* <ul>
* <li><b>Request</b></li>
* </ul>
* <pre>
* {@code {
   "request": {
		"header": {
			"messageName": "SYS.ADIMPEXP",
			"transactionId": "d25d62ce984949489e00cbd42a7c53ea",
			"orgName": "",
			"userName": "admin",
			"orgRrn": 1,
			"language": "ZH"
		},
		"body": {
			"adImpExp": {
				"authorityName": "Web.PMS.DailyCheckSetup",
				"type": "ONE2MANY",
				"adTableName": "SPCJobUpload",
				"isNewTable": true,
				"newTableName": "SPCJobUploadTST",
				"filedMaps": [{
					"fieldName": "jobFormulaVariables",
					"seqNo": "230",
					"isKey": false,
					"displayType": "tablelist",
					"reftableRrn": "312983869336264704",
					"isValidateRef": false
				}]
			},
			"actionType": "SAVE"
		}
	}
  }
}
* </pre>
* 
* <ul>
* <li><b>Response</b></li>
* </ul>
* <pre>
* {@code {
     "response": {
		"header": {
			"transactionId": "d25d62ce984949489e00cbd42a7c53ea",
			"result": "SUCCESS"
		},
		"body": {
			"adImpExps": [{
				"objectRrn": "432233932545015840",
				"orgRrn": "0",
				"authorityName": "Web.PMS.DailyCheckSetup",
				"type": "ONE2MANY",
				"adTableName": "SPCJobUploadTST"
			}]
		}
	}
  }
}
* </pre>
* 
* <h2>XML格式</h2>
* 
* <ul>
* 	<li><b>Request</b></li>
*</ul>
* <pre>
* {@code 
	<request>
    <header>
        <messageName>SYS.ADIMPEXP</messageName>
        <transactionId>d25d62ce984949489e00cbd42a7c53ea</transactionId>
        <orgName>
        </orgName>
        <userName>admin</userName>
        <orgRrn>1</orgRrn>
        <language>ZH</language>
    </header>
    <body>
        <adImpExp>
            <authorityName>Web.PMS.DailyCheckSetup</authorityName>
            <type>ONE2MANY</type>
            <adTableName>SPCJobUpload</adTableName>
            <isNewTable>true</isNewTable>
            <newTableName>SPCJobUploadTST</newTableName>
            <filedMaps>
                <fieldName>jobFormulaVariables</fieldName>
                <seqNo>230</seqNo>
                <isKey>false</isKey>
                <displayType>tablelist</displayType>
                <reftableRrn>312983869336264704</reftableRrn>
                <isValidateRef>false</isValidateRef>
            </filedMaps>
        </adImpExp>
        <actionType>SAVE</actionType>
    </body>
</request>
}
* </pre>
* 
*<ul>
*	<li><b>Response</b>
*</ul>
* <pre>
* {@code 
	<response>
    <header>
        <transactionId>d25d62ce984949489e00cbd42a7c53ea</transactionId>
        <result>SUCCESS</result>
    </header>
    <body>
        <adImpExps>
            <objectRrn>432233932545015840</objectRrn>
            <orgRrn>0</orgRrn>
            <authorityName>Web.PMS.DailyCheckSetup</authorityName>
            <type>ONE2MANY</type>
            <adTableName>SPCJobUploadTST</adTableName>
        </adImpExps>
    </body>
</response>
}


* 动态表导入信息adimpexpSub保存.
*  
* <h2>消息名称: SYS.ADIMPEXP</h2> 
* 
* <h2>JSON格式：</h2>
* 
* <ul>
* <li><b>Request</b></li>
* </ul>
* <pre>
* {@code {
   "request": {
		"header": {
			"messageName": "SYS.ADIMPEXP",
			"transactionId": "399ca410574247ffbdf3bc838961d1e3",
			"orgName": "",
			"userName": "admin",
			"orgRrn": 1,
			"language": "ZH"
		},
		"body": {
			"actionType": "SUBSAVE",
			"adImpExpFieldMap": {
				"objectRrn": "432233932545015841",
				"orgRrn": "0",
				"parentRrn": "432233932545015840",
				"fieldName": "jobFormulaVariables",
				"seqNo": "230",
				"isValidateRef": false,
				"isKey": false,
				"displayType": "tablelist",
				"reftableRrn": "312983869336264704"
			},
			"adImpExpSub": {
				"parentFieldName": "jobFormulaVariables",
				"filedMaps": [{
					"fieldName": "variableName",
					"seqNo": "5",
					"isKey": false
				}]
			},
			"adImpExp": {
				"objectRrn": "432233932545015840",
				"orgRrn": "0",
				"authorityName": "Web.PMS.DailyCheckSetup",
				"type": "ONE2MANY",
				"adTableName": "SPCJobUploadTST",
				"subs": null,
				"filedMaps": null,
				"modelName": "SpcJob",
				"modelClass": "com.glory.spc.model.SpcJob",
				"tableName": "SPC_JOB"
			}
		}
	}
  }
}
* </pre>
* 
* <ul>
* <li><b>Response</b></li>
* </ul>
* <pre>
* {@code {
     "response": {
		"header": {
			"transactionId": "399ca410574247ffbdf3bc838961d1e3",
			"result": "SUCCESS"
		},
		"body": {
			"adImpExps": [{
				"objectRrn": "432233932545015840",
				"orgRrn": "0",
				"authorityName": "Web.PMS.DailyCheckSetup",
				"type": "ONE2MANY",
				"adTableName": "SPCJobUploadTST"
			}],
			"adImpExpFieldMaps": []
		}
	}
  }
}
* </pre>
* 
* <h2>XML格式</h2>
* 
* <ul>
* 	<li><b>Request</b></li>
*</ul>
* <pre>
* {@code 
	<request>
    <header>
        <messageName>SYS.ADIMPEXP</messageName>
        <transactionId>399ca410574247ffbdf3bc838961d1e3</transactionId>
        <orgName>
        </orgName>
        <userName>admin</userName>
        <orgRrn>1</orgRrn>
        <language>ZH</language>
    </header>
    <body>
        <actionType>SUBSAVE</actionType>
        <adImpExpFieldMap>
            <objectRrn>432233932545015841</objectRrn>
            <orgRrn>0</orgRrn>
            <parentRrn>432233932545015840</parentRrn>
            <fieldName>jobFormulaVariables</fieldName>
            <seqNo>230</seqNo>
            <isValidateRef>false</isValidateRef>
            <isKey>false</isKey>
            <displayType>tablelist</displayType>
            <reftableRrn>312983869336264704</reftableRrn>
        </adImpExpFieldMap>
        <adImpExpSub>
            <parentFieldName>jobFormulaVariables</parentFieldName>
            <filedMaps>
                <fieldName>variableName</fieldName>
                <seqNo>5</seqNo>
                <isKey>false</isKey>
            </filedMaps>
        </adImpExpSub>
        <adImpExp>
            <objectRrn>432233932545015840</objectRrn>
            <orgRrn>0</orgRrn>
            <authorityName>Web.PMS.DailyCheckSetup</authorityName>
            <type>ONE2MANY</type>
            <adTableName>SPCJobUploadTST</adTableName>
            <subs  />
            <filedMaps  />
            <modelName>SpcJob</modelName>
            <modelClass>com.glory.spc.model.SpcJob</modelClass>
            <tableName>SPC_JOB</tableName>
        </adImpExp>
    </body>
</request>
}
* </pre>
* 
*<ul>
*	<li><b>Response</b>
*</ul>
* <pre>
* {@code 
	<response>
    <header>
        <transactionId>399ca410574247ffbdf3bc838961d1e3</transactionId>
        <result>SUCCESS</result>
    </header>
    <body>
        <adImpExps>
            <objectRrn>432233932545015840</objectRrn>
            <orgRrn>0</orgRrn>
            <authorityName>Web.PMS.DailyCheckSetup</authorityName>
            <type>ONE2MANY</type>
            <adTableName>SPCJobUploadTST</adTableName>
        </adImpExps>
    </body>
</response>
}
* </pre>
*/


package com.glory.msg.entitymanager.adimpexp;
