package com.glory.msg.entitymanager.adimpexp;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
public class ADImpExpResponse extends Response {
	
	private static final long serialVersionUID = -6433137287044107434L;

	@XmlElement(name="Body")
	private ADImpExpResponseBody body;

	private transient Class<?> xModelClass;

	public ADImpExpResponseBody getBody() {
		return body;
	}
	
	public void setBody(ADImpExpResponseBody body) {
		this.body = body;
	}

	public Class<?> getxModelClass() {
		return xModelClass;
	}

	public void setxModelClass(Class<?> xModelClass) {
		this.xModelClass = xModelClass;
	}
}
