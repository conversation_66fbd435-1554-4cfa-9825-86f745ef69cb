package com.glory.msg.entitymanager.adimpexp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADImpExp;
import com.glory.framework.activeentity.model.ADImpExpFieldMap;
import com.glory.framework.activeentity.model.ADImpExpSub;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.service.CoreMessage;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserModel;
import com.glory.msg.Request;
import com.glory.msg.Response;
import com.glory.msg.model.XADField;
import com.glory.msg.model.XADImpExp;
import com.glory.msg.model.XADImpExpFieldMap;
import com.glory.msg.model.XADImpExpSub;
import com.glory.msg.model.XADTable;
import com.glory.msg.trans.MsgTransHandler;
import com.glory.msg.trans.TransContext;

@CoreMessage(serviceGroup = "MESCore", serviceModule = "ACTIVEENTITY", messageName = ADImpExpRequest.MESSAGE_NAME)
public class ADImpExpHandler extends MsgTransHandler {

	public static final String TABLESELECT = "tableselect";
	public static final String TABLELIST = "tablelist";
	public static final String LISTTABLE = "listTable";
	public static final String TABLEEDITOR = "tableeditor";
	
	@Override
	protected TransContext internalExecute(TransContext context) throws Exception {
		//获得对应的消息解析器
		MessageParser parser = getMessageParser(ADImpExpRequest.MESSAGE_NAME);
		ADImpExpRequest request = (ADImpExpRequest)parser.readerRequest(context.getRequest(), context.isJson());
		ADImpExpResponse response = (ADImpExpResponse)executeRequest(request, context);
		
		context.setResponse(parser.writerResponse(response, context.isJson()));
		return context;
	}
	
	public Response executeRequest(Request request, TransContext context) throws Exception {
		context.setTransactionId(request.getHeader().getTransactionId());
		SessionContext sc = getSessionContext(request, context);

		ADImpExpRequestBody requestBody = (ADImpExpRequestBody)request.getBody();
		ADImpExpResponse response = new ADImpExpResponse();
		ADImpExpResponseBody body = new ADImpExpResponseBody();
		response.getHeader().setTransactionId(request.getHeader().getTransactionId());
		String actionType = requestBody.getActionType();
		XADImpExp xAdImpExp = requestBody.getAdImpExp();
		XADImpExpSub xAdImpExpSub = requestBody.getAdImpExpSub();
		XADImpExpFieldMap xAdImpExpFieldMap = requestBody.getAdImpExpFieldMap();
		List<XADImpExp> responseADImpExps = new ArrayList<XADImpExp>();
		List<XADImpExpFieldMap> responseADImpExpFieldMaps = new ArrayList<XADImpExpFieldMap>();
		
		if(actionType.equals(ADImpExpRequest.ACTION_QUERY)) {
			List<ADImpExp> adImpExps = context.getAdManager().getADImpExpList(
					sc.getOrgRrn(), xAdImpExp.getAuthorityName(), xAdImpExp.getButtonName(), xAdImpExp.getAdTableName());
			if(CollectionUtils.isNotEmpty(adImpExps)) {
				for (ADImpExp adImpExp : adImpExps) {
					XADImpExp copyImpExp = new XADImpExp();
					XADTable xAdTable = new XADTable();
					PropertyUtil.copyProperties(copyImpExp, adImpExp);
					ADTable adTable = adImpExp.getAdTable();
					PropertyUtil.copyProperties(xAdTable, adTable);
					copyImpExp.setAdTable(xAdTable);
					responseADImpExps.add(copyImpExp);
				}
			}
		}
		if (actionType.equals(ADImpExpRequest.ACTION_SELECT_FIELD)) {
			List<ADImpExpFieldMap> impExpFieldMaps = new ArrayList<ADImpExpFieldMap>();
			if (!StringUtil.isEmpty(xAdImpExp.getAdTableName())) {
				if (xAdImpExp.getObjectRrn() != null) {
					impExpFieldMaps = context.getAdManager().getEntityList(sc.getOrgRrn(), ADImpExpFieldMap.class,
							Integer.MAX_VALUE, "parentRrn = '" + xAdImpExp.getObjectRrn() + "'", "seqNo Asc");
				}
				ADTable adTable = context.getAdManager().getADTableDeep(sc.getOrgRrn(), xAdImpExp.getAdTableName());
				
				Map<String, ADImpExpFieldMap> currentMap = new HashMap<String, ADImpExpFieldMap>();
				//转map方便判断
				if(CollectionUtils.isNotEmpty(impExpFieldMaps)) {
					currentMap = impExpFieldMaps.stream().collect(Collectors.toMap(ADImpExpFieldMap::getFieldName, l -> l));
				}
				
				//将field写入
				if(CollectionUtils.isNotEmpty(adTable.getFields())) {
					// 将所有的字段信息返回
					for (ADField adField : adTable.getFields()) {
						XADImpExpFieldMap xADImpExpFieldMap = new XADImpExpFieldMap();
						XADField xADField = new XADField();
						PropertyUtil.copyProperties(xADField, adField);
						//如果包含已存在字段则获取可编辑栏位信息
						if(currentMap.containsKey(adField.getName())) {
							PropertyUtil.copyProperties(xADImpExpFieldMap, currentMap.get(adField.getName()));
						}else {
							xADImpExpFieldMap.setFieldName(adField.getName());
							xADImpExpFieldMap.setSeqNo(adField.getSeqNo());
						}
						xADImpExpFieldMap.setField(xADField);
						responseADImpExpFieldMaps.add(xADImpExpFieldMap);
					}
					if(CollectionUtils.isNotEmpty(impExpFieldMaps)) {//将已添加字段放入AdImpExp返回
						List<XADImpExpFieldMap> xADImpExpFieldMaps = new ArrayList<XADImpExpFieldMap>();
						for (ADField adField : adTable.getFields()) {
							for (ADImpExpFieldMap adImpExpFieldMap : impExpFieldMaps) {
								if (adField.getName().equals(adImpExpFieldMap.getFieldName())) {
									XADImpExpFieldMap xADImpExpFieldMap = new XADImpExpFieldMap();
									XADField xADField = new XADField();
									PropertyUtil.copyProperties(xADImpExpFieldMap, adImpExpFieldMap);
									PropertyUtil.copyProperties(xADField, adField);
									xADImpExpFieldMap.setField(xADField);
									xADImpExpFieldMaps.add(xADImpExpFieldMap);
								}
							}
						}
						xAdImpExp.setFiledMaps(xADImpExpFieldMaps);
					}
				}
				responseADImpExps.add(xAdImpExp);
			}
		}else if (actionType.equals(ADImpExpRequest.ACTION_SELECT_SUB_FIELD)) {
			if(xAdImpExpFieldMap !=null && xAdImpExpFieldMap.getObjectRrn() != null) {
				if (TABLESELECT.equals(xAdImpExpFieldMap.getField().getDisplayType()) ||
						TABLELIST.equals(xAdImpExpFieldMap.getField().getDisplayType()) ||
						LISTTABLE.equals(xAdImpExpFieldMap.getField().getDisplayType()) ||
						TABLEEDITOR.equals(xAdImpExpFieldMap.getField().getDisplayType())) {
					if (xAdImpExpFieldMap.getSubRrn() != null) {//subRrn不为空则为查询子表信息
						ADImpExpSub adImpExpSub = new ADImpExpSub();
						adImpExpSub.setObjectRrn(xAdImpExpFieldMap.getSubRrn());
						adImpExpSub = (ADImpExpSub) context.getAdManager().getEntity(adImpExpSub);
						List<ADImpExpFieldMap> impExpFieldMaps = context.getAdManager().getEntityList(sc.getOrgRrn(), ADImpExpFieldMap.class, Integer.MAX_VALUE, 
								"subRrn = '" + adImpExpSub.getObjectRrn()+"' and parentRrn IS NULL ", "seqNo Asc");
						ADTable adTable = context.getAdManager().getADTableDeep(sc.getOrgRrn(), adImpExpSub.getAdTableName());
						for (ADField adField : adTable.getFields()) {
							for (ADImpExpFieldMap impExpFieldMap : impExpFieldMaps) {
								if (adField.getName().equals(impExpFieldMap.getFieldName())) {
									XADImpExpFieldMap xADImpExpFieldMap = new XADImpExpFieldMap();
									XADField xADField = new XADField();
									PropertyUtil.copyProperties(xADImpExpFieldMap, impExpFieldMap);
									PropertyUtil.copyProperties(xADField, adField);
									xADImpExpFieldMap.setField(xADField);
									responseADImpExpFieldMaps.add(xADImpExpFieldMap);
								}
							}
						}
					} 
				}
			}
		}else if (actionType.equals(ADImpExpRequest.ACTION_SAVE)) {
			ADImpExp saveADImpExp = new ADImpExp();
			List<ADImpExpFieldMap> savefiledMaps = new ArrayList<ADImpExpFieldMap>();
			//用于修改已存在字段
			Map<String, ADImpExpFieldMap> currentMap = new HashMap<String, ADImpExpFieldMap>();
			PropertyUtil.copyProperties(saveADImpExp, xAdImpExp);
			//是否克隆动态表
			boolean checked = DBUtil.toBoolean(xAdImpExp.getIsNewTable());
			
			if(xAdImpExp.getObjectRrn() == null) {
				//校验是否已被使用
				String whereClause = "authorityName = '" + xAdImpExp.getAuthorityName() + "'";
				if (StringUtil.isEmpty(xAdImpExp.getButtonName())) {
					whereClause = whereClause + " And ( buttonName IS NULL or buttonName = '' )";
				} else {
					whereClause = whereClause + " And buttonName = '" + xAdImpExp.getButtonName() + "'" ;
				}
				List<ADImpExp> existADImpExps = context.getAdManager().getEntityList(sc.getOrgRrn(), ADImpExp.class, Integer.MAX_VALUE, whereClause, null);
				if(CollectionUtils.isNotEmpty(existADImpExps)) {
					throw ExceptionBundle.bundle.ObjectIsHave();
				}
			}else {
				//查询已存在字段
				List<ADImpExpFieldMap> impExpFieldMaps = context.getAdManager().getEntityList(sc.getOrgRrn(), ADImpExpFieldMap.class,
						Integer.MAX_VALUE, "parentRrn = '" + xAdImpExp.getObjectRrn() + "'", "seqNo Asc");
				//转map方便判断
				if(CollectionUtils.isNotEmpty(impExpFieldMaps)) {
					currentMap = impExpFieldMaps.stream().collect(Collectors.toMap(ADImpExpFieldMap::getFieldName, l -> l));
				}
				saveADImpExp = new ADImpExp();
				saveADImpExp = context.getAdManager().getEntityList(sc.getOrgRrn(), ADImpExp.class, Integer.MAX_VALUE, "objectRrn = " + xAdImpExp.getObjectRrn(), null).get(0);
			}
			
			//写入新字段或创建新field
			if(CollectionUtils.isNotEmpty(xAdImpExp.getFiledMaps())) {
				for(XADImpExpFieldMap xADImpExpFieldMap : xAdImpExp.getFiledMaps()) {
					ADImpExpFieldMap adImpExpFieldMap;
					if(currentMap.containsKey(xADImpExpFieldMap.getFieldName())) {
						adImpExpFieldMap = currentMap.get(xADImpExpFieldMap.getFieldName());
					}else {
						adImpExpFieldMap = new ADImpExpFieldMap();
					}
					PropertyUtil.copyProperties(adImpExpFieldMap, xADImpExpFieldMap);
					savefiledMaps.add(adImpExpFieldMap);
				}
			}
			
			List<String> mapNames = new ArrayList<String>();
			for (ADImpExpFieldMap adImpExpFieldMap : savefiledMaps) {
				if (StringUtil.isEmpty(adImpExpFieldMap.getMapName())) {
					mapNames.add(adImpExpFieldMap.getFieldName().toUpperCase());
				} else {
					mapNames.add(adImpExpFieldMap.getMapName().toUpperCase());
				}
			}
			
			List<String> distinctMapNames = mapNames.stream().distinct().collect(Collectors.toList());
			if (mapNames.size() != distinctMapNames.size()) {
				 for (Iterator<String> itA = mapNames.iterator(); itA.hasNext();) {
			            String temp = itA.next();
			            for (int i = 0; i < distinctMapNames.size(); i++) {
			                if (temp.equals(distinctMapNames.get(i))) {
			                    itA.remove();
			                    distinctMapNames.remove(temp);
			                }
			            }
			        }
				 String mapName = String.join(",", mapNames); 
				 throw ExceptionBundle.bundle.FieldNameOrMapNameRepeat(mapName);
			}
			
			saveADImpExp = context.getAdManager().saveAdImpExp(saveADImpExp, checked, xAdImpExp.getNewTableName(), savefiledMaps, sc);
			
			xAdImpExp = new XADImpExp();
			saveADImpExp.setFiledMaps(null);
			PropertyUtil.copyProperties(xAdImpExp, saveADImpExp);
			responseADImpExps.add(xAdImpExp);
		}else if (actionType.equals(ADImpExpRequest.ACTION_EDIT_SUB_FIELD)) {
			if (xAdImpExpFieldMap.getField().getReftableRrn() != null) {//subRrn为空则为创建子表信息，带出动态表字段信息
				XADImpExpSub xadImpExpSub = new XADImpExpSub();
				
				ADRefTable adRefTable = context.getAdManager().getEntityList(sc.getOrgRrn(), ADRefTable.class, Integer.MAX_VALUE, 
						"objectRrn = '"+ xAdImpExpFieldMap.getField().getReftableRrn()+"'", "").get(0);
				ADTable adTable = context.getAdManager().getADTableDeep(adRefTable.getTableRrn());
				Map<String, ADImpExpFieldMap> currentMap = new HashMap<String, ADImpExpFieldMap>();
				if(xAdImpExpFieldMap.getSubRrn() != null) {
					ADImpExpSub adImpExpSub = new ADImpExpSub();
					adImpExpSub.setObjectRrn(xAdImpExpFieldMap.getSubRrn());
					adImpExpSub = (ADImpExpSub) context.getAdManager().getEntity(adImpExpSub);
					PropertyUtil.copyProperties(xadImpExpSub, adImpExpSub);
					//如果subRrn不为空则使用sub的动态表
					adTable = context.getAdManager().getADTableDeep(sc.getOrgRrn(), adImpExpSub.getAdTableName());
					
					//查询已存在字段
					List<ADImpExpFieldMap> impExpFieldMaps = context.getAdManager().getEntityList(sc.getOrgRrn(), ADImpExpFieldMap.class, Integer.MAX_VALUE, 
							"subRrn = '" + adImpExpSub.getObjectRrn()+"' and parentRrn IS NULL ", "seqNo Asc");
					//转map方便判断
					if(CollectionUtils.isNotEmpty(impExpFieldMaps)) {
						currentMap = impExpFieldMaps.stream().collect(Collectors.toMap(ADImpExpFieldMap::getFieldName, l -> l));
					}
				}else {
					xadImpExpSub.setParentFieldName(xAdImpExpFieldMap.getFieldName());
				}
				
				XADTable xAdTable = new XADTable();
				PropertyUtil.copyProperties(xAdTable, adTable);
				xadImpExpSub.setAdTable(xAdTable);
				
				if(CollectionUtils.isNotEmpty(adTable.getFields())) {
					for (ADField adField : adTable.getFields()) {
						XADImpExpFieldMap xADImpExpFieldMap = new XADImpExpFieldMap();
						XADField xADField = new XADField();
						PropertyUtil.copyProperties(xADField, adField);
						//已存在则使用已存在字段可编辑栏位信息
						if(currentMap != null && currentMap.size() > 0 && currentMap.containsKey(adField.getName())) {
							PropertyUtil.copyProperties(xADImpExpFieldMap, currentMap.get(adField.getName()));
						}else {
							xADImpExpFieldMap.setFieldName(adField.getName());
							xADImpExpFieldMap.setSeqNo(adField.getSeqNo());
						}
						xADImpExpFieldMap.setField(xADField);
						responseADImpExpFieldMaps.add(xADImpExpFieldMap);
					}
				}
				xadImpExpSub.setFiledMaps(responseADImpExpFieldMaps);
				body.setAdImpExpSub(xadImpExpSub);
			}
		} else if (actionType.equals(ADImpExpRequest.ACTION_SUB_SAVE)) {
			ADImpExp saveADImpExp = new ADImpExp();
			saveADImpExp = context.getAdManager().getEntityList(sc.getOrgRrn(), ADImpExp.class, Integer.MAX_VALUE, "objectRrn = " + xAdImpExp.getObjectRrn(), null).get(0);
			
			ADImpExpFieldMap saveADImpExpFieldMap = new ADImpExpFieldMap();
			saveADImpExpFieldMap.setObjectRrn(xAdImpExpFieldMap.getObjectRrn());
			saveADImpExpFieldMap = (ADImpExpFieldMap) context.getAdManager().getEntity(saveADImpExpFieldMap);
			
			ADImpExpSub saveADImpExpSub = new ADImpExpSub();
			if (xAdImpExpSub.getObjectRrn() != null) {
				saveADImpExpSub.setObjectRrn(xAdImpExpSub.getObjectRrn());
				saveADImpExpSub = (ADImpExpSub) context.getAdManager().getEntity(saveADImpExpSub);
			}
			PropertyUtil.copyProperties(saveADImpExpSub, xAdImpExpSub);

			List<ADImpExpFieldMap> savefiledMaps = new ArrayList<ADImpExpFieldMap>();
			if (CollectionUtils.isNotEmpty(xAdImpExpSub.getFiledMaps())) {
				for (XADImpExpFieldMap xADImpExpFieldMap : xAdImpExpSub.getFiledMaps()) {
					ADImpExpFieldMap adImpExpFieldMap = new ADImpExpFieldMap();
					PropertyUtil.copyProperties(adImpExpFieldMap, xADImpExpFieldMap);
					savefiledMaps.add(adImpExpFieldMap);
				}
			}
			// 是否克隆动态表
			boolean checked = DBUtil.toBoolean(xAdImpExpSub.getIsNewTable());

			// 校验导入导出栏位和导入导出子栏位的名称或者映射名称不能一致
			List<ADImpExpFieldMap> impExpFieldMaps = context.getAdManager().getEntityList(sc.getOrgRrn(),
					ADImpExpFieldMap.class, Integer.MAX_VALUE, "parentRrn = '" + xAdImpExp.getObjectRrn() + "'",
					"seqNo Asc");
			List<String> mapNames = new ArrayList<String>();
			List<ADImpExpFieldMap> validateImpExpFields = new ArrayList<ADImpExpFieldMap>();
			validateImpExpFields.addAll(savefiledMaps);
			validateImpExpFields.addAll(impExpFieldMaps);
			for (ADImpExpFieldMap adImpExpFieldMap : validateImpExpFields) {
				if (StringUtil.isEmpty(adImpExpFieldMap.getMapName())) {
					mapNames.add(adImpExpFieldMap.getFieldName().toUpperCase());
				} else {
					mapNames.add(adImpExpFieldMap.getMapName().toUpperCase());
				}
			}
			List<String> distinctMapNames = mapNames.stream().distinct().collect(Collectors.toList());
			if (mapNames.size() != distinctMapNames.size()) {
				for (Iterator<String> mapNamesIterator = mapNames.iterator(); mapNamesIterator.hasNext();) {
					String mapName = mapNamesIterator.next();
					for (int i = 0; i < distinctMapNames.size(); i++) {
						if (mapName.equals(distinctMapNames.get(i))) {
							mapNamesIterator.remove();
							distinctMapNames.remove(mapName);
						}
					}
				}
				String mapName = String.join(",", mapNames); 
				throw ExceptionBundle.bundle.FieldNameOrMapNameRepeat(mapName);
			}

			saveADImpExp = context.getAdManager().saveSubAdImpExp(saveADImpExp, saveADImpExpFieldMap, saveADImpExpSub, checked, xAdImpExpSub.getNewTableName(), savefiledMaps, sc);
			xAdImpExp = new XADImpExp();
			saveADImpExp.setFiledMaps(null);
			saveADImpExp.setSubs(null);
			PropertyUtil.copyProperties(xAdImpExp, saveADImpExp);
			responseADImpExps.add(xAdImpExp);
		}else if (actionType.equals(ADImpExpRequest.ACTION_DELETE)) {
			if(xAdImpExp != null && xAdImpExp.getObjectRrn() != null) {
				ADImpExp deleteADImpExp = new ADImpExp();
				PropertyUtil.copyProperties(deleteADImpExp, xAdImpExp);
				deleteADImpExp = context.getAdManager().getADImpExpByAuthorityName(sc.getOrgRrn(), deleteADImpExp.getAuthorityName(), deleteADImpExp.getButtonName(), true);
				context.getAdManager().deleteEntity(deleteADImpExp, sc);
			}
		}
		
		body.setAdImpExps(responseADImpExps);
		body.setAdImpExpFieldMaps(responseADImpExpFieldMaps);
		response.setBody(body); 
		return response;
	}
	
	public void initMessageParser() {
		//注册LOTINFO方法
		MessageParserModel model = new MessageParserModel();
		model.setMessageName(ADImpExpRequest.MESSAGE_NAME);
		model.setRequestClass(ADImpExpRequest.class);
		registerMessageParser(ADImpExpRequest.MESSAGE_NAME, model);
	}
	
	public MessageParser getMessageParser() {
		return getMessageParser(ADImpExpRequest.MESSAGE_NAME);
	}
	
}
