package com.glory.msg.entitymanager.adimpexp;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;

import com.glory.msg.RequestBody;
import com.glory.msg.model.XADImpExp;
import com.glory.msg.model.XADImpExpFieldMap;
import com.glory.msg.model.XADImpExpSub;

public class ADImpExpResponseBody extends RequestBody {

	private static final long serialVersionUID = 7753904906114952986L;

	//导入表信息
	@XmlElement(name="ADIMPEXPS")
	private List<XADImpExp> adImpExps;
	
	@XmlElement(name="ADIMPEXPS")
	private List<XADImpExpFieldMap> adImpExpFieldMaps;
	
	//导入表信息
	@XmlElement(name="ADIMPEXPS")
	private XADImpExpSub adImpExpSub;

	public List<XADImpExp> getAdImpExps() {
		return adImpExps;
	}

	public void setAdImpExps(List<XADImpExp> adImpExps) {
		this.adImpExps = adImpExps;
	}

	public List<XADImpExpFieldMap> getAdImpExpFieldMaps() {
		return adImpExpFieldMaps;
	}

	public void setAdImpExpFieldMaps(List<XADImpExpFieldMap> adImpExpFieldMaps) {
		this.adImpExpFieldMaps = adImpExpFieldMaps;
	}

	public void setAdImpExpSub(XADImpExpSub adImpExpSub) {
		this.adImpExpSub = adImpExpSub;
	}

	public XADImpExpSub getAdImpExpSub() {
		return adImpExpSub;
	}
}
