package com.glory.msg.entitymanager.adimpexp;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
public class ADImpExpRequest extends Request {

	private static final long serialVersionUID = 1L;
	public static final String MESSAGE_NAME = "ADIMPEXP";
	
	public static final String ACTION_QUERY = "QUERY";
	public static final String ACTION_SELECT_FIELD = "SELECTFIELD";
	public static final String ACTION_SELECT_SUB_FIELD = "SELECTSUBFIELD";
	public static final String ACTION_SAVE = "SAVE";
	public static final String ACTION_EDIT_SUB_FIELD = "EDITSUBFIELD";
	public static final String ACTION_SUB_SAVE = "SUBSAVE";
	public static final String ACTION_DELETE = "DELETE";
	
	@XmlElement(name="Body")
	private ADImpExpRequestBody body;

	public ADImpExpRequestBody getBody() {
		return body;
	}
	
	public void setBody(ADImpExpRequestBody body) {
		this.body = body;
	}
}
