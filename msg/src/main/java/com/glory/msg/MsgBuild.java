package com.glory.msg;

import com.glory.msg.activeentity.entity.EntityRequest;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.util.Collections;

public class MsgBuild {

    protected static JAXBContext context;
    public static void main(String[] args) throws Exception {
        buildXml();
    }


    public static void buildJson(){

    }

    public static void buildXml() throws Exception {
        String request = writerRequest(new EntityRequest());
        System.out.println(request);
    }

    private static String writerRequest(Request request) throws Exception {
        try {
            if (context == null) {
                Class[] classes = new Class[] { request.getClass() };
                context = JAXBContext.newInstance(classes, Collections.<String, Object> emptyMap());
            }
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            marshaller.setListener(marListener);
            StringWriter writer = new StringWriter();
            marshaller.marshal(request, writer);
            String transString = writer.toString();
            return transString;
        } catch (JAXBException e) {
            throw e;
        }
    }


    private static final Marshaller.Listener marListener = new Marshaller.Listener() {
        @Override
        public void beforeMarshal(Object source) {
            Field[] fields = source.getClass().getDeclaredFields();
            for (Field f : fields) {
                f.setAccessible(true);
                try {
                    //对象为空且类型为String时候设置空值
                    if (f.getType() == String.class && !((f.getModifiers() & java.lang.reflect.Modifier.STATIC) != 0)) {
                        f.set(source, "1");
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    };

}
