/**
*
* 提供管理下拉列表信息.
*  
* <h2>消息名称: SYS.ADREFLISTHANDLER</h2> 
* 
* <h2>JSON格式：</h2>
* 
* <ul>
* <li><b>Request</b></li>
* </ul>
* <pre>
* {@code {
    "request": {
      "header": {
        "language": "en",
        "messageName": "SYS.GETADOWNERREFLIST",
        "transactionId": "20200213171301",
        "orgRrn": 378341,
        "orgName": "fab1",
        "userName": "admin"
      },
      "body": {
        "level": 1,
        "referenceName": "DefectCode",
        "department": "50100;50300"
      }
    }
  }
}
* </pre>
* 
* <ul>
* <li><b>Response</b></li>
* </ul>
* <pre>
* {@code {
      "response": {
          "header": {
              "transactionId": "20200213171301",
              "result": "SUCCESS"
          },
          "body": {
              "lists": [
                  {
                      "objectRrn": "145317",
                      "referenceName": "DefectCode",
                      "key": "Apperance",
                      "text": "Apperance",
                      "seqNo": "10",
                      "description": "Apperance",
                      "isAvailable": true
                  },
                  {
                      "objectRrn": "145318",
                      "referenceName": "DefectCode",
                      "key": "Spot",
                      "text": "Spot",
                      "seqNo": "20",
                      "description": "Spot",
                      "isAvailable": true
                  },
                  {
                      "objectRrn": "145319",
                      "referenceName": "DefectCode",
                      "key": "Mura",
                      "text": "Mura",
                      "seqNo": "30",
                      "description": "Mura",
                      "isAvailable": true
                  },
                  {
                      "objectRrn": "17982700",
                      "referenceName": "DefectCode",
                      "key": "MESDEF",
                      "text": "MESDEF",
                      "seqNo": "30",
                      "description": "MESDEF",
                      "isAvailable": true
                  }
              ]
          }
      }
  }
}
* </pre>
* 
* <h2>XML格式</h2>
* 
* <ul>
* 	<li><b>Request</b></li>
*</ul>
* <pre>
* {@code 
	<Request>
		<Header>
			<LANGUAGE>EN</LANGUAGE>
			<MESSAGENAME>SYS.ADREFLISTHANDLER</MESSAGENAME>
			<TRANSACTIONID>20200213171301</TRANSACTIONID>
			<ORGRRN>378341</ORGRRN>
			<ORGNAME>FAB1</ORGNAME>
			<USERNAME>admin</USERNAME>
		</Header>
		<Body>
			<LEVEL>1</LEVEL>
			<REFERENCENAME>DefectCode</REFERENCENAME>
			<DEPARTMENT>50100;50300</DEPARTMENT>
		</Body>
	</Request>
}
* </pre>
* 
*<ul>
*	<li><b>Response</b>
*</ul>
* <ul>
* <li><b>Response</b></li>
* </ul>
* <pre>
* {@code 
	<Response>
		<Header>
			<TRANSACTIONID>20200213171301</TRANSACTIONID>
			<RESULT>SUCCESS</RESULT>
		</Header>
		<Body>
			<ADOWNERREFLISTLIST>
				<OBJECTRRN>145317</OBJECTRRN>
				<REFERENCENAME>DefectCode</REFERENCENAME>
				<KEYID>Apperance</KEYID>
				<TEXT>Apperance</TEXT>
				<SEQNO>10</SEQNO>
				<DESCRIPTION>Apperance</DESCRIPTION>
				<ISAVAILABLE>Y</ISAVAILABLE>
			</ADOWNERREFLISTLIST>
		</Body>
	</Response>
}
* </pre>
*/
package com.glory.msg.getadowenrreflist;
