package com.glory.msg.getadowenrreflist;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
public class GetADOwnerRefListResponse extends Response {
	
	@XmlElement(name="Body")
	private GetADOwnerRefListResponseBody body;

	public GetADOwnerRefListResponseBody getBody() {
		return body;
	}
	
	public void setBody(GetADOwnerRefListResponseBody body) {
		this.body = body;
	}
}
