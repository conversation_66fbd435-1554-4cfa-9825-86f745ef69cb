package com.glory.msg.getadowenrreflist;

import com.glory.framework.activeentity.model.ADOwnerRefList;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreMessage;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserModel;
import com.glory.msg.Request;
import com.glory.msg.Response;
import com.glory.msg.model.XADOwnerRefList;
import com.glory.msg.trans.MsgTransHandler;
import com.glory.msg.trans.TransContext;

import java.util.ArrayList;
import java.util.List;

@CoreMessage(serviceGroup = "MESCore", serviceModule = "ACTIVEENTITY", messageName = GetADOwnerRefListRequest.MESSAGE_NAME)
public class GetADOwnerRefList<PERSON>andler extends MsgTransHandler {

	@Override
	protected TransContext internalExecute(TransContext context) throws Exception {
		//获得对应的消息解析器
		MessageParser parser = getMessageParser(GetADOwnerRefListRequest.MESSAGE_NAME);
		GetADOwnerRefListRequest request = (GetADOwnerRefListRequest)parser.readerRequest(context.getRequest(), context.isJson());
		
		GetADOwnerRefListResponse response = (GetADOwnerRefListResponse)executeRequest(request, context);
		
		context.setResponse(parser.writerResponse(response, context.isJson()));
		return context;	
	}
	
	public Response executeRequest(Request request, TransContext context) throws ClientException {
		SessionContext sc = getSessionContext(request, context);
		context.setTransactionId(request.getHeader().getTransactionId());

		GetADOwnerRefListResponse response = new GetADOwnerRefListResponse();
		response.getHeader().setTransactionId(request.getHeader().getTransactionId());
		GetADOwnerRefListResponseBody body = new GetADOwnerRefListResponseBody();
		
		GetADOwnerRefListRequestBody requestBody = (GetADOwnerRefListRequestBody)request.getBody();
		List<ADOwnerRefList> refLists = context.getAdManager().getADOwnerRefListDetail(sc.getOrgRrn(), 
				requestBody.getReferenceName(), requestBody.getLevel(), requestBody.getDepartment());
		
		List<XADOwnerRefList> xrefLists = new ArrayList<>();
		for (ADOwnerRefList list : refLists) {
			XADOwnerRefList xlist = new XADOwnerRefList();
			PropertyUtil.copyProperties(xlist, list);
			xrefLists.add(xlist);
		}
		
		body.setLists(xrefLists);
		response.setBody(body);
		
		return response;
	}
	
	public void initMessageParser() {
		//注册LOTINFO方法
		MessageParserModel model = new MessageParserModel();
		model.setMessageName(GetADOwnerRefListRequest.MESSAGE_NAME);
		model.setRequestClass(GetADOwnerRefListRequest.class);
		model.setResponseClass(GetADOwnerRefListResponse.class);
		registerMessageParser(GetADOwnerRefListRequest.MESSAGE_NAME, model);
	}
	
	public MessageParser getMessageParser() {
		return getMessageParser(GetADOwnerRefListRequest.MESSAGE_NAME);
	}
	
}
