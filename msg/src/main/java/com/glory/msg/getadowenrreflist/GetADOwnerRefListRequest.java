package com.glory.msg.getadowenrreflist;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
public class GetADOwnerRefListRequest extends Request {

	public static final String MESSAGE_NAME = "GETADOWNERREFLIST";
	
	@XmlElement(name="Body")
	private GetADOwnerRefListRequestBody body;

	public GetADOwnerRefListRequestBody getBody() {
		return body;
	}
	
	public void setBody(GetADOwnerRefListRequestBody body) {
		this.body = body;
	}
}
