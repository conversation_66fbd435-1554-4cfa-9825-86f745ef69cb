package com.glory.msg.getadowenrreflist;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import com.glory.msg.RequestBody;

@XmlAccessorType(XmlAccessType.NONE)
public class GetADOwnerRefListRequestBody extends RequestBody {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@XmlElement(name="REFERENCENAME")
	private String referenceName;
	
	@XmlElement(name="LEVEL")
	private int level;
	
	@XmlElement(name="DEPARTMENT")
	private String department;

	/**
	 * 描述：参考值名，映射为：REFERENCENAME；
	 * @return referenceName
	 */
	public String getReferenceName() {
		return referenceName;
	}

	public void setReferenceName(String referenceName) {
		this.referenceName = referenceName;
	}

	/**
	 * 描述：级别，映射为：LEVEL；
	 * @return level
	 */
	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	/**
	 * 描述：部门，映射为：DEPARTMENT；
	 * @return department
	 */
	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}
	
}
