package com.glory.msg.getadowenrreflist;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import com.glory.msg.RequestBody;
import com.glory.msg.model.XADOwnerRefList;

public class GetADOwnerRefListResponseBody extends RequestBody {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@XmlElementWrapper(name="ADOWNERREFLISTLIST")
	@XmlElementRef
	private List<XADOwnerRefList> lists;

	/**
	 * 描述：用户栏位列表，映射为：ADOWNERREFLISTLIST；
	 * @return lists
	 */
	public List<XADOwnerRefList> getLists() {
		return lists;
	}

	public void setLists(List<XADOwnerRefList> lists) {
		this.lists = lists;
	}
	
	

}
