/**
 *
 * 保存或者删除  ADOWNERREFNAME
 * 
 * <h2>消息名称: SYS.ADOWNERREFNAME</h2>
 * 
 * <h2>JSON格式： 保存 AdOwnerRefName</h2>
 * 
 * <ul>
 * <li><b>Request</b></li>
 * </ul>
 * 
 * <pre>
* {@code {
        "request": {
            "header": {
                "messageName": "SYS.ADOWNERREFNAME",
                "transactionId": "8c512aa1-dbeb-43fc-aa08-03fd988c6fcd",
                "orgName": "",
                "username": "",
                "token": "",
                "language": "",
                "orgRrn": 1
            },
            "body": {
                "actionType": "SAVE",
                "xadBaseRefName":
                    {
                        "name": "Test005",
                        "description": "Test",
                        "referenceType": "Value1"
                    }
            }
        }
    }
}
* </pre>
 * 
 * <ul>
 * <li><b>Response</b></li>
 * </ul>
 * 
 * <pre>
* {@code {
      "response": {
          "header": {
              "transactionId": "8c512aa1-dbeb-43fc-aa08-03fd988c6fcd",
              "result": "SUCCESS"
          }
      }
  }
}
* </pre>
 * <h2>JSON格式： 删除 AdOwnerRefName</h2>
 *
 * <ul>
 * <li><b>Request</b></li>
 * </ul>
 *
 * <pre>
* {@code {
          "request": {
              "header": {
                  "messageName": "SYS.ADOWNERREFNAME",
                  "transactionId": "8c512aa1-dbeb-43fc-aa08-03fd988c6fcd",
                  "orgName": "",
                  "username": "",
                  "token": "",
                  "language": "",
                  "orgRrn": 1
              },
              "body": {
                  "actionType": "DELETE",
                  "xadBaseRefName":
                      {
                          "objectRrn" : 404681269432922112
                      }
              }
          }
      }
}
* </pre>
 *
 * <ul>
 * <li><b>Response</b></li>
 * </ul>
 *
 * <pre>
* {@code {
      "response": {
          "header": {
              "transactionId": "8c512aa1-dbeb-43fc-aa08-03fd988c6fcd",
              "result": "SUCCESS"
          }
      }
  }
}
* </pre>
 *
 * <h2>XML格式 ,保存 AdOwnerRefName</h2>
 * 
 * <ul>
 * <li><b>Request</b></li>
 * </ul>
 * 
 * <pre>
* {@code
  <Request>
      <Header>
          <MESSAGENAME>SYS.ADOWNERREFNAME</MESSAGENAME>
          <TRANSACTIONID>8c512aa1-dbeb-43fc-aa08-03fd988c6fcd
            </TRANSACTIONID>
          <ORGNAME></ORGNAME>
          <USERNAME></USERNAME>
          <TOKEN></TOKEN>
          <LANGUAGE></LANGUAGE>
          <ORGRRN>1</ORGRRN>
      </Header>
      <Body>
          <ACTIONTYPE>SAVE</ACTIONTYPE>
          <REFNAME>
              <NAME>Test01</NAME>
              <DESCRIPTION>Test01</DESCRIPTION>
              <REFERENCETYPE>Test01</REFERENCETYPE>
          </REFNAME>
      </Body>
  </Request>
}
* </pre>
 * 
 * <ul>
 * <li><b>Response</b>
 * </ul>
 * 
 * <pre>
* {@code
  <Response>
      <Header>
          <TRANSACTIONID>8c512aa1-dbeb-43fc-aa08-03fd988c6fcd
            </TRANSACTIONID>
          <RESULT>SUCCESS</RESULT>
      </Header>
  </Response>
}
* </pre>
 * </pre>
 *
 * <h2>XML格式 ,删除 AdOwnerRefName</h2>
 *
 * <ul>
 * <li><b>Request</b></li>
 * </ul>
 *
 * <pre>
* {@code
  <Request>
      <Header>
          <MESSAGENAME>SYS.ADOWNERREFNAME</MESSAGENAME>
          <TRANSACTIONID>8c512aa1-dbeb-43fc-aa08-03fd988c6fcd
            </TRANSACTIONID>
          <ORGNAME></ORGNAME>
          <USERNAME></USERNAME>
          <TOKEN></TOKEN>
          <LANGUAGE></LANGUAGE>
          <ORGRRN>1</ORGRRN>
      </Header>
      <Body>
          <ACTIONTYPE>DELETE</ACTIONTYPE>
          <REFNAME>
              <OBJECTRRN>404686450677624832</OBJECTRRN>
          </REFNAME>
      </Body>
  </Request>
}
* </pre>
 *
 * <ul>
 * <li><b>Response</b>
 * </ul>
 *
 * <pre>
* {@code
  <Response>
      <Header>
          <TRANSACTIONID>8c512aa1-dbeb-43fc-aa08-03fd988c6fcd
            </TRANSACTIONID>
          <RESULT>SUCCESS</RESULT>
      </Header>
  </Response>
}
* </pre>
 */
package com.glory.msg.adownerrefname;
