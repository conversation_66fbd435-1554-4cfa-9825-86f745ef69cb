package com.glory.msg.adownerrefname;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
public class ADOwnerRefNameRequest extends Request {
	private static final long serialVersionUID = 1L;

	public static final String MESSAGE_NAME = "ADOWNERREFNAME";
	
	public static final String ACTION_SAVE = "SAVE";
	public static final String ACTION_DELETE = "DELETE";
	
	
	@XmlElement(name="Body")
	private ADOwnerRefNameRequestBody body;

	public ADOwnerRefNameRequestBody getBody() {
		return body;
	}
	
	public void setBody(ADOwnerRefNameRequestBody body) {
		this.body = body;
	}
}
