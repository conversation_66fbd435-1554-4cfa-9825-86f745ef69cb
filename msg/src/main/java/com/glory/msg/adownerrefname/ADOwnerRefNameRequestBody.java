package com.glory.msg.adownerrefname;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import com.glory.msg.RequestBody;
import com.glory.msg.model.XADBaseRefName;

@XmlAccessorType(XmlAccessType.NONE)
public class ADOwnerRefNameRequestBody extends RequestBody {
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name="ACTIONTYPE")
	private String actionType;
	
	@XmlElement(name="REFNAME")
	private XADBaseRefName xadBaseRefName;
	
	/**
	 * 描述：动作类型，映射为：ACTIONTYPE；
	 * @return actionType
	 */
	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public XADBaseRefName getXadBaseRefName() {
		return xadBaseRefName;
	}

	public void setXadBaseRefName(XADBaseRefName xadBaseRefName) {
		this.xadBaseRefName = xadBaseRefName;
	}

}
