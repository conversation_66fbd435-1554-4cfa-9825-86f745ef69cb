package com.glory.msg.adownerrefname;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
public class ADOwnerRefNameResponse extends Response {
	
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="Body")
	private ADOwnerRefNameResponseBody body;

	public ADOwnerRefNameResponseBody getBody() {
		return body;
	}
	
	public void setBody(ADOwnerRefNameResponseBody body) {
		this.body = body;
	}
}
