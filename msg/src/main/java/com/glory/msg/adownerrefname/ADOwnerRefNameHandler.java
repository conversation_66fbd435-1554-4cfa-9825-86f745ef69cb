package com.glory.msg.adownerrefname;

import com.glory.framework.activeentity.model.ADOwnerRefName;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreMessage;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserModel;
import com.glory.msg.Request;
import com.glory.msg.Response;
import com.glory.msg.model.XADBaseRefName;
import com.glory.msg.trans.MsgTransHandler;
import com.glory.msg.trans.TransContext;

@CoreMessage(serviceGroup = "MESCore", serviceModule = "ACTIVEENTITY", messageName = ADOwnerRefNameRequest.MESSAGE_NAME)
public class ADOwnerRefNameHandler extends MsgTransHandler {

	@Override
	protected TransContext internalExecute(TransContext context) throws Exception {
		// 获得对应的消息解析器
		MessageParser parser = getMessageParser(ADOwnerRefNameRequest.MESSAGE_NAME);
		ADOwnerRefNameRequest request = (ADOwnerRefNameRequest) parser.readerRequest(context.getRequest(), context.isJson());
		ADOwnerRefNameResponse response = (ADOwnerRefNameResponse) executeRequest(request, context);
		context.setResponse(parser.writerResponse(response, context.isJson()));
		return context;
	}

	public Response executeRequest(Request request, TransContext context) throws ClientException {
		SessionContext sc = getSessionContext(request, context);
		context.setTransactionId(request.getHeader().getTransactionId());

		ADOwnerRefNameResponse response = new ADOwnerRefNameResponse();
		response.getHeader().setTransactionId(request.getHeader().getTransactionId());
		ADOwnerRefNameRequestBody requestBody = (ADOwnerRefNameRequestBody) request.getBody();
		String actionType = requestBody.getActionType();
		XADBaseRefName xadBaseRefName = requestBody.getXadBaseRefName();
		
		ADOwnerRefName refName = new ADOwnerRefName();
		if(xadBaseRefName != null && xadBaseRefName.getObjectRrn() != null) {
			refName.setObjectRrn(xadBaseRefName.getObjectRrn());
			refName = (ADOwnerRefName) context.getAdManager().getEntity(refName);
		}
		
		if(ADOwnerRefNameRequest.ACTION_SAVE.equals(actionType)) {
			if(xadBaseRefName != null ) {
				PropertyUtil.copyProperties(refName, xadBaseRefName);
				context.getAdManager().saveEntity(refName, sc);
			}
		}else if(ADOwnerRefNameRequest.ACTION_DELETE.equals(actionType)){
			context.getAdManager().deleteEntity(refName, sc);
		}
		
		return response;
	}

	public void initMessageParser() {
		// 注册LOTINFO方法
		MessageParserModel model = new MessageParserModel();
		model.setMessageName(ADOwnerRefNameRequest.MESSAGE_NAME);
		model.setRequestClass(ADOwnerRefNameRequest.class);
		model.setResponseClass(ADOwnerRefNameResponse.class);
		registerMessageParser(ADOwnerRefNameRequest.MESSAGE_NAME, model);
	}

	public MessageParser getMessageParser() {
		return getMessageParser(ADOwnerRefNameRequest.MESSAGE_NAME);
	}

}
