package com.glory.msg.activeentity.entitymaplistbyquery;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreMessage;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserModel;
import com.glory.msg.Request;
import com.glory.msg.Response;
import com.glory.msg.trans.MsgTransHandler;
import com.glory.msg.trans.TransContext;
import com.glory.msg.utils.FieldItemMapType;
import com.glory.msg.utils.FieldItemType;

/**
 * 根据SQL语句获得Map形式数据
 * 
 * Request :
 * <Request>
 * 	<Header>
 * 		<MESSAGENAME>GETENTITYMAPLISTBYQUERY</MESSAGENAME>
 * 		<TRANSACTIONID></TRANSACTIONID>
 * 		<ORGRRN></ORGRRN>
 * 		<ORGNAME></ORGNAME>
 * 		<USERNAME></USERNAME>
 * 	</Header>
 * 	<Body>
 *  <QUERYNAME></QUERYNAME>
 * 	<QUERYTEXT></QUERYTEXT>
 * 	<FIRST></FIRST>
 * 	<MAX></MAX>
 * 	<WHERECLAUSE></WHERECLAUSE>
 *  <ORDERBYCLAUSE></ORDERBYCLAUSE>
 *  <PARAMETERS>
 * 		<ITEM key=""></ITEM>
 * 		<ITEM key=""></ITEM>
 *  </PARAMETERS>
 *  <FIELDS>
 * 		<FIELD></FIELD>
 * 		<FIELD></FIELD>
 *  </FIELDS>
 * 	</Body>
 * </Request>
 * 注:
 * QUERYNAME:ADQuery表中的查询名称
 * QUERYTEXT:SQL查询语句,当QUERYNAME有值时QUERYTEXT失效
 * PARAMETERS:查询语句参数
 * FIELDS:为所需查询栏位,栏位必须是查询语句中所带的栏位
 * 
 * Response : 
 * <Response>
 * 	<Header>
 *  	<TRANSACTIONID></TRANSACTIONID>
 *		<RESULT>SUCCESS</RESULT>
 *	</Header>
 *	<Body>
 *	<DATALIST>
 *		<DATA>
 *			<ITEM key=""></ITEM>
 *          <ITEM key=""></ITEM>
 *          ...
 *      </DATA>
 *	</DATALIST>
 *	</Body>
 *</Response>
 * 注:对udf对象,采用以下处理方式:
 *   1,如果Response对象也为udf对象,udf属性会保存到Response对象的udf栏位中
 *   2,否则会将udf对象的值,保存到Response对象的栏位中
 *     如:udf.customer,会将值保存到Response对象的customer栏位上
 *   3,如果以上都不满足,udf对象的值丢失
 */
@Deprecated
@CoreMessage(serviceGroup = "MESCore", serviceModule = "ACTIVEENTITY", messageName = EntityMapListByQueryRequest.MESSAGE_NAME)
public class EntityMapListByQueryHandler extends MsgTransHandler {

	@Override
	protected TransContext internalExecute(TransContext context) throws Exception {
		//获得对应的消息解析器
		MessageParser parser = getMessageParser(EntityMapListByQueryRequest.MESSAGE_NAME);
		EntityMapListByQueryRequest request = (EntityMapListByQueryRequest)parser.readerRequest(context.getRequest(), context.isJson());
		context.setTransactionId(request.getHeader().getTransactionId());

		EntityMapListByQueryResponse response = (EntityMapListByQueryResponse)executeRequest(request, context);

		context.setResponse(parser.writerResponse(response, context.isJson()));
		return context;	
	}
	
	public Response executeRequest(Request request, TransContext context) throws ClientException {
		EntityMapListByQueryResponse response = new EntityMapListByQueryResponse();
		response.getHeader().setTransactionId(request.getHeader().getTransactionId());		
		EntityMapListByQueryRequestBody requestBody = (EntityMapListByQueryRequestBody)request.getBody();

		List<Map> dataMap;
		
		Map<String, Object> paramStringMap = requestBody.getParamMap();
		Map<String, Object> paramObjectMap = new HashMap<>();
		if (paramStringMap != null) {
			paramObjectMap.putAll(paramStringMap);
		}
		if (!StringUtil.isEmpty(requestBody.getQueryName())) {
			dataMap = context.getAdManager().getEntityMapListByQueryName(
					requestBody.getQueryName(), paramObjectMap,
					requestBody.getFirst() != null ? requestBody.getFirst().intValue() : 0,
					requestBody.getMax() != null ? requestBody.getMax().intValue() : 9999,
					requestBody.getWhereClause(), requestBody.getOrderByClause());
		} else {
			dataMap = context.getAdManager().getEntityMapListByQueryText(
					requestBody.getQueryText(), paramObjectMap,
					requestBody.getFirst() != null ? requestBody.getFirst().intValue() : 0,
					requestBody.getMax() != null ? requestBody.getMax().intValue() : 9999,
					requestBody.getWhereClause(), requestBody.getOrderByClause());
		}
		
		List<FieldItemMapType> dataList = new ArrayList<FieldItemMapType>();
		for (Map map : dataMap) {
			FieldItemMapType data = new FieldItemMapType();
			List<FieldItemType> items = new ArrayList<FieldItemType>();
			for (Object key : map.keySet()) {
				if (requestBody.getFields() != null && !requestBody.getFields().contains(key)) {
					continue;
				}
				items.add(new FieldItemType((String)key, DBUtil.toString(map.get(key))));
			}
			data.setItems(items);
			dataList.add(data);
		}
		
		EntityMapListByQueryResponseBody body = new EntityMapListByQueryResponseBody();
		body.setDataList(dataList);
		response.setBody(body);
		
		return response;
	}
	
	public void initMessageParser() {
		//注册LOTINFO方法
		MessageParserModel model = new MessageParserModel();
		model.setMessageName(EntityMapListByQueryRequest.MESSAGE_NAME);
		model.setRequestClass(EntityMapListByQueryRequest.class);
		model.setResponseClass(EntityMapListByQueryResponse.class);
		registerMessageParser(EntityMapListByQueryRequest.MESSAGE_NAME, model);
	}
	
	public MessageParser getMessageParser() {
		return getMessageParser(EntityMapListByQueryRequest.MESSAGE_NAME);
	}
	
}
