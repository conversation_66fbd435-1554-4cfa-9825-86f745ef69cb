package com.glory.msg.activeentity.adfieldmanager;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
public class ADFieldManagerResponse extends Response {
	
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="Body")
	private ADFieldManagerResponseBody body;

	public ADFieldManagerResponseBody getBody() {
		return body;
	}
	
	public void setBody(ADFieldManagerResponseBody body) {
		this.body = body;
	}
}
