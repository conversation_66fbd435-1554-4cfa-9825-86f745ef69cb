package com.glory.msg.activeentity.adreftablemanager;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import com.glory.msg.RequestBody;
import com.glory.msg.model.XADRefTable;

@XmlAccessorType(XmlAccessType.NONE)
public class ADRefTableManagerRequestBody extends RequestBody {
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name="ACTIONTYPE")
	private String actionType;
	
	@XmlElement(name="ADREFTABLE")
	private XADRefTable xadRefTable;
	
	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public XADRefTable getXadRefTable() {
		return xadRefTable;
	}

	public void setXadRefTable(XADRefTable xadRefTable) {
		this.xadRefTable = xadRefTable;
	}

}
