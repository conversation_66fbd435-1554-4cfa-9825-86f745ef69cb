package com.glory.msg.activeentity.entitymapkeyrootlistbyquery;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
public class EntityMapKeyRootListByQueryResponse extends Response {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="Body")
	private EntityMapKeyRootListByQueryResponseBody body;

	public EntityMapKeyRootListByQueryResponseBody getBody() {
		return body;
	}
	
	public void setBody(EntityMapKeyRootListByQueryResponseBody body) {
		this.body = body;
	}
}
