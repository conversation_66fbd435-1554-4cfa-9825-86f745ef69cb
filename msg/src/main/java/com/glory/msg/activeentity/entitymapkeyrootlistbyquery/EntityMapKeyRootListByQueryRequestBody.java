package com.glory.msg.activeentity.entitymapkeyrootlistbyquery;

import java.util.List;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldMapAdapter;
import com.glory.msg.QueryCondition;
import com.glory.msg.RequestBody;

@XmlAccessorType(XmlAccessType.NONE)
public class EntityMapKeyRootListByQueryRequestBody extends RequestBody {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	

	@XmlElement(name="QUERYNAME")
	private String queryName;
	
	@XmlElement(name="QUERYTEXT")
	private String queryText;

	@XmlElement(name="FIRST")
	private Long first;
	
	@XmlElement(name="MAX")
	private Long max;
	
	@XmlElement(name="WHERECLAUSE")
	private String whereClause;
	
	@XmlElement(name="ORDERBYCLAUSE")
	private String orderByClause;
	
	@XmlElementWrapper(name="QUERYCONDITIONS")
	@XmlElementRef
    private List<QueryCondition> conditions;

	@XmlElement(name="PARAMETERS")
	@XmlJavaTypeAdapter(FieldMapAdapter.class)
    private Map<String, Object> paramMap;

	@XmlElementWrapper(name="FIELDS")
	@XmlElement(name="FIELD")
	private List<String> fields;

	public String getQueryName() {
		return queryName;
	}

	public void setQueryName(String queryName) {
		this.queryName = queryName;
	}

	public String getQueryText() {
		return queryText;
	}

	public void setQueryText(String queryText) {
		this.queryText = queryText;
	}

	public Long getFirst() {
		return first;
	}

	public void setFirst(Long first) {
		this.first = first;
	}

	public Long getMax() {
		return max;
	}

	public void setMax(Long max) {
		this.max = max;
	}

	public String getWhereClause() {
		return whereClause;
	}

	public void setWhereClause(String whereClause) {
		this.whereClause = whereClause;
	}

	public String getOrderByClause() {
		return orderByClause;
	}

	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	public List<QueryCondition> getConditions() {
		return conditions;
	}

	public void setConditions(List<QueryCondition> conditions) {
		this.conditions = conditions;
	}

	public Map<String, Object> getParamMap() {
		return paramMap;
	}

	public void setParamMap(Map<String, Object> paramMap) {
		this.paramMap = paramMap;
	}

	public List<String> getFields() {
		return fields;
	}

	public void setFields(List<String> fields) {
		this.fields = fields;
	}
}
