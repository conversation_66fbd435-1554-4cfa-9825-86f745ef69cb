package com.glory.msg.activeentity.entitymaplistbyquery;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import com.glory.msg.RequestBody;
import com.glory.msg.utils.FieldItemMapType;

@XmlAccessorType(XmlAccessType.FIELD)
public class EntityMapListByQueryResponseBody extends RequestBody {
	
	@XmlElementWrapper(name="DATALIST")
	@XmlElementRef
	private List<FieldItemMapType> dataList;

	public List<FieldItemMapType> getDataList() {
		return dataList;
	}

	public void setDataList(List<FieldItemMapType> dataList) {
		this.dataList = dataList;
	}

}
