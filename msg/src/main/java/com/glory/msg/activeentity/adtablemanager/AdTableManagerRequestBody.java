package com.glory.msg.activeentity.adtablemanager;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import com.glory.msg.RequestBody;
import com.glory.msg.model.XADTable;

@XmlAccessorType(XmlAccessType.NONE)
public class AdTableManagerRequestBody extends RequestBody {
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name="ACTIONTYPE")
	private String actionType;
	
	@XmlElement(name="ADTABLE")
	private XADTable xadTable;
	
	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public XADTable getXadTable() {
		return xadTable;
	}

	public void setXadTable(XADTable xadTable) {
		this.xadTable = xadTable;
	}

}
