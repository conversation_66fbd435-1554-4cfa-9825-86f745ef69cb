package com.glory.msg.activeentity.entitymaplistbyquery;

import java.util.List;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.msg.RequestBody;
import com.glory.msg.utils.FieldItemMapAdapter;
import com.glory.msg.utils.FieldItemMapList;

@XmlAccessorType(XmlAccessType.NONE)
public class EntityMapListByQueryRequestBody extends RequestBody {
		
	@XmlElement(name="QUERYNAME")
	private String queryName;
	
	@XmlElement(name="QUERYTEXT")
	private String queryText;

	@XmlElement(name="FIRST")
	private Long first;
	
	@XmlElement(name="MAX")
	private Long max;
	
	@XmlElement(name="WHERECLAUSE")
	private String whereClause;
	
	@XmlElement(name="ORDERBYCLAUSE")
	private String orderByClause;

	@XmlElement(name="PARAMETERS")
	@XmlJavaTypeAdapter(FieldItemMapAdapter.class)
    private Map<String, Object> paramMap;

	@XmlElementWrapper(name="FIELDS")
	@XmlElement(name="FIELD")
	private List<String> fields;

	public String getQueryName() {
		return queryName;
	}

	public void setQueryName(String queryName) {
		this.queryName = queryName;
	}

	public String getQueryText() {
		return queryText;
	}

	public void setQueryText(String queryText) {
		this.queryText = queryText;
	}

	public Long getFirst() {
		return first;
	}

	public void setFirst(Long first) {
		this.first = first;
	}

	public Long getMax() {
		return max;
	}

	public void setMax(Long max) {
		this.max = max;
	}

	public String getWhereClause() {
		return whereClause;
	}

	public void setWhereClause(String whereClause) {
		this.whereClause = whereClause;
	}

	public String getOrderByClause() {
		return orderByClause;
	}

	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	public Map<String, Object> getParamMap() {
		return paramMap;
	}

	public void setParamMap(Map<String, Object> paramMap) {
		this.paramMap = paramMap;
	}

	public List<String> getFields() {
		return fields;
	}

	public void setFields(List<String> fields) {
		this.fields = fields;
	}
}
