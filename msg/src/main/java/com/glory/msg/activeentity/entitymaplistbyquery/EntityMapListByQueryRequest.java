package com.glory.msg.activeentity.entitymaplistbyquery;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
public class EntityMapListByQueryRequest extends Request {

	public static final String MESSAGE_NAME = "GETENTITYMAPLISTBYQUERY";
	
	@XmlElement(name="Body")
	private EntityMapListByQueryRequestBody body;

	public EntityMapListByQueryRequestBody getBody() {
		return body;
	}
	
	public void setBody(EntityMapListByQueryRequestBody body) {
		this.body = body;
	}
}
