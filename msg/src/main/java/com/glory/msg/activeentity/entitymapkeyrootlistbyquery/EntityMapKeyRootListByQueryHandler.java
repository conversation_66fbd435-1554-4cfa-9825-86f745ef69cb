package com.glory.msg.activeentity.entitymapkeyrootlistbyquery;

import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreMessage;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserModel;
import com.glory.msg.QueryCondition;
import com.glory.msg.Request;
import com.glory.msg.Response;
import com.glory.msg.trans.MsgTransHandler;
import com.glory.msg.trans.TransContext;
import com.google.common.collect.Maps;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

/**
 * 根据SQL语句获得Map形式数据
 * 
 * Request :
 * <Request>
 * 	<Header>
 * 		<MESSAGENAME>GETENTITYMAPLISTBYQUERY</MESSAGENAME>
 * 		<TRANSACTIONID></TRANSACTIONID>
 * 		<ORGRRN></ORGRRN>
 * 		<ORGNAME></ORGNAME>
 * 		<USERNAME></USERNAME>
 * 	</Header>
 * 	<Body>
 *  <QUERYNAME></QUERYNAME>
 * 	<QUERYTEXT></QUERYTEXT>
 * 	<FIRST></FIRST>
 * 	<MAX></MAX>
 * 	<WHERECLAUSE></WHERECLAUSE>
 *  <ORDERBYCLAUSE></ORDERBYCLAUSE>
 *  <PARAMETERS>
 * 		<AAA></AAA>
 * 		<BBB></BBB>
 *  </PARAMETERS>
 *  <FIELDS>
 * 		<FIELD></FIELD>
 * 		<FIELD></FIELD>
 *  </FIELDS>
 * 	</Body>
 * </Request>
 * 注:
 * QUERYNAME:ADQuery表中的查询名称
 * QUERYTEXT:SQL查询语句,当QUERYNAME有值时QUERYTEXT失效
 * PARAMETERS:查询语句参数
 * FIELDS:为所需查询栏位,栏位必须是查询语句中所带的栏位
 * 
 * Response : 
 * <Response>
 * 	<Header>
 *  	<TRANSACTIONID></TRANSACTIONID>
 *		<RESULT>SUCCESS</RESULT>
 *	</Header>
 *	<Body>
 *	<DATALIST>
 *		<DATA>
 *			<OBJECT_RRN></OBJECT_RRN>
 *          <ORG_RRN></ORG_RRN>
 *          ...
 *      </DATA>
 *	</DATALIST>
 *	</Body>
 *</Response>
 * 注:对udf对象,采用以下处理方式:
 *   1,如果Response对象也为udf对象,udf属性会保存到Response对象的udf栏位中
 *   2,否则会将udf对象的值,保存到Response对象的栏位中
 *     如:udf.customer,会将值保存到Response对象的customer栏位上
 *   3,如果以上都不满足,udf对象的值丢失
 */
@CoreMessage(serviceGroup = "MESCore", serviceModule = "ACTIVEENTITY", messageName = EntityMapKeyRootListByQueryRequest.MESSAGE_NAME)
public class EntityMapKeyRootListByQueryHandler extends MsgTransHandler {

	@Override
	protected TransContext internalExecute(TransContext context) throws Exception {
		//获得对应的消息解析器
		MessageParser parser = getMessageParser(EntityMapKeyRootListByQueryRequest.MESSAGE_NAME);
		EntityMapKeyRootListByQueryRequest request = (EntityMapKeyRootListByQueryRequest)parser.readerRequest(context.getRequest(), context.isJson());
		context.setTransactionId(request.getHeader().getTransactionId());

		Response response = executeRequest(request, context);

		context.setResponse(parser.writerResponse(response, context.isJson()));
		return context;	
	}
	
	public Response executeRequest(Request request, TransContext context) throws ClientException {
		EntityMapKeyRootListByQueryRequestBody requestBody = (EntityMapKeyRootListByQueryRequestBody)request.getBody();
		
		Map<String, Object> params = requestBody.getParamMap();
		List<QueryCondition> conditions = requestBody.getConditions();
		if (CollectionUtils.isNotEmpty(conditions)) {
			if (params == null) {
				params = Maps.newHashMap();
			}
			params.putAll(QueryCondition.parseConditions(conditions, true));
		}
		
		List<Map> dataMap;
		if (!StringUtil.isEmpty(requestBody.getQueryName())) {
			dataMap = context.getAdManager().getEntityMapListByQueryName(
				requestBody.getQueryName(), params,
				requestBody.getFirst() != null ? requestBody.getFirst().intValue() : 0,
				requestBody.getMax() != null ? requestBody.getMax().intValue() : 9999,
				requestBody.getWhereClause(), requestBody.getOrderByClause());
		} else {
			dataMap = context.getAdManager().getEntityMapListByQueryText(
					requestBody.getQueryText(), params,
					requestBody.getFirst() != null ? requestBody.getFirst().intValue() : 0,
					requestBody.getMax() != null ? requestBody.getMax().intValue() : 9999,
					requestBody.getWhereClause(), requestBody.getOrderByClause());
		}
		List<Map<String, String>> mapList = new ArrayList<>();
		for (Map map : dataMap) {
			Map<String, String> itemMap = new HashMap<>();
			for (Object key : map.keySet()) {
				if (requestBody.getFields() != null && !requestBody.getFields().contains(key)) {
					continue;
				}
				itemMap.put((String)key, DBUtil.toString(map.get(key)));
			}
			mapList.add(itemMap);
		}
		Response response = null;
		if (context.isJson()) {
            EntityMapKeyRootListByQueryJsonResponse response1 = new EntityMapKeyRootListByQueryJsonResponse();
            EntityMapKeyRootListByQueryJsonResponseBody body = new EntityMapKeyRootListByQueryJsonResponseBody();
            body.setDataList(mapList);
            response1.setBody(body);

            response = response1;
        } else {
            EntityMapKeyRootListByQueryResponse response2 = new EntityMapKeyRootListByQueryResponse();
            EntityMapKeyRootListByQueryResponseBody body = new EntityMapKeyRootListByQueryResponseBody();
            body.setDataList(mapList);
            response2.setBody(body);

            response = response2;
        }

        response.getHeader().setTransactionId(request.getHeader().getTransactionId());
		return response;
	}
	
	public void initMessageParser() {
		//注册方法
		MessageParserModel model = new MessageParserModel();
		model.setMessageName(EntityMapKeyRootListByQueryRequest.MESSAGE_NAME);
		model.setRequestClass(EntityMapKeyRootListByQueryRequest.class);
		model.setResponseClass(EntityMapKeyRootListByQueryResponse.class);
		registerMessageParser(EntityMapKeyRootListByQueryRequest.MESSAGE_NAME, model);
	}
	
	public MessageParser getMessageParser() {
		return getMessageParser(EntityMapKeyRootListByQueryRequest.MESSAGE_NAME);
	}
}
