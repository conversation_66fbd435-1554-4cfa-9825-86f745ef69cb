package com.glory.msg.activeentity.entitymapkeyrootlistbyquery;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.glory.msg.Response;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
public class EntityMapKeyRootListByQueryJsonResponse extends Response {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="Body")
	private EntityMapKeyRootListByQueryJsonResponseBody body;

	public EntityMapKeyRootListByQueryJsonResponseBody getBody() {
		return body;
	}
	
	public void setBody(EntityMapKeyRootListByQueryJsonResponseBody body) {
		this.body = body;
	}
}
