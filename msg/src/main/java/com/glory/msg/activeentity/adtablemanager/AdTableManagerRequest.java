package com.glory.msg.activeentity.adtablemanager;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
public class AdTableManagerRequest extends Request {

	private static final long serialVersionUID = 1L;

	public static final String MESSAGE_NAME = "ADTABLEMANAGER";
	
	public static final String ACTION_SAVE = "SAVE";//保存
	public static final String ACTION_DELETE = "DELETE"; //删除
	
	@XmlElement(name="Body")
	private AdTableManagerRequestBody body;

	public AdTableManagerRequestBody getBody() {
		return body;
	}
	
	public void setBody(AdTableManagerRequestBody body) {
		this.body = body;
	}
}
