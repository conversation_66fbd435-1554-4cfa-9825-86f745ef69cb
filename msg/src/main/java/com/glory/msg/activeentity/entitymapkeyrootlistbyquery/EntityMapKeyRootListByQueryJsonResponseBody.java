package com.glory.msg.activeentity.entitymapkeyrootlistbyquery;

import com.glory.msg.RequestBody;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.List;
import java.util.Map;

@XmlAccessorType(XmlAccessType.FIELD)
public class EntityMapKeyRootListByQueryJsonResponseBody extends RequestBody {

	private static final long serialVersionUID = 1L;
	
	private List<Map<String, String>> dataList;

    @XmlElement(name="DATALIST")
	public Object getDataList() {
        Gson gs = new Gson();
	    return CollectionUtils.isNotEmpty(dataList) ?
                gs.fromJson(gs.to<PERSON>son(dataList), Object.class) : null;
	}

	public void setDataList(List<Map<String, String>> dataList) {
		this.dataList = dataList;
	}

}
