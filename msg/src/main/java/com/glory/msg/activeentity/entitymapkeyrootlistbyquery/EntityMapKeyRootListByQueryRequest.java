package com.glory.msg.activeentity.entitymapkeyrootlistbyquery;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
public class EntityMapKeyRootListByQueryRequest extends Request {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	

	public static final String MESSAGE_NAME = "GETENTITYKEYROOTMAPLISTBYQUERY";
	
	@XmlElement(name="Body")
	private EntityMapKeyRootListByQueryRequestBody body;

	public EntityMapKeyRootListByQueryRequestBody getBody() {
		return body;
	}
	
	public void setBody(EntityMapKeyRootListByQueryRequestBody body) {
		this.body = body;
	}
}
