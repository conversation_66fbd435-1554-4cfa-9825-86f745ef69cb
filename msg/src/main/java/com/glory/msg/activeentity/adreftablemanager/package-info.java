/**
*
* 修改、删除 参考表信息
* <h2>消息名称: SYS.ADFIELDMANAGER</h2>
* 
* <h2>JSON格式 (Delete)：</h2>
* 
* <ul>
* <li><b>Request</b></li>
* </ul>
* <pre>
* {@code {
	"request": {
		"header": {
			"language": "EN",
			"messageName": "SYS.ADREFTABLEMANAGER",
			"transactionId": "20200213171301",
			"orgRrn": 378341,
			"orgName": "FAB1",
			"userName": "admin"
		},
		"body": {
			"actionType": "DELETE",
			"xadRefTable": {
					"objectRrn" : 981310
				}
		}
	}
}
}
* </pre>
* 
* <ul>
* <li><b>Response</b></li>
* </ul>
* <pre>
* {@code {
	"response": {
		"header": {
			"transactionId": "20200213171301",
			"result": "SUCCESS"
		}
	}
}
}
 *<h2>JSO<PERSON>格式 (Save)：</h2>
*
* <ul>
* <li><b>Request</b></li>
* </ul>
* <pre>
* {@code {
	"request": {
		"header": {
			"language": "EN",
			"messageName": "SYS.ADREFTABLEMANAGER",
			"transactionId": "20200213171301",
			"orgRrn": 378341,
			"orgName": "FAB1",
			"userName": "admin"
		},
		"body": {
			"actionType": "SAVE",
			"xadRefTable": {
					"objectRrn" : 981310,
					"name" : "lotId",
					"tableRrn" : 9123819,
					"keyField" : "LotID",
					"textField" : "LotID",
					"whereClause" : "status = 'WAT'",
					"orderByClause" : "objectRrn desc",
					"isDistinct" : "Y"
				}
		}
	}
}
}
* </pre>
*
* <ul>
* <li><b>Response</b></li>
* </ul>
* <pre>
* {@code {
	"response": {
		"header": {
			"transactionId": "20200213171301",
			"result": "SUCCESS"
		}
	}
}
}


* 
* <h2>XML格式 Delete</h2>
* 
* <ul>
* 	<li><b>Request</b></li>
*</ul>
* <pre>
* {@code
<Request>
  <Header>
    <LANGUAGE>EN</LANGUAGE>
    <MESSAGENAME>SYS.ADREFTABLEMANAGER</MESSAGENAME>
    <TRANSACTIONID>20200213171301</TRANSACTIONID>
    <ORGRRN>378341</ORGRRN>
    <ORGNAME>FAB1</ORGNAME>
    <USERNAME>ADMIN</USERNAME>
  </Header>
  <Body>
    <ACTIONTYPE>DELETE</ACTIONTYPE>
    <ADREFTABLE>
      <OBJECTRRN>981310</OBJECTRRN>
    </ADREFTABLE>
  </Body>
</Request>
}
* </pre>
* 
*<ul>
*	<li><b>Response</b>
*</ul>
* <pre>
* {@code 
	<Response>
		<Header>
			<TRANSACTIONID>20200213171301</TRANSACTIONID>
			<RESULT>SUCCESS</RESULT>
		</Header>
	</Response>
}
* </pre>* <h2>XML格式 save</h2>
*
* <ul>
* 	<li><b>Request</b></li>
*</ul>
* <pre>
* {@code
<Request>
 <Header>
  <LANGUAGE>EN</LANGUAGE>
  <MESSAGENAME>SYS.ADREFTABLEMANAGER</MESSAGENAME>
  <TRANSACTIONID>20200213171301</TRANSACTIONID>
  <ORGRRN>378341</ORGRRN>
  <ORGNAME>FAB1</ORGNAME>
  <USERNAME>ADMIN</USERNAME>
 </Header>
 <Body>
  <ACTIONTYPE>SAVE</ACTIONTYPE>
  <ADREFTABLE>
     <OBJECTRRN>981310</OBJECTRRN>
     <NAME>LOTID</NAME>
     <TABLERRN>9123819</TABLERRN>
     <KEYFIELD>LOTID</KEYFIELD>
     <TEXTFIELD>LOTID</TEXTFIELD>
     <WHERECLAUSE>STATUS = 'WAT'</WHERECLAUSE>
     <ORDERBYCLAUSE>OBJECTRRN DESC</ORDERBYCLAUSE>
     <ISDISTINCT>Y</ISDISTINCT>
  </ADREFTABLE>
 </Body>
</Request>

}
* </pre>
*
*<ul>
*	<li><b>Response</b>
*</ul>
* <pre>
* {@code
	<Response>
		<Header>
			<TRANSACTIONID>20200213171301</TRANSACTIONID>
			<RESULT>SUCCESS</RESULT>
		</Header>
	</Response>
}
* </pre>
*/
package com.glory.msg.activeentity.adreftablemanager;

