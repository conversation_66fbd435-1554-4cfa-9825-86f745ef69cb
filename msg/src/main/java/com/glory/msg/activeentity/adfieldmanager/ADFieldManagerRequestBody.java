package com.glory.msg.activeentity.adfieldmanager;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

import com.glory.msg.RequestBody;
import com.glory.msg.model.XADField;

@XmlAccessorType(XmlAccessType.NONE)
public class ADFieldManagerRequestBody extends RequestBody {
	
	private static final long serialVersionUID = 1L;

	@XmlElement(name="ACTIONTYPE")
	private String actionType;
	
	@XmlElement(name="ADFIELD")
	private XADField xadField;
	
	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public XADField getXadField() {
		return xadField;
	}

	public void setXadField(XADField xadField) {
		this.xadField = xadField;
	}

}
