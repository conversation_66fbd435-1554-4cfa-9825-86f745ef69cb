package com.glory.msg.activeentity.adfieldmanager;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreMessage;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserModel;
import com.glory.msg.Request;
import com.glory.msg.Response;
import com.glory.msg.model.XADField;
import com.glory.msg.trans.MsgTransHandler;
import com.glory.msg.trans.TransContext;

@CoreMessage(serviceGroup = "MESCore", serviceModule = "ACTIVEENTITY", messageName = ADFieldManagerRequest.MESSAGE_NAME)
public class ADFieldManagerHandler extends MsgTransHandler {

	@Override
	protected TransContext internalExecute(TransContext context) throws Exception {
		// 获得对应的消息解析器
		MessageParser parser = getMessageParser(ADFieldManagerRequest.MESSAGE_NAME);
		ADFieldManagerRequest request = (ADFieldManagerRequest) parser.readerRequest(context.getRequest(), context.isJson());

		ADFieldManagerResponse response = (ADFieldManagerResponse) executeRequest(request, context);

		context.setResponse(parser.writerResponse(response, context.isJson()));
		return context;
	}

	public Response executeRequest(Request request, TransContext context) throws ClientException {
		SessionContext sc = getSessionContext(request, context);
		context.setTransactionId(request.getHeader().getTransactionId());

		ADFieldManagerResponse response = new ADFieldManagerResponse();
		response.getHeader().setTransactionId(request.getHeader().getTransactionId());

		ADFieldManagerRequestBody requestBody = (ADFieldManagerRequestBody) request.getBody();
		String actionType = requestBody.getActionType();
		
		XADField xadField = requestBody.getXadField();
		ADField field = new ADField();
		
		if(xadField != null && xadField.getObjectRrn() != null) {
			field.setObjectRrn(xadField.getObjectRrn());
			field = (ADField) context.getAdManager().getEntity(field);
		}
		
		if (ADFieldManagerRequest.ACTION_DELETE.equals(actionType)) {
			context.getAdManager().deleteEntity(field, sc);
		}else if(ADFieldManagerRequest.ACTION_SAVE.equals(actionType)) {
			PropertyUtil.copyProperties(field, xadField);
			context.getAdManager().saveEntity(field, sc);
		}
		
		return response;
	}

	public void initMessageParser() {
		// 注册LOTINFO方法
		MessageParserModel model = new MessageParserModel();
		model.setMessageName(ADFieldManagerRequest.MESSAGE_NAME);
		model.setRequestClass(ADFieldManagerRequest.class);
		model.setResponseClass(ADFieldManagerResponse.class);
		registerMessageParser(ADFieldManagerRequest.MESSAGE_NAME, model);
	}

	public MessageParser getMessageParser() {
		return getMessageParser(ADFieldManagerRequest.MESSAGE_NAME);
	}

}
