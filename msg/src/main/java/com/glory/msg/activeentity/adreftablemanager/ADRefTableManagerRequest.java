package com.glory.msg.activeentity.adreftablemanager;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
public class ADRefTableManagerRequest extends Request {

	private static final long serialVersionUID = 1L;

	public static final String MESSAGE_NAME = "ADREFTABLEMANAGER";
	
	public static final String ACTION_SAVE = "SAVE";//保存
	public static final String ACTION_DELETE = "DELETE"; //删除
	public static final String ACTION_QUERY = "QUERY"; //删除
	
	@XmlElement(name="Body")
	private ADRefTableManagerRequestBody body;

	public ADRefTableManagerRequestBody getBody() {
		return body;
	}
	
	public void setBody(ADRefTableManagerRequestBody body) {
		this.body = body;
	}
}
