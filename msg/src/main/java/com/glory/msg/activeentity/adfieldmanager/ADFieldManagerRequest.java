package com.glory.msg.activeentity.adfieldmanager;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
public class ADFieldManagerRequest extends Request {

	private static final long serialVersionUID = 1L;

	public static final String MESSAGE_NAME = "ADFIELDMANAGER";
	
	public static final String ACTION_SAVE = "SAVE";//保存
	public static final String ACTION_DELETE = "DELETE"; //删除

	@XmlElement(name="Body")
	private ADFieldManagerRequestBody body;

	public ADFieldManagerRequestBody getBody() {
		return body;
	}
	
	public void setBody(ADFieldManagerRequestBody body) {
		this.body = body;
	}
}
