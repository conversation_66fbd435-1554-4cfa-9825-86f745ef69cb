package com.glory.msg.activeentity.adreftablemanager;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
public class ADRefTableManagerResponse extends Response {
	
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="Body")
	private ADRefTableManagerResponseBody body;

	public ADRefTableManagerResponseBody getBody() {
		return body;
	}
	
	public void setBody(ADRefTableManagerResponseBody body) {
		this.body = body;
	}
}
