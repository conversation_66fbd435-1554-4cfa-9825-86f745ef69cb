package com.glory.msg.activeentity.adtablemanager;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreMessage;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserModel;
import com.glory.msg.Request;
import com.glory.msg.Response;
import com.glory.msg.model.XADTable;
import com.glory.msg.trans.MsgTransHandler;
import com.glory.msg.trans.TransContext;

@CoreMessage(serviceGroup = "MESCore", serviceModule = "ACTIVEENTITY", messageName = AdTableManagerRequest.MESSAGE_NAME)
public class AdTableManagerHandler extends MsgTransHandler {

	@Override
	protected TransContext internalExecute(TransContext context) throws Exception {
		// 获得对应的消息解析器
		MessageParser parser = getMessageParser(AdTableManagerRequest.MESSAGE_NAME);
		AdTableManagerRequest request = (AdTableManagerRequest) parser.readerRequest(context.getRequest(), context.isJson());

		AdTableManagerResponse response = (AdTableManagerResponse) executeRequest(request, context);

		context.setResponse(parser.writerResponse(response, context.isJson()));
		return context;
	}

	public Response executeRequest(Request request, TransContext context) throws ClientException {
		SessionContext sc = getSessionContext(request, context);
		context.setTransactionId(request.getHeader().getTransactionId());

		AdTableManagerResponse response = new AdTableManagerResponse();
		response.getHeader().setTransactionId(request.getHeader().getTransactionId());

		AdTableManagerRequestBody requestBody = (AdTableManagerRequestBody) request.getBody();
		String actionType = requestBody.getActionType();
		
		XADTable xadTable = requestBody.getXadTable();
		ADTable table = new ADTable();
		
		if(xadTable != null && xadTable.getObjectRrn() != null) {
			table.setObjectRrn(xadTable.getObjectRrn());
			table = (ADTable) context.getAdManager().getEntity(table);
		}
		
		if (AdTableManagerRequest.ACTION_DELETE.equals(actionType)) {
			context.getAdManager().deleteEntity(table, sc);
		}else if(AdTableManagerRequest.ACTION_SAVE.equals(actionType)) {
			PropertyUtil.copyProperties(table, xadTable);
			context.getAdManager().saveEntity(table, sc);
		}
		
		return response;
	}

	public void initMessageParser() {
		// 注册LOTINFO方法
		MessageParserModel model = new MessageParserModel();
		model.setMessageName(AdTableManagerRequest.MESSAGE_NAME);
		model.setRequestClass(AdTableManagerRequest.class);
		model.setResponseClass(AdTableManagerResponse.class);
		registerMessageParser(AdTableManagerRequest.MESSAGE_NAME, model);
	}

	public MessageParser getMessageParser() {
		return getMessageParser(AdTableManagerRequest.MESSAGE_NAME);
	}

}
