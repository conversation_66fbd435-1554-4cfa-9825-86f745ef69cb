package com.glory.msg.activeentity.entitymapkeyrootlistbyquery;

import java.util.List;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.glory.framework.core.xml.FieldListMapAdapter;
import com.glory.msg.RequestBody;

@XmlAccessorType(XmlAccessType.FIELD)
public class EntityMapKeyRootListByQueryResponseBody extends RequestBody {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	
	@XmlJavaTypeAdapter(FieldListMapAdapter.class)
	@XmlElement(name="DATALIST")
	private List<Map<String, String>> dataList;

	public List<Map<String, String>> getDataList() {
		return dataList;
	}

	public void setDataList(List<Map<String, String>> dataList) {
		this.dataList = dataList;
	}
	
}
