package com.glory.msg.activeentity.adreftablemanager;

import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.service.CoreMessage;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserModel;
import com.glory.msg.Request;
import com.glory.msg.Response;
import com.glory.msg.model.XADRefTable;
import com.glory.msg.trans.MsgTransHandler;
import com.glory.msg.trans.TransContext;

@CoreMessage(serviceGroup = "MESCore", serviceModule = "ACTIVEENTITY", messageName = ADRefTableManagerRequest.MESSAGE_NAME)
public class ADRefTableManagerHandler extends MsgTransHandler {

	@Override
	protected TransContext internalExecute(TransContext context) throws Exception {
		// 获得对应的消息解析器
		MessageParser parser = getMessageParser(ADRefTableManagerRequest.MESSAGE_NAME);
		ADRefTableManagerRequest request = (ADRefTableManagerRequest) parser.readerRequest(context.getRequest(), context.isJson());

		ADRefTableManagerResponse response = (ADRefTableManagerResponse) executeRequest(request, context);

		context.setResponse(parser.writerResponse(response, context.isJson()));
		return context;
	}

	public Response executeRequest(Request request, TransContext context) throws ClientException {
		SessionContext sc = getSessionContext(request, context);
		context.setTransactionId(request.getHeader().getTransactionId());

		ADRefTableManagerResponse response = new ADRefTableManagerResponse();
		response.getHeader().setTransactionId(request.getHeader().getTransactionId());

		ADRefTableManagerRequestBody requestBody = (ADRefTableManagerRequestBody) request.getBody();
		String actionType = requestBody.getActionType();
		
		XADRefTable xadRefTable = requestBody.getXadRefTable();
		ADRefTable refTable = new ADRefTable();
		
		if(xadRefTable != null && xadRefTable.getObjectRrn() != null) {
			refTable.setObjectRrn(xadRefTable.getObjectRrn());
			refTable = (ADRefTable) context.getAdManager().getEntity(refTable);
		}
		
		if (ADRefTableManagerRequest.ACTION_DELETE.equals(actionType)) {
			context.getAdManager().deleteEntity(refTable, sc);
		}else if(ADRefTableManagerRequest.ACTION_SAVE.equals(actionType)) {
			PropertyUtil.copyProperties(refTable, xadRefTable);
			context.getAdManager().saveEntity(refTable, sc);
		}
		
		return response;
	}

	public void initMessageParser() {
		// 注册LOTINFO方法
		MessageParserModel model = new MessageParserModel();
		model.setMessageName(ADRefTableManagerRequest.MESSAGE_NAME);
		model.setRequestClass(ADRefTableManagerRequest.class);
		model.setResponseClass(ADRefTableManagerResponse.class);
		registerMessageParser(ADRefTableManagerRequest.MESSAGE_NAME, model);
	}

	public MessageParser getMessageParser() {
		return getMessageParser(ADRefTableManagerRequest.MESSAGE_NAME);
	}

}
