/**
*
* 根据SQL语句获得Map形式数据
* <h2>消息名称: SYS.GETENTITYKEYROOTMAPLISTBYQUERY</h2>
*
* <h2>JSON格式 按照queryName查询 </h2>
*
* <ul>
* <li><b>Request</b></li>
* </ul>
* <pre>
* {@code {
      "request": {
          "header": {
              "language": "EN",
              "messageName": "SYS.GETENTITYKEYROOTMAPLISTBYQUERY",
              "transactionId": "20200213171301",
              "orgRrn": 378341,
              "orgName": "FAB1",
              "userName": "admin"
          },
          "body": {
              "queryName": "BomMaterialList",
              "queryText": "",
              "first": "",
              "max": "",
              "whereClause": "",
              "orderByClause": "",
              "paramMap": {
                  "orgRrn": 1
              },
              "fields": [
                  "BOMNAME","MATERIALNAME","MATERIALDESC"
              ]
          }
      }
  }
}
* </pre>
*
* <ul>
* <li><b>Response</b></li>
* </ul>
* <pre>
* {@code {
      "response": {
          "header": {
              "transactionId": "20200213171301",
              "result": "SUCCESS"
          },
          "body": {
              "dataList": [
                  {
                      "BOMNAME": "ZX_PART_01_BOM",
                      "MATERIALNAME": "CLEAVER",
                      "MATERIALDESC": "劈刀"
                  },
                  {
                      "BOMNAME": "ZX_PART_01_BOM",
                      "MATERIALNAME": "G10801",
                      "MATERIALDESC": "8寸Si基Power D HEMT SiN Cap"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "G10803",
                      "MATERIALDESC": "8寸Si基Power E HEMT SiN Cap"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "PRD_TEST",
                      "MATERIALDESC": ""
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "ZX_PART_01",
                      "MATERIALDESC": "ZX_PART_01"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "PAPT_ST_REWORK_01",
                      "MATERIALDESC": "PAPT_ST_REWORK_01"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "TESTPAPT",
                      "MATERIALDESC": "测试产品"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "CLEAVER",
                      "MATERIALDESC": "劈刀"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "PART_DUMMY_01",
                      "MATERIALDESC": ""
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "PAPT_ST_SORT_01",
                      "MATERIALDESC": "PAPT_ST_SORT_01"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "G10806",
                      "MATERIALDESC": "8寸Si基Power F HEMT"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "G10802",
                      "MATERIALDESC": "8寸Si基Power D HEMT"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "G10801",
                      "MATERIALDESC": "8寸Si基Power D HEMT SiN Cap"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "G10805",
                      "MATERIALDESC": "12寸Si基Power D HEMT SiN Cap"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "PARD_TEST",
                      "MATERIALDESC": "111"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "IC-A001",
                      "MATERIALDESC": "IC-A001"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "PR-A001",
                      "MATERIALDESC": "PHOTORESIST A001"
                  },
                  {
                      "BOMNAME": "PART_DUMMY_01",
                      "MATERIALNAME": "G10804",
                      "MATERIALDESC": "8寸Si基Power E HEMT"
                  },
                  {
                      "BOMNAME": "PARD_TEST02",
                      "MATERIALNAME": "G10801",
                      "MATERIALDESC": "8寸Si基Power D HEMT SiN Cap"
                  },
                  {
                      "BOMNAME": "PARD_TEST",
                      "MATERIALNAME": "G10801",
                      "MATERIALDESC": "8寸Si基Power D HEMT SiN Cap"
                  },
                  {
                      "BOMNAME": "PARD_TEST",
                      "MATERIALNAME": "G10804",
                      "MATERIALDESC": "8寸Si基Power E HEMT"
                  },
                  {
                      "BOMNAME": "PARD_TEST",
                      "MATERIALNAME": "G10803",
                      "MATERIALDESC": "8寸Si基Power E HEMT SiN Cap"
                  },
                  {
                      "BOMNAME": "PARD_TEST",
                      "MATERIALNAME": "G10805",
                      "MATERIALDESC": "12寸Si基Power D HEMT SiN Cap"
                  },
                  {
                      "BOMNAME": "PARD_TEST",
                      "MATERIALNAME": "G10806",
                      "MATERIALDESC": "8寸Si基Power F HEMT"
                  },
                  {
                      "BOMNAME": "PARD_TEST",
                      "MATERIALNAME": "G10802",
                      "MATERIALDESC": "8寸Si基Power D HEMT"
                  },
                  {
                      "BOMNAME": "BOM_PAPT_ST_SORT_01",
                      "MATERIALNAME": "BatchTest001",
                      "MATERIALDESC": "BatchTest001"
                  },
                  {
                      "BOMNAME": "PAPT_ST_REWORK_01",
                      "MATERIALNAME": "CLEAVER",
                      "MATERIALDESC": "劈刀"
                  },
                  {
                      "BOMNAME": "PAPT_ST_REWORK_01",
                      "MATERIALNAME": "G10801",
                      "MATERIALDESC": "8寸Si基Power D HEMT SiN Cap"
                  },
                  {
                      "BOMNAME": "XXX",
                      "MATERIALNAME": "G10803",
                      "MATERIALDESC": "8寸Si基Power E HEMT SiN Cap"
                  }
              ]
          }
      }
  }
}
 *<h2>JSON格式 (按照 queryText 查询)：</h2>
*
* <ul>
* <li><b>Request</b></li>
* </ul>
* <pre>
* {@code {
      "request": {
          "header": {
              "language": "EN",
              "messageName": "SYS.GETENTITYKEYROOTMAPLISTBYQUERY",
              "transactionId": "20200213171301",
              "orgRrn": 378341,
              "orgName": "FAB1",
              "userName": "admin"
          },
          "body": {
              "queryName": "",
              "queryText": "SELECT * FROM WIP_LOT ",
              "first": "1",
              "max": "5",
              "whereClause": "ORG_RRN = :orgRrn",
              "orderByClause": "",
              "paramMap": {
                  "orgRrn": 1
              },
              "fields": [
                    "LOT_ID","OBJECT_RRN"
              ]
          }
      }
  }
}
* </pre>
*
* <ul>
* <li><b>Response</b></li>
* </ul>
* <pre>
* {@code {
      "response": {
          "header": {
              "transactionId": "20200213171301",
              "result": "SUCCESS"
          },
          "body": {
              "dataList": [
                  {
                      "LOT_ID": "CPAL031.001",
                      "OBJECT_RRN": "278518386269413376"
                  },
                  {
                      "LOT_ID": "CPAL029.002",
                      "OBJECT_RRN": "278519399928926208"
                  },
                  {
                      "LOT_ID": "CPAL029.003",
                      "OBJECT_RRN": "278519834202968064"
                  },
                  {
                      "LOT_ID": "CPAL037.001",
                      "OBJECT_RRN": "279547399667531776"
                  },
                  {
                      "LOT_ID": "CPAL029.002.S02",
                      "OBJECT_RRN": "278578169539301376"
                  }
              ]
          }
      }
  }
}


*
* <h2>XML格式 按照 QUERYNAME 查询 </h2>
*
* <ul>
* 	<li><b>Request</b></li>
*</ul>
* <pre>
* {@code
  <Request>
      <Header>
          <MESSAGENAME>SYS.GETENTITYKEYROOTMAPLISTBYQUERY</MESSAGENAME>
          <ORGRRN>1</ORGRRN>
          <USERNAME>admin</USERNAME>
          <TRANSACTIONID>NsSgoemhrjGtohRTDCGEp4</TRANSACTIONID>
      </Header>
      <Body>
          <QUERYNAME>BomMaterialList</QUERYNAME>
          <QUERYTEXT></QUERYTEXT>
          <FIRST></FIRST>
          <MAX></MAX>
          <WHERECLAUSE></WHERECLAUSE>
          <ORDERBYCLAUSE></ORDERBYCLAUSE>
          <PARAMETERS>
              <orgRrn>1</orgRrn>
          </PARAMETERS>
          <FIELDS>
              <FIELD>BOMNAME</FIELD>
              <FIELD>MATERIALNAME</FIELD>
              <FIELD>MATERIALDESC</FIELD>
          </FIELDS>
      </Body>
  </Request>
}
* </pre>
*
*<ul>
*	<li><b>Response</b>
*</ul>
* <pre>
* {@code
  <Response>
      <Header>
          <TRANSACTIONID>NsSgoemhrjGtohRTDCGEp4</TRANSACTIONID>
          <RESULT>SUCCESS</RESULT>
      </Header>
      <Body>
          <DATALIST>
              <DATA>
                  <BOMNAME>ZX_PART_01_BOM</BOMNAME>
                  <MATERIALNAME>CLEAVER</MATERIALNAME>
                  <MATERIALDESC>劈刀</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>ZX_PART_01_BOM</BOMNAME>
                  <MATERIALNAME>G10801</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power D HEMT SiN Cap</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>G10803</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power E HEMT SiN Cap</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>PRD_TEST</MATERIALNAME>
                  <MATERIALDESC/>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>ZX_PART_01</MATERIALNAME>
                  <MATERIALDESC>ZX_PART_01</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>PAPT_ST_REWORK_01</MATERIALNAME>
                  <MATERIALDESC>PAPT_ST_REWORK_01</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>TESTPAPT</MATERIALNAME>
                  <MATERIALDESC>测试产品</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>CLEAVER</MATERIALNAME>
                  <MATERIALDESC>劈刀</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>PART_DUMMY_01</MATERIALNAME>
                  <MATERIALDESC/>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>PAPT_ST_SORT_01</MATERIALNAME>
                  <MATERIALDESC>PAPT_ST_SORT_01</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>G10806</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power F HEMT</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>G10802</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power D HEMT</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>G10801</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power D HEMT SiN Cap</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>G10805</MATERIALNAME>
                  <MATERIALDESC>12寸Si基Power D HEMT SiN Cap</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>PARD_TEST</MATERIALNAME>
                  <MATERIALDESC>111</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>IC-A001</MATERIALNAME>
                  <MATERIALDESC>IC-A001</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>PR-A001</MATERIALNAME>
                  <MATERIALDESC>PHOTORESIST A001</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PART_DUMMY_01</BOMNAME>
                  <MATERIALNAME>G10804</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power E HEMT</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PARD_TEST02</BOMNAME>
                  <MATERIALNAME>G10801</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power D HEMT SiN Cap</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PARD_TEST</BOMNAME>
                  <MATERIALNAME>G10801</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power D HEMT SiN Cap</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PARD_TEST</BOMNAME>
                  <MATERIALNAME>G10804</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power E HEMT</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PARD_TEST</BOMNAME>
                  <MATERIALNAME>G10803</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power E HEMT SiN Cap</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PARD_TEST</BOMNAME>
                  <MATERIALNAME>G10805</MATERIALNAME>
                  <MATERIALDESC>12寸Si基Power D HEMT SiN Cap</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PARD_TEST</BOMNAME>
                  <MATERIALNAME>G10806</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power F HEMT</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PARD_TEST</BOMNAME>
                  <MATERIALNAME>G10802</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power D HEMT</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>BOM_PAPT_ST_SORT_01</BOMNAME>
                  <MATERIALNAME>BatchTest001</MATERIALNAME>
                  <MATERIALDESC>BatchTest001</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PAPT_ST_REWORK_01</BOMNAME>
                  <MATERIALNAME>CLEAVER</MATERIALNAME>
                  <MATERIALDESC>劈刀</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>PAPT_ST_REWORK_01</BOMNAME>
                  <MATERIALNAME>G10801</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power D HEMT SiN Cap</MATERIALDESC>
              </DATA>
              <DATA>
                  <BOMNAME>XXX</BOMNAME>
                  <MATERIALNAME>G10803</MATERIALNAME>
                  <MATERIALDESC>8寸Si基Power E HEMT SiN Cap</MATERIALDESC>
              </DATA>
          </DATALIST>
      </Body>
  </Response>

}
* </pre>* <h2>XML 格式, 按照  QUERYTEXT 查询</h2>
*
* <ul>
* 	<li><b>Request</b></li>
*</ul>
* <pre>
* {@code
  <Request>
      <Header>
          <MESSAGENAME>SYS.GETENTITYKEYROOTMAPLISTBYQUERY</MESSAGENAME>
          <ORGRRN>1</ORGRRN>
          <USERNAME>admin</USERNAME>
          <TRANSACTIONID>NsSgoemhrjGtohRTDCGEp4</TRANSACTIONID>
      </Header>
      <Body>
          <QUERYNAME></QUERYNAME>
          <QUERYTEXT>SELECT * FROM WIP_LOT</QUERYTEXT>
          <FIRST>1</FIRST>
          <MAX>5</MAX>
          <WHERECLAUSE>ORG_RRN = :orgRrn</WHERECLAUSE>
          <ORDERBYCLAUSE></ORDERBYCLAUSE>
          <PARAMETERS>
              <orgRrn>1</orgRrn>
          </PARAMETERS>
          <FIELDS>
              <FIELD>LOT_ID</FIELD>
              <FIELD>OBJECT_RRN</FIELD>
          </FIELDS>
      </Body>
  </Request>

}
* </pre>
*
*<ul>
*	<li><b>Response</b>
*</ul>
* <pre>
* {@code
  <Response>
      <Header>
          <TRANSACTIONID>NsSgoemhrjGtohRTDCGEp4</TRANSACTIONID>
          <RESULT>SUCCESS</RESULT>
      </Header>
      <Body>
          <DATALIST>
              <DATA>
                  <LOT_ID>CPAL031.001</LOT_ID>
                  <OBJECT_RRN>278518386269413376</OBJECT_RRN>
              </DATA>
              <DATA>
                  <LOT_ID>CPAL029.002</LOT_ID>
                  <OBJECT_RRN>278519399928926208</OBJECT_RRN>
              </DATA>
              <DATA>
                  <LOT_ID>CPAL029.003</LOT_ID>
                  <OBJECT_RRN>278519834202968064</OBJECT_RRN>
              </DATA>
              <DATA>
                  <LOT_ID>CPAL037.001</LOT_ID>
                  <OBJECT_RRN>279547399667531776</OBJECT_RRN>
              </DATA>
              <DATA>
                  <LOT_ID>CPAL029.002.S02</LOT_ID>
                  <OBJECT_RRN>278578169539301376</OBJECT_RRN>
              </DATA>
          </DATALIST>
      </Body>
  </Response>

}
* </pre>
*/
package com.glory.msg.activeentity.entitymapkeyrootlistbyquery;

