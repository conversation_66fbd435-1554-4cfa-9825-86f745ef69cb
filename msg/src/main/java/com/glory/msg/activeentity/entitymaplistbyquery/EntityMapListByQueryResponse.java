package com.glory.msg.activeentity.entitymaplistbyquery;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
public class EntityMapListByQueryResponse extends Response {
	
	@XmlElement(name="Body")
	private EntityMapListByQueryResponseBody body;

	public EntityMapListByQueryResponseBody getBody() {
		return body;
	}
	
	public void setBody(EntityMapListByQueryResponseBody body) {
		this.body = body;
	}
}
