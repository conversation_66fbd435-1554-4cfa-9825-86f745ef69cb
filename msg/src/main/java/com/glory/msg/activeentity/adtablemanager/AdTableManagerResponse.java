package com.glory.msg.activeentity.adtablemanager;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Response;

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
public class AdTableManagerResponse extends Response {
	
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="Body")
	private AdTableManagerResponseBody body;

	public AdTableManagerResponseBody getBody() {
		return body;
	}
	
	public void setBody(AdTableManagerResponseBody body) {
		this.body = body;
	}
}
