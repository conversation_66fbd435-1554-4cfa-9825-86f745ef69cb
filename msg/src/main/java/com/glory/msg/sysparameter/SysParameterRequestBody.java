package com.glory.msg.sysparameter;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;

import com.glory.msg.RequestBody;
import com.glory.msg.model.XADSysParameterValue;

public class SysParameterRequestBody extends RequestBody {
	
	private static final long serialVersionUID = 1L;
	
	@XmlElement(name="ACTIONTYPE")
	private String actionType;
	
	@XmlElementWrapper(name="ADSYSPARAMETERVALUELIST")
	@XmlElementRef
	private List<XADSysParameterValue> xadSysParameterValues;

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public List<XADSysParameterValue> getXadSysParameterValues() {
		return xadSysParameterValues;
	}

	public void setXadSysParameterValues(List<XADSysParameterValue> xadSysParameterValues) {
		this.xadSysParameterValues = xadSysParameterValues;
	}

}
