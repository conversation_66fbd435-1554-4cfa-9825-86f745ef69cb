/**
 *
 * 系统参数 管理
 * 
 * <h2>消息名称: SYS.SYSPARAMETER</h2>
 * 
 * <h2>JSON格式： 修改系统参数</h2>
 * 
 * <ul>
 * <li><b>Request</b></li>
 * </ul>
 * 
 * <pre>
* {@code {
  	"request": {
  		"header": {
  			"messageName": "SYS.SYSPARAMETER",
  			"transactionId": "8c512aa1-dbeb-43fc-aa08-03fd988c6fcd",
  			"orgName": "",
  			"username": "",
  			"token": "",
  			"language": "",
  			"orgRrn": 1
                 },
  		"body": {
  			"actionType": "SAVE",
  			"xadSysParameterValues": [{
                "objectRrn":"70402",
  				"description": "Test",
  				"value": "Y",
  				"defaultValue": "Y",
  				"isStatic": false,
  				"isSystem": true,
  				"isGlobal": true,
  				"isModifiable": false,
  				"comments": "Test",
  				"currentValue": "Y"
             }]
         }
}
  }
}
* </pre>
 * 
 * <ul>
 * <li><b>Response</b></li>
 * </ul>
 * 
 * <pre>
* {@code {
      "response": {
          "header": {
              "transactionId": "8c512aa1-dbeb-43fc-aa08-03fd988c6fcd",
              "result": "SUCCESS"
          }
      }
  }
}
* </pre>
 * <h2>JSON格式：加载所有系统参数</h2>
 *
 * <ul>
 * <li><b>Request</b></li>
 * </ul>
 *
 * <pre>
* {@code {
  	"request": {
  		"header": {
  			"messageName": "SYS.SYSPARAMETER",
  			"transactionId": "8c512aa1-dbeb-43fc-aa08-03fd988c6fcd",
  			"orgName": "",
  			"username": "",
  			"token": "",
  			"language": "",
  			"orgRrn": 1
                 },
  		"body": {
  			"actionType": "LOADING"
         }
	}
  }
* </pre>
 *
 * <ul>
 * <li><b>Response</b></li>
 * </ul>
 *
 * <pre>
* {@code {
      "response": {
          "header": {
              "transactionId": "8c512aa1-dbeb-43fc-aa08-03fd988c6fcd",
              "result": "SUCCESS"
          },
          "body": {
              "xadSysParameterValues": [
                  {
                      "objectRrn": "191215",
                      "name": "sys_tenant_is_use",
                      "description": "系统使用租户管理",
                      "value": "Y",
                      "isStatic": false,
                      "isSystem": true,
                      "isGlobal": true,
                      "isModifiable": true,
                      "comments": "启用租户信息管理",
                      "currentValue": "Y"
                  },
                  {
                      "objectRrn": "999521",
                      "name": "tcard_use_bom",
                      "description": "流程卡是否带出bom信息",
                      "value": "Y",
                      "defaultValue": "Y",
                      "isStatic": false,
                      "isSystem": true,
                      "isGlobal": true,
                      "isModifiable": true,
                      "currentValue": "Y"
                  }
              ]
          }
      }
  }
  }
*/
package com.glory.msg.sysparameter;
