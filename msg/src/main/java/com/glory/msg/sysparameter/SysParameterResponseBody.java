package com.glory.msg.sysparameter;

import java.util.List;

import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;

import com.glory.msg.ResponseBody;
import com.glory.msg.model.XADSysParameterValue;

public class SysParameterResponseBody extends ResponseBody {
	
	private static final long serialVersionUID = 1L;
	
	@XmlElementWrapper(name="ADSYSPARAMETERVALUELIST")
	@XmlElementRef
	private List<XADSysParameterValue> xadSysParameterValues;

	public List<XADSysParameterValue> getXadSysParameterValues() {
		return xadSysParameterValues;
	}

	public void setXadSysParameterValues(List<XADSysParameterValue> xadSysParameterValues) {
		this.xadSysParameterValues = xadSysParameterValues;
	}
	
}
