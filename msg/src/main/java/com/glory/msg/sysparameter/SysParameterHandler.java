package com.glory.msg.sysparameter;

import java.util.ArrayList;
import java.util.List;

import com.glory.framework.activeentity.model.ADSysParameterValue;
import com.glory.framework.core.service.CoreMessage;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.msg.MessageParser;
import com.glory.msg.MessageParserModel;
import com.glory.msg.Request;
import com.glory.msg.Response;
import com.glory.msg.model.XADSysParameterValue;
import com.glory.msg.trans.MsgTransHandler;
import com.glory.msg.trans.TransContext;

@CoreMessage(serviceGroup = "MESCore", serviceModule = "ACTIVEENTITY", messageName = SysParameterRequest.MESSAGE_NAME)
public class SysParameterHandler extends MsgTransHandler {

	@Override
	public void initMessageParser() {
		// 注册MergeRule方法
		MessageParserModel parserModel = new MessageParserModel();
		parserModel.setRequestClass(SysParameterRequest.class);
		parserModel.setResponseClass(SysParameterResponse.class);
		parserModel.setMessageName(SysParameterRequest.MESSAGE_NAME);
		registerMessageParser(SysParameterRequest.MESSAGE_NAME, parserModel);
	}

	@Override
	protected TransContext internalExecute(TransContext context) throws Exception {
		MessageParser parser = getMessageParser(SysParameterRequest.MESSAGE_NAME);
		SysParameterRequest request = (SysParameterRequest) parser.readerRequest(context.getRequest(), context.isJson());
		SysParameterResponse response = (SysParameterResponse) executeRequest(request, context);
		response.getHeader().setTransactionId(request.getHeader().getTransactionId());

		context.setResponse(parser.writerResponse(response, context.isJson()));
		return context;
	}

	@Override
	public Response executeRequest(Request request, TransContext context) throws Exception {
		SessionContext sc = getSessionContext(request, context);
		SysParameterRequestBody reqBody = (SysParameterRequestBody) request.getBody();

		SysParameterResponse response = new SysParameterResponse();
		SysParameterResponseBody body = new SysParameterResponseBody();

		String actionType = reqBody.getActionType();
		List<XADSysParameterValue> xValues = reqBody.getXadSysParameterValues();
		
		if (SysParameterRequest.ACTION_LOADING.equals(actionType)) {
			List<ADSysParameterValue> sysParameterValues = context.getSysParameterManager().getSysParameterValues(sc.getOrgRrn());
			List<XADSysParameterValue> xadSysParameterValues = new ArrayList<XADSysParameterValue>();
			for(ADSysParameterValue value : sysParameterValues) {
				XADSysParameterValue xValue = new XADSysParameterValue();
				PropertyUtil.copyProperties(xValue, value);
				xadSysParameterValues.add(xValue);
			}
			body.setXadSysParameterValues(xadSysParameterValues);
		}else if(SysParameterRequest.ACTION_SAVE.equals(actionType)) {
			List<ADSysParameterValue> sysParameterValues = new ArrayList<ADSysParameterValue>();
			if(xValues != null && !xValues.isEmpty()) {
				for(XADSysParameterValue xValue : xValues) {
					if(xValue.getObjectRrn() != null) {
						ADSysParameterValue value = new ADSysParameterValue();
						value.setObjectRrn(xValue.getObjectRrn());
						value = (ADSysParameterValue) context.getAdManager().getEntity(value);
						value.setValue(xValue.getValue());
						value.setComments(xValue.getComments());
						sysParameterValues.add(value);
					}
				}
				context.getSysParameterManager().saveSysParameterValue(sysParameterValues, sc);
			}
		}
		
		response.setBody(body);
		return response;
	}

	@Override
	public MessageParser getMessageParser() {
		return getMessageParser(SysParameterRequest.MESSAGE_NAME);
	}

}
