package com.glory.msg.sysparameter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.msg.Request;

@XmlRootElement(name="Request")
@XmlAccessorType(XmlAccessType.NONE)
public class SysParameterRequest extends Request {
	
	private static final long serialVersionUID = 1L;

	public static final String MESSAGE_NAME = "SYSPARAMETER";
	
	public static final String ACTION_LOADING = "LOADING"; //加载
	public static final String ACTION_SAVE = "SAVE";//保存
	
	@XmlElement(name="Body")
	private SysParameterRequestBody body;

	public SysParameterRequestBody getBody() {
		return body;
	}

	public void setBody(SysParameterRequestBody body) {
		this.body = body;
	}

}
