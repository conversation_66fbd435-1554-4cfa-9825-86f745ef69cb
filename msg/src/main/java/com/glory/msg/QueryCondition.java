package com.glory.msg;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.DateUtil;
import com.glory.framework.core.util.IApfSupport;
import com.google.common.collect.Maps;

/**
 * 在通过Web方式查询时，处理查询条件
 * <AUTHOR>
 *
 */
@XmlRootElement(name = "QUERYCONDITION")
@XmlAccessorType(XmlAccessType.NONE)
public class QueryCondition implements Serializable, IApfSupport {

	private static final long serialVersionUID = 1515144758666843245L;
	
	private static final String CONDITION_TYPE_STRING = "string";
	private static final String CONDITION_TYPE_INTEGER = "integer";
	private static final String CONDITION_TYPE_DOUBLE = "double";
	private static final String CONDITION_TYPE_LONG = "long";
	private static final String CONDITION_TYPE_DECIMAL = "decimal";
	private static final String CONDITION_TYPE_DATE = "date";
	private static final String CONDITION_TYPE_TIME = "time";
	
	/**
	 * 条件名称（如"where lotId = :myLotId"中的myLotId）
	 */
	@XmlElement(name="NAME")
	private String name;
	
	/**
	 * 条件数据类型
	 */
	@XmlElement(name="TYPE")
	private String type;
	
	/**
	 * 条件值，值类型要与数据类型一致，否则会出现转换错误<br/>
	 * 时间类型要遵守规范
	 */
	@XmlElement(name="STRINGVALUE")
	private String stringValue;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getStringValue() {
		return stringValue;
	}

	public void setStringValue(String stringValue) {
		this.stringValue = stringValue;
	}
	
	private Object parseCondition(boolean isException) {
		boolean unSupportFlag = false;
		Object value = null;
		try {
			if (CONDITION_TYPE_STRING.equals(type)) {
				value = stringValue;
			} else if (CONDITION_TYPE_INTEGER.equals(type) || CONDITION_TYPE_LONG.equals(type)) {
				value = DBUtil.toLong(stringValue);
			} else if (CONDITION_TYPE_DOUBLE.equals(type)) {
				value = DBUtil.toDouble(stringValue);
			} else if (CONDITION_TYPE_DECIMAL.equals(type)) {
				value = new BigDecimal(stringValue);
			} else if (CONDITION_TYPE_DATE.equals(type)) {
				value = DateUtil.parseDate(stringValue);
			} else if (CONDITION_TYPE_TIME.equals(type)) {
				value = DateUtil.parseDate(stringValue, DateUtil.getDefaultDateTimePattern());
			} else {
				unSupportFlag = true;
			}
		} catch (Exception e) {
			if (isException) {
				throw ExceptionBundle.bundle.QueryConditionParseFailed(name, type, stringValue);
			}
		}
		
		if (isException) {
			if (unSupportFlag) {
				throw ExceptionBundle.bundle.QueryConditionTypeNotSupport(type);
			}
			
			if (value == null) {
				throw ExceptionBundle.bundle.QueryConditionParseFailed(name, type, stringValue);
			}
		}
		return value;
	}
	
	public static Map<String, Object> parseConditions(List<QueryCondition> conditions) throws ClientException {
		return parseConditions(conditions, false);
	}
	
	public static Map<String, Object> parseConditions(List<QueryCondition> conditions, boolean isException) throws ClientException {
		Map<String, Object> map = Maps.newHashMap();
		for (QueryCondition condition : conditions) {
			Object value = condition.parseCondition(isException);
			map.put(condition.getName(), value);
		}
		
		return map;
	}
	
}
