package com.glory.msg.utils;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Element;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.glory.framework.core.xml.XDynamicComponent;


class UdfTest  {

	@Test
	void udfTest1() throws Exception {
		ObjectMapper objectMapper = new ObjectMapper();
		JsonJaxbAnnotationModule module = new JsonJaxbAnnotationModule();
		module.addSerializer(Element.class, new FieldMapDomElementJsonSerializer());
		objectMapper.registerModule(module);
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		objectMapper.enable(SerializationFeature.WRAP_ROOT_VALUE);
		objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.enable(MapperFeature.USE_WRAPPER_NAME_AS_PROPERTY_NAME);
		objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

		ObjectWriter jsonWriter = objectMapper.writerWithView(XLot1.class);

		XLot1 lot = new XLot1();
		lot.setLotId("LOT1");
		lot.setLotAlias("LOT1Alias");
		lot.putUdfValue("AA", "AA1");
		lot.putUdfValue("BB", "BB1");
		
		String json = jsonWriter.writeValueAsString(lot);		
		Assertions.assertNotNull(json);
		
		ObjectMapper readMapper = new ObjectMapper();
		JsonJaxbAnnotationModule readModule = new JsonJaxbAnnotationModule();
        readMapper.registerModule(readModule);
        readMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        readMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        readMapper.enable(DeserializationFeature.UNWRAP_ROOT_VALUE);
        readMapper.enable(MapperFeature.USE_WRAPPER_NAME_AS_PROPERTY_NAME);

        ObjectReader jsonReader = readMapper.reader(XLot1.class);
        XLot1 lot1 = (XLot1)jsonReader.readValue(json);
        Assertions.assertEquals("LOT1", lot1.getLotId());
        Assertions.assertEquals("LOT1Alias", lot1.getLotAlias());
        Assertions.assertEquals("AA1", lot1.getUdf().get("AA"));
        Assertions.assertEquals("BB1", lot1.getUdf().get("BB"));
	}
	
	@Test
	void udfTest2() throws Exception {
		ObjectMapper objectMapper = new ObjectMapper();
		JsonJaxbAnnotationModule module = new JsonJaxbAnnotationModule();
		module.addSerializer(Element.class, new FieldMapDomElementJsonSerializer());
		objectMapper.registerModule(module);
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		objectMapper.enable(SerializationFeature.WRAP_ROOT_VALUE);
		objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.enable(MapperFeature.USE_WRAPPER_NAME_AS_PROPERTY_NAME);
		objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

		ObjectWriter jsonWriter = objectMapper.writerWithView(XLot1.class);

		XLot1 lot1 = new XLot1();
		lot1.setLotId("LOT1");
		lot1.setLotAlias("LOT1Alias");
		lot1.putUdfValue("AA", "AA1");
		lot1.putUdfValue("BB", "BB1");
		
		XLot1 lot2 = new XLot1();
		lot2.setLotId("LOT2");
		lot2.setLotAlias("LOT2Alias");
		lot2.putUdfValue("AA", "AA2");
		lot2.putUdfValue("BB", "BB2");
		
		XLot1List list = new XLot1List();
		list.setListId("LIST1");
		list.setDataList(Arrays.asList(lot1, lot2));
		
		String json = jsonWriter.writeValueAsString(list);		
		Assertions.assertNotNull(json);
		
		ObjectMapper readMapper = new ObjectMapper();
		JsonJaxbAnnotationModule readModule = new JsonJaxbAnnotationModule();
        readMapper.registerModule(readModule);
        readMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        readMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        readMapper.enable(DeserializationFeature.UNWRAP_ROOT_VALUE);
        readMapper.enable(MapperFeature.USE_WRAPPER_NAME_AS_PROPERTY_NAME);

        ObjectReader jsonReader = readMapper.reader(XLot1List.class);
        XLot1List lotList = (XLot1List)jsonReader.readValue(json);
        Assertions.assertEquals(2, lotList.getDataList().size());
        
        lot1 = lotList.getDataList().get(0);
        Assertions.assertEquals("LOT1", lot1.getLotId());
        Assertions.assertEquals("LOT1Alias", lot1.getLotAlias());
        Assertions.assertEquals("AA1", lot1.getUdf().get("AA"));
        Assertions.assertEquals("BB1", lot1.getUdf().get("BB"));
        
        lot2 = lotList.getDataList().get(1);
        Assertions.assertEquals("LOT2", lot2.getLotId());
        Assertions.assertEquals("LOT2Alias", lot2.getLotAlias());
        Assertions.assertEquals("AA2", lot2.getUdf().get("AA"));
        Assertions.assertEquals("BB2", lot2.getUdf().get("BB"));
	}

}
