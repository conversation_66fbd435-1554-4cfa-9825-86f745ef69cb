package com.glory.msg.utils;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XBase;

@XmlRootElement(name = "LIST")
@XmlAccessorType(XmlAccessType.NONE)
public class XLot1List {

	@XmlElement(name="LISTID")
	private String listId;
	
	@XmlElementWrapper(name="DATALIST")
	@XmlElementRef
	private List<XLot1> dataList;

	public String getListId() {
		return listId;
	}

	public void setListId(String listId) {
		this.listId = listId;
	}

	public List<XLot1> getDataList() {
		return dataList;
	}

	public void setDataList(List<XLot1> dataList) {
		this.dataList = dataList;
	}

}
