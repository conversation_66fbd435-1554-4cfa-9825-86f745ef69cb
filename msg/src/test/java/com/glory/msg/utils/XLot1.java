package com.glory.msg.utils;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import com.glory.framework.core.xml.XDynamicComponent;

@XmlRootElement(name = "LOT")
@XmlAccessorType(XmlAccessType.NONE)
public class XLot1 extends XDynamicComponent {

	@XmlElement(name="LOTID")
	private String lotId;
	
	@XmlElement(name="LOTALIAS")
	private String lotAlias;

	public XLot1() {
		super();
	}
    
	public String getLotId() {
		return lotId;
	}

	public void setLotId(String lotId) {
		this.lotId = lotId;
	}

	public String getLotAlias() {
		return lotAlias;
	}

	public void setLotAlias(String lotAlias) {
		this.lotAlias = lotAlias;
	}

}
