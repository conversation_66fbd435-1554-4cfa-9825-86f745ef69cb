<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.glory.msg</groupId>
		<artifactId>msg-pom</artifactId>
		<version>8.4.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>msg</artifactId>
	<name>MSG</name>
	<description>消息管理</description>
	<packaging>ejb</packaging>

	<dependencies>
		<!-- https://mvnrepository.com/artifact/cglib/cglib -->
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib</artifactId>
			<version>3.2.0</version>
		</dependency>
		<dependency>
			<groupId>com.glory.framework</groupId>
			<artifactId>activeentitymodel</artifactId>
			<version>${framework.version}</version>
		</dependency>
		<dependency>
			<groupId>com.glory.framework</groupId>
			<artifactId>activeentity</artifactId>
			<version>${framework.version}</version>
		</dependency>
			<dependency>
			<groupId>com.glory.framework</groupId>
			<artifactId>security</artifactId>
			<version>${framework.version}</version>
		</dependency>
		<dependency>
			<groupId>com.glory.framework</groupId>
			<artifactId>securitymodel</artifactId>
			<version>${framework.version}</version>
		</dependency>
		
		<dependency>
			<groupId>com.glory.msg</groupId>
			<artifactId>msgmodel</artifactId>
			<version>${msg.version}</version>
		</dependency>
	</dependencies>
</project>