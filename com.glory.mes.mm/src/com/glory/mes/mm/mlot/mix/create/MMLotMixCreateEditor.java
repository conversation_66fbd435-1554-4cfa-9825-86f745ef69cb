package com.glory.mes.mm.mlot.mix.create;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.viewers.StructuredSelection;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.bom.model.AbstractBomLine;
import com.glory.mes.mm.bom.model.Bom;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.model.Material;
import com.glory.mes.mm.model.RawMaterial;
import com.glory.mes.wip.client.MLotManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;

public class MMLotMixCreateEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.mm/com.glory.mes.mm.mlot.mix.create.MMMLotMixCreateManagerEditor";

	private static final String FIELD_MIXMLOTQUERYGLC = "mixMLotQueryGlc";
	private static final String FIELD_MIXMLOTCREATEGLC = "mixMLotCreateGlc";
	private static final String FIELD_MIXMATERIALLIST = "mixMaterialList";
	private static final String FIELD_MIXMATERIALLOTINVENTORY = "mixMaterialLotInventory";
	private static final String FIELD_MIXMATERIALBOMINFO = "mixMaterialBomInfo";
	private static final String FIELD_MIXMATERIALLOTCREATE = "mixMaterialLotCreate";
	private static final String FIELD_SUBMLOTID = "subMLotId";
	private static final String FIELD_SUBMATERIALLOTCONSUME = "subMaterialLotConsume";
	private static final String FIELD_NAME = "name";
	private static final String FIELD_LOTQTY = "requestQty";

	private static final String BUTTON_REFRESH = "refresh";
	private static final String BUTTON_CALCULATE = "calculate";
	private static final String BUTTON_CREATEMLOT = "createMLot";
	private static final String BUTTON_REMOVE = "remove";
	
	protected GlcFormField mixMLotQueryGlcField;
	protected GlcFormField mixMLotCreateGlcField;
	protected ListTableManagerField mixMaterialListField;
	protected ListTableManagerField mixMaterialLotInventoryField;
	protected ListTableManagerField mixMaterialBomInfoField;
	protected EntityFormField mixMaterialLotCreateField;
	protected CustomField subMLotIdField;
	protected ListTableManagerField subMaterialLotConsumeField;
	protected TextField nameField;
	protected TextField lotQtyField;
	
	protected ADManager adManager;
	protected MMManager mmManager;
	protected MLotManager mLotManager;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		mixMLotQueryGlcField = form.getFieldByControlId(FIELD_MIXMLOTQUERYGLC, GlcFormField.class);
		mixMLotCreateGlcField = form.getFieldByControlId(FIELD_MIXMLOTCREATEGLC, GlcFormField.class);
		mixMaterialListField = mixMLotQueryGlcField.getFieldByControlId(FIELD_MIXMATERIALLIST, ListTableManagerField.class);
		mixMaterialLotInventoryField = mixMLotQueryGlcField.getFieldByControlId(FIELD_MIXMATERIALLOTINVENTORY, ListTableManagerField.class);
		mixMaterialBomInfoField = mixMLotCreateGlcField.getFieldByControlId(FIELD_MIXMATERIALBOMINFO, ListTableManagerField.class);
		mixMaterialLotCreateField = mixMLotCreateGlcField.getFieldByControlId(FIELD_MIXMATERIALLOTCREATE, EntityFormField.class);
		subMLotIdField = mixMLotCreateGlcField.getFieldByControlId(FIELD_SUBMLOTID, CustomField.class);
		
		subMaterialLotConsumeField = mixMLotCreateGlcField.getFieldByControlId(FIELD_SUBMATERIALLOTCONSUME, ListTableManagerField.class);
		nameField = mixMaterialLotCreateField.getFieldByControlId(FIELD_NAME, TextField.class);
		lotQtyField = mixMaterialLotCreateField.getFieldByControlId(FIELD_LOTQTY, TextField.class);
		
		subscribeAndExecute(eventBroker, mixMaterialListField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::mixMaterialListSelectionChanged);
		subscribeAndExecute(eventBroker, mixMLotCreateGlcField.getFullTopic(GlcEvent.EVENT_ENTERPRESSED), this::enterPressedAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, mixMLotCreateGlcField.getFullTopic(BUTTON_CALCULATE), this::calculateAdapter);
		subscribeAndExecute(eventBroker, mixMLotCreateGlcField.getFullTopic(BUTTON_CREATEMLOT), this::createMLotAdapter);
		subscribeAndExecute(eventBroker, mixMLotCreateGlcField.getFullTopic(BUTTON_REMOVE), this::removeAdapter);
		
		try {
			adManager = Framework.getService(ADManager.class);
			mmManager = Framework.getService(MMManager.class);	
			mLotManager = Framework.getService(MLotManager.class);	
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
		init();
	}
	
	private void init() {
		try {
			List<RawMaterial> materials = adManager.getEntityList(Env.getOrgRrn(), RawMaterial.class, Integer.MAX_VALUE, "", "");
			mixMaterialListField.setValue(materials);
			mixMaterialListField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void mixMaterialListSelectionChanged(Object object) {
		Event event = (Event) object;
		RawMaterial material = (RawMaterial) event.getProperty(GlcEvent.PROPERTY_DATA);
		if (material != null) {
			List<MLot> mLotList = adManager.getEntityList(Env.getOrgRrn(), MLot.class, Integer.MAX_VALUE, " materialRrn = " + material.getObjectRrn() + "and mainQty > 0", " mLotId ");
			mixMaterialLotInventoryField.setValue(mLotList);
			mixMaterialLotInventoryField.refresh();
			
			Bom bom = mmManager.getActiveBom(Env.getOrgRrn(), material.getName(), material.getVersion(), Bom.BOMUSE_MIX, null);
			if (bom != null) {
				mixMaterialBomInfoField.setValue(bom.getBomLines());	
			} else {
				mixMaterialBomInfoField.setValue(null);
			}
			mixMaterialBomInfoField.refresh();
			
			nameField.setValue(material.getName());
			nameField.refresh();
			
			lotQtyField.setValue(material.getLotSize());
			lotQtyField.refresh();
			
			subMaterialLotConsumeField.setValue(null);
			subMaterialLotConsumeField.refresh();
		}
	}

	private void enterPressedAdapter(Object object) {
		Event event = (Event) object;
		MLot mLot = (MLot) event.getProperty(GlcEvent.PROPERTY_DATA);
		if (mLot != null) {
			if (mLot.getMainQty().compareTo(BigDecimal.ZERO) <= 0) {
				UI.showError(Message.getString("pms.material_qty_less_than_zero"));
				return;
			}	
			
			boolean flag = false;
			List<BomLine> bomLines = (List<BomLine>) mixMaterialBomInfoField.getValue();
			if (CollectionUtils.isNotEmpty(bomLines)) {
				for (BomLine bomLine : bomLines) {
					if (bomLine.getMaterialName().equals(mLot.getMaterialName())) {
						mLot.setAttribute1("1:" + bomLine.getUnitQty());
						flag = true;
					}
				}
			}
			if (!flag) {
				UI.showError(String.format(Message.getString("mm.bom_material_is_not_found"), mLot.getMaterialName()));
				return; 
			}
			
			List<MLot> mLots = (List<MLot>) subMaterialLotConsumeField.getValue();
			if (mLots != null) {
				Optional optMLot = mLots.stream().filter(p -> p.getmLotId().equals(mLot.getmLotId())).findFirst();
				if (optMLot.isEmpty()) {
					mLots.add(mLot);	
				} else {
					return;
				}
			} else {
				mLots = new ArrayList<>();
				mLots.add(mLot);
			}
			subMaterialLotConsumeField.setValue(mLots);
			subMaterialLotConsumeField.refresh();
		}
	}
	
	private void refreshAdapter(Object object) {
		init();
	}
	
	/**
	 * ���㷽��
	 * @param object
	 */
	private void calculateAdapter(Object object) {
		List<BomLine> bomLines = (List<BomLine>) mixMaterialBomInfoField.getValue();
		if (bomLines == null) {
			return;
		}
		List<AbstractBomLine> checkBomLines = Lists.newArrayList();
		checkBomLines.addAll(bomLines);
		
		Object ojb = mixMaterialListField.getListTableManager().getSelectedObject();
		if (ojb != null) {	
			if (mixMaterialLotCreateField.validate()) {
				Material material = (Material) mixMaterialLotCreateField.getValue();
				BigDecimal totalQty = material.getLotSize().multiply(material.getRequestQty());
				
				//alternateBomLineMap��ȡ�����
				Map<String, List<AbstractBomLine>> alternateBomLineMap = Maps.newHashMap();
				//materialQtyMap��ŷ�����ϵ���������
				Map<String, BigDecimal> materialQtyMap = Maps.newHashMap();
				//alternateMaterialQtyMap�������ϵ���������
				Map<String, BigDecimal> alternateMaterialQtyMap = Maps.newHashMap();
				List<String> existAlternateGroup = Lists.newArrayList();
				for (AbstractBomLine checkBomLine : checkBomLines) {					
					if (existAlternateGroup.contains(checkBomLine.getAlternateGroup())) {
						continue;
					}
					if (checkBomLine.getIsAlternate()) {
						existAlternateGroup.add(checkBomLine.getAlternateGroup());
						List<AbstractBomLine> alternateBomLines = AbstractBomLine.getAlternateBomLine(checkBomLines, checkBomLine.getAlternateGroup());
						alternateBomLineMap.put(checkBomLine.getAlternateGroup(), alternateBomLines);					
					} else {	
						List<BigDecimal> materialQty = AbstractBomLine.getChildMaterialQty(totalQty, checkBomLine.getMaterialRrn(), checkBomLines);
						materialQtyMap.put(checkBomLine.getMaterialName(), materialQty.get(0));
					}
				}		
				
				Map<String, BigDecimal> existMaterialQtyMap = Maps.newHashMap();
				List<MLot> mLots = (List<MLot>) subMaterialLotConsumeField.getValue();
				if (CollectionUtils.isNotEmpty(mLots)) {	
					for (MLot mLot : mLots) {
						mLot.setTransMainQty(BigDecimal.ZERO);
					}
					for (MLot mLot : mLots) {
						if (materialQtyMap.containsKey(mLot.getMaterialName())) {
							//Bom�в����ڵ�����ʾ
							if (!materialQtyMap.containsKey(mLot.getMaterialName())) {
								UI.showError(Message.getString("mm.bom_material_is_not_found") + "," 
							+ Message.getString("mm.mlot_id") + ": " + mLot.getmLotId());
								return;
							}
							//�Ȱѷ�����ϵ���������
							if (existMaterialQtyMap.containsKey(mLot.getMaterialName())) {
								BigDecimal existQty = existMaterialQtyMap.get(mLot.getMaterialName());
								BigDecimal requireQty = materialQtyMap.get(mLot.getMaterialName()).subtract(existQty);
								if (mLot.getMainQty().compareTo(requireQty) >= 0) {
									mLot.setTransMainQty(requireQty);
								} else {
									mLot.setTransMainQty(mLot.getMainQty());
								}
								existQty = existQty.add(mLot.getTransMainQty());
								existMaterialQtyMap.put(mLot.getMaterialName(), existQty);
							} else {
								if (mLot.getMainQty().compareTo(materialQtyMap.get(mLot.getMaterialName())) >= 0) {
									mLot.setTransMainQty(materialQtyMap.get(mLot.getMaterialName()));
								} else {
									mLot.setTransMainQty(mLot.getMainQty());
								}
								existMaterialQtyMap.put(mLot.getMaterialName(),  mLot.getTransMainQty());
							}
						}
					}	
									
					//����ȥ���������,���￼����һ�����ϻ���Ϊ������ϵ������
					for (String alternateGroup : alternateBomLineMap.keySet()) {										
						List<AbstractBomLine> alternateBomLines = alternateBomLineMap.get(alternateGroup);
						Map<Long, AbstractBomLine> maps = alternateBomLines.stream().collect(Collectors.toMap(AbstractBomLine::getMaterialRrn, Function.identity()));
						Long alternateMaterialRrn = alternateBomLines.get(0).getMaterialRrn();
						String alternateMaterialName = alternateBomLines.get(0).getMaterialName();
						int i = 0;
						for (MLot mLot : mLots) {
							if (maps.containsKey(mLot.getMaterialRrn())) {
								alternateMaterialRrn = mLot.getMaterialRrn();
								alternateMaterialName = mLot.getMaterialName();
								i++;
							}
						}
						if (i > 1) {
							UI.showError("mm.only_support_same_material_lot_alternate");
							return;
						}
						List<BigDecimal> materialQty = AbstractBomLine.getChildMaterialQty(totalQty, alternateMaterialRrn, checkBomLines);	
						alternateMaterialQtyMap.put(alternateMaterialName, materialQty.get(i));
						for (MLot mLot : mLots) {
							if (alternateMaterialRrn.equals(mLot.getMaterialRrn())) {
								if (mLot.getTransMainQty().compareTo(mLot.getMainQty()) < 0) {
									if (existMaterialQtyMap.containsKey(mLot.getMaterialName())) {
										BigDecimal existQty = existMaterialQtyMap.get(mLot.getMaterialName());
										BigDecimal requireQty = materialQty.get(0).subtract(existQty);
												
										BigDecimal remainder = mLot.getMainQty().subtract(mLot.getTransMainQty());
										if (remainder.compareTo(requireQty) >= 0) {
											BigDecimal qty = mLot.getTransMainQty().add(requireQty);
											mLot.setTransMainQty(qty);
											existMaterialQtyMap.put(mLot.getMaterialName(), existQty.add(requireQty));
										} else {
											mLot.setTransMainQty(mLot.getMainQty());
											existMaterialQtyMap.put(mLot.getMaterialName(), existQty.add(mLot.getMainQty().subtract(mLot.getTransMainQty())));
										}
									} else {
										//remainder��ʣ������ʣ����Ŀ���ǻ��ж��ٿ��Ը��������
										BigDecimal remainder = mLot.getMainQty().subtract(mLot.getTransMainQty());
										if (remainder.compareTo(materialQty.get(0)) >= 0) {
											BigDecimal qty = mLot.getTransMainQty().add(materialQty.get(0));
											mLot.setTransMainQty(qty);
											existMaterialQtyMap.put(mLot.getMaterialName(), materialQty.get(0));
										} else {
											mLot.setTransMainQty(mLot.getMainQty());
											existMaterialQtyMap.put(mLot.getMaterialName(), remainder);
										}
										
									}
								}
							}
						}	
					}
					
					subMaterialLotConsumeField.refresh();
				}
							
				//����Щ���ϻ�������ҳ��������������
				String materialNameStr = "";
				for (String materialName : materialQtyMap.keySet()) {
					BigDecimal materialTotalQty = materialQtyMap.get(materialName);	
					if (existMaterialQtyMap.containsKey(materialName)) {
						BigDecimal existTotalQty = existMaterialQtyMap.get(materialName);
						if (materialTotalQty.compareTo(existTotalQty) > 0) {					
							materialNameStr = materialNameStr + materialName + ",";
						}
					} else {
						materialNameStr = materialNameStr + materialName + ",";
					}	
				}
	
				for (String materialName : alternateMaterialQtyMap.keySet()) {
					BigDecimal materialTotalQty = alternateMaterialQtyMap.get(materialName);	
					if (existMaterialQtyMap.containsKey(materialName)) {
						BigDecimal existTotalQty = existMaterialQtyMap.get(materialName);							
						if (materialTotalQty.compareTo(existTotalQty) > 0) {
							materialNameStr = materialNameStr + materialName + ",";
						}
					} else {
						materialNameStr = materialNameStr + materialName + ",";
					}
				}	
				if (!"".equals(materialNameStr)) {
					materialNameStr = materialNameStr.substring(0, materialNameStr.length() - 1);
					UI.showError(Message.getString("wip.schedule_source_less_need") + Message.getString("mm.material_name") + ": " + materialNameStr);
					return;
				}	
			}
		}	
	}

	private void removeAdapter(Object object) {	
		List<MLot> mLots = (List<MLot>) subMaterialLotConsumeField.getValue();
		if (mLots == null) {
			return;
		}
		List<MLot> checkedMLots = (List<MLot>) (List)subMaterialLotConsumeField.getListTableManager().getCheckedObject();
		if (CollectionUtils.isNotEmpty(checkedMLots)) {
			mLots.removeAll(checkedMLots);
			subMaterialLotConsumeField.setValue(mLots);
			subMaterialLotConsumeField.refresh();
		}		
	}
	
	/**
	 * �������������
	 * @param object
	 */
	private void createMLotAdapter(Object object) {
		try {
			Object ojb = mixMaterialListField.getListTableManager().getSelectedObject();
			if (ojb != null) {	
				Material selectedMaterial = (Material)ojb;
	
				if (mixMaterialLotCreateField.validate()) {
					Material material = (Material) mixMaterialLotCreateField.getValue();
					
					Warehouse warehouse = null;
					if (material.getWarehouseRrn() != null) {
						warehouse = new Warehouse();
						warehouse.setObjectRrn(material.getWarehouseRrn());
						warehouse = (Warehouse) adManager.getEntity(warehouse);
					}	
					
					List<MLot> newMLots = Lists.newArrayList();
					for (int i = 0; i < material.getLotSize().intValue(); i++) {
						MLot mLot = new MLot();
						mLot.setOrgRrn(Env.getOrgRrn());
						mLot.setMaterialRrn(selectedMaterial.getObjectRrn());
						mLot.setMaterialName(selectedMaterial.getName());
						mLot.setMaterialDesc(selectedMaterial.getDescription());
						mLot.setMaterialVersion(selectedMaterial.getVersion());
						mLot.setMaterialType(selectedMaterial.getMaterialType());
						mLot.setMainQty(material.getRequestQty());
						mLot.setTransMainQty(material.getRequestQty());
						newMLots.add(mLot);
					}
					
					List<MLot> mLots = (List<MLot>) subMaterialLotConsumeField.getValue();
					if (CollectionUtils.isNotEmpty(mLots)) {
						for (MLot mLot : mLots) {
							if (mLot.getTransMainQty() == null) {
								UI.showError(Message.getString("please_input_request_material_qty"));
								return;
							}
							if (mLot.getTransMainQty().compareTo(BigDecimal.ZERO) <= 0) {
								UI.showError(Message.getString("mm.consumable_qty_can_not_less_zero"));
								return;
							}
							if (mLot.getTransMainQty().compareTo(mLot.getMainQty()) > 0) {						
								UI.showError(Message.getString("mm.consume_qty_largr_than_qty"));
								return;
							}
						}
					} else {
						UI.showError(Message.getString("mm.please_add_mlot"));
						return;
					}
					
					MLotAction mLotAction = new MLotAction();						
					mLotManager.createMLotByConsume(newMLots, mLots, mLotAction, warehouse, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));					
					mixMaterialListField.getListTableManager().setSelection(new StructuredSelection(new Object[] {ojb}));
				}			
			}	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
}