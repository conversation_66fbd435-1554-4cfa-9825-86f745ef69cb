package com.glory.mes.base.exception;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import com.glory.framework.core.exception.ClientException;
import java.lang.String;
import java.util.Arrays;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageBundleImplementor", date = "2025-07-31T09:49:12+0800")
public class MBasExceptionBundle_$bundle implements MBasExceptionBundle, Serializable {
    private static final long serialVersionUID = 1L;
    protected MBasExceptionBundle_$bundle() {}
    public static final MBasExceptionBundle_$bundle INSTANCE = new MBasExceptionBundle_$bundle();
    protected Object readResolve() {
        return INSTANCE;
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    protected String IdNotGenerate$str() {
        return "mbas-1000: mbas.id_not_generate";
    }
    @Override
    public final ClientException IdNotGenerate() {
        final ClientException result = new ClientException(IdNotGenerate$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    private static void _copyStackTraceMinusOne(final Throwable e) {
        final StackTraceElement[] st = e.getStackTrace();
        e.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
    }
    protected String IdGenerateRuleNotExist$str() {
        return "mbas-1001: mbas.generate_rule_not_exist#{0}";
    }
    @Override
    public final ClientException IdGenerateRuleNotExist(final String transType) {
        final ClientException result = new ClientException(_formatMessage(IdGenerateRuleNotExist$str(), transType));
        _copyStackTraceMinusOne(result);
        return result;
    }
    private String _formatMessage(final String format, final Object... args) {
        final java.text.MessageFormat formatter = new java.text.MessageFormat(format, getLoggingLocale());
        return formatter.format(args, new StringBuffer(), new java.text.FieldPosition(0)).toString();
    }
    protected String GeneratorRuleIsUsed$str() {
        return "mbas-1002: mbas.generator_rule_is_used";
    }
    @Override
    public final ClientException GeneratorRuleIsUsed() {
        final ClientException result = new ClientException(GeneratorRuleIsUsed$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdGeneratorRuleNotFound$str() {
        return "mbas-1003: mbas.idgenerator_rule_not_found";
    }
    @Override
    public final ClientException IdGeneratorRuleNotFound() {
        final ClientException result = new ClientException(IdGeneratorRuleNotFound$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdGeneratorUnSupportParamListInBatch$str() {
        return "mbas-1004: mbas.idgenerator_unsupport_paramlist_in_batch";
    }
    @Override
    public final ClientException IdGeneratorUnSupportParamListInBatch() {
        final ClientException result = new ClientException(IdGeneratorUnSupportParamListInBatch$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdGeneratorUnSupportMultiseqInBatch$str() {
        return "mbas-1005: mbas.idgenerator_unsupport_multiseq_in_batch";
    }
    @Override
    public final ClientException IdGeneratorUnSupportMultiseqInBatch() {
        final ClientException result = new ClientException(IdGeneratorUnSupportMultiseqInBatch$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdGeneratorContextNotFound$str() {
        return "mbas-1006: mbas.idgenerator_context_not_found";
    }
    @Override
    public final ClientException IdGeneratorContextNotFound() {
        final ClientException result = new ClientException(IdGeneratorContextNotFound$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String MergeRuleIsUsed$str() {
        return "mbas-1007: mbas.merge_rule_is_used";
    }
    @Override
    public final ClientException MergeRuleIsUsed() {
        final ClientException result = new ClientException(MergeRuleIsUsed$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdGeneratorSizeRule$str() {
        return "mbas-1008: mbas.idgenerator_size_rule";
    }
    @Override
    public final ClientException IdGeneratorSizeRule() {
        final ClientException result = new ClientException(IdGeneratorSizeRule$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdGeneratorHasBeenUsedCannotDelete$str() {
        return "mbas-1009: mbas.idgenerator_has_been_used_cannot_delete";
    }
    @Override
    public final ClientException IdGeneratorHasBeenUsedCannotDelete() {
        final ClientException result = new ClientException(IdGeneratorHasBeenUsedCannotDelete$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String ShiftIsExist$str() {
        return "mbas-1100: mbas.shift_is_exist";
    }
    @Override
    public final ClientException ShiftIsExist() {
        final ClientException result = new ClientException(ShiftIsExist$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String TeamIsExist$str() {
        return "mbas-1101: mbas.team_is_exist";
    }
    @Override
    public final ClientException TeamIsExist() {
        final ClientException result = new ClientException(TeamIsExist$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdGenerateRuleNotFound$str() {
        return "mbas-1102: mbas.idgenerator_rule_not_found#{0}";
    }
    @Override
    public final ClientException IdGenerateRuleNotFound(final String transType) {
        final ClientException result = new ClientException(_formatMessage(IdGenerateRuleNotFound$str(), transType));
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String MergeRuleNotFound$str() {
        return "mbas-1103: mbas.merge_rule_not_found";
    }
    @Override
    public final ClientException MergeRuleNotFound() {
        final ClientException result = new ClientException(MergeRuleNotFound$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String TeamNotFound$str() {
        return "mbas-1104: mbas.team_not_found";
    }
    @Override
    public final ClientException TeamNotFound() {
        final ClientException result = new ClientException(TeamNotFound$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdRuleDateRuleLineError$str() {
        return "mbas-1105: mbas.idgenerator_rule_date_rule_line_error#{1}#{2}#{3}";
    }
    @Override
    public final ClientException IdRuleDateRuleLineError(final String generatorRuleName, final String seqNo, final String errorInfo) {
        final ClientException result = new ClientException(_formatMessage(IdRuleDateRuleLineError$str(), generatorRuleName, seqNo, errorInfo));
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String CycleRuleCanNotGenerateId$str() {
        return "mbas-1106: mbas.cycle_rule_can_not_generate_id";
    }
    @Override
    public final ClientException CycleRuleCanNotGenerateId() {
        final ClientException result = new ClientException(CycleRuleCanNotGenerateId$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String CycleRuleIllegal$str() {
        return "mbas-1107: mbas.id_cycle_rule_illegal";
    }
    @Override
    public final ClientException CycleRuleIllegal() {
        final ClientException result = new ClientException(CycleRuleIllegal$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdRuleSequenceRuleLineError$str() {
        return "mbas-1108: mbas.idgenerator_rule_sequence_rule_line_error#{1}#{2}#{3}";
    }
    @Override
    public final ClientException IdRuleSequenceRuleLineError(final String generatorRuleName, final String seqNo, final String errorInfo) {
        final ClientException result = new ClientException(_formatMessage(IdRuleSequenceRuleLineError$str(), generatorRuleName, seqNo, errorInfo));
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdRuleXSequenceRuleLineError$str() {
        return "mbas-1109: mbas.idgenerator_rule_XSequence_rule_line_error#{1}#{2}#{3}";
    }
    @Override
    public final ClientException IdRuleXSequenceRuleLineError(final String generatorRuleName, final String seqNo, final String errorInfo) {
        final ClientException result = new ClientException(_formatMessage(IdRuleXSequenceRuleLineError$str(), generatorRuleName, seqNo, errorInfo));
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdRuleVariableRuleLineError$str() {
        return "mbas-1110: mbas.idgenerator_rule_variable_rule_line_error#{1}#{2}#{3}";
    }
    @Override
    public final ClientException IdRuleVariableRuleLineError(final String generatorRuleName, final String seqNo, final String errorInfo) {
        final ClientException result = new ClientException(_formatMessage(IdRuleVariableRuleLineError$str(), generatorRuleName, seqNo, errorInfo));
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdMoreThanSize$str() {
        return "mbas-1111: mbas.id_more_than_size";
    }
    @Override
    public final ClientException IdMoreThanSize() {
        final ClientException result = new ClientException(IdMoreThanSize$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String GeneratorXseqXparse$str() {
        return "mbas-1112: mbas.generator_xseq_xparse";
    }
    @Override
    public final ClientException GeneratorXseqXparse() {
        final ClientException result = new ClientException(GeneratorXseqXparse$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String IdgeneratorDBValueError$str() {
        return "mbas-1113: mbas.idgenerator_dbvalue_error";
    }
    @Override
    public final ClientException IdgeneratorDBValueError() {
        final ClientException result = new ClientException(IdgeneratorDBValueError$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String VersionControlStateNotAllow$str() {
        return "mbas-1114: mbas.versioncontrol_state_not_allow";
    }
    @Override
    public final ClientException VersionControlStateNotAllow() {
        final ClientException result = new ClientException(VersionControlStateNotAllow$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String LocationHasChildren$str() {
        return "mbas-1115: mbas.location_has_children";
    }
    @Override
    public final ClientException LocationHasChildren() {
        final ClientException result = new ClientException(LocationHasChildren$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String ParameterIsUsed$str() {
        return "mbas-1116: mbas.parameter_is_used";
    }
    @Override
    public final ClientException ParameterIsUsed() {
        final ClientException result = new ClientException(ParameterIsUsed$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
    protected String GetAdSequenceError$str() {
        return "mbas-2001: mbas.get_ad_sequence";
    }
    @Override
    public final ClientException GetAdSequenceError() {
        final ClientException result = new ClientException(GetAdSequenceError$str());
        _copyStackTraceMinusOne(result);
        return result;
    }
}
