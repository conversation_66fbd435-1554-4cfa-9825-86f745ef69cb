package com.glory.mes.wip.mm.warehousepart;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.mes.mm.warehouse.WarehouseBlock;

public class WarehousePartEditor extends EntityEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.mm.warehousepart.WarehousePartEditor";

	@Override
	protected void createBlock(ADTable adTable) {
		block = new WarehousePartBlock(new ListTableManager(adTable));
	}
	
}
