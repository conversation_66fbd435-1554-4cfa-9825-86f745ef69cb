package com.glory.mes.wip.mm.warehousepart;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class WarehousePartForm extends EntityForm {
	
	private static final Logger logger = Logger.getLogger(WarehousePartForm.class);
	public static final String FIELD_ID_PART = "attributeValues";
	
	public WarehousePartForm (Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}
	
	@Override
	public void addFields() {
		super.addFields();
		
		TableSelectField activePartField = null;
		try{
			ADManager entityManager = Framework.getService(ADManager.class);
			
			ADTable adTableActivePart = entityManager.getADTable(Env.getOrgRrn(), "MMWareHouseActivePart");
			ListTableManager tableManagerTarget = new ListTableManager(adTableActivePart, true);

			activePartField = createTableSelectField(FIELD_ID_PART, "", tableManagerTarget, 22);
			addField(FIELD_ID_PART, activePartField);
		} catch (Exception e){
			logger.error("WareHousePartForm : Init tablelist", e);
			ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
	}
	
	@Override
	public boolean saveToObject() {
		if (object != null){
			IField in = fields.get(FIELD_ID_PART);
			PropertyUtil.setProperty(object, in.getId(), in.getValue());
			
			return true;
		}
		return false;
	}
	
	@Override
	public void loadFromObject() {
		if (object != null) {
			IField in = fields.get(FIELD_ID_PART);
			in.setValue(PropertyUtil.getPropertyForIField(object, in.getId()));
			refresh();
		}
	}
	
}