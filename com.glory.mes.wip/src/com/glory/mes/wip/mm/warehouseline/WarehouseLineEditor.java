package com.glory.mes.wip.mm.warehouseline;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class WarehouseLineEditor extends EntityEditor{
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.mm.warehouseline.WarehouseLineEditor";

	@Override
	protected void createBlock(ADTable adTable) {
		block = new WarehouseLineBlock(new ListTableManager(adTable));
	}
	
}
