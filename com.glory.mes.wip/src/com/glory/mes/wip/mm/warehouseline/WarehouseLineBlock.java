package com.glory.mes.wip.mm.warehouseline;

import org.apache.log4j.Logger;
import org.eclipse.ui.forms.DetailsPart;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class WarehouseLineBlock extends EntityBlock {
	private static final Logger logger = Logger.getLogger(WarehouseLineBlock.class);
	EntityProperties page;

	public WarehouseLineBlock(ListTableManager tableManager){
		super(tableManager);
	}
	
	@Override
	protected void registerPages(DetailsPart detailsPart) {
		try{
			ADTable adTable = getTableManager().getADTable();
			Class<?> klass = Class.forName(adTable.getModelClass());
			page = new WarehouseLineProperties();
			page.setTable(adTable);
			page.setMasterParent(this); 
			detailsPart.registerPage(klass, page);
			initRefresh();
		} catch (Exception e){
			logger.error("WarehouseEquipmentBlock : registerPages ", e);
		}
	}
	
	public void setFocus() {
		((WarehouseLineProperties)page).setFocus();
	}
	
	@Override
	public void queryAdapter() {
		super.queryAdapter();
	}
}
