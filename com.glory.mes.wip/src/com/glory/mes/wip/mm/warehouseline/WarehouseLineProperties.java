package com.glory.mes.wip.mm.warehouseline;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.Line;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.wip.client.MLotManager;
import com.glory.mes.wip.mm.WarehouseLine;
import com.glory.framework.core.exception.ExceptionBundle;

public class WarehouseLineProperties extends EntityProperties {
	
	private WarehouseLineForm warehouseLineForm;

	public WarehouseLineProperties() {
		super();
    }
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		EntityForm itemForm;
		String tabName = tab.getName();
		if ("WarehoseLineManagement".equalsIgnoreCase(tabName)) {
		    warehouseLineForm = new WarehouseLineForm(composite, SWT.NONE, tab, mmng);
			return warehouseLineForm;
		} else {
			itemForm = new EntityForm(composite, SWT.NONE, tab, mmng);
		}
		return itemForm;
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	@SuppressWarnings("unchecked")
    @Override
	protected void saveAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
				}
			}
			if (saveFlag) {
			    Warehouse warehouse = (Warehouse) getAdObject();
				if (warehouse == null || warehouse.getObjectRrn() == null) {
					UI.showInfo(Message.getString("mm.select_warehouse"));
					return;
				}
				List<Line> selectLines = (List<Line>) getField("attributeValues").getValue();
				if (selectLines == null) {
				    selectLines = new ArrayList<Line>();
				}
				
				List<WarehouseLine> warehouseLines = new ArrayList<WarehouseLine>();
				for (Line line : selectLines) {
				    WarehouseLine warehouseLine = new WarehouseLine();
				    warehouseLine.setLineId(line.getName());
				    warehouseLine.setLineRrn(line.getObjectRrn());
				    warehouseLine.setWarehouseId(warehouse.getWarehouseId());
				    warehouseLine.setWarehouseRrn(warehouse.getObjectRrn());
				    warehouseLines.add(warehouseLine);
				}
				MLotManager mlotManager = Framework.getService(MLotManager.class);
				mlotManager.saveLineWarehouse(warehouse, warehouseLines, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// ������ʾ��
				refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	public void refresh() {
		super.refresh();
		try {
		    Warehouse warehouse = (Warehouse) getAdObject();
			if (warehouse != null && warehouse.getObjectRrn() != null) {
			    MLotManager mlotManager = Framework.getService(MLotManager.class);
				List<Line> warehouseLines = mlotManager.getLineWarehouseByWarehouse(warehouse, Env.getSessionContext());
				TableSelectField selectField = (TableSelectField) getField("attributeValues");
				selectField.setValue(warehouseLines);
				selectField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}
