package com.glory.mes.wip.pp.wo.form;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.part.PartFlowTreeField;

public class WorkOrderFlowPartTreeField extends PartFlowTreeField{
	
	private static final Logger logger = Logger.getLogger(PartFlowTreeField.class);
	private String altProcessName;
	private Long altProcessVersion;
	
	public WorkOrderFlowPartTreeField(String id, String label, TreeViewerManager manager) {
		super(id, label, manager);
	}
	
	@Override
	public void refresh() {
		if (getValue() != null) {
			Part part = (Part)getValue();
			if (part != null) {
				try {
					List<Process> processes = new ArrayList<Process>();
					PrdManager prdManager = Framework.getService(PrdManager.class);
					Process process = new Process();
					// �Ƿ�ʹ�ÿ�ѡ���� 
	        		if (part.getIsAlternateProcess() && !StringUtil.isEmpty(altProcessName) && altProcessVersion != null) {
	        			process.setOrgRrn(Env.getOrgRrn());
        				process.setName(altProcessName);
        				process.setVersion(altProcessVersion);
    					process = (Process) prdManager.getSimpleProcessDefinition(process);
	        		} else {	
	        			process = (Process)prdManager.getPartProcess(part);
	        		}
					
					processes.add(process);
					manager.setInput(processes);
					viewer.expandToLevel(2);
				} catch(Exception e) {
					logger.error("Error: " + e);
				}
			}
		}
	}

	public String getAltProcessName() {
		return altProcessName;
	}

	public void setAltProcessName(String altProcessName) {
		this.altProcessName = altProcessName;
	}

	public Long getAltProcessVersion() {
		return altProcessVersion;
	}

	public void setAltProcessVersion(Long altProcessVersion) {
		this.altProcessVersion = altProcessVersion;
	}
}
