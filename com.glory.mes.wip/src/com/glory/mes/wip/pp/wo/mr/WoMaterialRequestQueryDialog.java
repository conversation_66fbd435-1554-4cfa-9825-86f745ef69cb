package com.glory.mes.wip.pp.wo.mr;

import org.eclipse.swt.widgets.Shell;

import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.query.QueryDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.core.util.StringUtil;

public class WoMaterialRequestQueryDialog extends QueryDialog {
	
	protected WoMaterialRequestManagerEditor editor;
	
	public WoMaterialRequestQueryDialog(Shell parent, ListTableManager tableManager, IRefresh iRefresh, WoMaterialRequestManagerEditor editor) {
		super(parent, tableManager, iRefresh);
		this.editor = editor;
	}
	
	@Override
	protected void okPressed() {
		super.okPressed();
		editor.init(StringUtil.relpaceWildcardCondition(getWhereClause()));
	}
}
