package com.glory.mes.wip.pp.wo.subbybom;


import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.wip.pp.wo.WorkOrderProperties;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomContext;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomDialog;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomWizard;

public class SubByBomWorkOrderProperties extends WorkOrderProperties {

	public SubByBomWorkOrderProperties() {
		super();
	}
	
	@Override
	protected void bomAdapter() {
        try {
            if (getAdObject().getObjectRrn() != null) {
	            WorkOrderBomContext context = new WorkOrderBomContext();
	            context.setWorkOrder((WorkOrder)getAdObject());
	            context.setExpandProduct(true);
	            SubByBomWorkOrderBomWizard orderBomWizard = new SubByBomWorkOrderBomWizard(context, "WorkOrderBom");
	            WorkOrderBomDialog dialog = new WorkOrderBomDialog(Display.getCurrent().getActiveShell(), orderBomWizard);
	            if (dialog.open() == Dialog.OK) {
					refreshAdapter();
				}
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	@Override
	public ADBase save(ADBase obj) throws Exception {
		WorkOrder workOrder = (WorkOrder) obj;
		workOrder.setDocType("MO");//WorkOrder.PROWORKORDER_TYPE
		return super.save(workOrder);
	}
}
