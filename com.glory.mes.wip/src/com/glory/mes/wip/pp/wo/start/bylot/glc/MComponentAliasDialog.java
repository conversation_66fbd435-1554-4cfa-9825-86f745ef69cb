package com.glory.mes.wip.pp.wo.start.bylot.glc;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.google.common.collect.Sets;
import com.glory.framework.core.exception.ExceptionBundle;

public class MComponentAliasDialog extends BaseTitleDialog {
	
	private static int MIN_DIALOG_WIDTH = 800;
	private static int MIN_DIALOG_HEIGHT = 600;
	
	public static final String TABLE_NAME = "WIPLotStartComponentSetupAlias";
	
	public ListEditorTableManager tableManager;
	public List<MComponentUnit> selectComponentUnits = new ArrayList<MComponentUnit>();
	private List<MComponentUnit> mComponentUnits;
	
	public MComponentAliasDialog(List<MComponentUnit> mComponentUnits) {
		super();
		this.mComponentUnits = mComponentUnits;
	}

	@Override
	protected void constrainShellSize() {
		super.constrainShellSize();
		getShell().setText(Message.getString("wip.component_alias_steup"));
	}
	
	@Override
    protected Point getInitialSize() {
        Point shellSize = super.getInitialSize();
        return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
                Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
    }
	
	protected void createFormContent(Composite parent) {
		Composite content = new Composite(parent, SWT.NONE);
		content.setLayout(new GridLayout(1, false));
		content.setLayoutData(new GridData(GridData.FILL_BOTH));
		content.setBackground(new Color(Display.getCurrent(), 255, 255, 255));

		try {
			ADManager entityManager = (ADManager)Framework.getService(ADManager.class);	
			ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new ListEditorTableManager(adTable, false);
			tableManager.newViewer(content);
			tableManager.setInput(mComponentUnits);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void okPressed() {
		try {
			List<Object> os = (List<Object>) tableManager.getInput();
			if (os.size() > 0) {
				Set<String> set = Sets.newHashSet();
				for (Object o : os) {
					MComponentUnit mComponentUnit = (MComponentUnit)o;	
					if (!StringUtil.isEmpty(mComponentUnit.getmComponentAlias())) {
						set.add(mComponentUnit.getmComponentAlias());	
					}				
					selectComponentUnits.add(mComponentUnit);	
				}
				if (set.size() != os.size()) {
					UI.showError(Message.getString("wip.component_alias_is_same_or_null"));
					selectComponentUnits.clear();
					return;
				}
			}				
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		close();
	}

	public List<MComponentUnit> getSelectComponentUnits() {
		return selectComponentUnits;
	}

	public void setSelectComponentUnits(List<MComponentUnit> selectComponentUnits) {
		this.selectComponentUnits = selectComponentUnits;
	}

	@Override
	protected Control buildView(Composite parent) {
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
        setTitle(Message.getString(ExceptionBundle.bundle.CommonAdd()));
		createFormContent(parent);
		return parent;
	}

	
}
