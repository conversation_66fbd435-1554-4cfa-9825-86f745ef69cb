package com.glory.mes.wip.pp.wo.start;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityAttributeForm;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.pp.model.WorkOrderSource;
import com.glory.mes.wip.mm.MaterialRequisitionLine;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.pp.wo.WorkOrderProperties;
import com.glory.mes.wip.pp.wo.form.WorkOrderLotForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderLotStartByDurableForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderMLotSelectByBomForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderMrDetailForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderMrForm;
import com.glory.framework.core.exception.ExceptionBundle;

public class WoStartProperties extends WorkOrderProperties {
    
	public WorkOrderLotForm workOrderLotForm;
	public WorkOrderLotStartByDurableForm workOrderLotStartByDurableForm;

	protected ToolItem itemStart;
	
	public static String KEY_START = "start";

	public WoStartProperties() {
		super();
	}
	
	@Override
	protected IForm getForm(Composite composite, ADTab tab) {
		if (tab.getName().equals(EntityAttributeForm.NAME)) {
			return new EntityAttributeForm(composite, SWT.NONE, table.getModelName(), this.getAdObject(), tab.getGridY().intValue(), mmng);
		} else if (tab.getName().equals(TAB_LOT)) {
			workOrderLotForm = new WorkOrderLotForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
			return workOrderLotForm;
		} else if (tab.getName().equals(TAB_MLOT_SELECT_BY_BOM)) {
			formMLotByBom = new WorkOrderMLotSelectByBomForm(composite, SWT.NONE, this.getAdObject(), tab, mmng, false);
			return formMLotByBom;
		} else if (tab.getName().equals(TAB_LOT_START_BY_DURABLE)) {
			workOrderLotStartByDurableForm = new WorkOrderLotStartByDurableForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
			return workOrderLotStartByDurableForm;
		} else if (tab.getName().equals(TAB_MR)) {
			return new WorkOrderMrForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		}

		EntityForm entityFrom = new EntityForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		entityFrom.setADManager(getADManger());
		return entityFrom;
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemStart(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemStart(ToolBar tBar) {
		itemStart = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_START);
		itemStart.setText(Message.getString("common.start"));
		itemStart.setImage(SWTResourceCache.getImage("newlot_start"));
		itemStart.setEnabled(false);
		itemStart.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				startAdapter();
			}
		});
	}

	protected void startAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			WorkOrder workOrder = (WorkOrder) getAdObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				PpManager ppManager = Framework.getService(PpManager.class);
				ADManager adManager = Framework.getService(ADManager.class);
				
				if (workOrderLotForm != null) {
					//��������������Ͷ��
					if (formMLotByBom != null) {
						List<MLot> sourceMLots = (List<MLot>)formMLotByBom.getValue();
						ppManager.startWorkOrder(workOrder, workOrder.getWorkOrderLots(), sourceMLots, null, false, Env.getSessionContext());
					} else {
						ppManager.startWorkOrder(workOrder, null, false, Env.getSessionContext());
					}
				} else if (workOrderLotStartByDurableForm != null) {
					BigDecimal totalQty = BigDecimal.ZERO;
					//�����ؾߵ�����Ͷ��,֧�ֲ���Ͷ��
					List<WorkOrderLot> workOrderLots = workOrderLotStartByDurableForm.getWorkOrderLots();
					List<WorkOrderLot> startWorkOrderLots = new ArrayList<WorkOrderLot>();
					for (WorkOrderLot workOrderLot : workOrderLots) {
						if (!StringUtil.isEmpty(workOrderLot.getDurableId())) {
							totalQty = totalQty.add(workOrderLot.getMainQty());
							startWorkOrderLots.add(workOrderLot);
						}
					}
	
					if (startWorkOrderLots == null || startWorkOrderLots.size() == 0) {
						UI.showInfo(Message.getString("wip.lot_is_not_attach_durable"));
						return;
					}
					
					if (formMLotByBom != null) {
						List<MLot> sourceMLots = (List<MLot>)formMLotByBom.getValue();
						ppManager.startLotByWorkOrder(workOrder, startWorkOrderLots, sourceMLots, Lot.UNIT_TYPE_COMPONENT,
								Boolean.TRUE, Env.getSessionContext());
					} else {
						//����BOM��������������
						List<MaterialRequisitionLine> materialRequisitionLines = ppManager.getMaterialRequisitionLines(workOrder, Env.getSessionContext());
						Map<String, BigDecimal> requiredQtyMap = getMLotRequiredQty(totalQty, materialRequisitionLines);
						
						List<WorkOrderSource> workOrderSources = adManager.getEntityList(Env.getOrgRrn(), WorkOrderSource.class, Env.getMaxResult(),
								" woRrn = '" + workOrder.getObjectRrn() + "'", "");
								
						List<MLot> sourceMLots = new ArrayList<MLot>();
						for (String materialName : requiredQtyMap.keySet()) {
							BigDecimal requiredQty = requiredQtyMap.get(materialName);
							if (requiredQty != null) {
								for (WorkOrderSource workOrderSource : workOrderSources) {
									if (materialName.equals(workOrderSource.getSourceMaterialName())) {
										MLot sourceMLot = new MLot();
										sourceMLot.setObjectRrn(workOrderSource.getSourceMLotRrn());
										sourceMLot = (MLot)adManager.getEntity(sourceMLot);
										//TODO δ������������������,���һ��
										sourceMLot.setTransMainQty(requiredQty);
										sourceMLot.setTransIsReserved(workOrderSource.getIsReserved());
										
										sourceMLots.add(sourceMLot);
										break;
									}
								}
							}
						}
						ppManager.startLotByWorkOrder(workOrder, startWorkOrderLots, sourceMLots, Lot.UNIT_TYPE_COMPONENT,
								Boolean.TRUE, Env.getSessionContext());
					}
				}
				UI.showInfo(Message.getString("common.start_successed"));
				setAdObject(new WorkOrder());
				refresh();
				getMasterParent().refresh();
			} else {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public Map<String, BigDecimal> getMLotRequiredQty(BigDecimal startLotTotalQty, List<MaterialRequisitionLine> materialRequisitionLines) {
		Map<String, BigDecimal> requiredQtyMap = new HashMap<String, BigDecimal>();
		for (MaterialRequisitionLine mrl : materialRequisitionLines) {
			BigDecimal subMaterialQty = mrl.getUnitQty().multiply(startLotTotalQty);
			requiredQtyMap.put(mrl.getMaterialName(), subMaterialQty);
		}
		return requiredQtyMap;
	}
	
	@Override
	public void statusChanged(String newStatus, String holdStatus) {
		if (newStatus == null || "".equals(newStatus.trim())) {
			itemStart.setEnabled(false);
		} else if (Documentation.STATUS_APPROVED.equals(newStatus.trim())
				|| WorkOrder.STATUS_STARTED.equals(newStatus.trim())) {
			itemStart.setEnabled(true);
			checkHoldStatus();
		} else {
			itemStart.setEnabled(false);
		}
	}

	public void checkHoldStatus() {
		try {
			WorkOrder workOrder = (WorkOrder) getAdObject();
			if (workOrder == null || workOrder.getObjectRrn() == null) {
				return;
			}
			if (WorkOrder.HOLDSTATE_ON.equals(workOrder.getHoldState())) {
				itemStart.setEnabled(false);
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}
}