package com.glory.mes.wip.pp.wo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.query.SearchDialog;
import com.glory.framework.base.entitymanager.query.SearchMultiDialog;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.wip.client.MLotManager;

public class WorkOrderMLotSearchDialog extends SearchDialog {

    private List<BomLine> bomLines;
    private BigDecimal generationLotQty;
    protected Long materialRrn;

    public WorkOrderMLotSearchDialog(ListTableManager listTableManager, String initWhereClause, String baseWhereClause) {
    	super(listTableManager, initWhereClause, baseWhereClause);
	}

    @Override
    protected void refresh() {
        try {
            IField warehouseField = queryForm.getFields().get("transWarehouseRrn");
            List<MLot> availableMLots = new ArrayList<MLot>();
            if (warehouseField.getValue() != null) {
                    MLotManager mlotManager = Framework.getService(MLotManager.class);
                    List<MLot> getAvailableMLots = mlotManager.getMLotsByWarehouseId(Long.valueOf(warehouseField.getValue().toString()) , Env.getSessionContext());
                    for (MLot mlot : getAvailableMLots) {
                    	mlot.setTransWarehouseRrn(Long.valueOf(warehouseField.getValue().toString()));
                    	if (materialRrn != null) {
							if (mlot.getMaterialRrn().equals(materialRrn)) {
								availableMLots.add(mlot);
							}
						}else {
							availableMLots.add(mlot);
						}
                    
                }
                tableManager.getInput().clear();
                tableManager.setInput(availableMLots);
            }else {
				super.refresh();
			}
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
    
	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == 0) {
			List<Object> objs = tableManager.getCheckedObject();
			if (objs != null && objs.size() > 0) {
				for (Object obj : objs) {
					selectedItems.add((ADBase) obj);
				}
				okPressed();
			}
		} else {
			super.buttonPressed(buttonId);
		}
	}

    public List<BomLine> getBomLines() {
        return bomLines;
    }

    public void setBomLines(List<BomLine> bomLines) {
        this.bomLines = bomLines;
    }
}
