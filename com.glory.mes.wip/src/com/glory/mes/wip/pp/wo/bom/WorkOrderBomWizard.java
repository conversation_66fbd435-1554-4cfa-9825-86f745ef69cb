package com.glory.mes.wip.pp.wo.bom;

import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.wip.mm.MaterialRequisition;

public class WorkOrderBomWizard extends FlowWizard {

	private WorkOrderBomContext context;
	private String category;

	public WorkOrderBomWizard(WorkOrderBomContext context, String category) {
		super(category);
		this.context = context;
		this.category = category;
	}

	@Override
	public boolean performFinish() {
		try {
			MaterialRequisition materialRequisition = new MaterialRequisition();
	        materialRequisition.setPartName(context.getWorkOrder().getPartName());
	        materialRequisition.setPartVersion(context.getWorkOrder().getPartVersion());
	        materialRequisition.setWoRrn(context.getWorkOrder().getObjectRrn());
	        materialRequisition.setWoId(context.getWorkOrder().getDocId());
	        materialRequisition.setCustomerCode(context.getWorkOrder().getCustomerCode());
	        materialRequisition.setWoMainQty(context.getWorkOrder().getMainQty());
	        materialRequisition.setWoSubQty(context.getWorkOrder().getSubQty());
	        materialRequisition.setDocStatus(Documentation.STATUS_CREATED);
	       
	        PpManager ppManager = Framework.getService(PpManager.class);
	        ppManager.saveWorkOrderBomAndMaterialRequisition(context.getWorkOrder(), context.getWorkOrderBomLines(),
	        		materialRequisition, context.getMaterialRequisitionLines(), Env.getSessionContext());
	        
	        UI.showInfo(Message.getString("base.bom_setup_successful"));
	        return true;
		} catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
		
		return false;
	}

	public void setContext(WorkOrderBomContext context) {
		this.context = context;
	}

	public WorkOrderBomContext getContext() {
		return context;
	}

}
