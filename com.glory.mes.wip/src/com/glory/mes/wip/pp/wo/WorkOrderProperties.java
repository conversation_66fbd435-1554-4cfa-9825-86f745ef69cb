package com.glory.mes.wip.pp.wo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IFormPart;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.common.excel.ExcelDocument;
import com.glory.common.excel.write.PrintUtil;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADSysParameter;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityAttributeForm;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.model.MaterialAltProcess;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.pp.model.WorkOrderSource;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.model.WorkOrderContext;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.mm.MaterialRequisition;
import com.glory.mes.wip.mm.MaterialRequisitionLine;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomContext;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomDialog;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomWizard;
import com.glory.mes.wip.pp.wo.excel.WorkOrderWriter;
import com.glory.mes.wip.pp.wo.form.WorkOrderBomForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderFlowForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderLotForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderLotGenerateForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderMLotSelectByBomForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderMLotSelectForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderMrDetailForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderMrForm;
import com.glory.mes.wip.pp.wo.form.WorkOrderSubWoForm;
import com.glory.mes.wip.pp.wo.sub.GeneratorWoLotForm;
import com.glory.mes.wip.pp.wo.sub.MaterialReqForm;
import com.glory.framework.core.exception.ExceptionBundle;


public class WorkOrderProperties extends EntityProperties {

	public static String TAB_BOM = "PPWorkOrderBom";
	public static String TAB_LOT = "PPWorkOrderLot";
	public static String TAB_LOT_GENERATE = "PPWorkOrderLotGenerate";
	public static String TAB_MLOT = "PPWorkOrderMLot";
	public static String TAB_MLOT_SELECT = "PPWorkOrderMLotSelect";
	public static String TAB_MR = "PPWorkOrderMr";
	public static String TAB_MR_DETAIL = "PPWorkOrderMrDetails";
	public static String TAB_SUBWO = "PPWorkOrderSubWo";
	
	public static String TAB_MLOT_SELECT_BY_BOM = "PPWorkOrderMLotSelectByBom";
	public static String TAB_LOT_START_BY_DURABLE = "PPWorkOrderLotStartByDurable";
	public static String TAB_GENERATOR_LOT = "generatorLot";
	public static String TAB_MATERIAL_REQ = "materialReq";
	public static String TAB_PART_FLOW = "PPPartFlow";
	
	public static String KEY_APPROVE = "approve";
	public static String KEY_HOLD = "hold";
	public static String KEY_BOM = "bom";
	public static String KEY_CLOSE = "close";
	
	public WorkOrderLotGenerateForm formLotGenerate;
	public WorkOrderMLotSelectByBomForm formMLotByBom;
	public WorkOrderFlowForm formFlow;

	public ToolItem itemApprove;
	public ToolItem itemHold;
	public ToolItem itemClose;

	public ToolItem itemBom;
	public boolean isVerQty = true;
	public ToolItem printItem;
	private RefTableField fieldPart;
	private RefTableField fieldreworkProcess;
	private RefTableField refFieldPartVerion;
	private TextField fieldPartVerion;
	
	public boolean isLoading = false;
	
	public WorkOrderProperties() {
		super();
	}
	
	@Override
	public void createContents(Composite parent) {
		super.createContents(parent);
		if (super.getField("partName") instanceof RefTableField) {
			fieldPart = (RefTableField) super.getField("partName");
			fieldPart.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					try {
						if (formFlow != null && !isLoading) {
							if (fieldPart.getData() != null) {
								Part part = (Part)fieldPart.getData();
								formFlow.internalRefresh(part.getName(), part.getVersion());
							} else {
								formFlow.internalRefresh(fieldPart.getText(), null);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
		}

		if (super.getField("partVersion") instanceof TextField) {
			fieldPartVerion = (TextField) super.getField("partVersion");
		} else if (super.getField("partVersion") instanceof RefTableField) {
			refFieldPartVerion = (RefTableField) super.getField("partVersion");
			refFieldPartVerion.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					try {
						if (formFlow != null && !isLoading) {
							if (refFieldPartVerion != null && !StringUtil.isEmpty(refFieldPartVerion.getText()) && refFieldPartVerion.getText().matches("^[0-9]*$")) {
								formFlow.internalRefresh(fieldPart.getText(), Long.valueOf(refFieldPartVerion.getText()));
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
		}
		
		if (super.getField("reworkProcessName") instanceof RefTableField) {
			fieldreworkProcess = (RefTableField) super.getField("reworkProcessName");
			fieldreworkProcess.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					try {
						if (formFlow != null && !isLoading) {
							if (fieldreworkProcess.getData() != null) {
								MaterialAltProcess altProcess = (MaterialAltProcess)fieldreworkProcess.getData();
								formFlow.internalRefresh(altProcess.getPartName(), altProcess.getPartVersion(), altProcess.getProcessName(), altProcess.getProcessVersion());
							} else {
								formFlow.internalRefresh(fieldPart.getText(), null);
							} 
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			});
		}
	}
	
	@Override
	protected IForm getForm(Composite composite, ADTab tab) {
		if (tab.getName().equals(EntityAttributeForm.NAME)) {
			return new EntityAttributeForm(composite, SWT.NONE, table.getModelName(), this.getAdObject(), tab.getGridY().intValue(), mmng);
		} else if (tab.getName().equals(TAB_BOM)) {
			return new WorkOrderBomForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		} else if (tab.getName().equals(TAB_LOT)) {
			return new WorkOrderLotForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		} else if (tab.getName().equals(TAB_LOT_GENERATE)) {
			formLotGenerate = new WorkOrderLotGenerateForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
			return formLotGenerate;
		} else if (tab.getName().equals(TAB_MLOT)) {
			return new WorkOrderMLotSelectForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		} else if (tab.getName().equals(TAB_MLOT_SELECT_BY_BOM)) {
			formMLotByBom = new WorkOrderMLotSelectByBomForm(composite, SWT.NONE, this.getAdObject(), tab, mmng, isVerQty);
			return formMLotByBom;
		}else if (tab.getName().equals(TAB_MLOT_SELECT)) {
			return new WorkOrderMLotSelectForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		} else if (tab.getName().equals(TAB_MR)) {
			return new WorkOrderMrForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		} else if (tab.getName().equals(TAB_MR_DETAIL)) {
			return new WorkOrderMrDetailForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		} else if (tab.getName().equals(TAB_SUBWO)) {
			return new WorkOrderSubWoForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		} else if (tab.getName().equals(TAB_GENERATOR_LOT)) {
			return new GeneratorWoLotForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		} else if (tab.getName().equals(TAB_MATERIAL_REQ)) {
			return new MaterialReqForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		} else if (tab.getName().equals(TAB_PART_FLOW)) {
			formFlow = new WorkOrderFlowForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
			return formFlow;
		} 
		EntityForm entityFrom = new EntityForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		entityFrom.setADManager(getADManger());
		return entityFrom;
	}
	
	public void selectionChanged(IFormPart part, ISelection selection) {
		StructuredSelection ss = (StructuredSelection) selection;
		Object object = ss.getFirstElement();
		try {
			isLoading = true;
			setAdObject((ADBase) object);
			if (object != null && ((ADBase) object).getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity((ADBase) object));
			} else {
				setAdObject(createAdObject());
			}
			refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			isLoading = false;
		}
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemApprove(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemHold(tBar);
		createToolItemBom(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
//		createToolItemPrint(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
		createToolitemClose(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemPrint(ToolBar tBar) {
		printItem = new ToolItem(tBar, SWT.PUSH);
		printItem.setText(Message.getString(ExceptionBundle.bundle.CommonPrint()));
		printItem.setImage(SWTResourceCache.getImage("print"));
		printItem.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				printAdapter();
			}
		});
	}
	
	protected void createToolItemApprove(ToolBar tBar) {
		itemApprove = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_APPROVE);
		itemApprove.setText("    " + Message.getString(ExceptionBundle.bundle.CommonApprove()) + "    ");
		itemApprove.setImage(SWTResourceCache.getImage("approve"));
		itemApprove.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				approveAdapter();
			}
		});
	}

	protected void createToolItemHold(ToolBar tBar) {
		itemHold = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_HOLD);
		itemHold.setText(Message.getString("wip.hold"));
		itemHold.setImage(SWTResourceCache.getImage("hold"));
		itemHold.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				holdAdapter();
			}
		});
	}
	
	protected void createToolItemBom(ToolBar tBar) {
		try {
			//�����Ȩ�ޱ�����û��bomȨ�޵�����
			//���û���������ʾ��������ҪBOM,�򲻴���BOM Item
			ADManager adManager = Framework.getService(ADManager.class);
			List<ADAuthority> authorities = adManager.getEntityList(Env.getOrgRrn(), 
					ADAuthority.class, 1, " name = '" + getTable().getAuthorityKey() + "." + KEY_BOM + "'", "");
			if (authorities.isEmpty()) {
				return;
			}
			new ToolItem(tBar, SWT.SEPARATOR);
			itemBom = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_BOM);
			itemBom.setText("BOM");
			itemBom.setImage(SWTResourceCache.getImage("bom"));
			itemBom.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					bomAdapter();
				}
			});
		} catch (Exception e) {
	        ExceptionHandlerManager.asyncHandleException(e);
	        return;
	    }
	}
	
	protected void createToolitemClose(ToolBar tBar) {
		itemClose = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_CLOSE);
		itemClose.setText(Message.getString(ExceptionBundle.bundle.CommonComplete()));
		itemClose.setImage(SWTResourceCache.getImage("close"));
		itemClose.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				closeAdapter();
			}
		});
	}
	
	protected void approveAdapter() {
		try {
	    	WorkOrder workOrder = (WorkOrder) getAdObject();
	    	if (workOrder != null && workOrder.getObjectRrn() != null) {
	    		boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					PpManager ppManager = Framework.getService(PpManager.class);
			    	if (Documentation.STATUS_APPROVED.equals(workOrder.getDocStatus())) {
			    		if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmUnApprove()))) {
			    			workOrder = ppManager.unApproveWorkOrder(workOrder, Env.getSessionContext());
			                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonUnApproveSuccessed()));
			            }
			        } else {
			        	List<WorkOrderBomLine> bomLines = null;
			        	if (itemBom != null) {
			        		//���BOM��û������
			        		bomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
			        		if (bomLines == null || bomLines.isEmpty()) {
			        			UI.showWarning(Message.getString("pp.workorder_bom_is_not_exist"));
			        			return;
							}
			        	}
			        	if (formLotGenerate != null) {
			        		//�����û����������
			        		ADManager adManager = Framework.getService(ADManager.class);
			        		List<WorkOrderLot> workOrderLot = adManager.getEntityList(Env.getOrgRrn(),WorkOrderLot.class, Env.getMaxResult(),  " workOrderRrn = " + workOrder.getObjectRrn(), null);
			        		if (workOrderLot == null) {
			        			UI.showWarning(Message.getString("pp.workorder_lot_is_not_exist"));
			        			return;
							}
			        	}
			        	if (formMLotByBom != null) {
			        		//�����û�������������
			        		ADManager adManager = Framework.getService(ADManager.class);
			        		List<WorkOrderSource> workOrderSources = adManager.getEntityList(Env.getOrgRrn(), 
			        				WorkOrderSource.class, Env.getMaxResult(), "woRrn = " + workOrder.getObjectRrn(), null);
			        		if (workOrderSources == null || workOrderSources.isEmpty()) {
			        			UI.showWarning(Message.getString("pp.workorder_source_lot_is_not_exist"));
			        			return;
							}
			        	}
		            	workOrder = ppManager.approveWorkOrder(workOrder, Env.getSessionContext());
		            	UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonApproveSuccessed()));
			        } 
			    	getMasterParent().refreshUpdate(workOrder);
		            setAdObject(workOrder);
		            refresh();
				}
	    	}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
    }	
	
	protected void holdAdapter() {
        try {
            WorkOrder workOrder = (WorkOrder) getAdObject();
            if (workOrder != null && workOrder.getObjectRrn() != null) {
            	PpManager ppManager = Framework.getService(PpManager.class);
            	if (WorkOrder.HOLDSTATE_OFF.equals(workOrder.getHoldState())) {
            		workOrder = ppManager.holdWorkOrder(workOrder, Env.getSessionContext());
                    UI.showInfo(Message.getString("wip.hold_successed"));
            	} else {
            		workOrder = ppManager.releaseWorkOrder(workOrder, Env.getSessionContext());
                    UI.showInfo(Message.getString("wip.release_successed"));
            	}
                getMasterParent().refreshUpdate(workOrder);
                setAdObject(workOrder);
                refresh();
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	 
	protected void bomAdapter() {
        try {
            if (getAdObject().getObjectRrn() != null) {
	            WorkOrderBomContext context = new WorkOrderBomContext();
	            context.setWorkOrder((WorkOrder)getAdObject());
	            WorkOrderBomWizard orderBomWizard = new WorkOrderBomWizard(context, "WorkOrderBom");
	            WorkOrderBomDialog dialog = new WorkOrderBomDialog(Display.getCurrent().getActiveShell(), orderBomWizard);
	            if (dialog.open() == Dialog.OK) {
					refreshAdapter();
				}
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	protected void printAdapter() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			WorkOrder workOrder = (WorkOrder) getAdObject();
			WorkOrderContext context = new WorkOrderContext();
			if(workOrder != null && workOrder.getObjectRrn() != null) {
				context.setSchedule(workOrder);
				
				PpManager ppManager = Framework.getService(PpManager.class);
				ExcelDocument excelDocument = ppManager.getExcelDocument(workOrder, Env.getSessionContext());
				if (excelDocument == null) {
					UI.showError(Message.getString("OPI.Message.COMM10035"));
					return;
				}
				context.setDocument(excelDocument);
				
				//��ӡBom������Ϣ
    			List<WorkOrderBomLine> bomLines = new ArrayList<WorkOrderBomLine>();
    			bomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
    			context.setBomLines(bomLines);
    			
    			//��ӡ����������
    			List<WorkOrderLot> workOrderLots = null;
				adManager = Framework.getService(ADManager.class);
				workOrderLots = adManager.getEntityList(Env.getOrgRrn(), WorkOrderLot.class, Env.getMaxResult(), " workOrderRrn = " + workOrder.getObjectRrn(), null);
		        context.setWorkOrderLots(workOrderLots);
		        
		    	//��ӡʹ��������Ϣ
		        List<MLot> sourceMLots = null;
		    	if (formMLotByBom != null) {
		    		sourceMLots = (List<MLot>)formMLotByBom.getValue();
		    		context.setmLots(sourceMLots);
		    	}
		    	
		    	//��ӡ��Ʒ��Ϣ
		    	Part newPart= adManager.getEntityList(Env.getOrgRrn(), Part.class, 1, " name='"+workOrder.getPartName()+"' AND version='"+workOrder.getPartVersion()+"'", null).get(0);
		    	context.setPart(newPart);
		    	
		    	//ѭ����ӡstep(ֻ�ܴ򹤲���ţ��������汾�ź�stage)
		    	List<StepState> stepStates = new ArrayList<StepState>();
		    	List<Step> steps = new ArrayList<Step>();
		    	PrdManager prdManager = Framework.getService(PrdManager.class);
		    	List<ProcessDefinition> pro = adManager.getEntityList(Env.getOrgRrn(),ProcessDefinition.class,Env.getMaxResult()," name= '" + newPart.getProcessName() + "'","");
		    	if (pro.size() != 0) {
		    		stepStates = prdManager.getStepChildren(pro.get(0));
			    	if (stepStates.size() != 0) {
			    		for (StepState stepState : stepStates) {
			    			Step step = new Step();
			    			step.setName(stepState.getUsedStep().getName());
			    			step.setDescription(stepState.getUsedStep().getDescription());
			    			step.setVersion(stepState.getUsedStep().getVersion());
			    			step.setStageId(stepState.getStageId());
			    			steps.add(step);
			    		}
			    	}
		    	}
		    	context.setSteps(steps);
		    	
		    	WorkOrderWriter writer = new WorkOrderWriter(context);
				PrintUtil.print(writer, context);
			} 
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
  
    protected void closeAdapter() {
        try {
            WorkOrder workOrder = (WorkOrder) getAdObject();
            if (workOrder != null && workOrder.getObjectRrn() != null) {
                if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmClose()))) {
                	PpManager ppManager = Framework.getService(PpManager.class);
                	//COMPLETE
                    WorkOrder newWorkOrder = (WorkOrder)ppManager.completedWorkOrder(workOrder, Env.getSessionContext());
                    UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonCloseSuccessed()));
                    setAdObject(newWorkOrder);
                    refresh();
                }
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }

    @Override
    protected void deleteAdapter() {
        try {
            WorkOrder workOrder = (WorkOrder) getAdObject();
            if (workOrder != null && workOrder.getObjectRrn() != null) {
            	boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
    			if (confirmDelete) {
    				PpManager ppManager = Framework.getService(PpManager.class);
                    ppManager.deleteWorkOrder(workOrder, Env.getSessionContext());
                    UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
                    setAdObject(createAdObject());
                    refresh();
                    getMasterParent().refreshDelete(workOrder);
    			}
            } else {
                UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
    
	@Override
	protected void saveAdapter() {
		try {
			this.form.getMessageManager().setAutoUpdate(false);
			this.form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				ADBase oldBase = getAdObject();

				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						if ((detailForm instanceof EntityAttributeForm)) {
							getAdObject().setAttributeValues(((EntityAttributeForm) detailForm).getAttributeValues());
						}
					}

					ADBase obj = save(getAdObject());
					if(obj == null){
						return;
					}
					ADManager entityManager = getADManger();
					ADBase newBase = entityManager.getEntity(obj);
					if (getTable().isContainAttribute()) {
						newBase.setAttributeValues(entityManager.getEntityAttributeValues(getTable().getModelName(),
								newBase.getObjectRrn().longValue()));
					}
					if (oldBase.getObjectRrn() == null)
						getMasterParent().refreshAdd(newBase);
					else {
						getMasterParent().refreshUpdate(newBase);
					}

					setAdObject(newBase);

					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
					refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			this.form.getMessageManager().setAutoUpdate(true);
		}
	}

    /*
     * ����adObject����д��save�У�������дsaveAdapter
     */
    @Override
    public ADBase save(ADBase obj) throws Exception {
    	PpManager ppManager = Framework.getService(PpManager.class);

    	boolean isSaveLot = false;
    	boolean isSaveSource = false;
    	if (formLotGenerate != null) {
    		isSaveLot = true;
    	}
    	List<MLot> sourceMLots = null;
    	if (formMLotByBom != null) {
    		isSaveSource = true;
    		sourceMLots = (List<MLot>)formMLotByBom.getValue();
    	}
    	
    	if(isVerQty){
    		if(sourceMLots != null && sourceMLots.size() > 0){
//		    	if(!getHasQty(sourceMLots)){
//		    		return null;
//		    	}
    			if(!checkWorkOrderBomQty(sourceMLots)){
		    		return null;
		    	}
    		}
    	}
    	WorkOrder workOrder = (WorkOrder) obj;
    	workOrder.setStartedMainQty(BigDecimal.ZERO);
    	
    	SysParameterManager paraManager = Framework.getService(SysParameterManager.class);
		boolean isUseInterface = paraManager.getBooleanSysParameterValue(Env.getOrgRrn(), ADSysParameter.SYS_INTERFACE_USE);
    	if (isUseInterface) {
    		//���ʹ�ýӿ�
    		//��������ʽ
    		//IfContext context = new IfContext();
    		//String ifTransaction = paraManager.getSysParameterValueFromCache(Env.getOrgRrn(), IfContext.SYS_INTERFACE_TRANSACTION);
    		//context.setIfTransaction(ifTransaction);
    		//
    		//IfClientManager ifClient = Framework.getService(IfClientManager.class);
    		//ifClient.saveWorkOrder(context, workOrder, sourceMLots, null, false, isSaveLot, isSaveSource, false, Env.getSessionContext());
    	} else {
        	obj = ppManager.saveWorkOrder(workOrder, sourceMLots, null, false, isSaveLot, isSaveSource, false, Env.getSessionContext());
    	}
    	
    	return obj;
    }
    
    public boolean getHasQty(List<MLot> mLot){
    	List<MaterialRequisitionLine> materialRequisitionLines = formMLotByBom.getMaterialRequisitionLines();
    	for(MaterialRequisitionLine mrl : materialRequisitionLines){
    		BigDecimal hasQtye = BigDecimal.ZERO;
    		for(MLot ml : mLot){
	    		if(mrl.getMaterialName().equals(ml.getMaterialName())){
	    			hasQtye = hasQtye.add(ml.getTransMainQty());
	    		}
    		}
    		if(mrl.getLineMaxQty().compareTo(hasQtye) == -1){
    			UI.showInfo(Message.getString("wip.wo_lot_qty_more_than"));
    			return false;
    		}
    	}
    	return true;
    }
    /**
	 * ��������У�鷽ʽ(None/Exact/Less/Large),Ĭ��ΪsysParameter = Exact
	 * 
	 * None:��У��
	 * Exact:���������������BOM�涨������
	 * Large:�����������Դ��ڵ���BOM�涨������
	 * Less:������������С�ڵ���BOM�涨������
	 */
    public boolean checkWorkOrderBomQty(List<MLot> mLots) {
    	boolean flag = false;
    	List<MaterialRequisitionLine> materialRequisitionLines = formMLotByBom.getMaterialRequisitionLines();
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
	    	String sysParameter = MesCfMod.getWoMaterailCheck(Env.getOrgRrn(), sysParamManager);
	    	flag = MaterialRequisition.checkWorkOrderMLotByBom(materialRequisitionLines,mLots,sysParameter);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return flag;
    }

	@Override
	public void refresh() {
		super.refresh();
		try {
			WorkOrder workOrder = (WorkOrder) getAdObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
			} else {
				statusChanged(null, null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public void superRefresh() {
		super.refresh();
	}

	public void statusChanged(String docStatus, String holdStatus) {
		if (Documentation.STATUS_CREATED.equals(docStatus)) {
            itemSave.setEnabled(true);
            if(itemApprove != null) {
            	itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText("    " + Message.getString(ExceptionBundle.bundle.CommonApprove()) + "    ");
     			itemApprove.setEnabled(true);
            }
            itemDelete.setEnabled(true);
            if(itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(true);
            }
            if(itemHold != null) {
            	itemHold.setEnabled(false);
            }
        } else if (Documentation.STATUS_APPROVED.equals(docStatus)) {
        	itemSave.setEnabled(false);
        	if(itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText(Message.getString(ExceptionBundle.bundle.CommonUnApprove()));
    			itemApprove.setEnabled(true);
        	}
            itemDelete.setEnabled(false);
            if(itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if(itemHold != null) {
            	itemHold.setEnabled(true);
            }
        } else if (WorkOrder.STATUS_STARTED.equals(docStatus)) {
        	itemSave.setEnabled(false);
        	if(itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText(Message.getString(ExceptionBundle.bundle.CommonUnApprove()));
      			itemApprove.setEnabled(false);
        	}
            itemDelete.setEnabled(false);
            if(itemClose != null) {
            	itemClose.setEnabled(true);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if(itemHold != null) {
            	itemHold.setEnabled(true);
            }
        } else if (WorkOrder.STATUS_CLOSED.equals(docStatus)) {
        	itemSave.setEnabled(false);
        	if(itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText(Message.getString(ExceptionBundle.bundle.CommonUnApprove()));
    			itemApprove.setEnabled(false);
        	}
            itemDelete.setEnabled(false);
            if(itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if(itemHold != null) {
            	itemHold.setEnabled(false);
            }
        } else {
        	itemSave.setEnabled(true);
        	if(itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText("    " + Message.getString(ExceptionBundle.bundle.CommonApprove()) + "    ");
     			itemApprove.setEnabled(false);
        	}
            itemDelete.setEnabled(false);
            if(itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if(itemHold != null) {
            	itemHold.setEnabled(false);
            }
        }
		
        if (WorkOrder.HOLDSTATE_OFF.equals(holdStatus)) {
        	if(itemHold != null) {
        		itemHold.setImage(SWTResourceCache.getImage("hold"));
        		itemHold.setText(Message.getString("wip.hold"));
        	}
        } else {
        	if(itemHold != null) {
        		itemHold.setImage(SWTResourceCache.getImage("release"));
        		itemHold.setText(Message.getString("wip.release"));
        	}
        }
    }
	
	@Override
	protected void newAdapter() {
		super.newAdapter();
		statusChanged(null, null);
	}
}
