package com.glory.mes.wip.pp.wo;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class WorkOrderEditor extends EntityEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.wo.WorkOrderEditor";
	@Override
	protected void createBlock(ADTable adTable) {
		block = new WorkOrderBlock(new ListTableManager(adTable));
	}
	
}
