package com.glory.mes.wip.pp.wo.start.bylot;

import org.apache.log4j.Logger;
import org.eclipse.ui.forms.DetailsPart;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class WoStartByLotBlock extends EntityBlock {
	
	private static final Logger logger = Logger.getLogger(WoStartByLotBlock.class);
	WoStartByLotProperties page;
	private boolean isGenNewCompId;
	
	public WoStartByLotBlock(ListTableManager tableManager) {
		super(tableManager);
	}
	
	public WoStartByLotBlock(ListTableManager tableManager, boolean isGenNewCompId) {
		super(tableManager);
		this.isGenNewCompId = isGenNewCompId;
	}

	@Override
	protected void registerPages(DetailsPart detailsPart) {
		try{
			ADTable table = getTableManager().getADTable();
			Class<?> klass = Class.forName(table.getModelClass());
			page = new WoStartByLotProperties(isGenNewCompId);
			page.setTable(table);
			page.setMasterParent(this);
			detailsPart.registerPage(klass, page);
		} catch (Exception e){
			logger.error("EntityBlock : registerPages ", e);
		}
	}
	
	
	public void setFocus() {
		((WoStartByLotProperties)page).setFocus();
	}

}
