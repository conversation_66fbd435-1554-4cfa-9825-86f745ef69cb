package com.glory.mes.wip.pp.wo.start.glc;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.wip.client.MLotManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class WoMLotSearchDialog extends GlcBaseDialog {

	private static final String FIELD_TRANSWAREHOUSERRN = "transWarehouseRrn";
	private static final String FIELD_MLOTLIST = "mlotList";

	private static final String BUTTON_QUERY = "query";

	protected RefTableField transWarehouseRrnField;
	protected ListTableManagerField mlotListField;
	
    protected Long materialRrn;
    protected String whereClause;
    WoStartGlcEditor woStartGlcEditor;

	public WoMLotSearchDialog(String adFormName, String authority, IEventBroker eventBroker, String whereClause, WoStartGlcEditor woStartGlcEditor) {
		super(adFormName, authority, eventBroker);
		this.whereClause = whereClause;
		this.woStartGlcEditor = woStartGlcEditor;
		setBlockOnOpen(false);
	}

	protected void createFormAction(GlcForm form) {
		transWarehouseRrnField = form.getFieldByControlId(FIELD_TRANSWAREHOUSERRN, RefTableField.class);
		mlotListField = form.getFieldByControlId(FIELD_MLOTLIST, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_QUERY), this::queryAdapter);
		setTitle(Message.getString(ExceptionBundle.bundle.CommonSearch()));
		setMessage(Message.getString("common.keys"));
		init();
	}

	private void init() {
		try {
			ADTable table = mlotListField.getListTableManager().getADTable();
			StringBuffer whereCluase = new StringBuffer("");
			whereCluase.append(" 1 = 1 ");
			if (!StringUtil.isEmpty(table.getInitWhereClause())) {
				whereCluase.append(" AND ");
				whereCluase.append(table.getInitWhereClause());
			}
			if (!StringUtil.isEmpty(whereClause)) {
				whereCluase.append(" AND ");
				whereCluase.append(whereClause);
			}
			
			ADManager adManager = Framework.getService(ADManager.class);
			List<MLot> bases = adManager.getEntityList(Env.getOrgRrn(), MLot.class, 
            		Env.getMaxResult(), whereCluase.toString(), "");
			mlotListField.getListTableManager().setInput(bases);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void queryAdapter(Object object) {
		try {
			List<MLot> availableMLots = new ArrayList<MLot>();
			if (transWarehouseRrnField.getValue() != null) {
				MLotManager mlotManager = Framework.getService(MLotManager.class);
				List<MLot> getAvailableMLots = mlotManager.getMLotsByWarehouseId(Long.valueOf(transWarehouseRrnField.getValue().toString()) , Env.getSessionContext());
				for (MLot mlot : getAvailableMLots) {
					mlot.setTransWarehouseRrn(Long.valueOf(transWarehouseRrnField.getValue().toString()));
					if (materialRrn != null) {
						if (mlot.getMaterialRrn().equals(materialRrn)) {
							availableMLots.add(mlot);
						}
					} else {
						availableMLots.add(mlot);
					}
				}
				mlotListField.getListTableManager().getInput().clear();
				mlotListField.getListTableManager().setInput(availableMLots);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected void okPressed() {
		List<MLot> checkMlot = new ArrayList<MLot>();
		List<Object> objs = mlotListField.getListTableManager().getCheckedObject();
		if (objs != null && objs.size() > 0) {
			for (Object obj : objs) {
				checkMlot.add((MLot) obj);
			}
		}
		if(woStartGlcEditor.validateMlot(checkMlot)) {
			super.okPressed();
		}
	}

	@Override
	protected Point getInitialSize() {
		return new Point(800,900);
	}
	
}
