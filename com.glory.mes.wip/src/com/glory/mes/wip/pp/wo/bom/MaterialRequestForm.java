package com.glory.mes.wip.pp.wo.bom;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.TableViewerManager;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.mm.MaterialRequisitionLine;

public class MaterialRequestForm extends EntityForm {
	
    private TableViewerManager tableManager;
    private static final String TABLE_NAME = "BOMMaterialRequisitionLine";

    public MaterialRequestForm(Composite parent, int style, ADTable table, IMessageManager mmng) {
        super(parent, style, table, mmng);
    }

    @Override
    public void createForm() {
        try {
            FormToolkit toolkit = new FormToolkit(getShell().getDisplay());
            Composite bodyComposite = toolkit.createComposite(getParent(), SWT.NONE);
            bodyComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
            GridLayout layoutBody = new GridLayout(1, true);
            bodyComposite.setLayout(layoutBody);
            GridData gd = new GridData(GridData.FILL_BOTH);
        //  gd.heightHint = 700;
            bodyComposite.setLayoutData(gd);
            ADManager entityManager = Framework.getService(ADManager.class);
            ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
            tableManager = new TableViewerManager(adTable);

            tableManager.newViewer(bodyComposite);
        } catch (Exception e) {
            e.printStackTrace();
        }
        super.createForm();
    }

    public void setInput(List<MaterialRequisitionLine> requisitionLines) {
        tableManager.setInput(requisitionLines);
        tableManager.refresh();
    }

    public TableViewerManager getTableManager() {
        return tableManager;
    }

    public void setTableManager(CheckBoxFixEditorTableManager tableManager) {
        this.tableManager = tableManager;
    }

    
}
