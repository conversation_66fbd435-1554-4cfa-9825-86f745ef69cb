package com.glory.mes.wip.pp.wo.excel;

import java.io.InputStream;
import java.net.URL;
import java.util.Collections;

import org.apache.commons.vfs2.FileObject;
import org.apache.commons.vfs2.VFS;
import org.apache.log4j.Logger;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.glory.common.excel.write.ExcelWriter;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.TimeUID;
import com.glory.mes.prd.model.WorkOrderContext;


public class WorkOrderWriter extends ExcelWriter{
	
	private static final Logger logger = Logger.getLogger(WorkOrderWriter.class);
	
	public WorkOrderWriter(WorkOrderContext context) {
		super(context);
	}
	
//	@Override
//	public InputStream openFile(String filePath) throws ClientException {
//		TCardCfMod cfMod = readConfig();
//		filePath = cfMod.getFilePath() + filePath;
//		if (cfMod.isAuth()) {
//			return super.openFile(filePath, true, cfMod.getUserName(), cfMod.getPassword());
//		}
//		return super.openFile(filePath);
//	}
//
//	@Override
//	public InputStream openLocalFile(String filePath) throws ClientException {
//		return this.getClass().getResourceAsStream(filePath);
//	}
//	
//	
//	@Override
//	public FileObject getTempFile() throws ClientException {
//		try {
//			TCardCfMod cfMod = readConfig();
//			String targetPath = cfMod.getTargetPath();
//			if (targetPath == null || targetPath.trim().length() == 0) {
//				return super.getTempFile();
//			}
//			
//			WorkOrderContext context = (WorkOrderContext)this.getContext();
//			if (targetWb instanceof XSSFWorkbook) {
//				outFilePath = targetPath + "//" 
//					+  context.getSchedule().getDocId() + "_" + 
//					String.valueOf(TimeUID.next()).substring(0, 12) + ".xlsx";
//			} else {
//				outFilePath = targetPath + "//" 
//						+  context.getSchedule().getDocId() + "_" + 
//						String.valueOf(TimeUID.next()).substring(0, 12) + ".xls";
//			}
//
//			FileObject fileObject = VFS.getManager().resolveFile(outFilePath); ;
//			if (!fileObject.exists()) {
//				fileObject.createFile();
//			}
//			return fileObject;
//		} catch (Exception e) {
//			logger.error("Can not get read ", e);
//			throw new ClientException(e);
//		}
//	}
//	
//	public TCardCfMod readConfig() {
//		try {
//			URL url = TCardCfMod.class.getResource("TCardCf.xml");
//			if (url == null) {
//				throw new RuntimeException("Configuration file FileCfMod.xml cannot be found. " +
//						"File does not exist in class resource directory.");
//			}
//			
//			InputStream in = url.openStream();
//			try {
//				Class[] classes = new Class[]{ TCardCfMod.class };
//				JAXBContext context = JAXBContext.newInstance(classes, Collections.<String, Object>emptyMap());
//				TCardCfMod cfMod = (TCardCfMod)context.createUnmarshaller().unmarshal(in);
//				return cfMod;
//			} finally {
//				in.close();
//			}
//		} catch (Exception e) {
//			logger.warn("Unable to load config file: FileCfMod.xml", e);
//		}
//		return null;
//	}
	
}
