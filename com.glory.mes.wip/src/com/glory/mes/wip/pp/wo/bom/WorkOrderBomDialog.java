package com.glory.mes.wip.pp.wo.bom;

import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.base.ui.wizard.FlowWizardDialog;

public class WorkOrderBomDialog extends FlowWizardDialog {
	
	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 380;

	public WorkOrderBomDialog(Shell parentShell, FlowWizard newWizard) {
		super(parentShell, newWizard);
	}

	/*
	 * ȡ��������Ĭ�ϵķ���Next Button��
	 */
	public void updateButtons() {
		boolean canFlipToNextPage = false;
		if (currentPage != null) {
			if (backButton != null) {
				backButton.setEnabled(getCurrentPage().getPreviousPage() != null);
			}
			if (nextButton != null) {
				canFlipToNextPage = getCurrentPage().canFlipToNextPage();
				nextButton.setEnabled(canFlipToNextPage);
			}
		}
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}

}
