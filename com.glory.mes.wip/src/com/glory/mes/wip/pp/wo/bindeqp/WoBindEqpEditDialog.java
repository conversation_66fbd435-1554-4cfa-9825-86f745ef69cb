package com.glory.mes.wip.pp.wo.bindeqp;

import java.util.List;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderEqp;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * <AUTHOR>
 *
 */
public class WoBindEqpEditDialog extends EntityDialog {
	
	private List<WorkOrderEqp> values;

	public WoBindEqpEditDialog(ADTable table, ADBase adObject, List<WorkOrderEqp> values) {
		super(table, adObject);
		this.values = values;
	}

	@Override
	protected boolean saveAdapter() {
		try {
			managedForm.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (Form detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				
				WorkOrderEqp orderEqp = (WorkOrderEqp) getAdObject();
				Equipment equipment = new Equipment();
				equipment.setObjectRrn(orderEqp.getEquipmentRrn());
				equipment.setEquipmentId(orderEqp.getEquipmentId());
				
				if (saveFlag && checkRepeat(orderEqp)) {
					PpManager ppManager = Framework.getService(PpManager.class);
					WorkOrder workOrder = new WorkOrder();
					workOrder.setDocId(orderEqp.getWoId());
					
					ppManager.bindingWorkOrderEqp(workOrder, 
							equipment, false, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));// ������ʾ��
					return true;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
		return false;
	}
	
	protected boolean checkRepeat(WorkOrderEqp operation) {
		if (operation == null) {
			return false;
		}
			
		if (values == null) {
			return true;
		} else {
			// ������Ŀ�Ƿ��ظ�
			boolean repeat = false;
			for (WorkOrderEqp orderEqp : values) {
				if (operation.getWoId().equals(orderEqp.getWoId())) {
					repeat = true;
					break;
				}
			}

			if (repeat) {
				UI.showInfo(Message.getString("wip.wo_repeat"));
			}
			
			return !repeat;
		}
	}
	
	@Override
	protected void okPressed() {
		super.okPressed();
	}
	
}
