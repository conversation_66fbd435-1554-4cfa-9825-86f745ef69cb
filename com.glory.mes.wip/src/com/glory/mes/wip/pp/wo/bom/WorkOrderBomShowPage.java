package com.glory.mes.wip.pp.wo.bom;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ExpandItem;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.model.Material;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.wip.mm.MaterialRequisitionLine;
import com.glory.mes.wip.model.Lot;

public class WorkOrderBomShowPage extends FlowWizardPage {
	
	protected Lot lot;
	protected WorkOrderBomContext context;
	protected MaterialRequestForm materialRequestForm;
	
	public WorkOrderBomShowPage() {
		super();
	}
	
	public WorkOrderBomShowPage(String pageName, Wizard wizard,
			String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public String doNext() {
		try {
			List<Object> objects = (List<Object>) materialRequestForm.getTableManager().getInput();
	        List<MaterialRequisitionLine> requisitionLines = new ArrayList<MaterialRequisitionLine>();
	        for (Object object : objects) {
	            MaterialRequisitionLine materialRequisitionLine = (MaterialRequisitionLine) object;
	            requisitionLines.add(materialRequisitionLine);	            
	        }
	        context.setMaterialRequisitionLines(requisitionLines);
	        return FINISH;
		} catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }
		return null;
	}

	@Override
	public String doPrevious() {
		if (this.getControl() != null) {
			this.getControl().dispose();
			this.setControl(null);
		}
		setErrorMessage(null);
		setMessage(null);
		return super.doPrevious();
	}

	@Override
	public void createControl(Composite parent) {
		context = (WorkOrderBomContext) ((WorkOrderBomWizard) getWizard()).getContext();
		
		FormToolkit toolkit = new FormToolkit(getShell().getDisplay());
		Composite composite = toolkit.createComposite(parent, SWT.NONE);
		GridLayout layout = new GridLayout();
		layout.numColumns = 1;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		composite.setLayout(layout);
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));
		
        ScrolledForm sForm = toolkit.createScrolledForm(composite);
        ManagedForm managedForm = new ManagedForm(toolkit, sForm);
        final IMessageManager mmng = managedForm.getMessageManager();
        sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
        Composite body = sForm.getForm().getBody();
        configureBody(body);
        
        createFrom(body);
        
		setControl(composite);
		setTitle(Message.getString("wip.wo_bom_show_title"));
		setMessage(Message.getString("wip.wo_bom_show_message"));
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
	public void createFrom(Composite composite) {
        materialRequestForm = new MaterialRequestForm(composite, SWT.NONE, null, null);
        materialRequestForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
        materialRequestForm.setInput(buildMaterialRequest());
	}
	
	protected List<MaterialRequisitionLine> buildMaterialRequest() {
		List<MaterialRequisitionLine> materialRequisitionLines = new ArrayList<MaterialRequisitionLine>();
		List<WorkOrderBomLine> workOrderBomLines = context.getWorkOrderBomLines();
		try {
			PpManager ppManager = Framework.getService(PpManager.class);
			materialRequisitionLines = ppManager.generateMaterialRequisition(context.getWorkOrder(), workOrderBomLines, Env.getSessionContext());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return materialRequisitionLines;
    }
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}

}
