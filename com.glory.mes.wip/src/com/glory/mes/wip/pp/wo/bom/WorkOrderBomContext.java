package com.glory.mes.wip.pp.wo.bom;

import java.util.List;

import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.wip.mm.MaterialRequisitionLine;

public class WorkOrderBomContext {
	
	/**
	 * �Ƿ�չ�����Ʒ
	 * ���ڶ༶BOMʱ(���ƷBOM),�Ƿ�չ������ײ��ԭ����
	 * Ĭ��Ϊfalse,��ֻչ�����¼�����
	 */
	private boolean isExpandProduct = false;

	private WorkOrder workOrder;
	
	private List<WorkOrderBomLine> workOrderBomLines;
	
	private List<MaterialRequisitionLine> materialRequisitionLines;
	
	public WorkOrderBomContext() {}

	public boolean isExpandProduct() {
		return isExpandProduct;
	}

	public void setExpandProduct(boolean isExpandProduct) {
		this.isExpandProduct = isExpandProduct;
	}
	
	public WorkOrder getWorkOrder() {
		return workOrder;
	}

	public void setWorkOrder(WorkOrder workOrder) {
		this.workOrder = workOrder;
	}

	public List<WorkOrderBomLine> getWorkOrderBomLines() {
		return workOrderBomLines;
	}

	public void setWorkOrderBomLines(List<WorkOrderBomLine> workOrderBomLines) {
		this.workOrderBomLines = workOrderBomLines;
	}

	public List<MaterialRequisitionLine> getMaterialRequisitionLines() {
		return materialRequisitionLines;
	}

	public void setMaterialRequisitionLines(List<MaterialRequisitionLine> materialRequisitionLines) {
		this.materialRequisitionLines = materialRequisitionLines;
	}

}
