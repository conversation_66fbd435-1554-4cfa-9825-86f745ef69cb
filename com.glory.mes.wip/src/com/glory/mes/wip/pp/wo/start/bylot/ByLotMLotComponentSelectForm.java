package com.glory.mes.wip.pp.wo.start.bylot;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.mes.mm.lot.model.MComponentUnit;
import com.glory.mes.wip.model.ComponentUnit;

/**
 * ����Lotѡ��Դ�������μ����Ӧ��MComponentUnit
 */
public class ByLotMLotComponentSelectForm extends EntityForm {
	public static final String FIELD_ID = "lotId";

	private MLotComponentAssignComposite mlotComponentAssignComposite;

	public ByLotMLotComponentSelectForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}
	
	//ҳ���Ϊ��������
	//��������ؾ���Ϣ,�ұ�ΪԴ�������μ�Component��Ϣ
	@Override
	protected void createContent() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));
		layout = new GridLayout(3, false);
		form.setLayout(layout);
		
		Composite body = getForm().getBody();
		layout = new GridLayout(1, false);
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);

		mlotComponentAssignComposite = new MLotComponentAssignComposite(body, SWT.NONE);
	}

	public List<ComponentUnit> getTargetComponentUnit() throws CloneNotSupportedException {
		return mlotComponentAssignComposite.getTargetComponentUnit();
	}
	
	public List<MComponentUnit> getTargetMComponentUnit() {
		return mlotComponentAssignComposite.getTargetMComponentUnit();
	}
	
	public String getCarrierId() {
		return mlotComponentAssignComposite.getTargetCarrierId();
	}
	
	@Override
	public void refresh() {
		super.refresh();
		mlotComponentAssignComposite.refresh();
	}
}
