package com.glory.mes.wip.pp.wo.bindeqp;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.eqp.Equipment;

/**
 * <AUTHOR>
 *
 */
public class WoBindEqpForm extends EntityForm {
	
	private static final String TABLE_NAME = "WIPWorkOrderBindEqp";
	
	private IField fieldWoEqps;
	private static final String FIELD_WOEQP = "WoEqp";
	public static final String FIELD_WOEQP_ID = "woEqp";
	
	private Equipment equipment;

	public WoBindEqpForm(Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}

	@Override
	public void addFields() {
		super.addFields();
		try {
			ListTableManager tableManager = new ListTableManager(getBindAdTable());
			fieldWoEqps = createOperationTableField(FIELD_WOEQP, "", tableManager);
			addField(FIELD_WOEQP_ID, fieldWoEqps);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public WoBindEqpField createOperationTableField(String id, String label, ListTableManager tableManager) {
		WoBindEqpField otf = new WoBindEqpField(id, tableManager);
		otf.setLabel(label);
		otf.setValue(null);
		return otf;

	}
	
	private ADTable getBindAdTable() {
		try {
			ADManager adManager = getADManger();
			return adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	public Equipment getEquipment() {
		return equipment;
	}

	public void setEquipment(Equipment equipment) {
		this.equipment = equipment;
		((WoBindEqpField)fieldWoEqps).setEquipment(equipment);
	}

	@Override
	public boolean saveToObject() {
		return true;
	}

	@Override
	public void loadFromObject() {
	}
}
