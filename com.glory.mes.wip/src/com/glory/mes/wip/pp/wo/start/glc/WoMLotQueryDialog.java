package com.glory.mes.wip.pp.wo.start.glc;


import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.model.Material;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.wip.client.MLotManager;

public class WoMLotQueryDialog extends GlcBaseDialog { 

	public static final String FIELD_MLOTID = "mLotId";
	public static final String FIELD_MATERIALNAME = "materialName";
	public static final String FIELD_TRANSWAREHOUSERRN = "transWarehouseRrn";
	public static final String FIELD_MLOTLIST = "mlotList";

	protected QueryFormField mlotListField;
	protected TextField mlotTextField;
	protected RefTableField materialRefTableField;
	protected RefTableField transWarehouseRrnField;
	
    protected String whereClause;
    protected WoStartGlcEditor woStartGlcEditor;
    protected List<WorkOrderBomLine> workOrderBomLines;

	public WoMLotQueryDialog(String adFormName, String authority, IEventBroker eventBroker, String whereClause, List<WorkOrderBomLine> workOrderBomLines, WoStartGlcEditor woStartGlcEditor) {
		super(adFormName, authority, eventBroker);
		this.whereClause = whereClause;
		this.woStartGlcEditor = woStartGlcEditor;
		this.workOrderBomLines = workOrderBomLines;
		setBlockOnOpen(false);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		mlotListField = form.getFieldByControlId(FIELD_MLOTLIST, QueryFormField.class);
		subscribeAndExecute(eventBroker, mlotListField.getFullTopic(GlcEvent.EVENT_QUERY), this::mlotListQuery);
		
		LinkedHashMap<String, IField> fields = mlotListField.getQueryForm().getQueryForm().getFields();
		Iterator var5 = fields.values().iterator();
		while (var5.hasNext()) {
			IField f = (IField) var5.next();
			ADField adField = (ADField)f.getADField();
			if (FIELD_MATERIALNAME.equals(adField.getName())) {
        		materialRefTableField = (RefTableField) f;
        	} else if (FIELD_TRANSWAREHOUSERRN.equals(adField.getName())){
        		transWarehouseRrnField = (RefTableField) f;
        		mlotListField.getQueryForm().getQueryForm().getFields().remove(FIELD_TRANSWAREHOUSERRN);
			}
		}
		mlotListField.getQueryForm().getTableManager().setAutoSizeFlag(true);
		mlotListField.getQueryForm().getTableManager().refresh();
		init();
	}
	
	private void init() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			//�޸���������������ֵ
			List<Material> materials = adManager.getEntityList(Env.getOrgRrn(), Material.class, 
	        		Env.getMaxResult(), whereClause.toString().replace("materialName", "name") + "and status = 'Active'", "");
			List<WorkOrderBomLine> bomLines = workOrderBomLines.stream().filter(workOrderBomLine -> workOrderBomLine.getIsMain()).collect(Collectors.toList()); 
			List<String> materialNames = bomLines.stream().map(WorkOrderBomLine::getMaterialName).collect(Collectors.toList()); 
			List<Material> inputMaterials = new ArrayList<Material>();
			for (Material material : materials) {
				if (materialNames.contains(material.getName())) {
					inputMaterials.add(material);
				}
			}
			materialRefTableField.setInput(inputMaterials);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void mlotListQuery(Object object) {
		try {
			if (mlotListField.getQueryForm().getQueryForm().validate()) {
				ADManager adManager = Framework.getService(ADManager.class);
				//���ɲ�ѯ����
				String createWhereCluase = mlotListField.getQueryForm().getQueryForm().createWhereClause();
				StringBuffer whereCluase = new StringBuffer("");
				whereCluase.append(" 1 = 1 ");
				if(!StringUtil.isEmpty(createWhereCluase)) {
					whereCluase.append(createWhereCluase);
				} 
				if (StringUtil.isEmpty(materialRefTableField.getText())) {
					whereCluase.append(" AND ");
					whereCluase.append(whereClause);
				}
				whereCluase.append(" AND state = 'IN' AND mainQty >0");
				List<MLot> bases = adManager.getEntityList(Env.getOrgRrn(), MLot.class, 
		        		Env.getMaxResult(), whereCluase.toString(), "");
				
				//���ѡ���˲ֿ⣬��ѯ��ѡ��Ĳֿ��е�������
				List<MLot> availableMLots = new ArrayList<MLot>();
				List<MLot> inputMLots = new ArrayList<MLot>();
				if (!StringUtil.isEmpty(transWarehouseRrnField.getText())) {
					MLotManager mlotManager = Framework.getService(MLotManager.class);
					List<MLot> getAvailableMLots = mlotManager.getMLotsByWarehouseId(Long.valueOf(transWarehouseRrnField.getValue().toString()) , Env.getSessionContext());
					for (MLot mlot : getAvailableMLots) {
						mlot.setTransWarehouseRrn(Long.valueOf(transWarehouseRrnField.getValue().toString()));
						availableMLots.add(mlot);
					}
					//ɸѡ�ڲֿ����������ѯ����������
					for (MLot mLot :availableMLots) {
						if (bases.contains(mLot)) {
							inputMLots.add(mLot);
						}
					}
					mlotListField.getQueryForm().getTableManager().setInput(inputMLots);
				} else {
					mlotListField.getQueryForm().getTableManager().setInput(bases);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			mlotListField.getQueryForm().getTableManager().setInput(new ArrayList<Object>());
			return;
		}
	}

	@Override
	protected void okPressed() {
		List<MLot> checkMlot = new ArrayList<MLot>();
		List<Object> objs = mlotListField.getCheckedObjects();
		if (objs != null && objs.size() > 0) {
			for (Object obj : objs) {
				checkMlot.add((MLot) obj);
			}
		}
		if(woStartGlcEditor.validateMlot(checkMlot)) {
			super.okPressed();
		}
	}

	@Override
	protected Point getInitialSize() {
		return new Point(1400,900);
	}
	
}