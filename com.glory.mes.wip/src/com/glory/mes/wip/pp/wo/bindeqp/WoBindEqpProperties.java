package com.glory.mes.wip.pp.wo.bindeqp;

import org.eclipse.jface.viewers.ISelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.ui.forms.IFormPart;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.eqp.Equipment;

public class WoBindEqpProperties extends EntityProperties {

	protected WoBindEqpForm eqpForm;

	public WoBindEqpProperties() {
		super();
	}

	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		eqpForm = new WoBindEqpForm(composite, SWT.NONE, tab, mmng);
		return eqpForm;
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	public void refresh() {
		getMessageManager().removeAllMessages();
	}
	
	@Override
	public void selectionChanged(IFormPart part, ISelection selection) {
		super.selectionChanged(part, selection);
		loadOrderEqp();
	}
	
	
	private void loadOrderEqp() {
		try {
			Equipment equipment = (Equipment) getAdObject();
			if (equipment == null || equipment.getObjectRrn() == null || eqpForm == null) {
				return;
			}
			
			eqpForm.setEquipment(equipment);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}