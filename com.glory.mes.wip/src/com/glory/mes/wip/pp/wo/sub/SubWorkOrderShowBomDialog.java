package com.glory.mes.wip.pp.wo.sub;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.wip.mm.MaterialRequisitionLine;
import com.glory.mes.wip.pp.wo.bom.MaterialRequestForm;

public class SubWorkOrderShowBomDialog extends EntityDialog {
	
	protected MaterialRequestForm materialRequestForm;
	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 380;
	
	public SubWorkOrderShowBomDialog(ADTable table, ADBase adObject) {
		super(table, adObject);
	}

	@Override
	protected void createFormContent(Composite composite) {
        materialRequestForm = new MaterialRequestForm(composite, SWT.NONE, null, null);
        materialRequestForm.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
        materialRequestForm.setInput(buildMaterialRequest());
	}
	
	protected List<MaterialRequisitionLine> buildMaterialRequest() {
		List<MaterialRequisitionLine> materialRequisitionLines = new ArrayList<MaterialRequisitionLine>();
		WorkOrder workOrder = (WorkOrder) getAdObject();
		if (workOrder != null && workOrder.getObjectRrn() != null) {
			try {
				PpManager ppManager = Framework.getService(PpManager.class);
				List<WorkOrderBomLine> workOrderBomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
				materialRequisitionLines = ppManager.generateMaterialRequisition(workOrder, workOrderBomLines, Env.getSessionContext());
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return materialRequisitionLines;
    }
	
	@Override
	protected boolean saveAdapter() {
		return true;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}
}
