package com.glory.mes.wip.pp.wo.bindeqp;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class WoBindEqpEditor extends EntityEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.wo.bindeqp.WoBindEqpEditor";

	@Inject
	protected MPart mPart;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		ADTable adTable = (ADTable)mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
		
		configureBody(parent);
		createBlock(adTable);
		
		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		ScrolledForm form = toolkit.createScrolledForm(parent);
		form.setLayout(new GridLayout(1, true));
		form.setLayoutData(new GridData(GridData.FILL_BOTH));
		ManagedForm mform = new ManagedForm(toolkit, form);
		
		block.createContent(mform);    
		fireSelectionChanged(mform);
	}
	
	@Override
	protected void createBlock(ADTable adTable) {
		block = new WoBindEqpBlock(new ListTableManager(adTable));
	}

}
