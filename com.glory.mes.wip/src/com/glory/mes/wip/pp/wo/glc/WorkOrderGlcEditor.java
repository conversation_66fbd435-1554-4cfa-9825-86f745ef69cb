package com.glory.mes.wip.pp.wo.glc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SearchField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.model.MaterialAltProcess;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.wip.custom.FlowCustomComposite;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomContext;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomDialog;
import com.glory.mes.wip.pp.wo.bom.WorkOrderBomWizard;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.exception.ExceptionBundle;

public class WorkOrderGlcEditor extends GlcEditor{
	
	public static final Logger logger = Logger.getLogger(WorkOrderGlcEditor.class);
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.wo.glc.WorkOrderGlcEditor";
	
	protected static final String FIELD_LOTLIST = "lotList";
	protected static final String FIELD_MATERIALIST = "materialList";
	
	protected static final String BUTTON_NEW = "new";
	protected static final String BUTTON_SAVE = "save";
	protected static final String BUTTON_UNAPPROVE = "approve";
	protected static final String BUTTON_HOLD = "hold";
	protected static final String BUTTON_BOM = "bom";
	protected static final String BUTTON_CLOSE = "close";
	protected static final String BUTTON_DELETE = "delete";
	protected static final String BUTTON_REFRESH = "entityRefresh";
	public static final String BUTTON_NAME_ADD = "add"; 
	public static final String BUTTON_NAME_DELETE = "delete"; 
	
	protected static final String FORM_BASIC_INFO = "BasicInfo";
	protected static final String FORM_LOT_ID = "lotId";
	protected static final String FORM_LOT_QTY = "lotQty";
	protected static final String FORM_LOT_NUMBER = "lotNumber";
	protected static final String FORM_PARTFLOW = "partFlow";
	protected static final String FIELD_PARTVERSION = "partVersion";
	protected static final String FIELD_PARTNAME = "partName";
	protected static final String FIELD_REWORKPROCESSNAME = "reworkProcessName";
	protected static final String FIELD_ISLOCKVERSION = "isLockVersion";
	
	protected ListTableManagerField lotListField;
	protected ListTableManagerField materialListField;
	protected CustomField flowTreeCustomField;
	protected FlowCustomComposite flowCustomComposite;
	
	protected EntityBlock block;
	protected EntityForm entityForm;
	
	protected TextField lotIdField;
	protected TextField lotQtyField;
	protected TextField lotNumberQtyField;
	protected SearchField partNameField;
	protected RefTableField partVersionField;
	protected RefTableField reworkProcessNameField;
	
	protected ToolItem itemSave;
	protected AuthorityToolItem itemApprove;
	protected AuthorityToolItem itemHold;
	protected AuthorityToolItem itemBom;
	protected AuthorityToolItem itemClose;
	protected ToolItem itemDelete;
	
	protected Part part = null;
	
	public boolean isVerQty = true;
	
	public boolean isitemBom = true;
	
	public static String KEY_BOM = "bom";
	
	public static String WORKORDER_NAME = "Wip.WorkOrder";
	
	public boolean isLoading = false;
	
	protected Boolean isCaseSensitive;
	
	protected void postAddFields(Object object) {
		try {
			if (object instanceof EntityForm) {
				EntityForm entityForm = (EntityForm)object;	
				if (entityForm.getFields().containsKey(FIELD_ISLOCKVERSION)) {
					//???????????????????汾??λ??????
					SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
					boolean isDisplayLockVersion = MesCfMod.isUseIsLockVersion(Env.getOrgRrn(), sysParamManager);
					if (!isDisplayLockVersion) {
						entityForm.getFields().remove(FIELD_ISLOCKVERSION);
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}		
	};
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotListField = form.getFieldByControlId(FIELD_LOTLIST, ListTableManagerField.class);
		flowTreeCustomField = form.getFieldByControlId(FORM_PARTFLOW, CustomField.class);
		flowCustomComposite = (FlowCustomComposite) flowTreeCustomField.getCustomComposite();
		materialListField = form.getFieldByControlId(FIELD_MATERIALIST, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNAPPROVE), this::approveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_HOLD), this::holdAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_BOM), this::bomAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLOSE), this::closeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		lotIdField = form.getFieldByControlId(FORM_LOT_ID, TextField.class);
		lotQtyField = form.getFieldByControlId(FORM_LOT_QTY, TextField.class);
		lotNumberQtyField = form.getFieldByControlId(FORM_LOT_NUMBER, TextField.class);
		
		subscribeAndExecute(eventBroker, lotIdField.getFullTopic(BUTTON_NAME_ADD), this::addAdapter);
    	subscribeAndExecute(eventBroker, lotIdField.getFullTopic(BUTTON_NAME_DELETE), this::removeAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		
		init();
	}
	
	protected void init() {
		block = (EntityBlock) form.getBlock();
		entityForm = (EntityForm) form.getSubFormById(FORM_BASIC_INFO);
		 
		itemSave = (ToolItem) form.getButtonByControl(null, BUTTON_SAVE);
		itemApprove = (AuthorityToolItem) form.getButtonByControl(null, BUTTON_UNAPPROVE);
		itemHold = (AuthorityToolItem) form.getButtonByControl(null, BUTTON_HOLD);
		itemBom = (AuthorityToolItem) form.getButtonByControl(null, BUTTON_BOM);
		itemClose = (AuthorityToolItem) form.getButtonByControl(null, BUTTON_CLOSE);
		itemDelete = (ToolItem) form.getButtonByControl(null, BUTTON_DELETE);
		
		partNameField = entityForm.getFieldByControlId(FIELD_PARTNAME, SearchField.class);
		partVersionField = entityForm.getFieldByControlId(FIELD_PARTVERSION, RefTableField.class);		
		//????汾???
		partVersionField.addValueChangeListener(new IValueChangeListener() {
			@Override
			public void valueChanged(Object arg0, Object arg1) {
				try {
					if (flowTreeCustomField != null && !isLoading) {
						if (partVersionField != null && !StringUtil.isEmpty(partVersionField.getText()) && partVersionField.getText().matches("^[0-9]*$")) {
							partInternalRefresh(partNameField.getText(), Long.valueOf(partVersionField.getText()));
							if (reworkProcessNameField != null) {
								reworkProcessNameField.refresh();
							}
						}
					}
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
					return;
				}
			}
		});
		
		//???????
		reworkProcessNameField = entityForm.getFieldByControlId(FIELD_REWORKPROCESSNAME, RefTableField.class);
		if (reworkProcessNameField != null) {
			reworkProcessNameField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					try {
						if (flowTreeCustomField != null && !isLoading) {
							if (reworkProcessNameField.getData() != null) {
								Part curPart = (Part) partNameField.getData();
								MaterialAltProcess altProcess = (MaterialAltProcess)reworkProcessNameField.getData();
								loadFlowTreeByPartAltProcess(curPart, null, altProcess.getProcessName(), altProcess.getProcessVersion());
							} else {
								partInternalRefresh(partNameField.getText(), null);
							} 
						}
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
						return;
					}
				}
			});
		}
		
		statusChanged(null, null);
		
		try {
			//????????????????bom????????
			//???????????????????????BOM,?????BOM Item
			ADManager adManager = Framework.getService(ADManager.class);
			List<ADAuthority> authorities = adManager.getEntityList(Env.getOrgRrn(), 
					ADAuthority.class, 1, " name = '" + WORKORDER_NAME + "." + KEY_BOM + "'", "");
			if (authorities.isEmpty()) {
				isitemBom = false;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		
		if(!isitemBom) {
			itemBom = null;
		}
		WorkOrder workOrder =  new WorkOrder();
		workOrder.setOrgRrn(Env.getOrgRrn());
		entityForm.setObject(workOrder);
		
		if (lotNumberQtyField != null) {
			lotNumberQtyField.setText("1");
			lotQtyField.setText("25");
		}
	}
	
	//???
	protected void selectionChanged(Object object) {
		try {
			Event event = (Event) object;
			StructuredSelection selection = (StructuredSelection) event.getProperty(GlcEvent.PROPERTY_DATA);
			WorkOrder workOrder = (WorkOrder)selection.getFirstElement();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				entityForm.setObject(entityManager.getEntity((WorkOrder) workOrder));
				statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
			}
			isLoading = true;
			
			//????entityForm
			entityForm.loadFromObject();
			
			if (lotListField != null) {
				//lotList?б?
				lotListLoadFromObject();
			}
			
			if (flowCustomComposite != null) {
				//partFlow
				partFlowLoadFromObject();
			}
			
			if (materialListField != null) {
				//bomLine?б?
				bomLinesLoadFromObject();
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			isLoading = false;
		}
	}
	
	//?????
	public void statusChanged(String docStatus, String holdStatus) {
		if (Documentation.STATUS_CREATED.equals(docStatus)) {
            itemSave.setEnabled(true);
            if (itemApprove != null) {
            	itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText("  " + Message.getString(ExceptionBundle.bundle.CommonApprove()) + "  ");
     			itemApprove.setEnabled(true);
            }
            itemDelete.setEnabled(true);
            if (itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(true);
            }
            if (itemHold != null) {
            	itemHold.setEnabled(false);
            }
        } else if (Documentation.STATUS_APPROVED.equals(docStatus)) {
        	itemSave.setEnabled(false);
        	if (itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText(Message.getString(ExceptionBundle.bundle.CommonUnApprove()));
    			itemApprove.setEnabled(true);
        	}
            itemDelete.setEnabled(false);
            if (itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if (itemHold != null) {
            	itemHold.setEnabled(true);
            }
        } else if (WorkOrder.STATUS_STARTED.equals(docStatus)) {
        	itemSave.setEnabled(false);
        	if (itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText(Message.getString(ExceptionBundle.bundle.CommonUnApprove()));
      			itemApprove.setEnabled(false);
        	}
            itemDelete.setEnabled(false);
            if (itemClose != null) {
            	itemClose.setEnabled(true);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if (itemHold != null) {
            	itemHold.setEnabled(true);
            }
        } else if (WorkOrder.STATUS_CLOSED.equals(docStatus)) {
        	itemSave.setEnabled(false);
        	if (itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText(Message.getString(ExceptionBundle.bundle.CommonUnApprove()));
    			itemApprove.setEnabled(false);
        	}
            itemDelete.setEnabled(false);
            if (itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if (itemHold != null) {
            	itemHold.setEnabled(false);
            }
        } else {
        	itemSave.setEnabled(true);
        	if(itemApprove != null) {
        		itemApprove.setImage(SWTResourceCache.getImage("approve"));
                itemApprove.setText(Message.getString(ExceptionBundle.bundle.CommonUnApprove()));
     			itemApprove.setEnabled(false);
        	}
            itemDelete.setEnabled(false);
            if (itemClose != null) {
            	itemClose.setEnabled(false);
            }
            if (itemBom != null) {
            	itemBom.setEnabled(false);
            }
            if (itemHold != null) {
            	itemHold.setEnabled(false);
            }
        }
		
        if (WorkOrder.HOLDSTATE_OFF.equals(holdStatus)) {
        	if (itemHold != null) {
        		itemHold.setImage(SWTResourceCache.getImage("hold"));
        		itemHold.setText(Message.getString("wip.hold"));
        	}
        } else {
        	if(itemHold != null) {
        		itemHold.setImage(SWTResourceCache.getImage("release"));
        		itemHold.setText(Message.getString("wip.release"));
        	}
        }
    }
	
	//??????????
	protected void addAdapter(Object object) {
		try {
			if (lotListField.getListTableManager().getInput() != null) {
				for (WorkOrderLot woLot : ((List<WorkOrderLot>) lotListField.getListTableManager().getInput())) {
					if (lotIdField.getText().equalsIgnoreCase(woLot.getLotId())) {
						UI.showError(Message.getString("wip.lotid_repeat"));
						return;
					}
				}
			}
			List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
			if (lotListField.getListTableManager().getInput() != null) {
				workOrderLots.addAll((List<WorkOrderLot>) lotListField.getListTableManager().getInput());
			}
			long lotNumber = Long.parseLong(lotNumberQtyField.getText());
			BigDecimal mainQty = new BigDecimal(lotQtyField.getText());
			for (int i = 0; i < lotNumber; i++) {
				WorkOrderLot workOrderLot = new WorkOrderLot();
				workOrderLot.setOrgRrn(Env.getOrgRrn());
				workOrderLot.setIsActive(true);
				if (lotIdField.getText() != null && !"".equals(lotIdField.getText())) {
					String lotId = lotIdField.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					workOrderLot.setLotId(lotId);
				}
				workOrderLot.setMainQty(mainQty);
				workOrderLots.add(workOrderLot);
			}
			
			lotListField.getListTableManager().setInput(workOrderLots);
		} catch (Exception e) {
			e.printStackTrace();
			UI.showError(Message.getString(ExceptionBundle.bundle.ErrorInvalidNumber()));
		}
	}
	
	//?????????
	protected void removeAdapter(Object object) {
		try {		
			List<Object> removeWLots = lotListField.getListTableManager().getCheckedObject();
			for (Object removeWoLot : removeWLots) {
				WorkOrderLot pre = (WorkOrderLot) removeWoLot;
				((List<WorkOrderLot>) lotListField.getListTableManager().getInput()).remove(pre);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ????????
	 * @param object
	 */
	protected void newAdapter(Object object) {
		try {			
			WorkOrder workOrder = new WorkOrder();
			workOrder.setOrgRrn(Env.getOrgRrn());
			entityForm.setObject(workOrder);
			entityForm.loadFromObject();
			statusChanged(null, null);
			block.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ???湤??
	 * @param object
	 */
	protected void saveAdapter(Object object) {
		try {
			entityForm.removeAllMessages();
			if (entityForm.saveToObject()) {
				WorkOrder workOrder = (WorkOrder) entityForm.getObject();

				// Ч?????α?
				List<WorkOrderLot> startLots = new ArrayList<WorkOrderLot>();
				if (lotListField != null && lotListField.getListTableManager().getInput() != null) {
					startLots.addAll((List<WorkOrderLot>) lotListField.getListTableManager().getInput());
				}

				if (workOrder.getMainQty() == null) {
					return;
				}
				// ??鹤????????????????????????
				if (lotListField != null && getLotQty().compareTo(workOrder.getMainQty()) != 0) {
					UI.showError(Message.getString("wip.wo_qty_match_total_lot_qty"));
					return;
				}
				workOrder.setWorkOrderLots(startLots);
				workOrder.setStartedMainQty(BigDecimal.ZERO);
				
				boolean isSaveLot = false;
		    	boolean isSaveSource = false;
		    	if (lotListField != null && CollectionUtils.isNotEmpty(lotListField.getListTableManager().getInput())) {
		    		isSaveLot = true;
		    	}
		    	
				PpManager ppManager = Framework.getService(PpManager.class);
				ADBase obj = ppManager.saveWorkOrder(workOrder, null, null, false, isSaveLot, isSaveSource, false, Env.getSessionContext());
				if (obj == null) {
					return;
				}
				ADManager entityManager = getADManger();
				ADBase newBase = entityManager.getEntity(obj);
				if (form.getAdForm().getAdTable().isContainAttribute()) {
					newBase.setAttributeValues(entityManager.getEntityAttributeValues(
							form.getAdForm().getAdTable().getModelName(), newBase.getObjectRrn().longValue()));
				}
				if (workOrder.getObjectRrn() == null)
					block.refreshAdd(newBase);
				else {
					block.refreshUpdate(newBase);
				}
				entityForm.setObject(newBase);
				refreshAdapter(object);
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ???????????????????????WorkOrder??Main???
	 * @return
	 */
	protected BigDecimal getLotQty() {
		BigDecimal generationLotQty = BigDecimal.ZERO;
		 List<WorkOrderLot> startLots = new ArrayList<WorkOrderLot>();
         if (lotListField != null && lotListField.getListTableManager().getInput() != null) {
             startLots.addAll((List<WorkOrderLot>) lotListField.getListTableManager().getInput());
         }
        for (WorkOrderLot woLot : startLots) {
            generationLotQty = generationLotQty.add(woLot.getMainQty());
        }
        return generationLotQty;
	}
    
	/**
	 * ??????
	 * @param object
	 */
	protected void approveAdapter(Object object) {
		try {
			entityForm.removeAllMessages();
			PpManager ppManager = Framework.getService(PpManager.class);
			ADManager adManager = Framework.getService(ADManager.class);
			if (entityForm.saveToObject()) {
				WorkOrder workOrder = (WorkOrder) entityForm.getObject();
				if (workOrder != null && workOrder.getObjectRrn() != null) {
					if (Documentation.STATUS_APPROVED.equals(workOrder.getDocStatus())) {
						if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmUnApprove()))) {
							workOrder = ppManager.unApproveWorkOrder(workOrder, Env.getSessionContext());
							UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonUnApproveSuccessed()));
						}
					} else {
						List<WorkOrderBomLine> bomLines = null;
						if (itemBom != null) {
							// ???BOM?????????
							bomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
							if (bomLines == null || bomLines.isEmpty()) {
								UI.showWarning(Message.getString("pp.workorder_bom_is_not_exist"));
								return;
							}
						}
						// ?????д???ЩTabδ??д
						if (lotListField != null && CollectionUtils.isNotEmpty(lotListField.getListTableManager().getInput())) {
							// ????????????????
							List<WorkOrderLot> workOrderLot = adManager.getEntityList(Env.getOrgRrn(), WorkOrderLot.class,
									Env.getMaxResult(), " workOrderRrn = " + workOrder.getObjectRrn(), null);
							if (workOrderLot == null) {
								UI.showWarning(Message.getString("pp.workorder_lot_is_not_exist"));
								return;
							}
						}
						workOrder = ppManager.approveWorkOrder(workOrder, Env.getSessionContext());
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonApproveSuccessed()));
					}
					statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
					entityForm.setObject(workOrder);
					entityForm.loadFromObject();
					block.refreshUpdate(workOrder);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ???????
	 * @param object
	 */
	protected void holdAdapter(Object object) {
		try {
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				PpManager ppManager = Framework.getService(PpManager.class);
				if (WorkOrder.HOLDSTATE_OFF.equals(workOrder.getHoldState())) {
					workOrder = ppManager.holdWorkOrder(workOrder, Env.getSessionContext());
					UI.showInfo(Message.getString("wip.hold_successed"));
				} else {
					workOrder = ppManager.releaseWorkOrder(workOrder, Env.getSessionContext());
					UI.showInfo(Message.getString("wip.release_successed"));
				}
				statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
				entityForm.setObject(workOrder);
				entityForm.loadFromObject();
				block.refreshUpdate(workOrder);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	/**
	 * bom
	 * @param object
	 */
	protected void bomAdapter(Object object) {
		try {
			if (((WorkOrder) entityForm.getObject()).getObjectRrn() != null) {
	            WorkOrderBomContext context = new WorkOrderBomContext();
	            context.setWorkOrder((WorkOrder) entityForm.getObject());
	            WorkOrderBomWizard orderBomWizard = new WorkOrderBomWizard(context, "WorkOrderBom");
	            WorkOrderBomDialog dialog = new WorkOrderBomDialog(Display.getCurrent().getActiveShell(), orderBomWizard);
	            if (dialog.open() == Dialog.OK) {
	            	ADManager adManager = Framework.getService(ADManager.class);
	            	entityForm.setObject(adManager.getEntity((WorkOrder) entityForm.getObject()));
	            	refreshAdapter(object);
				}
            }
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ??????
	 * @param object
	 */
	protected void closeAdapter(Object object) {
		try {
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmClose()))) {
					PpManager ppManager = Framework.getService(PpManager.class);
					// COMPLETE
					WorkOrder newWorkOrder = (WorkOrder) ppManager.completedWorkOrder(workOrder, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonCloseSuccessed()));
					entityForm.setObject(newWorkOrder);
					refreshAdapter(object);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ???
	 * @param object
	 */
	protected void deleteAdapter(Object object) {
		try {
			WorkOrder workOrder = (WorkOrder) block.getTableManager().getSelectedObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				boolean confirmDelete = UI.showConfirm(Message.getString("wip.wo_sure_delete"));
				if (confirmDelete) {
					PpManager ppManager = Framework.getService(PpManager.class);
					ppManager.deleteWorkOrder(workOrder, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
					entityForm.setObject(new WorkOrder());
					refreshAdapter(object);
				}
			} else {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ???
	 * @param object
	 */
	protected void refreshAdapter(Object object) {
		try {
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				block.refreshUpdate(workOrder);
				statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
				//????entityForm
				entityForm.loadFromObject();
				
				if (lotListField != null) {
					//lotList?б?
					lotListLoadFromObject();
				}
				
				if (flowCustomComposite != null) {
					//partFlow
					partFlowLoadFromObject();
				}
				
				if (materialListField != null) {
					//bomLine?б?
					bomLinesLoadFromObject();
				}
			} else {
				entityForm.setObject(new WorkOrder());
				entityForm.loadFromObject();
				statusChanged(null, null);
				block.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ?????????б????
	 */
	protected void lotListLoadFromObject() {
		if (lotListField != null) {
			lotListField.getListTableManager().getInput().clear();
		}
		try {
			if (entityForm.getObject() != null 
					&& ((WorkOrder) entityForm.getObject()).getObjectRrn() != null) {
				WorkOrder workOrder = (WorkOrder) entityForm.getObject();
				List<WorkOrderLot> workOrderLots = workOrder.getWorkOrderLots();
				if (workOrderLots == null) {
					ADManager adManager = Framework.getService(ADManager.class);
					workOrderLots = adManager.getEntityList(Env.getOrgRrn(), WorkOrderLot.class, Env.getMaxResult(), 
							" workOrderRrn = " + workOrder.getObjectRrn(), null);
				}
				lotListField.getListTableManager().setInput(workOrderLots);
				
				if (StringUtils.isEmpty(workOrder.getPartName())) {
					return;
				}
				PrdManager prdManager = Framework.getService(PrdManager.class);
				Part part = null;
				if (workOrder.getPartVersion() == null) {
					part = prdManager.getActivePart(Env.getOrgRrn(), workOrder.getPartName(), true);
				} else {
					part = prdManager.getPartById(Env.getOrgRrn(), workOrder.getPartName(), workOrder.getPartVersion());
				}
				if (part != null && part.getLotSize() != null) {
					lotQtyField.setText(String.valueOf(part.getLotSize()));
				} else {
					lotQtyField.setText("25");
				}
			}	
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}
	
	/**
	 * ??????????, ??????????????
	 */
	protected void partFlowLoadFromObject() {
		try {
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				WorkOrder tempWordOrder = new WorkOrder();
				tempWordOrder.setObjectRrn(workOrder.getObjectRrn());
				WorkOrder nextWordOrder = (WorkOrder) adManager.getEntity(tempWordOrder);

				String partName = nextWordOrder.getPartName();
				Long partVersion = nextWordOrder.getPartVersion();
				//???????汾?????治?????????
				Boolean isNewPart = true;
				if (part !=null 
						&& part.getName().equals(workOrder.getPartName()) 
						&& part.getVersion().equals(workOrder.getPartVersion())) {
						isNewPart = false;
				}
				if (StringUtils.isNotEmpty(partName) && isNewPart) {
					if (partVersion != null) {
						// ???????汾????????
						List<Part> verisonParts = adManager.getEntityList(Env.getOrgRrn(), Part.class,
								Integer.MAX_VALUE, " name = '" + partName + "' and version = " + partVersion, "");
						if (verisonParts != null && verisonParts.size() > 0) {
							part = verisonParts.get(0);
							flowCustomComposite.loadFlowTreeByPart(part, null);
						} else {
							part = null;
							flowCustomComposite.loadFlowTreeByPart(null, null);
						}
					} else {
						// ?汾??????????????????????
						List<Part> activeParts = adManager.getEntityList(Env.getOrgRrn(), Part.class,
								Integer.MAX_VALUE, " name = '" + partName + "' and status = 'Active' ", "");
						if (activeParts != null && activeParts.size() > 0) {
							part = activeParts.get(0);
							flowCustomComposite.loadFlowTreeByPart(part, null);
						} else {
							part = null;
							flowCustomComposite.loadFlowTreeByPart(null, null);
						}
					}
				}
			} else {
				part = null;
				flowCustomComposite.loadFlowTreeByPart(null, null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ???????????????????????
	 * @param part
	 * @param stepPath
	 * @param altProcessName
	 * @param altProcessVersion
	 */
	protected void loadFlowTreeByPartAltProcess(Part part, String stepPath, String altProcessName, Long altProcessVersion) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			if (part != null) {
				Process process = new Process();
				if (part.getIsAlternateProcess() && !StringUtil.isEmpty(altProcessName) && altProcessVersion != null) {
        			process.setOrgRrn(Env.getOrgRrn());
    				process.setName(altProcessName);
    				process.setVersion(altProcessVersion);
					process = (Process) prdManager.getSimpleProcessDefinition(process);
        		} else {	
        			process = (Process)prdManager.getPartProcess(part);
        		}
				process = (Process)prdManager.getProcessDefinition(process);
				List<Process> processes = new ArrayList<Process>();
				processes.add(process);
				flowCustomComposite.getTreeManager().setInput(processes);
				if (stepPath != null) {
					List<ADBase> processNodes = new ArrayList<ADBase>();
					processNodes.add(process);
					List<Node> nodes = process.getNodes().stream().filter(s -> s.getPath().equals(stepPath)).collect(Collectors.toList());
					processNodes.addAll(nodes);
					flowCustomComposite.setCurrentFlow((List)processNodes);	
				} else {
					flowCustomComposite.getViewer().expandToLevel(2);
				}
			} else {
				flowCustomComposite.getTreeManager().setInput(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	/**
	 * ??????????????????
	 * @param partName
	 * @param partVersion
	 */
	protected void partInternalRefresh(String partName, Long partVersion) {
		if (entityForm.getObject() != null) {
			try {
				ADManager adManager = Framework.getService(ADManager.class);
				Part curPart = (Part) partNameField.getData();
				if (part != null 
						&& curPart != null 
						&& curPart.getName().equals(part.getName()) 
						&& curPart.getVersion().equals(part.getVersion())) {
					//???????汾?????治?????????
				} else {
					if (partName != null) {
						if (partVersion != null) {
							// ???????汾????????
							List<Part> verisonParts = adManager.getEntityList(Env.getOrgRrn(), Part.class, Integer.MAX_VALUE,
									" name = '" + partName + "' and version = " + partVersion, "");
							if (verisonParts != null && verisonParts.size() > 0) {
								Part versionPart = verisonParts.get(0);
								part = versionPart;
								flowCustomComposite.loadFlowTreeByPart(part, null);
							} else {
								part = null;
								flowCustomComposite.loadFlowTreeByPart(null,null);
							}
						} else {
							// ?汾??????????????????????
							List<Part> activeParts = adManager.getEntityList(Env.getOrgRrn(), Part.class, Integer.MAX_VALUE,
									" name = '" + partName + "' and status = 'Active' ", "");
							if (activeParts != null && activeParts.size() > 0) {
								Part activePart = activeParts.get(0);
								part = activePart;
								flowCustomComposite.loadFlowTreeByPart(part, null);
							}else {
								part = null;
								flowCustomComposite.loadFlowTreeByPart(null, null);
							}
						}
					}
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		}
	}
	
	/**
	 * ????BomLines
	 */
	protected void bomLinesLoadFromObject() {	
		try {
			if (entityForm.getObject() != null && ((WorkOrder) entityForm.getObject()).getObjectRrn() != null) {
				List<WorkOrderBomLine> bomLines = new ArrayList<WorkOrderBomLine>();
				PpManager ppManager = Framework.getService(PpManager.class);
				bomLines = ppManager.getWorkOrderBomLines((WorkOrder) entityForm.getObject(), Env.getSessionContext());
				materialListField.getListTableManager().setInput(bomLines);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}	
	}
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}

}
