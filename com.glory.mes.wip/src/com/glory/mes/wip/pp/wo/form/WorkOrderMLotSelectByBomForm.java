package com.glory.mes.wip.pp.wo.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.pp.model.WorkOrderSource;
import com.glory.mes.wip.mm.MaterialRequisitionLine;
import com.glory.mes.wip.pp.wo.WorkOrderMLotTableSelectField;

/**
 * ���ݹ���ѡ��Դ��������
 * ���Դ�������������Ƿ����������������,���ݹ���BOMѡ����������
 */
public class WorkOrderMLotSelectByBomForm extends WorkOrderMLotSelectForm {
	
	private static final String TABLE_NAME = "PPWorkOrderMLotSelect";
	private boolean isVerQty = false;
	private List<MaterialRequisitionLine> materialRequisitionLines = null;
    
	public WorkOrderMLotSelectByBomForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng, boolean isVerQty) {
		super(parent, style, object, tab, mmng);
		setVerQty(isVerQty);
	}

	@Override
	protected void createContent() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);
		
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
	        ListTableManager sourceTableManager = new ListEditorTableManager(adTable,true);
	        
			lotTableSelectField = new WorkOrderMLotTableSelectField("", sourceTableManager, getWhereClause());
	        lotTableSelectField.createContent(body, toolkit);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void loadFromObject() {
		if (object == null) {
			return;
		}
		WorkOrder workOrder = (WorkOrder) object;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<WorkOrderLot> workOrderLot = adManager.getEntityList(Env.getOrgRrn(),WorkOrderLot.class, Env.getMaxResult(),  " workOrderRrn = " + workOrder.getObjectRrn(), null);
		    BigDecimal generationLotQty = BigDecimal.ZERO;
			for(WorkOrderLot wol :workOrderLot){
				generationLotQty = generationLotQty.add(wol.getMainQty());
			}
			lotTableSelectField.setVerQty(isVerQty());
			PpManager ppManager = Framework.getService(PpManager.class);

			setMaterialRequisitionLines(ppManager.getMaterialRequisitionLines(workOrder, Env.getSessionContext()));
			lotTableSelectField.setMaterialRequisitionLines(getMaterialRequisitionLines());
			lotTableSelectField.setGenerationLotQty(generationLotQty);
			
			List<WorkOrderSource> workOrderSources = adManager.getEntityList(Env.getOrgRrn(), WorkOrderSource.class, 
                    Env.getMaxResult(), "woRrn = " + workOrder.getObjectRrn(), null);
            List<MLot> mlots = new ArrayList<MLot>();
            for (WorkOrderSource workOrderSource : workOrderSources) {
                MLot mlot = new MLot();
                mlot.setObjectRrn(workOrderSource.getSourceMLotRrn());
                mlot = (MLot) adManager.getEntity(mlot);
                mlot.setTransMainQty(workOrderSource.getMainQty());
                mlots.add(mlot);
            }
            lotTableSelectField.setValue(mlots);
            setWhereClause(getWhereClause());
            refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	// ���ݹ�����BOMLines ����sql������ֻ��ʾ������������������
	protected String getWhereClause() {
		StringBuffer whereClause = new StringBuffer();
		
		if (object == null) {
			return whereClause.toString();
		}
		
		WorkOrder workOrder = (WorkOrder) object;
		try {
			PpManager ppManager = Framework.getService(PpManager.class);
			List<WorkOrderBomLine> bomLines = ppManager.getWorkOrderBomLines(workOrder, Env.getSessionContext());
			if (bomLines == null || bomLines.size() == 0) {
				return whereClause.toString();
			}
			whereClause.append(" materialName in (");
			for (WorkOrderBomLine workOrderBomLine : bomLines) {
				whereClause.append("'"+workOrderBomLine.getMaterialName()+"',");
			}
			whereClause.replace(0, whereClause.length(), whereClause.substring(0, whereClause.length()-1));
			whereClause.append(")");
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return whereClause.toString();
	}

	public boolean isVerQty() {
		return isVerQty;
	}

	public void setVerQty(boolean isVerQty) {
		this.isVerQty = isVerQty;
	}

	public List<MaterialRequisitionLine> getMaterialRequisitionLines() {
		return materialRequisitionLines;
	}

	public void setMaterialRequisitionLines(List<MaterialRequisitionLine> materialRequisitionLines) {
		this.materialRequisitionLines = materialRequisitionLines;
	}
	
}
