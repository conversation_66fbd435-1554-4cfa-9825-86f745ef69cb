package com.glory.mes.wip.pp.wo.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.graphics.Rectangle;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * ���ɹ�������
 */
public class WorkOrderLotGenerateForm extends EntityForm {

	protected ADTable lotAdTable;
	protected ListTableManager lotTableManager;
	protected Text txtLotSize;
	protected Text txtLotCount;
	protected Text lotIdText;
	
	public static final String TABLE_NAME = "PPWorkOrderLots"; 

	public WorkOrderLotGenerateForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	@Override
	public void createForm() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);
		
		Composite top = toolkit.createComposite(body);
		top.setLayout(new GridLayout(8, false));
		GridData gd = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		top.setLayoutData(gd);

		GridData gText = new GridData(GridData.FILL_HORIZONTAL);
		gText.widthHint = 60;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			toolkit.createLabel(top, Message.getString("common.lotqty"));
			txtLotSize = toolkit.createText(top, "", SWT.BORDER);
			txtLotSize.setLayoutData(gText);
			txtLotSize.setTextLimit(32);
			txtLotSize.setText("25");

			gText = new GridData(GridData.FILL_HORIZONTAL);
			gText.widthHint = 60;
			toolkit.createLabel(top, Message.getString("common.lot_number"));
			txtLotCount = toolkit.createText(top, "", SWT.BORDER);
			txtLotCount.setLayoutData(gText);
			txtLotCount.setTextLimit(32);
			txtLotCount.setText("1");

			gText = new GridData(GridData.FILL_HORIZONTAL);
			gText.widthHint = 140;
			toolkit.createLabel(top, Message.getString("wip.lot_id"));
			lotIdText = toolkit.createText(top, "", SWT.BORDER);
			lotIdText.setLayoutData(gText);
			lotIdText.setTextLimit(32);

			SysParameterManager sysParameterManager = Framework.getService(SysParameterManager.class);
			String regEx = MesCfMod.getLotNamingRule(0, sysParameterManager);

			lotIdText.addVerifyListener(new VerifyListener() {
				public void verifyText(VerifyEvent e) {
					// ������ʽ��֤
					Pattern pattern = Pattern.compile(regEx);
					Matcher matcher = pattern.matcher(e.text);
					if (matcher.matches()) {
						// �����Сд��ĸ���л��ߡ��»��ߡ����֡���
						e.doit = true;
					} else if (e.text.length() > 0) {
						// �����������
						e.doit = false;
					} else {
						// ���Ƽ�
						e.doit = true;
					}
				}
			});
			
			SquareButton addLotBtn = UIControlsFactory.createButton(top, UIControlsFactory.BUTTON_DEFAULT);
			addLotBtn.setText(Message.getString(ExceptionBundle.bundle.CommonAdd()));
			addLotBtn.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent event) {
					try {
						if (lotTableManager.getInput() != null) {
							for (WorkOrderLot woLot : ((List<WorkOrderLot>) lotTableManager.getInput())) {
								if (lotIdText.getText().equalsIgnoreCase(woLot.getLotId())) {
									UI.showError(Message.getString("wip.lotid_repeat"));
									return;
								}
							}
						}
						List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
						if (lotTableManager.getInput() != null) {
							workOrderLots.addAll((List<WorkOrderLot>) lotTableManager.getInput());
						}
						long lotNumber = Long.parseLong(txtLotCount.getText());
						BigDecimal mainQty = new BigDecimal(txtLotSize.getText());
						for (int i = 0; i < lotNumber; i++) {
							WorkOrderLot workOrderLot = new WorkOrderLot();
							workOrderLot.setOrgRrn(Env.getOrgRrn());
							workOrderLot.setIsActive(true);
							if (lotIdText.getText() != null && !"".equals(lotIdText.getText())) {
								String lotId = lotIdText.getText();
								if (!isLotIdCaseSensitive()) {
									lotId = lotId.toUpperCase();
								}
								workOrderLot.setLotId(lotId);
							}

							workOrderLot.setMainQty(mainQty);
							workOrderLots.add(workOrderLot);
						}
						
						lotTableManager.setInput(workOrderLots);
						lotQtyChange();
					} catch (Exception e) {
						e.printStackTrace();
						UI.showError(Message.getString(ExceptionBundle.bundle.ErrorInvalidNumber()));
					}
				}
			});
			
			SquareButton removeLotbtn = UIControlsFactory.createButton(top, UIControlsFactory.BUTTON_DEFAULT);
			removeLotbtn.setText(Message.getString(ExceptionBundle.bundle.CommonDelete()));
			removeLotbtn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					List<Object> removeWLots = lotTableManager.getCheckedObject();
					for (Object removeWoLot : removeWLots) {
						WorkOrderLot pre = (WorkOrderLot) removeWoLot;
						((List<WorkOrderLot>) lotTableManager.getInput()).remove(pre);
					}
				}
			});
			Composite tableCom = toolkit.createComposite(body);
			tableCom.setLayout(new GridLayout(1, false));
			GridData tablegd = new GridData(GridData.FILL_BOTH);
			tableCom.setLayoutData(tablegd);

			lotAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			lotTableManager = new ListTableManager(lotAdTable, true);
			lotTableManager.setIndexFlag(true);
			lotTableManager.newViewer(tableCom);

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void lotQtyChange() {
		fireFormDataChanged(this, getLotQty());
	}

	@Override
	public List<String> getCopyProperties() {
		List<String> properties = new ArrayList<String>();
		properties.add("workOrderLots");
		return properties;
	}
	
	@Override
	public boolean saveToObject() {
		if (object != null) {
			WorkOrder workOrder = (WorkOrder) object;
			//if (workOrder.getObjectRrn() != null) {
				if (!validate()) {
					return false;
				}
				List<WorkOrderLot> startLots = new ArrayList<WorkOrderLot>();
				if (lotTableManager.getInput() != null) {
					startLots.addAll((List<WorkOrderLot>) lotTableManager.getInput());
				}

				if (workOrder.getMainQty() == null) {
					return false;
				}
				// ��鹤�������������������Ƿ�һ��
				if (getLotQty().compareTo(workOrder.getMainQty()) != 0) {
					UI.showError(Message.getString("wip.wo_qty_match_total_lot_qty"));
					return false;
				}
				workOrder.setWorkOrderLots(startLots);
			//}
		}
		return true;
	}
	
	@Override
	public void loadFromObject() {
		lotTableManager.getInput().clear();
		if (object != null) {
			try {
				WorkOrder workOrder = (WorkOrder) object;
				List<WorkOrderLot> workOrderLots = workOrder.getWorkOrderLots();
				if (workOrderLots == null) {
					ADManager adManager = Framework.getService(ADManager.class);
					workOrderLots = adManager.getEntityList(Env.getOrgRrn(),
		                    WorkOrderLot.class, Env.getMaxResult(), " workOrderRrn = " + workOrder.getObjectRrn(), null);
				}
				PrdManager prdManager = Framework.getService(PrdManager.class);
				Part part = null;
				if (workOrder.getPartVersion() == null) {
					if(workOrder.getPartName() == null || "".equals(workOrder.getPartName())){
						return;
					}
					part = prdManager.getActivePart(Env.getOrgRrn(), workOrder.getPartName(), true);
				} else {
					part = prdManager.getPartById(Env.getOrgRrn(), workOrder.getPartName(), workOrder.getPartVersion());
				}
				if (part != null && part.getLotSize() != null) {
					setDefaultSize(String.valueOf(part.getLotSize()));
				} else {
					setDefaultSize("25");
				}
	            lotTableManager.setInput(workOrderLots);
	            lotQtyChange();
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}
	
	protected BigDecimal getLotQty() {
		BigDecimal generationLotQty = BigDecimal.ZERO;
		 List<WorkOrderLot> startLots = new ArrayList<WorkOrderLot>();
         if (lotTableManager.getInput() != null) {
             startLots.addAll((List<WorkOrderLot>) lotTableManager.getInput());
         }
        for (WorkOrderLot woLot : startLots) {
            generationLotQty = generationLotQty.add(woLot.getMainQty());
        }
        return generationLotQty;
	}
	
	public List<WorkOrderLot> getWorkOrderLots() {
		List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
		if (lotTableManager != null) {
			List<WorkOrderLot> lots = (List<WorkOrderLot>) lotTableManager.getInput();
			for (WorkOrderLot workOrderLot : lots) {
				workOrderLots.add(workOrderLot);
			}
		}
		return workOrderLots;
	}
	
	public void setDefaultSize(String size){
		txtLotSize.setText(size);
	}
	
	public void setDefaultCount(String size){
		txtLotCount.setText(size);
	}
	
	private Boolean isCaseSensitive;
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
}
