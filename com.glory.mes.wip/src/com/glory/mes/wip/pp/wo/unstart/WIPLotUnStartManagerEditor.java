package com.glory.mes.wip.pp.wo.unstart;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.wip.exception.WipExceptionBundle;

public class WIPLotUnStartManagerEditor extends GlcEditor{ 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.wo.unstart.WIPLotUnStartManagerEditor";

	private static final String FIELD_WORKORDERLOTSSELECT = "workOrderLotsSelect";

	private static final String BUTTON_UNSTARTLOT = "unStartLot";
	private static final String BUTTON_REFRESH = "refresh";
	
	protected ListTableManagerField workOrderLotsSelectField;
	private ListTableManager listTableManager;
	
	private EntityBlock block;
	private EntityForm entityForm;
	
	private ToolItem itemUnStart;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		workOrderLotsSelectField = form.getFieldByControlId(FIELD_WORKORDERLOTSSELECT, ListTableManagerField.class);
		listTableManager = workOrderLotsSelectField.getListTableManager();

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNSTARTLOT), this::unStartLotAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		
		itemUnStart = (ToolItem) ((EntityBlock)form.getBlock()).getButtonByName(BUTTON_UNSTARTLOT);
		init();
	}
	
	private void init() {
		block = (EntityBlock) form.getBlock();
		for (Form subForm : form.getSubForms()) {
			if (StringUtil.isEmpty(subForm.getId())) {
				entityForm =  (EntityForm)subForm;
			}
		}
	}
	
	private void selectionChanged(Object obj) {
		try {
			Event event = (Event) obj;
			StructuredSelection selection = (StructuredSelection) event.getProperty(GlcEvent.PROPERTY_DATA);
			ADManager adManager = Framework.getService(ADManager.class);
			if (!selection.isEmpty()) {
				WorkOrder workOrder = (WorkOrder) selection.getFirstElement();
				entityForm.setObject(workOrder);
				List<WorkOrderLot> workOrderLots = adManager.getEntityList(Env.getOrgRrn(), WorkOrderLot.class, Env.getMaxResult(),
		                " workOrderRrn = " + workOrder.getObjectRrn(), null);
				
				listTableManager.setInput(workOrderLots);
				
				statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}	

	private void unStartLotAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				PpManager ppManager = Framework.getService(PpManager.class);
				
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				if (MesCfMod.isWoStartByMlot(Env.getOrgRrn(), sysParamManager)) {
					//��ѡ�е�����ȡ��Ͷ��
					List<WorkOrderLot> unStartWorkOrderLots = getCheckedWorkOrderLots();
					if (unStartWorkOrderLots == null || unStartWorkOrderLots.isEmpty()) {
						UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
						return;
					}
					for (WorkOrderLot unStartWorkOrderLot : unStartWorkOrderLots) {
						if (!WorkOrderLot.STATUS_STARTED.equals(unStartWorkOrderLot.getState())) {
							UI.showError(Message.getString(WipExceptionBundle.bundle.WipLotStateNotAllow()));
							return;
						}
					}
					ppManager.unStartWorkOrder(workOrder, unStartWorkOrderLots, false, false, Env.getSessionContext());
				} else {
					ppManager.unStartWorkOrder(workOrder, false, true, Env.getSessionContext());
				}			
				UI.showInfo(Message.getString("wip.unstart_successed"));//������ʾ��
				refresh(new WorkOrder());
				workOrderLotsSelectField.refresh();
			} else {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	
	}

	private void refresh(WorkOrder workOrder) {
		try {
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				workOrder = (WorkOrder) adManager.getEntity(workOrder);
				block.refreshUpdate(workOrder);
				entityForm.setObject(workOrder);
				entityForm.loadFromObject();
				List<WorkOrderLot> workOrderLots = adManager.getEntityList(Env.getOrgRrn(), WorkOrderLot.class, Env.getMaxResult(),
		                " workOrderRrn = " + workOrder.getObjectRrn(), null);
				
				listTableManager.setInput(workOrderLots);
			} else {
				refreshAdapter(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void refreshAdapter(Object object) {
		block.refresh();
		entityForm.setObject(new WorkOrder());
		entityForm.loadFromObject();
		listTableManager.setInput(new ArrayList<WorkOrderLot>());
		listTableManager.refresh();
	}
	
	public void statusChanged(String newStatus, String holdStatus) {
		if (newStatus == null || "".equals(newStatus.trim())) {
			itemUnStart.setEnabled(false);
		} else if (Documentation.STATUS_APPROVED.equals(newStatus.trim())
				|| WorkOrder.STATUS_STARTED.equals(newStatus.trim())) {
			itemUnStart.setEnabled(true);
			checkHoldStatus();
		} else {
			itemUnStart.setEnabled(false);
		}
	}

	public void checkHoldStatus() {
		try {
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder == null || workOrder.getObjectRrn() == null) {
				return;
			}
			if (WorkOrder.HOLDSTATE_ON.equals(workOrder.getHoldState())) {
				itemUnStart.setEnabled(false);
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}

	public List<WorkOrderLot> getCheckedWorkOrderLots() {
		List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
		if (listTableManager != null) {
			List<Object> workOrderLotObjs = (List<Object>) listTableManager.getCheckedObject();
			for (Object obj : workOrderLotObjs) {
				WorkOrderLot workOrderLot = (WorkOrderLot) obj;
				workOrderLots.add(workOrderLot);
			}
		}
		return workOrderLots;
	}
	
}