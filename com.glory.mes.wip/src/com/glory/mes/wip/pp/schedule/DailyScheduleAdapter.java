package com.glory.mes.wip.pp.schedule;

import java.util.Map;

import org.apache.poi.ss.formula.functions.T;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.widgets.Shell;

import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;

public class DailyScheduleAdapter extends ListItemAdapter<T> {

	public static final Shell shell = new Shell();
	

}
