package com.glory.mes.wip.pp.schedule;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.CheckboxTableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseDialog;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.viewers.FixEditorTableManager;
import com.glory.framework.core.exception.ExceptionManager;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.DailyScheduleLine;
import com.glory.mes.pp.model.DailyScheduleTable;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * ������ƻ�����Wo
 */
public class DailyScheduleWoDialog extends BaseDialog {
	
	private static final Logger logger = Logger.getLogger(DailyScheduleWoDialog.class);
	
	protected ADTable adTable;
	
	protected DailyScheduleTable dailyScheduleTable;
	
	protected FixEditorTableManager tableManager;
	
	protected CheckboxTableViewer viewer;
	
	protected static final String TABLE_NAME="PPDailyScheduleLine";
	
	protected ADManager adManager;
	
	public DailyScheduleWoDialog(Shell parent, ADTable adTable, DailyScheduleTable dailyScheduleTable) {
		super(parent);
		this.adTable = adTable;
		this.dailyScheduleTable = dailyScheduleTable;
	}
	
	@Override
	protected Control buildView(Composite parent) {
		try {
			FormToolkit toolkit = new FormToolkit(getShell().getDisplay());
			ScrolledForm sForm = toolkit.createScrolledForm(parent);
			sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			List<DailyScheduleLine> dailyScheduleLineList=new ArrayList<DailyScheduleLine>();
			int isEditablCount=0;
			for(int i=0;i<adTable.getFields().size();i++){
				if (adTable.getFields().get(i).getIsEditable() == true) {
					DailyScheduleLine dailyScheduleLine = dailyScheduleTable.generateDailyScheduleLine(i+1-isEditablCount,adTable.getOrgRrn());
					if(dailyScheduleLine.getGenerateWoId() != null && !dailyScheduleLine.getGenerateWoId().equals("")){
						PpManager ppManager = Framework.getService(PpManager.class);
						WorkOrder workOrder = new WorkOrder();
						workOrder.setDocId(dailyScheduleLine.getGenerateWoId());
						workOrder = ppManager.getWorkOrder(workOrder, Env.getSessionContext());
						dailyScheduleLine.setDocStatus(workOrder.getDocStatus());
					}
					dailyScheduleLine.setDocType(WorkOrder.DOC_TYPE_WO);					
					dailyScheduleLineList.add(dailyScheduleLine);
				}else{
					isEditablCount++;
				}
			}
		
			adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(),TABLE_NAME);
			tableManager = new FixEditorTableManager(adTable);
			tableManager.addStyle(SWT.CHECK);
			viewer = (CheckboxTableViewer)tableManager.createViewer(parent, toolkit, 500);
			tableManager.setInput(dailyScheduleLineList);
			
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return parent;
	}
	
	@Override
	protected Point getInitialSize() {
		return new Point(700, 500);
	}
	
	@Override
	protected void okPressed() {
		try {
			setReturnCode(OK);
			List<? extends Object> input = (List<? extends Object>) tableManager.getCheckedInput();
			if (input.size() < 1) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
	
			if(WorkOrder.HOLDSTATE_ON.equals(dailyScheduleTable.getHoldState())){
				UI.showInfo(Message.getString("pp.workorder_is_hold"));
				return;
			}
			
			List<DailyScheduleLine> dailyScheduleLineList =new ArrayList<>();
			
			for (Object object : input) {
				DailyScheduleLine dailyScheduleLine= (DailyScheduleLine) object;
				if(dailyScheduleLine.getScheduleQty() !=null && dailyScheduleLine.getScheduleQty().compareTo(new BigDecimal(0)) == 1 ){
					if (dailyScheduleLine.getDocStatus() != null ){
						if(WorkOrder.STATUS_CREATED.equals(dailyScheduleLine.getDocStatus())){
							dailyScheduleLineList.add(dailyScheduleLine);
						}else{
							UI.showConfirm(Message.getString("wip.not_generate") + dailyScheduleLine.getGenerateWoId());
							return;
						}
					}else{
						dailyScheduleLineList.add(dailyScheduleLine);
					}
				}else{
					SimpleDateFormat dateFormater = new SimpleDateFormat("yyyy/MM/dd");
					UI.showConfirm(Message.getString("wip.not_generate_date") + dateFormater.format(dailyScheduleLine.getScheduleDate()));
					return;
				}
			}
			
			PpManager ppManager = Framework.getService(PpManager.class);
			//��������id���WorkOrder
			WorkOrder parentWorkOrder = new WorkOrder();
			parentWorkOrder.setDocId(dailyScheduleTable.getRefDocId());
			parentWorkOrder = ppManager.getWorkOrder(parentWorkOrder, Env.getSessionContext());
			
			//���BomLine
			List<WorkOrderBomLine> parentBomLines = new ArrayList<WorkOrderBomLine>();
			parentBomLines = ppManager.getWorkOrderBomLines(parentWorkOrder, Env.getSessionContext());

			ppManager.generateWoByDailyScheduleLine(dailyScheduleTable, dailyScheduleLineList, parentBomLines, true, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.generate_success"));
			close();
				
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			logger.error("Error at DailyScheduleWoDialog : okPressed" + e.getStackTrace());
			return;
		}
	}
}
