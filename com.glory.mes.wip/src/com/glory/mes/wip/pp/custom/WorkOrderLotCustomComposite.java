package com.glory.mes.wip.pp.custom;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * �������ι������
 */
public class WorkOrderLotCustomComposite extends CustomCompsite {

	private static final Logger logger = Logger.getLogger(WorkOrderLotCustomComposite.class);

	public static final String EVENT_SETINPUT = "setInput";

	public static final String TABLE_NAME = "PPWorkOrderLots"; 
	public static final String LOT_ID_NAMING_RULE = "^[A-Za-z0-9-_]+$";
	
	public static final String TXT_LOT_SIZE = "lotSize";
	public static final String TXT_LOT_COUNT = "lotCount";
	public static final String TXT_LOT_ID = "lotId";

	public static final String ATTRIBUTE_SHOW_LOT_INPUT = "ShowInput";
	public static final String ATTRIBUTE_LOT_SIZE = "LotSize";
	public static final String ATTRIBUTE_LOT_COUNT = "LotCount";
	public static final String ATTRIBUTE_SHOW_LOT_ID = "ShowLotId";
	public static final String ATTRIBUTE_LOT_ID_NAMING_RULE = "LotIdNamingRule";
	public static final String ATTRIBUTE_LOT_TABLE_NAME = "LotTableName";

	protected boolean showLotInputFlag = true;
	protected int lotSize = 32;
	protected int lotCount = 1;
	protected boolean showLotIdFlag = true;
	protected String lotIdNaming = LOT_ID_NAMING_RULE;
	protected String lotTableName = TABLE_NAME;

	protected Text txtLotSize;
	protected Text txtLotCount;
	protected Text txtLotId;
	
	protected ListTableManager lotTableManager;

	protected WorkOrder workOrder;

	protected EventHandler setInputHandler = new EventHandler() {
        public void handleEvent(Event event) {
        	Object obj = event.getProperty(GlcEvent.PROPERTY_DATA);
        	if (obj != null && obj instanceof WorkOrder) {
        		workOrder = (WorkOrder)workOrder;
        	}
        }
	};
	
	@Override
	public void initSubscribeEvent() {
		subscribeEvent(null, EVENT_SETINPUT, setInputHandler);
	}
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		try {
			parent.setLayout(new GridLayout(1, false));
			parent.setBackgroundMode(SWT.INHERIT_FORCE);

			if (showLotInputFlag) {
				int gridY = 6;
				if (showLotIdFlag) {
					gridY += 2;
				}
				Composite top = toolkit.createComposite(parent);
				top.setLayout(new GridLayout(gridY, false));
				GridData gd = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
				top.setLayoutData(gd);
	
				GridData gText = new GridData(GridData.FILL_HORIZONTAL);
				gText.widthHint = 60;
				
				toolkit.createLabel(top, Message.getString("common.lotqty"));
				txtLotSize = toolkit.createText(top, "", SWT.BORDER);
				txtLotSize.setLayoutData(gText);
				txtLotSize.setText(String.valueOf(lotSize));
	
				gText = new GridData(GridData.FILL_HORIZONTAL);
				gText.widthHint = 60;
				toolkit.createLabel(top, Message.getString("common.lot_number"));
				txtLotCount = toolkit.createText(top, "", SWT.BORDER);
				txtLotCount.setLayoutData(gText);
				txtLotCount.setText(String.valueOf(lotCount));
	
				if (showLotIdFlag) {
					gText = new GridData(GridData.FILL_HORIZONTAL);
					gText.widthHint = 216;
					toolkit.createLabel(top, Message.getString("wip.lot_id"));
					txtLotId = toolkit.createText(top, "", SWT.BORDER);
					txtLotId.setLayoutData(gText);
					txtLotId.setTextLimit(64);
					
					txtLotId.addVerifyListener(new VerifyListener() {
						public void verifyText(VerifyEvent e) {
							// ������ʽ��֤
							Pattern pattern = Pattern.compile(lotIdNaming);
							Matcher matcher = pattern.matcher(e.text);
							if (matcher.matches()) {
								// �����Сд��ĸ���л��ߡ��»��ߡ�����
								e.doit = true;
							} else if (e.text.length() > 0) {
								// �����������
								e.doit = false;
							} else {
								// ���Ƽ�
								e.doit = true;
							}
						}
					});
				}
				
	    		final String addLotEvent = "addLot";
				EventHandler defaultCarrierEvent = new EventHandler() {
		             public void handleEvent(Event event) {
		            	 addLot((String)event.getProperty(TXT_LOT_SIZE), (String)event.getProperty(TXT_LOT_COUNT), (String)event.getProperty(TXT_LOT_ID));
		             }
		        }; 
		        subscribeDefaultEvent(addLotEvent, GlcEvent.EVENT_ENTERPRESSED, defaultCarrierEvent);
	
				SquareButton addLotBtn = UIControlsFactory.createButton(top, UIControlsFactory.BUTTON_DEFAULT);
				addLotBtn.setText(Message.getString(ExceptionBundle.bundle.CommonAdd()));
				addLotBtn.addSelectionListener(new SelectionAdapter() {
					public void widgetSelected(SelectionEvent event) {
						Map<String, Object> eventData = Maps.newHashMap();
						eventData.put(TXT_LOT_SIZE, txtLotSize.getText().trim());
						eventData.put(TXT_LOT_COUNT, txtLotCount.getText().trim());
						if (txtLotId != null) {
							eventData.put(TXT_LOT_ID, txtLotId.getText().trim());
						}
						postEvent(addLotEvent, GlcEvent.EVENT_ENTERPRESSED, eventData);
					}
				});
	
				SquareButton removeLotbtn = UIControlsFactory.createButton(top, UIControlsFactory.BUTTON_DEFAULT);
				removeLotbtn.setText(Message.getString(ExceptionBundle.bundle.CommonDelete()));
				removeLotbtn.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_NORMAL));
				removeLotbtn.addSelectionListener(new SelectionAdapter() {
					public void widgetSelected(SelectionEvent event) {
						List<Object> removeWLots = lotTableManager.getCheckedObject();
						for (Object removeWoLot : removeWLots) {
							WorkOrderLot pre = (WorkOrderLot) removeWoLot;
							((List<WorkOrderLot>) lotTableManager.getInput()).remove(pre);
						}
					}
				});
			}
			
			Composite tableCom = toolkit.createComposite(parent);
			tableCom.setLayout(new GridLayout(1, false));
			GridData tablegd = new GridData(GridData.FILL_BOTH);
			tableCom.setLayoutData(tablegd);
			
			lotTableManager = new ListTableManager(getLotADTable(), true);
			lotTableManager.setIndexFlag(true);
			lotTableManager.newViewer(tableCom);
			
		} catch (Exception e) {
			logger.error("WorkOrderLotCustomComposite createForm error:", e);
		}
		return parent;
	}
	
	public void addLot(String lotSize, String lotCount, String lotId) {
		try {
			if (lotTableManager.getInput() != null) {
				for (WorkOrderLot woLot : ((List<WorkOrderLot>) lotTableManager.getInput())) {
					if (txtLotId.getText().equalsIgnoreCase(woLot.getLotId())) {
						UI.showError(Message.getString("wip.lotid_repeat"));
						return;
					}
				}
			}
			List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
			if (lotTableManager.getInput() != null) {
				workOrderLots.addAll((List<WorkOrderLot>) lotTableManager.getInput());
			}
			long lotNumber = Long.parseLong(txtLotCount.getText());
			BigDecimal mainQty = new BigDecimal(txtLotSize.getText());
			for (int i = 0; i < lotNumber; i++) {
				WorkOrderLot workOrderLot = new WorkOrderLot();
				workOrderLot.setOrgRrn(Env.getOrgRrn());
				workOrderLot.setIsActive(true);
				if (txtLotId.getText() != null && !"".equals(txtLotId.getText())) {
					String id = txtLotId.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					workOrderLot.setLotId(lotId);
				}

				workOrderLot.setMainQty(mainQty);
				workOrderLots.add(workOrderLot);
			}
			
			lotTableManager.setInput(workOrderLots);
		} catch (Exception e) {
			UI.showError(Message.getString(ExceptionBundle.bundle.ErrorInvalidNumber()));
		}
	}
	
	public ADTable getLotADTable() throws Exception {
		if (StringUtil.isEmpty(lotTableName)) {
			lotTableName = TABLE_NAME;
		}
		ADManager adManager = Framework.getService(ADManager.class);
		ADTable lotAdTable = adManager.getADTable(Env.getOrgRrn(), lotTableName);
		return lotAdTable;
	}
	
	@Override
	public void refresh() {

	}

	@Override
	public void setValue(Object value) {
		// TODO Auto-generated method stub

	}

	@Override
	public Object getValue() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		for (ADFormAttribute formAttribute : formAttributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_SHOW_LOT_INPUT:
				showLotInputFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_LOT_SIZE:
				lotSize = formAttribute.getIntValue() != null ? formAttribute.getIntValue() : lotSize;
				break;
			case ATTRIBUTE_LOT_COUNT:
				lotCount = formAttribute.getIntValue() != null ? formAttribute.getIntValue() : lotCount;
				break;
			case ATTRIBUTE_SHOW_LOT_ID:
				showLotIdFlag = formAttribute.getBooleanValue();
				break;
			case ATTRIBUTE_LOT_ID_NAMING_RULE:
				lotIdNaming = !StringUtil.isEmpty(formAttribute.getStringValue()) ? formAttribute.getStringValue() : LOT_ID_NAMING_RULE;
				break;
			case ATTRIBUTE_LOT_TABLE_NAME:
				lotTableName = !StringUtil.isEmpty(formAttribute.getStringValue()) ? formAttribute.getStringValue() : TABLE_NAME;
				break;
			}
		}
	}
	
	private Boolean isCaseSensitive;
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}

    public void preDestory() {
    	super.preDestory();
		unsubscribeEvent(setInputHandler);
    }
}
