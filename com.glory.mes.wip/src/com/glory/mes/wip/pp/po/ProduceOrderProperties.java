package com.glory.mes.wip.pp.po;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.wip.pp.wo.WorkOrderProperties;

public class ProduceOrderProperties extends WorkOrderProperties {

	public ProduceOrderProperties() {
		super();
	}

	@Override
	public ADBase save(ADBase obj) throws Exception {
		WorkOrder workOrder = (WorkOrder) obj;
		workOrder.setDocType(WorkOrder.DOC_TYPE_PO);
		return super.save(workOrder);
	}

}
