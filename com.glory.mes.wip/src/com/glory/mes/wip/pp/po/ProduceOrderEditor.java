package com.glory.mes.wip.pp.po;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.mes.wip.pp.wo.WorkOrderEditor;

public class ProduceOrderEditor extends WorkOrderEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.po.ProduceOrderEditor";
	
	@Override
	protected void createBlock(ADTable adTable) {
		block = new ProduceOrderBlock(new ListTableManager(adTable));
	}
}
