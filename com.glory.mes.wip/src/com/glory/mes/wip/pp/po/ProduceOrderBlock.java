package com.glory.mes.wip.pp.po;

import org.apache.log4j.Logger;
import org.eclipse.ui.forms.DetailsPart;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.mes.wip.pp.wo.WorkOrderBlock;

public class ProduceOrderBlock extends WorkOrderBlock {

	private static final Logger logger = Logger.getLogger(ProduceOrderBlock.class);
	EntityProperties page;

	public ProduceOrderBlock(ListTableManager tableManager){
		super(tableManager);
	}
	
	@SuppressWarnings("rawtypes")
    @Override
	protected void registerPages(DetailsPart detailsPart) {
		try{
			ADTable table = getTableManager().getADTable();
			Class klass = Class.forName(table.getModelClass());
			page = new ProduceOrderProperties();
			page.setTable(table);
			page.setMasterParent(this);
			detailsPart.registerPage(klass, page);
		} catch (Exception e){
			logger.error("EntityBlock : registerPages ", e);
		}
	}
	
	public void setFocus() {
		((ProduceOrderProperties)page).setFocus();
	}
}