package com.glory.mes.wip.pp.outsource;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;

import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.pp.model.WorkOrderOutSourcing;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.client.OutsourceManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class OutSourceCompleteDialog extends EntityDialog {
	
	private static int MIN_DIALOG_WIDTH = 500;
    private static int MIN_DIALOG_HEIGHT = 300;
    private final String TABLE_NAME = "WIPOutSourceCompleteList";
    protected CheckBoxFixEditorTableManager viewer;
    protected CheckBoxFixEditorTableManager outSourcePropertie;
    private Composite tableContainer;
    public WorkOrder workOrder;

    public OutSourceCompleteDialog(ADTable table, ADBase adObject, CheckBoxFixEditorTableManager sourceLotTableManager) {
        super(table, adObject);
        this.workOrder = (WorkOrder)adObject;
        this.outSourcePropertie=sourceLotTableManager;
    }

    protected void createFormContent(Composite composite) {
		try {
			FormToolkit toolkit = new FormToolkit(getShell().getDisplay());
			ScrolledForm sForm = toolkit.createScrolledForm(composite);
			managedForm = new ManagedForm(toolkit, sForm);
			sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
			Composite body = sForm.getForm().getBody();
			configureBody(body);

			GridLayout layout = new GridLayout();
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 0;
			layout.marginWidth = 0;
			layout.marginHeight = 0;
			composite.setLayout(new GridLayout(1, true));
			body.setLayout(layout);

			Composite content = toolkit.createComposite(body);
			content.setLayoutData(new GridData(GridData.FILL_BOTH));
			content.setLayout(new GridLayout(1, false));
			tableContainer = toolkit.createComposite(content, SWT.NULL);
			tableContainer.setLayout(new GridLayout());
			tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			List<WorkOrderLot> workOrderLots = ((WorkOrder)workOrder).getWorkOrderLots();
			List<WorkOrderLot> requestLots = new ArrayList<WorkOrderLot>();
			for (WorkOrderLot workOrderLot : workOrderLots) {
				if (!LotStateMachine.STATE_OUTSRCCMP.equals(workOrderLot.getState())) {
					requestLots.add(workOrderLot);
				}
			}
			
			viewer = new CheckBoxFixEditorTableManager(adTable);
			viewer.newViewer(tableContainer);
			viewer.setAutoSizeFlag(false);
			viewer.setInput(requestLots);
			viewer.setCheckedObject((List)requestLots);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
   }

    @SuppressWarnings("unchecked")
    @Override
	protected boolean saveAdapter() {
		try {
			List<WorkOrderLot> requestLots = (List)viewer.getCheckedObject();
			if (requestLots.isEmpty()) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return false;
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Lot> lots = new ArrayList<Lot>();
			for (WorkOrderLot requestLot : requestLots) {
				Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), requestLot.getLotId(), true);
				lot.setMainQty(requestLot.getMainQty());
				lots.add(lot);
			}
			 
			Procedure procedure = new Procedure();
			procedure.setOrgRrn(Env.getOrgRrn());
			procedure.setName(workOrder.getReworkProcessName());
			procedure.setVersion(workOrder.getReworkProcessVersion());
			PrdManager prdManager = Framework.getService(PrdManager.class);
			procedure = (Procedure) prdManager.getSimpleProcessDefinition(procedure, false);
			
            ADManager adManager = Framework.getService(ADManager.class);
			List<WorkOrderOutSourcing> workOrderSources = adManager.getEntityList(Env.getOrgRrn(), WorkOrderOutSourcing.class, 
                    Env.getMaxResult(), " woRrn = " + workOrder.getObjectRrn(), "seqNo");
			WorkOrderOutSourcing last = workOrderSources.get(workOrderSources.size() - 1);
			Node nextNode = prdManager.getNextState(Env.getOrgRrn(), procedure, last.getStepStateName());
			OutsourceManager outsourceManager = Framework.getService(OutsourceManager.class);
			nextNode.setPath(procedure.getName() + "/" + nextNode.getName() + "/");
			workOrder = outsourceManager.completeOutsourcingWorkOrder(workOrder, lots, null,
					workOrder.getReworkProcessVersion(), nextNode, null, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.outsource_complete_success"));
			return true;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}
    
    protected List<WorkOrderLot> getWorkOrderLot() {
    	List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
		if (viewer.getInput().size() > 0) {
			List<WorkOrderLot> wolots = (List<WorkOrderLot>) viewer.getInput();
			for (WorkOrderLot workOrderLot : wolots) {
				workOrderLots.add(workOrderLot);
			}
		}
		return workOrderLots;
    }
    
    @Override
    protected Point getInitialSize() {
        Point shellSize = super.getInitialSize();
        return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
                Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
    }
}
