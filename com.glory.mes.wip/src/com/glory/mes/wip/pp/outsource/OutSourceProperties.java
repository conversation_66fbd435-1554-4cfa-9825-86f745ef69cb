package com.glory.mes.wip.pp.outsource;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IFormPart;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.nattable.editor.CheckBoxFixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.pp.model.WorkOrderOutSourcing;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.OutsourceManager;
import com.glory.mes.wip.lot.ILotChangeListener;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.framework.core.exception.ExceptionBundle;

public class OutSourceProperties extends EntityProperties {

	private static final Logger logger = Logger.getLogger(OutSourceProperties.class);
	
	private final String TABLE_NAME = "WIPOutSourceWOLotList";
	private final String TABLE_NAME1 = "WIPOutSourceStep";

	protected List<ILotChangeListener> lotChangeListeners = new LinkedList<ILotChangeListener>();
	protected ADField procedureAdField;
	protected Section sourceSection;
	protected Text txtLot;
	protected FormToolkit toolkit;
	protected CheckBoxFixEditorTableManager woLotTableManager;
    protected CheckBoxFixEditorTableManager stepTableManager;
	public ToolItem itemComplete;
	public ToolItem itemOutSource;
	public ToolItem itemClose;
	public ToolItem itemUnOutSource;

	protected Lot lot;
	protected ADBase object;
	protected List<Lot> lotList;
	protected Procedure procedure;

	public OutSourceProperties() {
		super();
	}
	
	public void selectionChanged(IFormPart part, ISelection selection) {
        StructuredSelection lists = (StructuredSelection) selection;
        Object object1 = lists.getFirstElement();
        setAdObject((ADBase) object1);
        
        try {
            if (object1 != null && ((ADBase) object1).getObjectRrn() != null) {
                ADManager adManager = Framework.getService(ADManager.class);
                WorkOrder workOrder = (WorkOrder) adManager.getEntity((ADBase) object1);
                List<WorkOrderLot> preWorkOrderLots = adManager.getEntityList(Env.getOrgRrn(), WorkOrderLot.class, 
                        Env.getMaxResult(), " workOrderRrn = " + workOrder.getObjectRrn(), null);
                workOrder.setWorkOrderLots(preWorkOrderLots);
                setAdObject(workOrder);
                object = getAdObject();
                woLotTableManager.setInput(workOrder.getWorkOrderLots());
                
                List<WorkOrderLot> workOrderLots = workOrder.getWorkOrderLots();
                lotList = new ArrayList<>();
                if(workOrderLots.size()>1){
                	for(WorkOrderLot workOrderLot : workOrderLots){
                		String lotId = workOrderLot.getLotId();
                		Lot lot = getLotById(lotId);
                		lotList.add(lot);
                	}
                	txtLot.setText("");
                } else if(workOrderLots.size() == 1){
                	String lotId = workOrderLots.get(0).getLotId();
                	Lot lot = getLotById(lotId);
                	txtLot.setText(lotId);
                	lotList.add(lot);
                }
                
                List<StepState> stepStates = new ArrayList<StepState>();
                if (!StringUtil.isEmpty(workOrder.getReworkProcessName())) {
                	procedure = new Procedure();
                	procedure.setOrgRrn(Env.getOrgRrn());
                	procedure.setName(workOrder.getReworkProcessName());
                	procedure.setVersion(workOrder.getReworkProcessVersion());
                	PrdManager prdManager = Framework.getService(PrdManager.class);
                	procedure = (Procedure) prdManager.getSimpleProcessDefinition(procedure, false);
                	stepStates = prdManager.getStepChildren(procedure);
                }
                List<WorkOrderOutSourcing> preWorkOrderSources = adManager.getEntityList(Env.getOrgRrn(), WorkOrderOutSourcing.class, 
                        Env.getMaxResult(), " woRrn = " + workOrder.getObjectRrn(), "seqNo");
                List<Object> checkSteps = new ArrayList<>();
                stepTableManager.setInput(stepStates);
                for (StepState stepState : stepStates){
                	for(WorkOrderOutSourcing preWorkOrderSource : preWorkOrderSources){
                		if (stepState.getName().equals(preWorkOrderSource.getStepStateName())){
                			checkSteps.add(stepState);
                		}
                	}
                }
    			stepTableManager.setCheckedObject(checkSteps);
            } else {
            	txtLot.setText("");
                woLotTableManager.setInput(null);
                stepTableManager.setInput(null);
            }
            this.refresh();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemOutSource(tBar);
		createToolItemComplete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemUnOutSource(tBar);
		createToolItemClose(tBar);

		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemClose(ToolBar tBar) {
		itemClose = new ToolItem(tBar, SWT.PUSH);
		itemClose.setText(Message.getString(ExceptionBundle.bundle.CommonClose()));
		itemClose.setImage(SWTResourceCache.getImage("close"));
		itemClose.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				closeAdapter();
			}
		});
	}
	
	protected void createToolItemUnOutSource(ToolBar tBar) {
		itemUnOutSource = new ToolItem(tBar, SWT.PUSH);
		itemUnOutSource.setText(Message.getString(ExceptionBundle.bundle.CommonCancel()));
		itemUnOutSource.setImage(SWTResourceCache.getImage("cancel"));
		itemUnOutSource.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				cancelAdapter();
			}
		});
	}
	
	
	
	protected void createSectionContent(Composite client) {
        FormToolkit toolkit = form.getToolkit();
        mmng = form.getMessageManager();
        createBasicSection(client);

        try {
            Section section = toolkit.createSection(client, Section.NO_TITLE | FFormSection.FFORM);
            section.setText(Message.getString("wip.outsource_lot_manage"));
            GridData gd = new GridData(GridData.FILL_BOTH);
            gd.minimumHeight=200;
            section.setLayoutData(gd);

            Composite compLot = toolkit.createComposite(section);
			GridLayout layout = new GridLayout(1, true);
            layout.verticalSpacing = 0;
            layout.marginHeight = 0;

            compLot.setLayout(layout);
            gd = new GridData(GridData.FILL_BOTH);
            gd.grabExcessHorizontalSpace = true;
            compLot.setLayoutData(gd);
            
			Composite left = toolkit.createComposite(compLot);
			left.setLayout(new GridLayout(3, false));
			toolkit.createLabel(left, Message.getString("wip.lot_id"));
			txtLot = toolkit.createText(left, "", SWT.BORDER);
			GridData gText = new GridData();
			gText.widthHint = 216;
			txtLot.setLayoutData(gText);
			txtLot.setTextLimit(32);
			txtLot.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
			txtLot.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					Text tLotId = ((Text) event.widget);
					tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					switch (event.keyCode) {
					case SWT.CR:
						Lot lot = null;
						String lotId = tLotId.getText();
						if (!isLotIdCaseSensitive()) {
							lotId = lotId.toUpperCase();
						}
						tLotId.setText(lotId);
						lot = searchLot(lotId);
						tLotId.selectAll();
						if (lot == null) {
							tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
							setLot(new Lot());
						} else {
							setLot(lot);
							setStepList(getLot());
							getInputData();
						}
						break;
					}
				}

			});
			txtLot.addFocusListener(new FocusListener() {
				public void focusGained(FocusEvent e) {
				}

				public void focusLost(FocusEvent e) {
					Text tLotId = ((Text) e.widget);
					String lotId = tLotId.getText();
					if (!isLotIdCaseSensitive()) {
						lotId = lotId.toUpperCase();
					}
					tLotId.setText(lotId);
				}
			});
			Button removeLotbtn = toolkit.createButton(left, " " + Message.getString(ExceptionBundle.bundle.CommonDelete()) + " ", SWT.BUTTON1);
			removeLotbtn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					List<Object> removeWLots = woLotTableManager.getCheckedObject();
					List<WorkOrderLot> workOrderLots = new ArrayList<>();
					for (Object removeWoLot : removeWLots) {
						WorkOrderLot pre = (WorkOrderLot) removeWoLot;
						workOrderLots.add(pre);
						((List<WorkOrderLot>) woLotTableManager.getInput()).remove(pre);
					}
				}
			});
            ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			woLotTableManager = new CheckBoxFixEditorTableManager(adTable);
			woLotTableManager.newViewer(compLot);
			woLotTableManager.setAutoSizeFlag(false);
			woLotTableManager.setIndexFlag(false);
            section.setClient(compLot);

            Section sec = toolkit.createSection(client, Section.NO_TITLE | FFormSection.FFORM);
            sec.setText(Message.getString("wip.outsource_step_manage"));
            GridData gd2 = new GridData(GridData.FILL_BOTH);
            gd2.grabExcessHorizontalSpace = true;
            gd2.grabExcessVerticalSpace = true;
            gd2.heightHint = 200;
            layout = new GridLayout();
            sec.setLayout(layout);
            sec.setLayoutData(gd2);

            Composite compSource = toolkit.createComposite(sec);
            layout.horizontalSpacing = 0;
            layout.marginWidth = 0;
            compSource.setLayout(layout);
            GridData gd3 = new GridData(GridData.FILL_BOTH);
            gd2.grabExcessHorizontalSpace = true;
            gd2.grabExcessVerticalSpace = true;
            gd2.minimumHeight = 150;
            compSource.setLayoutData(gd3);
			ADTable stepadTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME1);
			stepTableManager = new CheckBoxFixEditorTableManager(stepadTable);
			stepTableManager.newViewer(compSource);
			stepTableManager.setAutoSizeFlag(false);
			stepTableManager.setIndexFlag(false);
            sec.setClient(compSource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
	
	@Override
	protected void newAdapter() {
		super.newAdapter();
		txtLot.setText("");
		woLotTableManager.setInput(new ArrayList<>());
		stepTableManager.setInput(new ArrayList<>());
		this.refresh();
	}
	
	@Override
	protected void saveAdapter() {
    	try {
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				ADBase oldBase = getAdObject();
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(),
								detailForm.getCopyProperties());
					}
					
					WorkOrder workOrder = (WorkOrder) getAdObject();
					List<WorkOrderLot> workOrderLot = new ArrayList<>();
					List<WorkOrderLot> workOrderLots = (List<WorkOrderLot>) woLotTableManager.getInput();
					if (workOrderLots.isEmpty()) {
						UI.showError(Message.getString("wip.wo_add_lot"));
						return;
					}
					workOrderLot.addAll(workOrderLots);
					workOrder.setDocType(WorkOrder.DOC_TYPE_OS);
					workOrder.setCreatedUser(Env.getUserName());
					workOrder.setCreatedDate(new Date());
					workOrder.setWorkOrderLots(workOrderLot);
					
					PpManager ppManager = Framework.getService(PpManager.class);
					List<WorkOrderOutSourcing> ourSourcingSteps = new ArrayList<WorkOrderOutSourcing>();
					
					List<StepState> allStepStates = (List)stepTableManager.getInput();
					List<StepState> checkSteps = (List)stepTableManager.getCheckedObject();
					
					if (checkSteps.size() == 0) {
						UI.showWarning(Message.getString("wip.outsource_pls_select_step"));
						return;
					}
					
					//����˳��
					List<StepState> checkStepStates = new ArrayList<StepState>();
					for (StepState stepState : allStepStates) {
						if (checkSteps.contains(stepState)) {
							checkStepStates.add(stepState);
						}
					}
					StepState firstCheckStep = checkStepStates.get(0);
					StepState lastCheckStep = checkStepStates.get(checkStepStates.size() - 1);
					int first = allStepStates.indexOf(firstCheckStep);
					int last = allStepStates.indexOf(lastCheckStep);
					if ((last - first) != checkStepStates.size() - 1) {
						UI.showWarning(Message.getString("wip.outsource_continuous_step"));
						return;
					}
					
					int i = 0;
					for (StepState checkStepState : checkStepStates) {
						WorkOrderOutSourcing ourSourcingStep = new WorkOrderOutSourcing();
						Long seqNo = (long) (i + 1);
						ourSourcingStep.setOrgRrn(Env.getOrgRrn());
						ourSourcingStep.setProcedureName(getLot().getProcedureName());
						ourSourcingStep.setSeqNo(seqNo);
						ourSourcingStep.setStepName(checkStepState.getStepName());
						ourSourcingStep.setStepStateName(checkStepState.getName());
						ourSourcingSteps.add(ourSourcingStep);
						i++;
					}
					
					if (procedure == null) {
						UI.showWarning(Message.getString("wip.outsource_procedure_is_null"));
						return;
					}
					//���÷�������
					workOrder.setReworkProcessName(procedure.getName());
					workOrder.setReworkProcessVersion(procedure.getVersion());
					workOrder.setReworkStartStepName(firstCheckStep.getStepName());
					workOrder.setReworkEndStepName(lastCheckStep.getStepName());
					
					for (WorkOrderLot lot : workOrderLot) {
						if (!workOrder.getReworkStartStepName().equals(lot.getReserved3())) {
							UI.showWarning(Message.getString("wip.outsource_must_start_current_step"));
							return;
						}
					}
					
					workOrder = ppManager.saveWorkOrderOurSourcing(workOrder, ourSourcingSteps, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
					ADManager adManager = Framework.getService(ADManager.class);
					setAdObject(adManager.getEntity(workOrder));
					ADBase newBase = getAdObject();
					if (oldBase.getObjectRrn() == null) {
						getMasterParent().refreshAdd(newBase);
					} else {
						getMasterParent().refreshUpdate(newBase);
					}
					this.refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			this.form.getMessageManager().setAutoUpdate(true);
		}
	}
	
	protected void outSourceAdapter() {
		try {
			OutsourceManager outsourceManager = Framework.getService(OutsourceManager.class);
			WorkOrder workOrder = (WorkOrder) getAdObject();

			PpManager ppManager = Framework.getService(PpManager.class);
			workOrder = ppManager.getWorkOrder(workOrder, true, true, Env.getSessionContext());
			List<WorkOrderLot> woLots = workOrder.getWorkOrderLots();
			//��������ͬ�����̺���ͬ�Ĺ�������һ��ί��
			String procedureName = workOrder.getReworkProcessName();
			String stepName = workOrder.getReworkStartStepName();

			for (WorkOrderLot woLot : woLots) {
				String lotid = woLot.getLotId();
				lot = getLotById(lotid);
				if (!(procedureName.equals(lot.getProcedureName()) && stepName.equals(lot.getStepName()))) {
					UI.showWarning(Message.getString("wip.outsource_lot_must_same_step"));
					return;
				}
			}
			
			workOrder = outsourceManager.outsourcingWorkOrder(workOrder, Env.getSessionContext());
			setAdObject(workOrder);
			UI.showInfo(Message.getString("wip.outsource_successed"));
			getMasterParent().refreshUpdate(workOrder);
			this.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void cancelAdapter() {
		try {
			OutsourceManager outsourceManager = Framework.getService(OutsourceManager.class);
			WorkOrder workOrder = (WorkOrder) getAdObject();
			workOrder = outsourceManager.cancelOutsourcingWorkOrder(workOrder, Env.getSessionContext());
			setAdObject(workOrder);
			UI.showInfo(Message.getString("wip.outsource_cancel_successed"));
			getMasterParent().refreshUpdate(workOrder);
			this.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void closeAdapter() {
		try {
			OutsourceManager outsourceManager = Framework.getService(OutsourceManager.class);
			WorkOrder workOrder = (WorkOrder) getAdObject();
			workOrder = outsourceManager.closeOutsourcingWorkOrder(workOrder, Env.getSessionContext());
			setAdObject(workOrder);
			UI.showInfo(Message.getString("wip.outsource_close_successed"));
			getMasterParent().refreshUpdate(workOrder);
			this.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void createToolItemOutSource(ToolBar tBar) {
		itemOutSource = new ToolItem(tBar, SWT.PUSH);
		itemOutSource.setText(Message.getString("wip.outsource"));
		itemOutSource.setImage(SWTResourceCache.getImage("outsource"));
		itemOutSource.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				outSourceAdapter();
			}
		});
	}
	
	protected void createToolItemComplete(ToolBar tBar) {
		itemComplete = new ToolItem(tBar, SWT.PUSH);
		itemComplete.setText(Message.getString("wip.outsource_complete"));
		itemComplete.setImage(SWTResourceCache.getImage("outsource_complete"));
		itemComplete.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				completeAdapter();
			}
		});
	}
	
	public Lot searchLot(String lotId) {
		try {
			Lot lot = LotProviderEntry.getLot(lotId);
			notifyLotChangeListeners(this, lot);
			if(lot.getState().equals(LotStateMachine.STATE_WAIT)){
				if(lot.getHoldState().equals(Lot.HOLDSTATE_OFF)){
					return lot;
				} else {
					UI.showInfo(Message.getString("wip.lot_is_holdstate"));
					return null;
				}
			} else {
				UI.showInfo(Message.getString("wip.lot_is_not_state_wait"));
				return null;
			}
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
			return null;
		}
	}
	
	public void notifyLotChangeListeners(Object sender, Lot newLot) {
		synchronized (lotChangeListeners) {
			for (ILotChangeListener listener : lotChangeListeners) {
				try {
					listener.lotChanged(sender, newLot);
				} catch (Throwable t) {
					t.printStackTrace();
				}
			}
		}
	}
	
    protected void completeAdapter() {
    	try {
			if (getAdObject().getObjectRrn() != null) {
				ADManager adManager = (ADManager) Framework.getService(ADManager.class);
				ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
				OutSourceCompleteDialog dialog = new OutSourceCompleteDialog(adTable, getAdObject(), stepTableManager);
	            if (dialog.open() == Dialog.OK) {
	            	setAdObject(dialog.workOrder);
	    			getMasterParent().refreshUpdate(dialog.workOrder);
	    			this.refresh();
	            }
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
	        return;
	    }
	}

    @Override  
    public boolean delete() {
		try {
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				if (getAdObject().getObjectRrn() != null) {
					WorkOrder workOrder = (WorkOrder) getAdObject();
					PpManager ppManager = Framework.getService(PpManager.class);
					ppManager.deleteWorkOrder(workOrder, Env.getSessionContext());
					setAdObject(createAdObject());
					refresh();
					return true;
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
		return false;
	}

	@Override
	public void refresh() {
		super.refresh();
		try {
			WorkOrder workOrder = (WorkOrder) getAdObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				List<WorkOrderLot> workOrderLots = getWorkOrderLots();
				String lotstatus = null;
				for(WorkOrderLot woLot:workOrderLots){
					woLot.getLotId();
					Lot lot = getLotById(woLot.getLotId());
					lotstatus = lot.getState();
				}
				statusChanged(lotstatus, workOrder.getDocStatus());
			} else {
				statusChanged(null, null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	public void superRefresh() {
		super.refresh();
	}

	public void statusChanged(String lotStatus, String woStatus) {
		if (Documentation.STATUS_CREATED.equals(woStatus)) {
			itemSave.setEnabled(true);
			itemOutSource.setEnabled(true);
			itemComplete.setEnabled(false);
			itemClose.setEnabled(false);
			itemUnOutSource.setEnabled(false);
			itemDelete.setEnabled(true);
		} else if (Documentation.STATUS_APPROVED.equals(woStatus)) {
			itemSave.setEnabled(false);
			itemOutSource.setEnabled(false);
			itemComplete.setEnabled(true);
			itemClose.setEnabled(true);
			itemUnOutSource.setEnabled(true);
			itemDelete.setEnabled(false);
		} else if (Documentation.STATUS_CLOSED.equals(woStatus)) {
			itemSave.setEnabled(false);
			itemOutSource.setEnabled(false);
			itemComplete.setEnabled(false);
			itemClose.setEnabled(false);
			itemUnOutSource.setEnabled(false);
			itemDelete.setEnabled(false);
		} else if (Documentation.STATUS_COMPLETED.equals(woStatus)) {
			itemSave.setEnabled(false);
			itemOutSource.setEnabled(false);
			itemComplete.setEnabled(false);
			itemClose.setEnabled(false);
			itemUnOutSource.setEnabled(false);
			itemDelete.setEnabled(false);
		} else {
			itemSave.setEnabled(true);
			itemOutSource.setEnabled(false);
			itemComplete.setEnabled(false);
			itemClose.setEnabled(false);
			itemUnOutSource.setEnabled(false);
			itemDelete.setEnabled(true);
		}
	}
	
	public void getInputData() {
		WorkOrderLot workOrderLot = new WorkOrderLot();
		List<WorkOrderLot> workOrderLots;
		lot = getLot();
		//LOT���ҵ�����
		if (lot != null && lot.getLotId() != null) {
			workOrderLots = getWorkOrderLots();
			if (workOrderLots.isEmpty()) {
				workOrderLot.setOrgRrn(Env.getOrgRrn());
				workOrderLot.setLotId(lot.getLotId());
				workOrderLot.setReserved1(lot.getWoId());
				workOrderLot.setMainQty(lot.getMainQty());
				workOrderLot.setReserved2(lot.getProcessName());
				workOrderLot.setReserved3(lot.getStepName());
				workOrderLots.add(workOrderLot);
				
				woLotTableManager.setInput(workOrderLots);
			} else {
				for(WorkOrderLot WOLot:workOrderLots){
					if(WOLot.getLotId().equals(lot.getLotId())) {
						UI.showError(Message.getString("error.lot_exist"));
						setLot(null);
						return;
					}
				}
				Lot lotById = getLotById(workOrderLots.get(0).getLotId());
				if (lotById.getProcedureName().equals(lot.getProcedureName())) {
					if (lotById.getStepName().equals(lot.getStepName())) {
						workOrderLot.setOrgRrn(Env.getOrgRrn());
						workOrderLot.setLotId(getLot().getLotId());
						workOrderLot.setReserved1(getLot().getWoId());
						workOrderLot.setMainQty(getLot().getMainQty());
						workOrderLot.setReserved2(getLot().getProcessName());
						workOrderLot.setReserved3(getLot().getStepName());
						workOrderLots.add(workOrderLot);
						woLotTableManager.setInput(workOrderLots);
					} else {
						UI.showError(Message.getString("wip.step_is_not_same"));
						setLot(null);
						return;
					}
				} else {
					UI.showError(Message.getString("wip.procedure_is_not_same"));
					setLot(null);
					return;
				}
			}
		} else {
			return;
		}
	}
	
	public void setStepList(Lot lot) {
		try {
			if (lot == null || lot.getStepRrn() == null) {
				return;
			}
			if (procedure != null) {
				if (woLotTableManager.getInput() != null && !woLotTableManager.getInput().isEmpty()) {
					return;
				}
			}
			
			procedure = new Procedure();
			procedure.setObjectRrn(lot.getProcedureRrn());
			PrdManager prdManager = Framework.getService(PrdManager.class);
			procedure = (Procedure) prdManager.getSimpleProcessDefinition(procedure, false);

			List<StepState> stepStates = prdManager.getStepChildren(procedure);
			StepState currentStartNode = prdManager.getCurrentStepState(lot.getProcessInstanceRrn());
			List<Object> checkLines = new ArrayList<>();
			for (StepState stepState : stepStates) {
				if(stepState.getName().equals(currentStartNode.getName())) {
					checkLines.add(stepState);//
				}
			}
			stepTableManager.setInput(stepStates);//stepList
			stepTableManager.setCheckedObject(checkLines);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
		
	public List<WorkOrderLot> getWorkOrderLots() {
		List<WorkOrderLot> workOrderLots = new ArrayList<WorkOrderLot>();
		if (woLotTableManager.getInput().size() > 0) {
			List<WorkOrderLot> lots = (List<WorkOrderLot>) woLotTableManager.getInput();
			for (WorkOrderLot workOrderLot : lots) {
				workOrderLots.add(workOrderLot);
			}
		}
		return workOrderLots;
	}
	
	public Lot getLotById(String lotId) {
		try {
			if(lotId != null) {
				LotManager lotManager = Framework.getService(LotManager.class);
				Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), lotId);
				return lot;
			} else {
				return null;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
	}
	
	public CheckBoxFixEditorTableManager getSourceLotTableManager() {
		return stepTableManager;
	}

	public Lot getLot() {
		return lot;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}

	private Boolean isCaseSensitive;
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
}
