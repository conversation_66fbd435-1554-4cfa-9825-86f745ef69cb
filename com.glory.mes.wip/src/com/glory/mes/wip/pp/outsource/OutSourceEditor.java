package com.glory.mes.wip.pp.outsource;

import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class OutSourceEditor extends EntityEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.outsource.OutSourceEditor";
	@Inject
	protected MPart mPart;
	@Override
	protected void createBlock(ADTable adTable) {
		block = new OutSourceBlock(new ListTableManager(adTable));
	}
	
}
