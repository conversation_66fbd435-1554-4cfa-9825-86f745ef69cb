package com.glory.mes.wip.pp.outsource;

import org.apache.log4j.Logger;
import org.eclipse.ui.forms.DetailsPart;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class OutSourceBlock extends EntityBlock {

	private static final Logger logger = Logger.getLogger(OutSourceBlock.class);
	EntityProperties page;

	public OutSourceBlock(ListTableManager tableManager){
		super(tableManager);
	}
	
	@SuppressWarnings("rawtypes")
    @Override
	protected void registerPages(DetailsPart detailsPart) {
		try{
			ADTable table = getTableManager().getADTable();
			Class klass = Class.forName(table.getModelClass());
			page = new OutSourceProperties();
			page.setTable(table);
			page.setMasterParent(this);
			detailsPart.registerPage(klass, page);
		} catch (Exception e){
			logger.error("EntityBlock : registerPages ", e);
		}
	}
	
	public void setFocus() {
		((OutSourceProperties)page).setFocus();
	}
}