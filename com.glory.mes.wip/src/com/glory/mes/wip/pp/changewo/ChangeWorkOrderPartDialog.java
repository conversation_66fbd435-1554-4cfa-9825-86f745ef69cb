package com.glory.mes.wip.pp.changewo;


import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.TreeItem;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.wip.custom.NewPartFlowCustomComposite;
import com.glory.mes.wip.exception.WipExceptionBundle;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.base.ui.forms.field.CustomField;

public class ChangeWorkOrderPartDialog extends GlcBaseDialog { 
	
	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 400;

	public static final String FIELD_CHANGEPROCESSINFO = "changeProcessInfo";
	public static final String FIELD_CHANGEPROCESSPART = "changeProcessPart";

	protected GlcFormField changeProcessInfoField;
	protected CustomField changeProcessPartField;
	protected NewPartFlowCustomComposite flowCustomComposite;
	
	protected List<Node> flowList;
	protected Lot lot;

	public ChangeWorkOrderPartDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot) {
		super(adFormName, authority, eventBroker);
		this.lot = lot;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		changeProcessInfoField = form.getFieldByControlId(FIELD_CHANGEPROCESSINFO, GlcFormField.class);
		changeProcessPartField = changeProcessInfoField.getFieldByControlId(FIELD_CHANGEPROCESSPART, CustomField.class);
		flowCustomComposite = (NewPartFlowCustomComposite) changeProcessPartField.getCustomComposite();
		flowCustomComposite.getTxtId().setEnabled(false);
		init();
	}
	
	protected void init() {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Part part = prdManager.getActivePart(Env.getOrgRrn(), lot.getPartName(), true);
			flowCustomComposite.loadFlowTreeByPart(part, null);
			flowCustomComposite.getTxtId().setText(part.getName());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void okPressed() {
		if (CollectionUtils.isEmpty(getSelectFlowList())) {
			UI.showError(Message.getString(WipExceptionBundle.bundle.NoFlowBeSelect()));
			return;
		}
		super.okPressed();
	}
	
	public List<Node> getSelectFlowList() {
		try {
			flowList = new ArrayList<Node>();
			TreeItem[] items = flowCustomComposite.getViewer().getTree().getSelection();
			if(items != null && items.length > 0) {
				TreeItem item = items[0];
				while(item != null && (item.getData() instanceof Node)){
					flowList.add(0, (Node)item.getData());
					item = item.getParentItem();
				}	
				return flowList;
			} else {
				flowList = null;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}

	public List<Node> getFlowList() {
		return flowList;
	}

	public void setFlowList(List<Node> flowList) {
		this.flowList = flowList;
	}
	
}