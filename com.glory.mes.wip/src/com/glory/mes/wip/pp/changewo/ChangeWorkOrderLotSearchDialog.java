package com.glory.mes.wip.pp.changewo;

import java.util.List;

import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.query.SearchDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class ChangeWorkOrderLotSearchDialog extends SearchDialog {

    protected Long materialRrn;

    public ChangeWorkOrderLotSearchDialog(ListTableManager listTableManager, String initWhereClause, String baseWhereClause) {
    	super(listTableManager, initWhereClause, baseWhereClause);
	}

    @Override
    protected void refresh() {
    	super.refresh();
    }
    
	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == 0) {
			List<Object> objs = tableManager.getCheckedObject();
			if (objs != null && objs.size() > 0) {
				for (Object obj : objs) {
					selectedItems.add((ADBase) obj);
				}
				okPressed();
			}
		} else {
			super.buttonPressed(buttonId);
		}
	}
	
	@Override
	protected void createSearchTable(Composite parent) {
		super.createSearchTable(parent);
	}

}
