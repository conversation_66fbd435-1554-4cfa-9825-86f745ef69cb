package com.glory.mes.wip.pp.changewo.partflowdialog;


import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.part.PartFlowSection;

public class ChangeWorkOrderSelectStepPartFlowSection extends PartFlowSection {
	
	private static final Logger logger = Logger.getLogger(ChangeWorkOrderSelectStepPartFlowSection.class);

	protected IField partTextField;

	public ChangeWorkOrderSelectStepPartFlowSection(ADTable table) {
		super(table);
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		Composite top = toolkit.createComposite(client);
		top.setLayout(new GridLayout(3, false));
		top.setLayoutData(gd);
		int mStyle = SWT.READ_ONLY | SWT.BORDER;
		Label label = toolkit.createLabel(top, Message.getString("wip.part"));
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));		
		partTextField = new TextField("", mStyle);
		partTextField.createContent(top, toolkit);
	}

	@Override
	protected void createSectionContent(Composite client) {
		final IMessageManager mmng = form.getMessageManager();
		itemForm = new ChangeWorkOrderSelectStepPartFlowForm(client, SWT.NONE, table, mmng);
		itemForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		getDetailForms().add(itemForm);
	}

	@Override
	public void valueChanged(Object sender, Object newValue) {
		Part part = null;
		part = searchPart((String)newValue);
		if (part == null) {
			initAdObject();
		} else {
			setAdObject(part);
			refresh();
		}
		
		// ��ʾ��Ʒ
		((TextField)partTextField).setText((String)newValue);
	}
	
	@SuppressWarnings("deprecation")
	protected Part searchPart(String partName) {
		try {
			if (!StringUtil.isEmpty(partName)) {
				PrdManager prdManager = Framework.getService(PrdManager.class);
				return prdManager.getActivePart(Env.getOrgRrn(), partName);
			}		
		} catch (Exception e) {
			logger.error("Shsi2SelectStepPartFlowSection searchPart(): Part isn' t exsited!");
		}
		return null;
	}
}
