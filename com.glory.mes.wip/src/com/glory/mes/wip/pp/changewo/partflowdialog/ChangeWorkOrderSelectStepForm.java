package com.glory.mes.wip.pp.changewo.partflowdialog;


import java.util.List;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IManagedForm;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.SeparatorField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.workflow.graph.def.Node;

public class ChangeWorkOrderSelectStepForm extends EntityForm {
	
	private List<Node> flowList;
	protected IManagedForm managerForm;
	protected IField fieldSashForm;
	protected static final String SASHFORM = "SashForm";
	protected static final String SASHFORM_ID = "sashForm";


	public ChangeWorkOrderSelectStepForm(Composite parent, int style, ADTable table) {
		super(parent, style, table,null);
		super.createForm();
	}
	
	@Override
	public void createForm() {
		// DO NOTHING but it's important
	}

	@Override
	public void addFields() {
		super.addFields();
		fieldSashForm = new ChangeWorkOrderSelectStepFormField(SASHFORM_ID, null, table);
		addField(SASHFORM, fieldSashForm);
	}

	@Override
	public void loadFromObject() {
		if (object != null) {
			for (IField f : fields.values()) {
				if (!(f instanceof SeparatorField) && !f.equals(fieldSashForm)) {
					Object o = PropertyUtil.getPropertyForIField(object, f.getId());
					f.setValue(o);
				}
			}
			refresh();
			setEnabled();
		}
	}

	@Override
	public boolean saveToObject() {
		flowList = ((ChangeWorkOrderSelectStepFormField)fieldSashForm).getFlowList();
		if(validate()){
			return true;
		}	
		return false;
	}
	
	public Part getNewPart(){
		return ((ChangeWorkOrderSelectStepFormField)fieldSashForm).getNewPart();
	}
	
	public void setNewPart(String partName) {
		((ChangeWorkOrderSelectStepFormField)fieldSashForm).setNewPart(partName);
	}

	@Override
	public void refresh() {
		super.refresh();
	}

	@Override
	public boolean validate() {
		boolean validFlag = true;
		String message = "";
		
		if(flowList != null && flowList.size() != 0){
			validFlag = validFlag && true;
		} else {
			validFlag = validFlag && false;
			message = message + "" + Message.getString("wip.noflow_be_selected");
		}
		if(!validFlag){
			UI.showError(message);
		}
		return validFlag;
	}

	public List<Node> getFlowList() {
		return flowList;
	}
}
