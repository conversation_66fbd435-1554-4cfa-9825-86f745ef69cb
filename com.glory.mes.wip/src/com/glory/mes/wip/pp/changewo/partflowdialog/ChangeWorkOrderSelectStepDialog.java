package com.glory.mes.wip.pp.changewo.partflowdialog;


import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseDialog;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.wip.model.Lot;

/**
 * Parent/Child���Dialog
 */
public class ChangeWorkOrderSelectStepDialog extends BaseDialog {
	
	private static int MIN_DIALOG_WIDTH = 500;
	private static int MIN_DIALOG_HEIGHT = 400;
	
	ChangeWorkOrderSelectStepForm selectStepForm;
	protected ManagedForm managedForm;
	protected Lot lot;

	public ChangeWorkOrderSelectStepDialog(Shell parent) {
		super();
		setViewMargin(0, 0, 0, 0);
	}

	public ChangeWorkOrderSelectStepDialog(Lot lot) {
		super();
		setViewMargin(0, 0, 0, 0);
		this.lot = lot;
	}

	@Override
	protected Control buildView(Composite parent) {
		try {
			FormToolkit toolkit = new FormToolkit(getShell().getDisplay());
			ScrolledForm sForm = toolkit.createScrolledForm(parent);
			sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
			managedForm = new ManagedForm(toolkit, sForm);

			Composite body = sForm.getForm().getBody();
			// ���� body Ĭ�ϲ���
			GridLayout layout = new GridLayout();
			layout.horizontalSpacing = 0;
			layout.verticalSpacing = 0;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			layout.marginLeft = 0;
			layout.marginRight = 0;
			layout.marginTop = 0;
			layout.marginBottom = 0;
			body.setLayout(layout);
			body.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			GridData gd = new GridData(GridData.FILL_BOTH);

			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), "WIPNewPart");
			selectStepForm = new ChangeWorkOrderSelectStepForm(body, SWT.NONE, table);
			selectStepForm.setLayoutData(gd);
			
			selectStepForm.setNewPart(lot.getPartName());
			
			return parent;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return parent;
	}

	@Override
	protected void okPressed() {
		if (!selectStepForm.saveToObject()) {
			return;
		}
		
		super.okPressed();
	}
	
	public List<Node> getFlowList() {
		return selectStepForm.getFlowList();
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
				convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT),
						shellSize.y));
	}
}
