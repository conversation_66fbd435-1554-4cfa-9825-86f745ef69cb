package com.glory.mes.wip.pp.changewo.partflowdialog;


import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.part.PartFlowTreeField;

public class ChangeWorkOrderSelectStepPartFlowTreeField extends PartFlowTreeField {
	
	private static final Logger logger = Logger.getLogger(ChangeWorkOrderSelectStepPartFlowTreeField.class);
	
	public ChangeWorkOrderSelectStepPartFlowTreeField(String id, String label, TreeViewerManager manager) {
		super(id, label, manager);
	}

	@Override
	public void refresh() {
		if (getValue() != null) {
			Part part = (Part)getValue();
			if (part != null && !StringUtil.isEmpty(part.getProcessName())) {
				try {
					List<Process> processes = new ArrayList<Process>();
					PrdManager prdManager = Framework.getService(PrdManager.class);
					Process process = (Process)prdManager.getPartProcess(part);
					processes.add(process);
					manager.setInput(processes);
					viewer.expandToLevel(2);
				} catch(Exception e) {
					logger.error("Error: " + e);
				}
			} else {
				manager.setInput(null);
			}
		}
	}
}
