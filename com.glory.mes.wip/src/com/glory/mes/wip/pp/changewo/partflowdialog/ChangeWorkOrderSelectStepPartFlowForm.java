package com.glory.mes.wip.pp.changewo.partflowdialog;


import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.part.PartFlowForm;
import com.glory.mes.prd.viewer.ProcessFlowTreeManager;

public class ChangeWorkOrderSelectStepPartFlowForm extends PartFlowForm {
	
	private static final Logger logger = Logger.getLogger(ChangeWorkOrderSelectStepPartFlowForm.class);

	protected ProcessFlowTreeManager treeManager;
	
	public ChangeWorkOrderSelectStepPartFlowForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
		super.createForm();
	}

	@Override
	public void addFields() {
		try {
			treeManager = new ProcessFlowTreeManager();
			field = new ChangeWorkOrderSelectStepPartFlowTreeField(FIELD_ID, "", treeManager);
			addField(FIELD_ID, field);
		} catch (Exception e) {
			logger.error("NewPartForm : createForm", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
