package com.glory.mes.wip.pp.reworkwo.start;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.query.SearchMultiDialog;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.pp.client.PpManager;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class ReworkWorkorderStartManagerEditor extends GlcEditor { 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.pp.reworkwo.start.ReworkWorkorderStartManagerEditor";

	public static final String TABLE_NAME = "WIPReworkLot";
	
	public static final String FORM_BASIC_INFO = "BasicInfo";
	
	public static final String FIELD_REWORKLOTLIST = "reworkLotList";
	public static final String FIELD_REWORKLOT = "reworkLot";

	public static final String BUTTON_START = "start";
	public static final String BUTTON_UNSTART = "unstart";
	public static final String BUTTON_ASSIGN = "assign";
	public static final String BUTTON_DEASSIGN = "deassign";

	protected GlcFormField reworkLotListField;
	protected ListTableManagerField reworkLotField;
	
	protected EntityBlock block;
	protected EntityForm entityForm;
	
	protected ToolItem itemStart;
	protected ToolItem itemUnStart;
	
	public boolean isLoading = false;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		reworkLotListField = form.getFieldByControlId(FIELD_REWORKLOTLIST, GlcFormField.class);
		reworkLotField = reworkLotListField.getFieldByControlId(FIELD_REWORKLOT, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_START), this::startAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_UNSTART), this::unstartAdapter);
		
		subscribeAndExecute(eventBroker, reworkLotListField.getFullTopic(BUTTON_ASSIGN), this::assignAdapter);
		subscribeAndExecute(eventBroker, reworkLotListField.getFullTopic(BUTTON_DEASSIGN), this::deassignAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		
		block = (EntityBlock) form.getBlock();
		entityForm = (EntityForm) form.getSubFormById(FORM_BASIC_INFO);
		
		itemStart = (ToolItem) form.getButtonByControl(null, BUTTON_START);
		itemUnStart = (ToolItem) form.getButtonByControl(null, BUTTON_UNSTART);
	}

	//ѡ��
	protected void selectionChanged(Object object) {
		try {
			Event event = (Event) object;
			StructuredSelection selection = (StructuredSelection) event.getProperty(GlcEvent.PROPERTY_DATA);
			WorkOrder workOrder = (WorkOrder)selection.getFirstElement();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				entityForm.setObject(entityManager.getEntity((WorkOrder) workOrder));
				statusChanged(workOrder.getDocStatus(), workOrder.getHoldState());
			}
			isLoading = true;
			//����entityForm
			entityForm.loadFromObject();
			
			reworkLotField.getListTableManager().setInput(getWorkOrderLots());
			reworkLotField.getListTableManager().refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			isLoading = false;
		}
	}
	
	private void startAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				
				List<Lot> lots = (List<Lot>)(List<? extends Object>)reworkLotField.getListTableManager().getInput();
				BigDecimal allLotMainQty = BigDecimal.ZERO;
				List<Lot> allLots = new ArrayList<Lot>();
				if (lots != null && lots.size() > 0) {
					for (Lot lot : lots) {
						BigDecimal mainQty = lot.getMainQty();
						allLotMainQty = allLotMainQty.add(mainQty);
						allLots.add(lot);
					}
				} else {
					UI.showError(Message.getString("pp.workorder_no_select_rework_lot"));
					return;	
				}
				
				// �ж� �����Ƿ�һ��
				if (workOrder.getMainQty().compareTo(allLotMainQty) != 0) {
					UI.showError(Message.getString("wip.rework_woqty_not_match_lotqty"));
					return;
				}
				
				PpManager ppManager = Framework.getService(PpManager.class);
				workOrder = ppManager.getWorkOrder(workOrder, Env.getSessionContext());
				
				ppManager.startAssignedWorkOrderLot(workOrder, allLots, Lot.UNIT_TYPE_QTY, false, Env.getSessionContext());
				UI.showInfo(Message.getString("common.start_successed"));
				entityForm.setObject(new WorkOrder());
				block.refresh();
				reworkLotField.getListTableManager().setInput(new ArrayList<Lot>());
				reworkLotField.getListTableManager().refresh();
			} else {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void unstartAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder != null && workOrder.getObjectRrn() != null) {
				ADManager adManager = Framework.getService(ADManager.class);
				PpManager ppManager = Framework.getService(PpManager.class);
				
				// ȫ��������һ����ȡ��Ͷ��
				List<Lot> lots = (List<Lot>)(List<? extends Object>)reworkLotField.getListTableManager().getInput();
				BigDecimal allLotMainQty = BigDecimal.ZERO;
				List<Lot> allLots = new ArrayList<Lot>();
				if (lots != null && lots.size() > 0) {
					for (Lot lot : lots) {
						BigDecimal mainQty = lot.getMainQty();
						allLotMainQty = allLotMainQty.add(mainQty);
						allLots.add(lot);
					}
				} else {
					UI.showError(Message.getString("pp.workorder_no_select_rework_lot"));
					return;	
				}
				workOrder.setStartedMainQty(workOrder.getStartedMainQty().subtract(allLotMainQty));
				ppManager.unStartLotByReworkWorkOrder(workOrder, allLots, false, Env.getSessionContext());				
				UI.showInfo(Message.getString("wip.unstart_successed"));//������ʾ��
				entityForm.setObject(new WorkOrder());
				block.refresh();
				reworkLotField.getListTableManager().setInput(new ArrayList<Lot>());
				reworkLotField.getListTableManager().refresh();
			} else {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	private void assignAdapter(Object object) {
		try {
			WorkOrder reworkWorkOrder = (WorkOrder) entityForm.getObject();
			if (reworkWorkOrder == null) {
				return;
			}
			
			List<Lot> tableLots = (List<Lot>)(List<? extends Object>)reworkLotField.getListTableManager().getInput();
			String whereCause = "";
			if (tableLots != null && tableLots.size() > 0) {
				whereCause = "lotId NOT IN(";
				int i = 1;
				for (Lot tableLot : tableLots) {
					if (i == tableLots.size()) {
						whereCause = whereCause + "'" + tableLot.getLotId() + "'";
					} else {
						whereCause = whereCause + "'" + tableLot.getLotId() + "',";
					}
					i++;
				}	
				whereCause = whereCause + ")";
			}
			
			ADManager adManager = (ADManager) Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			String baseWhereClause = "";
			if (!StringUtil.isEmpty(adTable.getWhereClause())) {
				baseWhereClause = baseWhereClause + adTable.getWhereClause();
			}
			if (!StringUtil.isEmpty(whereCause)) {
				if (StringUtil.isEmpty(baseWhereClause)) {
					baseWhereClause = baseWhereClause + whereCause;
				} else {
					baseWhereClause = baseWhereClause + " AND " + whereCause;
				}		
			}
			if (!StringUtil.isEmpty(reworkWorkOrder.getPartName())) {
				if (StringUtil.isEmpty(baseWhereClause)) {
					baseWhereClause = baseWhereClause + " partName = '" + reworkWorkOrder.getPartName() + "'";
				} else {
					baseWhereClause = baseWhereClause + " AND partName = '" + reworkWorkOrder.getPartName() + "'";
				}		
			}
			SearchMultiDialog dialog = new SearchMultiDialog(new ListTableManager(adTable, true), " 1 = 1 ", baseWhereClause);
			if (dialog.open() == IDialogConstants.OK_ID) {
				List<Lot> allLots = new ArrayList<Lot>();				
				List<Lot> lots = (List<Lot>)(List<? extends Object>)reworkLotField.getListTableManager().getInput();	
				for (Lot lot : lots) {
					allLots.add(lot);
				}
				
				List<Lot> selectedLots = new ArrayList<Lot>();	
				List<ADBase> selectedObjects = dialog.getSelectionList();
				if (selectedObjects == null || selectedObjects.size() == 0) {
					return;
				}
				for (ADBase selectedObject : selectedObjects) {
					selectedLots.add((Lot)selectedObject);
					
				}
				PpManager ppManager = Framework.getService(PpManager.class);
				reworkWorkOrder = ppManager.getWorkOrder(reworkWorkOrder, Env.getSessionContext());
				if (!Documentation.STATUS_APPROVED.equals(reworkWorkOrder.getDocStatus())) {
					UI.showError(Message.getString("wip.wo_not_is_approved_state"));
					return;
				}
				selectedLots = ppManager.assignWorkOrderLot(reworkWorkOrder, selectedLots, Env.getSessionContext());
				allLots.addAll(selectedLots);		
				
				reworkLotField.getListTableManager().setInput(allLots);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}

	private void deassignAdapter(Object object) {
		try {
			WorkOrder reworkWorkOrder = (WorkOrder) entityForm.getObject();
			if (reworkWorkOrder == null) {
				return;
			}
			
			List<Lot> lots = (List<Lot>)(List<? extends Object>)reworkLotField.getListTableManager().getInput();
			List<Lot> allLots = new ArrayList<Lot>();
			for (Lot lot : lots) {
				allLots.add(lot);
			}				
			List<Object> os = reworkLotField.getListTableManager().getCheckedObject();
			if (os.size() != 0) {
				List<Lot> checkedLots = new ArrayList<Lot>();
				for (Object o : os) {
					Lot checkedLot = (Lot) o;
					checkedLots.add(checkedLot);
					allLots.remove(checkedLot);
				}	
				
				String whereCause = "";
				if (checkedLots != null && checkedLots.size() > 0) {
					whereCause = "lotId IN(";
					int i = 1;
					for (Lot checkedLot : checkedLots) {
						if (i == checkedLots.size()) {
							whereCause = whereCause + "'" + checkedLot.getLotId() + "'";
						} else {
							whereCause = whereCause + "'" + checkedLot.getLotId() + "',";
						}
						i++;
					}	
					whereCause = whereCause + ")";
					
					ADManager adManager = Framework.getService(ADManager.class);
					List<Lot> newLots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, Env.getMaxResult(), whereCause, "");
					
					PpManager ppManager = Framework.getService(PpManager.class);
					reworkWorkOrder = ppManager.getWorkOrder(reworkWorkOrder, Env.getSessionContext());
					if (!Documentation.STATUS_APPROVED.equals(reworkWorkOrder.getDocStatus())) {
						UI.showError(Message.getString("wip.wo_not_is_approved_state"));
						return;
					}
					
					ppManager.deassignWorkOrderLot(reworkWorkOrder, newLots, Env.getSessionContext());
				}		
			} else {
				return;
			}
			
			reworkLotField.getListTableManager().setInput(allLots);		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	public void statusChanged(String newStatus, String holdStatus) {
		if (newStatus == null || "".equals(newStatus.trim())) {
			itemStart.setEnabled(false);
			itemUnStart.setEnabled(false);
		} else if (Documentation.STATUS_APPROVED.equals(newStatus.trim())
				|| WorkOrder.STATUS_STARTED.equals(newStatus.trim())) {
			if (WorkOrder.STATUS_APPROVED.equals(newStatus.trim())) {
				itemStart.setEnabled(true);
			}
			itemUnStart.setEnabled(true);
			checkHoldStatus();
		} else {
			itemStart.setEnabled(false);
			itemUnStart.setEnabled(false);
		}
	}

	public void checkHoldStatus() {
		try {
			WorkOrder workOrder = (WorkOrder) entityForm.getObject();
			if (workOrder == null || workOrder.getObjectRrn() == null) {
				return;
			}
			if (WorkOrder.HOLDSTATE_ON.equals(workOrder.getHoldState())) {
				itemStart.setEnabled(false);
				itemUnStart.setEnabled(false);
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}
	
	protected List<Lot> getWorkOrderLots() {
		List<Lot> lots = new ArrayList<Lot>();
		WorkOrder workOrder = (WorkOrder) entityForm.getObject();
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			lots = adManager.getEntityList(Env.getOrgRrn(), Lot.class, Env.getMaxResult(),
	                " woId = '" + workOrder.getDocId() + "'", "lotId desc");
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return lots; 
	}

}