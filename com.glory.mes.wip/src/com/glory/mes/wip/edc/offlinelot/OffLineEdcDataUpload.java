package com.glory.mes.wip.edc.offlinelot;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.collections.CollectionUtils;

import com.glory.edc.client.EDCManager;
import com.glory.edc.collection.EdcDataItem;
import com.glory.edc.collection.EdcDataTableComposite;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.AbstractEdcSetLine;
import com.glory.edc.model.EdcBinSet;
import com.glory.edc.model.EdcBinSetLine;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcItem;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTextSet;
import com.glory.edc.model.EdcTextSetLine;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADImpExp;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.excel.UploadErrorLog;
import com.glory.framework.base.excel.UploadException;
import com.glory.framework.base.excel.UploadProgress;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.track.model.EdcContext;
import com.google.common.collect.Maps;
import com.glory.framework.core.exception.ExceptionBundle;

public class OffLineEdcDataUpload {
	
	public String authorityName;
	
	public Lot lot = new Lot();
	
	public String euipmentId = "";//�豸
	
	public List<Lot> edcLots = new ArrayList<Lot>();
	
	public List<EdcData> uploadTemps = new ArrayList<EdcData>();
	
	public AbstractEdcSet itemSet = new AbstractEdcSet(); 
	
	public int row = 2;
	
	public AbstractEdcSetLine edcSetLine;
	
	public List<String> fields = new ArrayList<String>(); //���ñ��ֶΣ�����ɸѡ����̬���ӵ�data��
	
	String selectedComponentIds = "";
	
	String selectedComponentRrn = "";
	
	public Map<String, String> componentMap = Maps.newHashMap();
	
	public UploadProgress progress ;
	
	public static final String HEADER_LOTID = "LOTID";
	
	public static final String HEADER_EQUIPMENT = "EQUIPMENT";
	
	public static final String HEADER_EDCSETNAME = "EDCSETNAME";
	
	public static final String HEADER_EDCTYPE = "EDCTYPE";
	
	public static final String HEADER_ITEMNAME = "ITEMNAME";
	
	public static final String COMPONENT = "COMPONENT";
	
	public OffLineEdcDataUpload(String authorityName, String tableName , AbstractEdcSet itemSet, Lot lot, String euipmentId, AbstractEdcSetLine edcSetLine) {
		this.itemSet = itemSet;
		this.lot = lot;
		this.euipmentId = euipmentId;
		this.edcSetLine = edcSetLine;
		this.authorityName = authorityName;
		progress = new UploadProgress(authorityName, null, tableName);
	}

	protected void cudEntityList() {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			if (CollectionUtils.isNotEmpty(uploadTemps)) {
				for (EdcData data : uploadTemps) {
					//BIN����Ҫд��batch
					//BIN�����豸ʹ�������豸
					if(data.getEdcType().equals(EdcData.EDCTYPE_BIN)) {
						data.setMeasureEqp(lot.getEquipmentId());
					}else {
						if(data.getBatchId() != null){
							List<Lot> lots = lotManager.getLotsByBatch(Env.getOrgRrn(), data.getBatchId());
							String lotIds = "";
							for (Lot lot : lots) {
								lotIds += lot.getLotId() + ";";
							}
							data.setBatchLots(lotIds.substring(0, lotIds.length() - 1));
						}
					}
					data.setIsTemp(false);
					data.setObjectRrn(null);
				}
				
				List<Lot> lots = new ArrayList<Lot>();
				lot.setOperator1(Env.getUserName());
				lots.add(lot);
				lotManager.edcLotData(lots, uploadTemps, EdcData.EDCFROM_OFFLINELOT, false, new EdcContext(),Env.getSessionContext());
				// �����޸�ʱ��
				UI.showInfo(Message.getString("com.import_success"));
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public boolean preCheck(List<Map> valueMap) throws UploadException {
		try {
			EDCManager edcManager = Framework.getService(EDCManager.class);
			if(itemSet instanceof EdcItemSet) {
				itemSet = (AbstractEdcSet)edcManager.getEdcSet(Env.getOrgRrn(), itemSet.getName(), itemSet.getVersion(), true, false);
				if(CollectionUtils.isNotEmpty(itemSet.getEdcSetLine())) {
					for(AbstractEdcSetLine line : itemSet.getEdcSetLine()) {
						if(line.getName().equals(edcSetLine.getName())) {
							edcSetLine = line;
						}
					}
				}
			}else {
				ADManager adManager = Framework.getService(ADManager.class);
				itemSet = (AbstractEdcSet)adManager.getEntity(itemSet);
			}
			
			selectedComponentIds = "";
			selectedComponentRrn = "";
			//��ȡ��Ϣ
			getInfo();
			
			if(CollectionUtils.isNotEmpty(valueMap)) {
				for (int i = 0; i < valueMap.size(); i++) {
					if(valueMap.get(i).get(HEADER_LOTID) == null || !valueMap.get(i).get(HEADER_LOTID).equals(lot.getLotId())) {
						addErrorLog(Message.getString("wip.lot_id_is_not_same"), row, "LOT_ID");
					}
					if(valueMap.get(i).get(HEADER_EQUIPMENT) == null || !valueMap.get(i).get(HEADER_EQUIPMENT).equals(euipmentId)) {
						addErrorLog(Message.getString("wip.equipment_id_is_not_same"), row, "EQUIPMENT_ID");
					}
					
					if(itemSet instanceof EdcItemSet && (valueMap.get(i).get(HEADER_EDCTYPE) == null || !valueMap.get(i).get(HEADER_EDCTYPE).equals("ITEM"))) {
						addErrorLog(Message.getString("wip.type_not_same"), row, "TYPE");
					}else if(itemSet instanceof EdcBinSet && (valueMap.get(i).get(HEADER_EDCTYPE) == null || !valueMap.get(i).get(HEADER_EDCTYPE).equals("BIN"))) {
						addErrorLog(Message.getString("wip.type_not_same"), row, "TYPE");
					}else if(itemSet instanceof EdcTextSet && (valueMap.get(i).get(HEADER_EDCTYPE) == null || !valueMap.get(i).get(HEADER_EDCTYPE).equals("TEXT"))) {
						addErrorLog(Message.getString("wip.type_not_same"), row, "TYPE");
					}
					
                    TreeMap<String, String> map = new TreeMap<String, String>(valueMap.get(i));
                    List<EdcData> edcdats = new ArrayList<EdcData>();
                    if(valueMap.get(i).get(HEADER_EDCTYPE).equals("ITEM")) {
                    	edcdats = getItemEdcData(map);
                    }else if (valueMap.get(i).get(HEADER_EDCTYPE).equals("BIN")) {
                    	edcdats = getBinEdcData(map);
					}else if (valueMap.get(i).get(HEADER_EDCTYPE).equals("TEXT")) {
                    	edcdats = getTextEdcData(map);
					}
					
					if(CollectionUtils.isNotEmpty(edcdats)) {
						//���ǿ������
						checkMandatory(edcdats, itemSet);
						uploadTemps.addAll(edcdats);
					}
					
					row++;
				}
			}else {
				return false;
			}
		} catch (Exception e) {
			UI.showError(Message.getString(e.toString()));
			e.printStackTrace();

		}
		return progress.isSuccess();
	}
	
	/**
	 * ��ȡ��Ϣ
	 */
	protected void getInfo() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			LotManager lotManager = Framework.getService(LotManager.class);
			//��ȡ���ñ��ֶ�
			ADImpExp adImpExp = adManager.getADImpExpByAuthorityName(Env.getOrgRrn(), authorityName, null, true);
			if(adImpExp != null) {
				List<ADField> exportFields = adManager.buildADFieldByADImpExp(adImpExp, true, Env.getSessionContext());
				if(CollectionUtils.isNotEmpty(exportFields)) {
					for(ADField adField : exportFields) {
						//����ɸѡ��̬���ӵĲɼ�Valueֵ�ֶ�
						if (!StringUtil.isEmpty(adField.getColumnName())) {
							fields.add(adField.getColumnName().toUpperCase());
						} else {
							fields.add(adField.getName().toUpperCase());
						}
					}
				}
			}
			
			// ��ȡCompentList��Ϣ
			Lot lotWithComps = lotManager.getLotWithComponent(lot.getObjectRrn());
			if (CollectionUtils.isNotEmpty(lotWithComps.getSubProcessUnit())) {
				for (ProcessUnit processUnit : lotWithComps.getSubProcessUnit()) {
					ComponentUnit componentUnit = (ComponentUnit) processUnit;
					componentUnit.setAttribute1(lot.getLotId());
					componentMap.put(componentUnit.getComponentId(), componentUnit.getObjectRrn().toString());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * ��ȡ��װText����datas
	 */
	protected List<EdcData> getTextEdcData(TreeMap<String, String> map) {
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		try {
			EdcTextSet edcTextSet = (EdcTextSet) itemSet;
			List<EdcTextSetLine> lines = edcTextSet.getTextSetLines();
			//Ч�������Ƿ���ȷ
			if(verifyTextcolumn(map, lines)) {
				for (EdcTextSetLine line : lines) {
					EdcData edcData = new EdcData();
					edcData.setOrgRrn(Env.getOrgRrn());
					edcData.setOrgId(Env.getOrgName());
					edcData.setMeasureTime(new Date());
					edcData.setEdcSetRrn(itemSet.getObjectRrn());
					edcData.setEdcSetName(itemSet.getName());
					edcData.setEdcType(EdcData.EDCTYPE_TEXT);
					edcData.setEdcSetVersion(itemSet.getVersion());
					edcData.setItemName(line.getName());
					edcData.setDcName(line.getName());
					
					//ƥ����������
					String value = map.get(edcTextSet.getName() + "-" + line.getName());
                    if(EdcTextSetLine.DATATYPE_BOOLEAN.equals(line.getDataType())) {
						if(!(value.equals("Y") || value.equals("N"))) {
							addErrorLog(Message.getString("common.import_field_data_type_matching_exception"), row, (edcTextSet.getName() + "-" + line.getName()).trim());
						}
					}else if (EdcTextSetLine.DATATYPE_DOUBLE.equals(line.getDataType())) {
						if(!match("^\\d+(\\.\\d+)?$", value.toString())) {
							addErrorLog(Message.getString("common.import_field_data_type_matching_exception"), row, (edcTextSet.getName() + "-" + line.getName()).trim());
						}
					}else if (EdcTextSetLine.DATATYPE_INTEGER.equals(line.getDataType())) {
						if(!match("^[1-9]\\d*$", value.toString())) {
							addErrorLog(Message.getString("common.import_field_data_type_matching_exception"), row, (edcTextSet.getName() + "-" + line.getName()).trim());
						}
					}else if (EdcTextSetLine.DATATYPE_ADFIELD.equals(line.getDataType())){
						ADManager adManager = Framework.getService(ADManager.class);
						String whereClause = " referenceName = '" + line.getSampleType() + "'";
						List<ADURefList> list = adManager.getEntityList(Env.getOrgRrn(), ADURefList.class, Env.getMaxResult(), whereClause, "seqNo");
						if(CollectionUtils.isNotEmpty(list)) {
							Boolean isVerf = false;
							for(ADURefList uList : list) {
								if(uList.getText().equals(value)) {
									value = uList.getKey();
									isVerf = true;
								}
							}
							if(!isVerf) {
								value = "";
								addErrorLog(Message.getString("common.import_field_data_type_matching_exception"), row, (edcTextSet.getName() + "-" + line.getName()).trim());
							}
						}else {
							addErrorLog(Message.getString("common.import_field_data_type_matching_exception"), row, (edcTextSet.getName() + "-" + line.getName()).trim());
						}
					}
					edcData.setDcData(value);
					
					edcData.setUsl(DBUtil.toDouble(line.getUslString()));
					edcData.setLsl(DBUtil.toDouble(line.getLslString()));
					edcData.setSl(DBUtil.toDouble(line.getSlString()));
					edcData.setOperator(Env.getUserName());
					if (lot != null) {
						edcData.setLineId(lot.getLineId());
						edcData.setLotRrn(lot.getObjectRrn());
						edcData.setBatchId(lot.getBatchId());
						edcData.setBatchLots("");
						edcData.setLotCount(null);
						edcData.setLotType(lot.getLotType());
						edcData.setLotId(lot.getLotId());
						edcData.setPartName(lot.getPartName());
						edcData.setPartVersion(lot.getPartVersion());
						edcData.setStepName(lot.getStepName());
						edcData.setStepVersion(lot.getStepVersion());
						edcData.setMaterialId(null);
						edcData.setCustomer(lot.getCustomerCode());
						edcData.setMeasureEqp(map.get(HEADER_EQUIPMENT));
						edcData.setProcessEqp("");
					}
					edcDatas.add(edcData);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return edcDatas;
	}
	
	/**
	 * ��ȡ��װBin����datas
	 */
	protected List<EdcData> getBinEdcData(TreeMap<String, String> map) {
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		try {
			if(itemSet instanceof EdcBinSet) {
				EdcBinSet edcBinSet = (EdcBinSet) itemSet;
				List<EdcBinSetLine> lines = edcBinSet.getBinSetLines();
				
				String[] select = null;
				
				if(lot.getSubUnitType().equals(MLot.UNIT_TYPE_COMPONENT)) {
					select = map.get(COMPONENT).split(";");
				}else {
					List<String> Names = new ArrayList<String>();
					Names.add(itemSet.getName());
					select = Names.toArray(new String[Names.size()]);
				}
				
				if(select == null || select.length <= 0) {
					addErrorLog(Message.getString("wip.identify_dialog_title") + Message.getString(ExceptionBundle.bundle.CommonIsNotExist()), row, "COMPONENT");
				}
				
				//Ч����
				if(verifyBincolumn(map, lines)) {
					for (int i = 0; i < select.length; i++) {
						EdcData edcData = new EdcData();
						edcData.setOrgRrn(Env.getOrgRrn());
						edcData.setOrgId(Env.getOrgName());

						String name = null;
						String value = null;
						String oosList = "";

						BigDecimal totalQty = BigDecimal.ZERO;
						BigDecimal badQty = BigDecimal.ZERO;
						for (EdcBinSetLine line : lines) {
							if (EdcBinSetLine.BINTYPE_GROUP.equals(line.getBinType()) || EdcBinSetLine.BINTYPE_OTHER.equals(line.getBinType())) {
								continue;
							}
							if (EdcData.ATTRIBUTE_TOTAL.equals(line.getName())){
								continue;
							} else if (map.get(select[i] + "-" + line.getName())!= null && map.get(select[i] + "-" + line.getName()).trim().length() > 0){
								if (EdcBinSetLine.BINTYPE_FAIL.equals(line.getBinType())) {
									badQty = badQty.add(new BigDecimal(map.get(select[i] + "-" + line.getName()).trim()));
								}
								totalQty = totalQty.add(new BigDecimal(map.get(select[i] + "-" + line.getName()).trim()));
							}
						}

						// ѭ��д��Value
						int num = 2;
						for (EdcBinSetLine line : lines) {
							if (name == null) {
								name = DBUtil.toString(line.getName());
								value = DBUtil.toString(map.get(select[i] + "-" + line.getName()));
								// ��������OOS����oosList�ĵ�һ����λ
								EdcDataItem dataItem = new EdcDataItem();
								dataItem.setValue(String.valueOf(map.get(select[i] + "-" + line.getName())));
								dataItem.setDescription(line.getDescription());
								dataItem.setSpecType(line.getSpecType());
								dataItem.setGroup(line.getBinGroup());
								if(!StringUtil.isEmpty(line.getUslString())) {
									dataItem.setUsl(Double.valueOf(line.getUslString()));
								}
								if(!StringUtil.isEmpty(line.getSlString())) {
									dataItem.setSl(Double.valueOf(line.getSlString()));
								}
								if(!StringUtil.isEmpty(line.getLslString())) {
									dataItem.setLsl(Double.valueOf(line.getLslString()));
								}
								dataItem.setFlag(line.getBinType());
								
								if (!dataItem.checkSpec(totalQty.doubleValue())) {
									oosList += num + ";";
								}
							} else {
								name += ";" + DBUtil.toString(line.getName());
								value += ";" + DBUtil.toString(map.get(select[i] + "-" + line.getName()));
								// ��������OOS����oosList�ĵ�һ����λ
								EdcDataItem dataItem = new EdcDataItem();
								dataItem.setValue(String.valueOf(map.get(select[i] + "-" + line.getName())));
								dataItem.setValue(String.valueOf(map.get(select[i] + "-" + line.getName())));
								dataItem.setDescription(line.getDescription());
								dataItem.setSpecType(line.getSpecType());
								dataItem.setGroup(line.getBinGroup());
								if(!StringUtil.isEmpty(line.getUslString())) {
									dataItem.setUsl(Double.valueOf(line.getUslString()));
								}
								if(!StringUtil.isEmpty(line.getSlString())) {
									dataItem.setSl(Double.valueOf(line.getSlString()));
								}
								if(!StringUtil.isEmpty(line.getLslString())) {
									dataItem.setLsl(Double.valueOf(line.getLslString()));
								}
								dataItem.setFlag(line.getBinType());
								if (!dataItem.checkSpec(totalQty.doubleValue())) {
									oosList += num + ";";
								}
							}
							num++;
						}
						

						Double doubleUsl = DBUtil.toDouble(edcBinSet.getUslString());
						Double doubleSl = DBUtil.toDouble(edcBinSet.getSlString());
						Double doubleLsl = DBUtil.toDouble(edcBinSet.getLslString());

						edcData.setDcName("TOTAL;" + name);
						edcData.setDcData(totalQty + ";" + value);
						
						EdcDataItem totalItem = new EdcDataItem();
						totalItem.setValue(String.valueOf(totalQty.doubleValue()));
						totalItem.setSpecType(AbstractEdcSetLine.SPECTYPE_PERCENT);
						totalItem.setUsl(doubleUsl);
						totalItem.setSl(doubleSl);
						totalItem.setLsl(doubleLsl);
						if (!totalItem.checkSpec(totalQty.doubleValue())) {
							edcData.setOosList("1;" + oosList);
						}else {
							edcData.setOosList(oosList);
						}
						
						edcData.setTotalQty(totalQty);
						edcData.setBadQty(badQty);
						edcData.setMeasureTime(new Date());
						edcData.setEdcSetRrn(edcBinSet.getObjectRrn());
						edcData.setEdcSetName(edcBinSet.getName());
						edcData.setEdcType(EdcData.EDCTYPE_BIN);
						edcData.setEdcSetVersion(edcBinSet.getVersion());
						edcData.setSeqNo(10L);
						edcData.setUsl(doubleUsl);
						edcData.setSl(doubleSl);
						edcData.setLsl(doubleLsl);
						edcData.setOperator(Env.getUserName());
						if (lot != null) {
							edcData.setLineId(lot.getLineId());
							edcData.setLotRrn(lot.getObjectRrn());
							edcData.setBatchId(lot.getBatchId());
							edcData.setBatchLots("");
							edcData.setLotCount(null);
							edcData.setLotType(lot.getLotType());
							edcData.setLotId(lot.getLotId());
							edcData.setPartName(lot.getPartName());
							edcData.setPartVersion(lot.getPartVersion());
							edcData.setStepName(lot.getStepName());
							edcData.setStepVersion(lot.getStepVersion());
							edcData.setMaterialId(null);
							edcData.setCustomer(lot.getCustomerCode());
							edcData.setMeasureEqp(map.get(HEADER_EQUIPMENT));
							edcData.setProcessEqp("");
							edcData.setComponentList(select[i]);
							edcData.setComponentRrnList(componentMap.get(select[i]));
						}

						edcDatas.add(edcData);
					}	
				}
			}
			return edcDatas;
		} catch (Exception e) {
			UI.showError(e.toString());
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * Bin����Ч�������ֶ�
	 */
	protected Boolean verifyBincolumn(TreeMap<String, String> map, List<EdcBinSetLine> lines) {
		Boolean verify = true;
		String columnName = "";

		for (int i = 0; i < map.get(COMPONENT).split(";").length; i++) {
			for (EdcBinSetLine line : lines) {
				// ƴ��������
				if (StringUtil.isEmpty(columnName)) {
					columnName = map.get(COMPONENT).split(";")[i] + "-" + line.getName();
				} else {
					columnName += ";" + map.get(COMPONENT).split(";")[i] + "-" + line.getName();
				}
			}
		}

		List<String> Names = new ArrayList<String>();

		if (lot.getSubUnitType().equals(MLot.UNIT_TYPE_COMPONENT)) {
			Names = Arrays.asList(columnName.split(";"));
		} else {
			if (CollectionUtils.isNotEmpty(lines)) {
				for (EdcBinSetLine line : lines) {
					Names.add(itemSet.getName() + "-" + line.getName());
				}
			}
		}

		// �ж����Ƿ����
		for (String key : map.keySet()) {
			if (!fields.contains(key)) {
				if (!Names.contains(key)) {
					addErrorLog(Message.getString("wip.import_field_not_exist"), row, key.toUpperCase());
					verify = false;
				}
			}
		}

		// �жϵ������Ƿ���
		for (String column : Names) {
			if (!map.keySet().contains(column)) {
				addErrorLog(Message.getString("wip.import_field_not_exist"), row, column.toUpperCase());
				verify = false;
			}
		}
		return verify;
	}
	
	/**
	 * Text����Ч�������ֶ�
	 */
	protected Boolean verifyTextcolumn(TreeMap<String, String> map, List<EdcTextSetLine> lines) {
		Boolean verify = true;
		String columnName = "";
		for (EdcTextSetLine line : lines) {
			// ƴ��������
			if (StringUtil.isEmpty(columnName)) {
				columnName = itemSet.getName() + "-" + line.getName();
			} else {
				columnName += ";" + itemSet.getName() + "-" + line.getName();
			}
		}
		String[] itemDescs = columnName.split(";");
		
		// �жϵ������Ƿ���
		for (String key : map.keySet()) {
			if (!fields.contains(key)) {
				if (!Arrays.asList(itemDescs).contains(key)) {
					addErrorLog(Message.getString("wip.import_field_oversupply"), row, key.toUpperCase());
					verify = false;
				}
			}
		}
		
		// �жϵ������Ƿ���
		for(String column : Arrays.asList(itemDescs)) {
			if(!map.keySet().contains(column)) {
				addErrorLog(Message.getString("wip.import_field_not_exist"), row, column.toUpperCase());
				verify = false;
			}
		}
		
		return verify;
	}
	
	/**
	 * ��ȡ��װItem����datas
	 */
	protected List<EdcData> getItemEdcData(TreeMap<String, String> map) {
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		EdcData edcData = new EdcData();
		try {
			EdcItemSetLine line = (EdcItemSetLine)edcSetLine;
			int itemNumber = line.getItem().intValue();
			if (line.getIsItemUsePercent()) {
				// ʹ�ðٷֱ�
				itemNumber = EdcItemSetLine.getActualItemNumber(lot.getMainQty(), new BigDecimal(itemNumber));
				if (itemNumber < 1) {
					// ������С��1
					itemNumber = 1;
				}
			}
			
			selectedComponentIds = map.get(COMPONENT);
			if(itemNumber < selectedComponentIds.split(";").length) {
				addErrorLog(Message.getString("edc_waferId_toomuch") + itemNumber, row, "COMPONENT");
			}
			//�ж��Ƿ�Componnet
			Boolean isComponnet = isComponentUnitType() && !EdcItem.SAMPLETYPE_ITEM.equals(line.getSampleType());
			if(isComponnet) {
				if(selectedComponentIds != null && selectedComponentIds.split(";") != null) {
					for(String id : selectedComponentIds.split(";")) {
						if(componentMap.containsKey(id)) {
							if(selectedComponentRrn.equals("")) {
								selectedComponentRrn = componentMap.get(id);
							}else {
								selectedComponentRrn += ";" + componentMap.get(id);
							}
						}else {
							addErrorLog(Message.getString("wip.component_not_in_lot"), row, "COMPONENT");
						}
					}
				}else {
					addErrorLog(Message.getString("wip.identify_dialog_title") + Message.getString(ExceptionBundle.bundle.CommonIsNotExist()), row, "COMPONENT");
				}
			}
			//����Ӧ�����ɵ�����Ϣ
			String[] itemDescs = EdcDataTableComposite.createIds(line, Arrays.asList(selectedComponentIds.split(";")), isComponnet,itemNumber);
			
			edcData.setOrgRrn(Env.getOrgRrn());
			edcData.setOrgId(Env.getOrgName());

			String name = null;
			String value = null;

			// ѭ��д��Value
			for (String key : map.keySet()) {
				if (!fields.contains(key)) {
					//�ж����Ƿ����
					if(!Arrays.asList(itemDescs).contains(key)) {
						addErrorLog(Message.getString("wip.import_field_not_exist"), row, key.toUpperCase());
					}
					if (name == null) {
						name = DBUtil.toString(key);
						value = DBUtil.toString(map.get(key));
					} else {
						name += ";" + DBUtil.toString(key);
						value += ";" + DBUtil.toString(map.get(key));
					}
				}
			}

			edcData.setDcName(name);
			edcData.setDcData(value);
			edcData.setItemName(line.getName());

			edcData.setMeasureTime(new Date());
			edcData.setEdcType(EdcData.EDCTYPE_ITEM);
			edcData.setEdcSetRrn(line.getEdcSetRrn());
			edcData.setEdcSetName(line.getItemSet().getName());
			edcData.setEdcSetVersion(line.getItemSet().getVersion());
			edcData.setItemName(line.getName());
			edcData.setSeqNo(line.getSeqNo());
			edcData.setUsl(DBUtil.toDouble(line.getUslString()));
			edcData.setLsl(DBUtil.toDouble(line.getLslString()));
			edcData.setSl(DBUtil.toDouble(line.getSlString()));
			edcData.setSubgroupSize(line.getSubgroupSize() != null ? line.getSubgroupSize().longValue() : null);
			edcData.setSampleSize(line.getSampleSize());
			edcData.setSamplePlan(line.getSamplePlan(new Long(itemNumber)));
			edcData.setDataType(line.getEdcItem().getDataType());
			edcData.setSampleType(line.getSampleType());
			if (lot != null) {
				edcData.setLineId(lot.getLineId());
				edcData.setLotRrn(lot.getObjectRrn());
				edcData.setBatchId(lot.getBatchId());
				edcData.setBatchLots("");
				edcData.setLotCount(null);
				edcData.setLotType(lot.getLotType());
				edcData.setLotId(lot.getLotId());
				edcData.setPartName(lot.getPartName());
				edcData.setPartVersion(lot.getPartVersion());
				edcData.setStepName(lot.getStepName());
				edcData.setStepVersion(lot.getStepVersion());
				edcData.setMaterialId(null);
				edcData.setCustomer(lot.getCustomerCode());
				edcData.setMeasureEqp(map.get(HEADER_EQUIPMENT));
				edcData.setOperator(Env.getUserName());
				edcData.setProcessEqp("");
				edcData.setLineId(lot.getLineId());
			}
			edcData.setTeamId(Env.getTeam());
			edcData.setComponentList(selectedComponentIds);
			edcData.setComponentRrnList(selectedComponentRrn);
			String[] valueStrings = edcData.getDcData().split(";");
			if (valueStrings.length > 0) {
				edcData.setDcDataAvg(DBUtil.toDouble(edcData.buildDcDataAvg()));
				edcData.setOosList(edcData.buildOosList());
			}

			edcData.setIsTemp(false);
			edcData.setObjectRrn(null);
			if (itemSet.getIsRepeatable()) {
				edcData.setIsRetest(EdcSetCurrent.TEST_TYPE_RETEST);
			}

			lot.setOperator1(Env.getUserName());
			edcDatas.add(edcData);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return edcDatas;
	}
	
	
	/**
	 * �ڶ�����ݲɼ���ʱ
	 * ������û���������ݵĲɼ����
	 */
	public List<EdcData> removeEmptyEdcData(List<EdcData> datas){
		List<EdcData> edcDatas = new ArrayList<EdcData>();
		for (EdcData data : datas) {
			if (EdcTextSet.ITEM_JUDGE_BY_MANUAL.equals(data.getItemName())) {
				edcDatas.add(data);
			} else if (data.getDcData() != null) {
				String dcDatas[] = data.getDcData().split(";");
				if (!data.getDcData().equals("") 
						&& dcDatas != null && dcDatas.length != 0){
					edcDatas.add(data);
				}
			}
		}
		return edcDatas;
	}
	
	/**
	 * ����Ƿ�ǿ�����룬���ǿ������
	 * 1��Variable�������ݱ���ÿ��������ֵ
	 * 2��Attribute��BIN���͵�����Total������ֵ�Ҳ���Ϊ0
	 * 3.����ѡ����ѡ���豸�����ݲɼ�������ѡ���豸
	 * 4.�ı����ݲɼ������˲ɼ���Ϊ����ļ��ǿ������
	 */
	public void checkMandatory(List<EdcData> dcDatas, AbstractEdcSet edcSet){

		if (edcSet instanceof EdcItemSet){
			for (EdcItemSetLine line : ((EdcItemSet) edcSet).getItemSetLines()) {
				//���Ϊ�������ݲɼ�
				if (line.getIsMandatory()){
					//�ж��Ƿ�������dcDatas
					boolean check = false;
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							check = true;
							if(line.getDataType().equals(EdcItem.DATATYPE_ATTRIBUTE)){
								//���Attribute����,Total������ֵ�Ҳ���Ϊ0
								String[] edcDatas = data.getDcData().split(";");
								String[] edcIds = data.getDcName().split(";");
								for (int i = 0; i < edcIds.length; i++) {
									if (EdcData.ATTRIBUTE_TOTAL.equals(edcIds[i])) {
										if (i < edcDatas.length) {
											String totalData = edcDatas[i];
											if (totalData.trim().length() > 0 && !"0".equals(totalData.trim())) {
												return;
											}
										}
									}
								}
								addErrorLog(Message.getString("edc.data_mandatory_fail"), row, "DATA");
							} else {
								//���Variable����
								String[] edcDatas = data.getDcData().split(";");
								String[] edcIds = data.getDcName().split(";");
								//��鳤���Ƿ���ͬ
								if (!(edcIds.length == edcDatas.length)) {
									addErrorLog(Message.getString("edc.data_mandatory_fail"), row, "DATA");
								}
								//���ÿ����λ�Ƿ���ֵ
								for (String edcData : edcDatas) {
									if (edcData.trim().length() == 0) {
										addErrorLog(Message.getString("edc.data_mandatory_fail"), row, "DATA");
									}
								}
							}
						}
					}
					//��������˱��䣬��δ����dcDatasδ¼��ֵ����Ҳ���п���
					if(!check) {
						addErrorLog(Message.getString("edc.data_mandatory_fail"), row, "DATA");
					}
				}
				
				if (line.getIsJudgeByManual()) {
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							if (StringUtil.isEmpty(data.getJudge1())) {
								addErrorLog(Message.getString("edc.data_judge_must_input"), row, "DATA");
							}
						}
					}
				}
			}
		} else if (edcSet instanceof EdcBinSet){
			//�����BIN���ݲɼ�
			if (edcSet.getIsJudgeByManual()) {
				for(EdcData data : dcDatas){
					if (StringUtil.isEmpty(data.getJudge1())) {
						addErrorLog(Message.getString("edc.data_judge_must_input"), row, "DATA");
					}
				}
			}
		} else if (edcSet instanceof EdcTextSet) {
			for (EdcTextSetLine line : ((EdcTextSet) edcSet).getTextSetLines()) {
				if (edcSet.getIsShowEquipment()) {
					for (EdcData edcData : dcDatas) {
						if (edcData.getMeasureEqp().equals("") || edcData.getMeasureEqp() == null) {
							addErrorLog(Message.getString("common.is_require"), row, "DATA");
						}
					}
				}
				//���Ϊ�������ݲɼ�
				if (line.getIsMandatory()){
					for(EdcData data : dcDatas){
						if (line.getName().equals(data.getItemName())){
							if (data.getDcData().equals("") || data.getDcData() == null) {
								addErrorLog(Message.getString("common.is_require"), row, "DATA");
							}
						}
					}
				}
			}	
			
			if (edcSet.getIsJudgeByManual()) {
				for(EdcData data : dcDatas){
					if (EdcTextSet.ITEM_JUDGE_BY_MANUAL.equals(data.getItemName())){
						if (StringUtil.isEmpty(data.getJudge1())) {
							addErrorLog(Message.getString("edc.data_judge_must_input"), row, "DATA");
						}
					}
				}
			}
		}
	}
	
	/**
	 * @param regex ������ʽ�ַ���
	 * @param str   Ҫƥ����ַ���
	 * @return ���str ���� regex��������ʽ��ʽ,����true, ���򷵻� false;
	 */
	private static boolean match(String regex, String str) {
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(str);
		return matcher.matches();
	}
	
	/**
	 * @param �����Ƿ�Component
	 */
	protected boolean isComponentUnitType() {
		if (lot != null) {
			if (ComponentUnit.getUnitType().equals(lot.getSubUnitType())) {
				return true;
			} else {
				return false;
			}
		}
		return false;
	}
	
	/**
	 * @param message          massage
	 * @param row              ��
	 * @param columnName       ��Ԫ��
	 * @param messageParameter �쳣��Ϣ;
	 */
	private void addErrorLog(String message, Integer row, String columnName, Object... messageParameter) {
		Long index = null;
		if (row != null) {
			index = Long.valueOf(row);
		}
		UploadErrorLog errorLog = null;
		if (messageParameter != null && messageParameter.length > 0) {
			errorLog = new UploadErrorLog(index, null, columnName, String.format(message, messageParameter));
		} else {
			errorLog = new UploadErrorLog(index, null, columnName, message);
		}
		progress.getErrLogs().add(errorLog);
	}
}
