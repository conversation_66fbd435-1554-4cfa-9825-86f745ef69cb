package com.glory.mes.wip.edc.tecn;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.glory.common.context.ContextRuleDialog;
import com.glory.common.context.client.ContextManager;
import com.glory.common.context.custom.ContextEditCustomComposite;
import com.glory.common.context.custom.ContextObjectQueryCustomComposite;
import com.glory.common.context.model.Context;
import com.glory.common.context.model.ContextValue;
import com.glory.edc.client.EDCManager;
import com.glory.edc.model.EdcTecn;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.forms.field.AbstractField;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.RadioField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * ����EDC��ʱ���̱��(׷�Ӳ���)
 * �ϲ�Ϊ��ѯ����,��ʾEDCTECN Context�����Ϣ
 * �ؼ�ʹ��ContextObjectQueryCustomComposite
 * ��ѯ����ContextManagerBean.getContextValueObject
 * Table��λ������
 * ���,����,״̬,�豸,����,��Ʒ,����,����,�����ռ�����,��������,���,���γ����ƻ�,Ƭ�����ƻ�,��Чʱ��
 * 
 * 
 * �²�Ϊ�����ͱ༭����,����������:
 * 1,���ΪContext����(Ҫ�ܸ���Context�趨�仯)
 * 2,�ұ�ΪEDCTecn����,EDCTecn�����ַ�ʽ(EDC/Sampling/Pilot(Pilot�ݲ�����))
 *   ����ѡ���EDCTecn��ʽ,��ʾ��ͬ��Tecn����
 */
public class EdcTecnEditor extends GlcEditor {

	private static final Logger logger = Logger.getLogger(EdcTecnEditor.class);

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.edc.tecn.EdcTecnEditor";
	
	public static final String BUTTON_NAME_SHOWRULE = "showRule";
	
	public static final String CONTROL_TECN_QUERY_INFO = "edcTecnQueryInfo";
	public static final String CONTROL_TECN_EDIT_INFO = "edcTecnEditInfo";
	public static final String CONTROL_CONTEXT_INFO = "contextInfo";
	public static final String CONTROL_TECN_INFO = "tecnInfo";
	public static final String CONTROL_ECN_TYPE = "ecnType";
	
	public CustomField edcTecnQueryInfoCustomField; 
	public GlcFormField tecnEditInfoGlcFormField; 
	public CustomField contextInfoCustomField;
	public EntityFormField tecnInfoEntityFormField; 
	public RadioField ecnTypeRadioField; 
	
	private static final String CONTEXT_NAME = "EDCTECN";
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		//��ȡ��ѯ�ؼ�
		edcTecnQueryInfoCustomField = form.getFieldByControlId(CONTROL_TECN_QUERY_INFO, CustomField.class);
		
		//��ȡ�����Context��EdcTecn�༭�ؼ�
		tecnEditInfoGlcFormField = form.getFieldByControlId(CONTROL_TECN_EDIT_INFO, GlcFormField.class);
		
		//��ȡContext�༭�ؼ�
		contextInfoCustomField = tecnEditInfoGlcFormField.getFieldByControlId(CONTROL_CONTEXT_INFO, CustomField.class);		
		
		//��ȡEdcTecn�༭�ؼ�
		tecnInfoEntityFormField = tecnEditInfoGlcFormField.getFieldByControlId(CONTROL_TECN_INFO, EntityFormField.class);	
		
		//��ȡEcnType��ѡ�ؼ�
		ecnTypeRadioField = tecnInfoEntityFormField.getFieldByControlId(CONTROL_ECN_TYPE, RadioField.class);	
		ecnTypeRadioField.addValueChangeListener(new IValueChangeListener() {
			@Override
			public void valueChanged(Object sender, Object newValue) {
				switchAdapter(newValue);
			}		
		});	
	
		//��ѯ���ѡ���¼�
		subscribeAndExecute(eventBroker, edcTecnQueryInfoCustomField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectAdapter);
		
		//�½��¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NAME_SHOWRULE), this::ruleAdapter);
				
		//�½��¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_NEW), this::newAdapter);
		
		//�����¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_SAVE), this::saveAdapter);			
		
		//�����¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_ACTIVE), this::activeAdapter);		
		
		//ʧЧ�¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_INACTIVE), this::inActiveAdapter);		
		
		//ɾ���¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_DELETE), this::deleteAdapter);		
		
		//ˢ���¼�
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_ENTITYREFRESH), this::refreshAdapter);		
		
		//��ҳ������½��¼�
		newAdapter(null);
	}

	protected void selectAdapter(Object object) {
		try {
			if (object == null) {
				return;
			}
			org.osgi.service.event.Event event = (org.osgi.service.event.Event) object;		
			ContextValue edcTecnContextValue = (ContextValue) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (edcTecnContextValue == null) {
				return;
			}
			CustomCompsite contextInfoCustomCompsite = contextInfoCustomField.getCustomComposite();
			contextInfoCustomCompsite.setValue(edcTecnContextValue);
			
			ADManager adManager = Framework.getService(ADManager.class);
			List<EdcTecn> tecns = adManager.getEntityList(Env.getOrgRrn(), EdcTecn.class, Integer.MAX_VALUE, 
					" status = 'Active' and name = '" + edcTecnContextValue.getResultValue1() + "'", "");
			if (tecns != null && tecns.size() > 0) {
				tecnInfoEntityFormField.setValue(tecns.get(0));
				tecnInfoEntityFormField.refresh();	
			}							
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
   /**
    * ѡ��ͬ��ECN���ͣ���ʾ��Ӧ����λ���������ɱ༭
    * @param object
    */
	protected void switchAdapter(Object object) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			
			ADField entityFormField = (ADField) tecnInfoEntityFormField.getADField();
			ADRefTable refTable = new ADRefTable();
			refTable.setObjectRrn(entityFormField.getReftableRrn());
			refTable = (ADRefTable) adManager.getEntity(refTable);
			
			ADTable table = new ADTable();
			table.setObjectRrn(refTable.getTableRrn());
			table = (ADTable) adManager.getEntity(table);
			
			for (ADTab tab : table.getTabs()) {
				if (tab.getName().equalsIgnoreCase((String)object)) {
					for (ADField field : table.getFields()) {					
						if (field.getTabRrn() != null && field.getTabRrn().equals(tab.getObjectRrn()) && field.getIsDisplay()) {
							AbstractField abstractField = tecnInfoEntityFormField.getFieldByControlId(field.getName(), AbstractField.class);
							abstractField.setEnabled(true);
							abstractField.refresh();
						}
					}
				} else {
					for (ADField field : table.getFields()) {					
						if (field.getTabRrn() != null && field.getTabRrn().equals(tab.getObjectRrn()) && field.getIsDisplay()) {
							AbstractField abstractField = tecnInfoEntityFormField.getFieldByControlId(field.getName(), AbstractField.class);
							abstractField.setEnabled(false);
							abstractField.refresh();
						}
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void ruleAdapter(Object object) {
        try {
        	ContextManager contextManager = Framework.getService(ContextManager.class);
            Context context = contextManager.getContextByName(Env.getOrgRrn(), CONTEXT_NAME);
             
        	ContextRuleDialog dialog = new ContextRuleDialog(UI.getActiveShell(), context);   
            dialog.open();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	protected void newAdapter(Object object) {
		try {
			CustomCompsite contextInfoCustomCompsite = contextInfoCustomField.getCustomComposite();
			contextInfoCustomCompsite.setValue(new ContextValue());
			
			tecnInfoEntityFormField.setValue(new EdcTecn());
			tecnInfoEntityFormField.refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void saveAdapter(Object object) {
		ContextEditCustomComposite contextInfoCustomCompsite = (ContextEditCustomComposite) contextInfoCustomField.getCustomComposite();
		EntityForm tecnInfoEntityForm = (EntityForm) tecnInfoEntityFormField.getControls()[0];
		try {	
			contextInfoCustomCompsite.getMessageManager().setAutoUpdate(true); //����Ϊtrueʱ�Ż�ˢ�½������icon
			contextInfoCustomCompsite.getMessageManager().removeAllMessages();
			
			tecnInfoEntityForm.getMessageManager().setAutoUpdate(true); //����Ϊtrueʱ�Ż�ˢ�½������icon
			tecnInfoEntityForm.getMessageManager().removeAllMessages();
			
			if (contextInfoCustomCompsite.getValue() != null && tecnInfoEntityFormField.getValue() != null) {
				boolean saveFlag = true;			
				if (!contextInfoCustomCompsite.getContextForm().saveToObject()) {
					saveFlag = false;
				}
				if (!tecnInfoEntityForm.saveToObject()) {
					saveFlag = false;
				}
				if (saveFlag) {
					EdcTecn edcTecn = (EdcTecn) tecnInfoEntityFormField.getValue();
					
					ContextManager contextManager = Framework.getService(ContextManager.class);
			        Context context = contextManager.getContextByName(Env.getOrgRrn(), CONTEXT_NAME);
			           	
					ContextValue contextValue = (ContextValue) contextInfoCustomCompsite.getValue();
					contextValue.setContextRrn(context.getObjectRrn());
					contextValue.setOrgRrn(Env.getOrgRrn());
					
					EDCManager edcManager = Framework.getService(EDCManager.class);
					edcManager.saveEdcTecn(edcTecn, contextValue, Env.getSessionContext());
					
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// ������ʾ��

					refreshAdapter(object);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}  finally {
			contextInfoCustomCompsite.getMessageManager().setAutoUpdate(false);
			tecnInfoEntityForm.getMessageManager().setAutoUpdate(false);
		}
	}
	
	protected void activeAdapter(Object object) {
		try {
			EdcTecnContextActiveDialog dialog = new EdcTecnContextActiveDialog(UI.getActiveShell());
            dialog.open();
            
            refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void inActiveAdapter(Object object) {
		try {		
			ContextObjectQueryCustomComposite contextObjectQueryComposite = 
					(ContextObjectQueryCustomComposite) edcTecnQueryInfoCustomField.getCustomComposite();
			ContextValue edcTecnContextValue = (ContextValue)contextObjectQueryComposite.getQueryForm().getTableManager().getSelectedObject();	
            if (edcTecnContextValue == null) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }
            if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmInActive()))) {             
            	EDCManager edcManager = Framework.getService(EDCManager.class);
                List<ContextValue> contextValues = new ArrayList<ContextValue>();
                contextValues.add(edcTecnContextValue);
                	              
                edcManager.inActiveEdcTecn(contextValues, Env.getSessionContext());
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonInActiveSuccess()));
            }	
            
            refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void deleteAdapter(Object object) {
		try {
			ContextObjectQueryCustomComposite contextObjectQueryComposite = 
					(ContextObjectQueryCustomComposite) edcTecnQueryInfoCustomField.getCustomComposite();
			ContextValue edcTecnContextValue = (ContextValue)contextObjectQueryComposite.getQueryForm().getTableManager().getSelectedObject();		
            if (edcTecnContextValue == null ) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				EDCManager edcManager = Framework.getService(EDCManager.class);
                List<ContextValue> contextValues = new ArrayList<ContextValue>();
                contextValues.add(edcTecnContextValue);
                	              
                edcManager.deleteEdcTecn(contextValues, Env.getSessionContext());
			}
			
			refreshAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} 
	}
	
	protected void refreshAdapter(Object object) {
		try {
			//��ѯ�б����
			edcTecnQueryInfoCustomField.refresh();
			
			//��ҳ������½��¼�
			newAdapter(null);
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}
	
}
