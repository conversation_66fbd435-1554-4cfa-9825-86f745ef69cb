package com.glory.mes.wip.lot.bin.repeat.collection;

import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.bin.repeat.collection.BinRepeatCollectionDialog;
import com.glory.edc.model.EdcData;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotBinRepeatCollectionSection extends LotSection {

	protected ToolItem itemDcop;
	
	public LotBinRepeatCollectionSection() {
		super();
	}
	
	public LotBinRepeatCollectionSection(ADTable table) {
		super(table);
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);		
		createToolItemDcop(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);	
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);

	}
	
	protected void createToolItemDcop(ToolBar tBar) {
		itemDcop = new ToolItem(tBar, SWT.PUSH);
		itemDcop.setText("BIN" + Message.getString("wip.dcop_repeat"));
		itemDcop.setImage(SWTResourceCache.getImage("dcop"));
		itemDcop.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				dcopAdapter();
			}
		});
	}

	protected void createToolItemRefresh(ToolBar tBar) {
		itemRefresh = new ToolItem(tBar, SWT.PUSH);
		itemRefresh.setText(Message.getString(ExceptionBundle.bundle.CommonRefresh()));
		itemRefresh.setImage(SWTResourceCache.getImage("refresh"));
		itemRefresh.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				refreshAdapter();
			}
		});
	}
	
	protected void dcopAdapter() {
		try {
			List<EdcData> edcDatas = getEdcDatas();
			if (edcDatas != null) {
				BinRepeatCollectionDialog dialog = new BinRepeatCollectionDialog(UI.getActiveShell(), (Lot)getAdObject(), edcDatas, EdcData.EDCFROM_OFFLINELOT);
				if (Dialog.OK == dialog.open()) {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));			
					//ˢ��
					refreshAdapter();
				}
			} else {
				UI.showError(Message.getString("spc.chart_nodatas"));
				return;
			}	
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	// ���µ�EdcData
	public List<EdcData> getEdcDatas() {
		try {
			Lot lot = (Lot)getAdObject();
			ADManager adManager = Framework.getService(ADManager.class);
			List<EdcData> edcDataList = adManager.getEntityList(Env.getOrgRrn(), EdcData.class, Env.getMaxResult(),
					"edcType = 'BIN' and lotRrn = " + lot.getObjectRrn() + " and isRetest = 'N' and stepName = '" + lot.getStepName() + "'", " componentList, updated desc ");
			if (edcDataList != null && edcDataList.size() > 0) {
				return edcDataList;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}			
	
}
