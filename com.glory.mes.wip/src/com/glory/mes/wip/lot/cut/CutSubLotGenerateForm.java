package com.glory.mes.wip.lot.cut;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.VerifyEvent;
import org.eclipse.swt.events.VerifyListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

/**
 * ���ɹ�������
 */
public class CutSubLotGenerateForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(CutSubLotGenerateForm.class);
	
	protected ADTable lotAdTable;
	protected ListTableManager lotTableManager;
	protected Text txtLotSize;
	protected Text txtLotCount;
	//protected Text lotIdText;
	
	public static final String TABLE_NAME = "WIPCutSubLots"; 

	protected static String regEx = "^[A-Za-z0-9-_]+$";
	
	public CutSubLotGenerateForm(Composite parent, int style, Object object, ADTab tab, IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	@Override
	public void createForm() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);
		
		Composite top = toolkit.createComposite(body);
		top.setLayout(new GridLayout(8, false));
		GridData gd = new GridData(GridData.HORIZONTAL_ALIGN_FILL);
		top.setLayoutData(gd);

		GridData gText = new GridData(GridData.FILL_HORIZONTAL);
		gText.widthHint = 60;
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			toolkit.createLabel(top, Message.getString("common.lotqty"));
			txtLotSize = toolkit.createText(top, "", SWT.BORDER);
			txtLotSize.setLayoutData(gText);
			txtLotSize.setTextLimit(32);
			txtLotSize.setText("1");

			gText = new GridData(GridData.FILL_HORIZONTAL);
			gText.widthHint = 60;
			toolkit.createLabel(top, Message.getString("common.lot_number"));
			txtLotCount = toolkit.createText(top, "", SWT.BORDER);
			txtLotCount.setLayoutData(gText);
			txtLotCount.setTextLimit(32);
			txtLotCount.setText("1");

			//gText = new GridData(GridData.FILL_HORIZONTAL);
			//gText.widthHint = 140;
			//toolkit.createLabel(top, Message.getString("wip.lot_id"));
			//lotIdText = toolkit.createText(top, "", SWT.BORDER);
			//lotIdText.setLayoutData(gText);
			//lotIdText.setTextLimit(32);

			//lotIdText.addVerifyListener(new VerifyListener() {
			//	public void verifyText(VerifyEvent e) {
			//		// ������ʽ��֤
			//		Pattern pattern = Pattern.compile(regEx);
			//		Matcher matcher = pattern.matcher(e.text);
			//		if (matcher.matches()) {
			//			// �����Сд��ĸ���л��ߡ��»��ߡ�����
			//			e.doit = true;
			//		} else if (e.text.length() > 0) {
			//			// �����������
			//			e.doit = false;
			//		} else {
			//			// ���Ƽ�
			//			e.doit = true;
			//		}
			//	}
			//});
			
			Button addLotBtn = toolkit.createButton(top, Message.getString(ExceptionBundle.bundle.CommonAdd()), SWT.BUTTON1);
			addLotBtn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					try {
//						if (lotTableManager.getInput() != null) {
//							for (Lot lot : ((List<Lot>) lotTableManager.getInput())) {
//								if (lotIdText.getText().equalsIgnoreCase(lot.getLotId())) {
//									UI.showError(Message.getString("wip.lotid_repeat"));
//									return;
//								}
//							}
//						}
						List<Lot> lots = new ArrayList<Lot>();
						if (lotTableManager.getInput() != null) {
							lots.addAll((List<Lot>) lotTableManager.getInput());
						}
						long lotNumber = Long.parseLong(txtLotCount.getText());
						BigDecimal mainQty = new BigDecimal(txtLotSize.getText());
						for (int i = 0; i < lotNumber; i++) {
							Lot lot = new Lot();
							lot.setOrgRrn(Env.getOrgRrn());
							lot.setIsActive(true);
//							if (lotIdText.getText() != null && !"".equals(lotIdText.getText())) {
//								lot.setLotId(lotIdText.getText().toUpperCase());
//							}

							lot.setMainQty(mainQty);
							lots.add(lot);
						}
						
						lotTableManager.setInput(lots);
						lotQtyChange();
					} catch (Exception e) {
						e.printStackTrace();
						UI.showError(Message.getString(ExceptionBundle.bundle.ErrorInvalidNumber()));
					}
				}
			});

			Button removeLotbtn = toolkit.createButton(top, Message.getString(ExceptionBundle.bundle.CommonDelete()), SWT.BUTTON1);
			removeLotbtn.addSelectionListener(new SelectionAdapter() {
				public void widgetSelected(SelectionEvent event) {
					List<Object> removeLots = lotTableManager.getCheckedObject();
					for (Object removeLot : removeLots) {
						Lot pre = (Lot) removeLot;
						((List<Lot>) lotTableManager.getInput()).remove(pre);
					}
				}
			});
			Composite tableCom = toolkit.createComposite(body);
			tableCom.setLayout(new GridLayout(1, false));
			GridData tablegd = new GridData(GridData.FILL_BOTH);
			tableCom.setLayoutData(tablegd);

			lotAdTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			lotTableManager = new ListTableManager(lotAdTable, true);
			lotTableManager.setIndexFlag(true);
			lotTableManager.newViewer(tableCom);

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void lotQtyChange() {
//		fireFormDataChanged(this, getLotQty());
	}

	@Override
	public List<String> getCopyProperties() {
		List<String> properties = new ArrayList<String>();
		properties.add("lots");
		return properties;
	}
	
	@Override
	public boolean saveToObject() {
		try {
			if (object != null) {
				Lot lot = (Lot) object;
				if (lot.getObjectRrn() != null) {
					if (!validate()) {
						return false;
					}
					if (lot.getMainQty() == null) {
						return false;
					}
					
					List<Lot> inputLots = new ArrayList<Lot>();
					if (lotTableManager.getInput() != null) {
						inputLots.addAll((List<Lot>) lotTableManager.getInput());
					}
	
					List<Lot> children = new ArrayList<Lot>();
					for(Lot lt : inputLots) {
						if (lt.getLotId() == null) {
							Lot child = (Lot)lot.clone();
							child.setLotId(null);
							child.setMainQty(lt.getMainQty());
							child.setSubQty(lt.getSubQty());
							child.setOperator1(Env.getUserName());
							children.add(child);
						}
					}
	
	//				// ��鹤�������������������Ƿ�һ��
	//				if (getLotQty().compareTo(lot.getMainQty()) != 0) {
	//					UI.showError(Message.getString("wip.wo_qty_match_total_lot_qty"));
	//					return false;
	//				}
					lot.setChildrenLots(children);
				}
			}		
			return true;
		} catch(Exception e) {
			logger.error("Cut Lot Failure at CutSubLotGenerateForm : " + e);
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}
	
	@Override
	public void loadFromObject() {
		lotTableManager.getInput().clear();
		if (object != null) {
			try {
				Lot lot = (Lot) object;
				List<Lot> lots = new ArrayList<Lot>();
				if (lot != null && lot.getObjectRrn() != null) {
					lots = lot.getChildrenLots();
					if (lots == null) {
						ADManager adManager = Framework.getService(ADManager.class);
						lots = adManager.getEntityList(Env.getOrgRrn(),
			                    Lot.class, Env.getMaxResult(), " orgRrn = " + lot.getOrgRrn() + " and parentLotRrn = " + lot.getObjectRrn(), null);
					}
	//				PrdManager prdManager = Framework.getService(PrdManager.class);
	//				Part part = null;
	//				if (workOrder.getPartVersion() == null) {
	//					if(workOrder.getPartName() == null){
	//						return;
	//					}
	//					part = prdManager.getActivePart(Env.getOrgRrn(), workOrder.getPartName(), true);
	//				} else {
	//					part = prdManager.getPartById(Env.getOrgRrn(), workOrder.getPartName(), workOrder.getPartVersion());
	//				}
	//				if (part.getLotSize() != null) {
	//					setDefaultSize(String.valueOf(part.getLotSize()));
	//				} else {
	//					setDefaultSize("25");
	//				}
				}
	            lotTableManager.setInput(lots);
	            lotQtyChange();
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}
	
	protected BigDecimal getLotQty() {
		BigDecimal generationLotQty = BigDecimal.ZERO;
		 List<Lot> childrenLots = new ArrayList<Lot>();
         if (lotTableManager.getInput() != null) {
        	 childrenLots.addAll((List<Lot>) lotTableManager.getInput());
         }
        for (Lot childLot : childrenLots) {
            generationLotQty = generationLotQty.add(childLot.getMainQty());
        }
        return generationLotQty;
	}
	
	public List<Lot> getChildrenLots() {
		List<Lot> childrenLots = new ArrayList<Lot>();
		if (lotTableManager != null) {
			List<Lot> lots = (List<Lot>) lotTableManager.getInput();
			for (Lot childLot : lots) {
				childrenLots.add(childLot);
			}
		}
		return childrenLots;
	}
	
	public void setDefaultSize(String size){
		txtLotSize.setText(size);
	}
	
	public void setDefaultCount(String size){
		txtLotCount.setText(size);
	}
	
}
