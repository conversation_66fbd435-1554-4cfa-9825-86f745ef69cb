package com.glory.mes.wip.lot.cut;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.MouseListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableItem;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityAttributeForm;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class CutSection extends LotSection implements ISelectionChangedListener ,MouseListener{
	private static final Logger logger = Logger.getLogger(CutSection.class);

	public static String TAB_LOT_GENERATE = "WIPCutSubLots";
	public static String SCRAPCODE = "CutSurplus";
	
	private CutSubLotGenerateForm formLotGenerate;
	
	protected ToolItem itemSplit;
	protected ToolItem itemTCard;
	protected ToolItem itemLabel;
	protected ToolItem itemScrap;
	
	protected CutForm cutForm;
	protected Lot selectedElement;
	protected List<Lot> checkedElements;
	//private EntityForm itemForm;

	public CutSection() {
		super();
	}

	public CutSection(ADTable table) {
		super(table);
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("wip.cut_lot"));
		initAdObject();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSplit(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
//		createToolItemTCard(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemScrap(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemSplit(ToolBar tBar) {
		itemSplit = new ToolItem(tBar, SWT.PUSH);
		itemSplit.setText(Message.getString("wip.cut"));
		itemSplit.setImage(SWTResourceCache.getImage("split-lot"));
		itemSplit.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				splitAdapter(event);
			}
		});
	}
		
	protected void createToolItemTCard(ToolBar tBar) {
		itemTCard = new ToolItem(tBar, SWT.PUSH);
		itemTCard.setText(Message.getString("wip.tcard"));
		itemTCard.setImage(SWTResourceCache.getImage("tcard"));
		itemTCard.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				
			}
		});
	}
	
	protected void createToolItemScrap(ToolBar tBar) {
		itemScrap = new ToolItem(tBar, SWT.PUSH);
		itemScrap.setText(Message.getString("wip.cut_scrap_lot"));
		itemScrap.setImage(SWTResourceCache.getImage("scrap-lot"));
		itemScrap.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				scrapAdapter(event);
			}
		});
	}
	
	@Override
	protected IForm getForm(Composite composite, ADTab tab) {
		if (tab.getName().equals(EntityAttributeForm.NAME)) {
			return new EntityAttributeForm(composite, SWT.NONE, table.getModelName(), this.getAdObject(), tab.getGridY().intValue(), mmng);
		} else if (tab.getName().equals(TAB_LOT_GENERATE)) {
			formLotGenerate = new CutSubLotGenerateForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
			return formLotGenerate;
		}
		EntityForm entityFrom = new EntityForm(composite, SWT.NONE, this.getAdObject(), tab, mmng);
		entityFrom.setADManager(getADManger());
		return entityFrom;
	}
	
	protected void splitAdapter(SelectionEvent event) {
		try {
			this.form.getMessageManager().setAutoUpdate(false);
			this.form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					Lot lot = (Lot)getAdObject();
					if (lot == null || lot.getObjectRrn() == null) {
						UI.showError(Message.getString("wip.input_lot_first"));
					} else {
						List<Lot> childrenLots = lot.getChildrenLots();
						if (childrenLots == null || childrenLots.size() == 0) {
							UI.showError(Message.getString("wip.input_cut_sub_lot_first"));
							return;
						}
						
//					    List<StepState> stepStates = cutForm.getFieldStepSate().getInput();
//					    StepState stepState = null;
//					    if (stepStates.size() > 0) {
////					        int index = splitForm.getFieldStepSate().getComboControl().get
//					    	// TODO �ص����
//			                stepState = (StepState) cutForm.getFieldStepSate().getData();
//			                lot.setAttribute2(stepState);
//					    }
					    
					    lot.setOperator1(Env.getUserName());
						LotManager lotManager = Framework.getService(LotManager.class);
						lotManager.splitLot(lot, childrenLots, Env.getSessionContext());
						UI.showInfo(Message.getString("wip.cut_success"));
					    
						refreshAdapter(); 
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			this.form.getMessageManager().setAutoUpdate(true);
		}
	}
	
	protected void scrapAdapter(SelectionEvent event) {
		try {
			Lot lot = (Lot)getAdObject();
			if (lot == null || lot.getObjectRrn() == null) {
				UI.showError(Message.getString("wip.no_lot_to_scrap"));
				return;
			}

			LotManager lotManager = Framework.getService(LotManager.class);
            lot.setOperator1(Env.getUserName());

            Map<String, List<ProcessUnit>> lotActionsMap = new HashMap<String, List<ProcessUnit>>();

            //QtyUnit
            QtyUnit qtyUnit = new QtyUnit();
            qtyUnit.setActionCode(SCRAPCODE);
            qtyUnit.setMainQty(lot.getMainQty());
            qtyUnit.setSubQty(lot.getSubQty());
			List<ProcessUnit> units = new ArrayList<ProcessUnit>();
            units.add(qtyUnit);
            lotActionsMap.put(qtyUnit.getActionCode(), units);
            
            //LotAction
            LotAction lotAction = new LotAction();
            lotAction.setActionCode(SCRAPCODE);
    		lotAction.setOcapId("");
    		
    		//scrapLotActions
            List<LotAction> scrapLotActions = new ArrayList<LotAction>();
            for (String key : lotActionsMap.keySet()) {
                LotAction la = lotAction;
                la.setActionType(LotAction.ACTIONTYPE_SCRAP);
                la.setLotRrn(lot.getObjectRrn());
                la.setActionCode(key);
                la.setActionUnits(lotActionsMap.get(key));
                la.setActionComment("");
                scrapLotActions.add(la);
            }

            lotManager.scrapLot(lot, scrapLotActions, lotAction, Env.getSessionContext());
            UI.showInfo(Message.getString("wip.scrapLot_success"));
				
			refreshAdapter();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}	
	}	
	
	@Override
	protected EntityForm getForm(ADTab tab) {
		cutForm = new CutForm(getTabs(), SWT.NONE, tab, mmng, this);
		return cutForm;
	}
	
	@Override
	protected void refreshAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			Lot lot = (Lot)getAdObject();
			if (lot != null && lot.getObjectRrn() != null) {
				LotManager lotManager = Framework.getService(LotManager.class);
				lot = lotManager.getLotWithChildren(lot.getObjectRrn());
				setAdObject(lot);
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
        	return;
		}		
		refresh();
	}
	
	public Lot searchLot(String lotId) {
		try {
			Lot lot = super.searchLot(lotId);
			LotManager lotManager = Framework.getService(LotManager.class);
			if (lot != null) {
//			    List<StepState> stepStates = lotManager.getFutureSteps(lot);
//			    if (stepStates != null && stepStates.size() > 0) {
//			        StepState stepState = stepStates.get(0);
//			        stepState.setObjectRrn(null);
//			        stepState.setOrgRrn(null);
//			        stepState.setStepName("");
//			        stepState.setName("");
//			    }
//			    cutForm.getFieldStepSate().setInput(stepStates);
//			    cutForm.getFieldStepSate().refresh();
				return lotManager.getLotWithChildren(lot.getObjectRrn());
			}
		} catch (Exception e) {
			logger.error("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	@Override
	public void statusChanged(String newStatus) {
		if (LotStateMachine.STATE_WAIT.equalsIgnoreCase(newStatus) || 
				LotStateMachine.STATE_FIN.equalsIgnoreCase(newStatus)) {
			itemSplit.setEnabled(true);
			itemScrap.setEnabled(true);
		} else {
			itemSplit.setEnabled(false);
			itemScrap.setEnabled(false);
		}
	}

	@Override
	public void selectionChanged(SelectionChangedEvent event) {
		StructuredSelection ss = (StructuredSelection) event.getSelection();
		if (!ss.isEmpty()) {
			selectedElement = (Lot) ss.getFirstElement();			
		} else {
			
		}
	}

	@Override
	public void mouseDoubleClick(MouseEvent e) {}

	@Override
	public void mouseDown(MouseEvent e) {
		Table table = (Table) e.widget;
		//boolean hasItemChecked = false;
		checkedElements = new ArrayList<Lot>();
		for (TableItem item : table.getItems()) {
			if (item.getChecked()) {
				//hasItemChecked = true;
				checkedElements.add((Lot) item.getData());
			}
		}
	}

	@Override
	public void mouseUp(MouseEvent e) {}
	
}
