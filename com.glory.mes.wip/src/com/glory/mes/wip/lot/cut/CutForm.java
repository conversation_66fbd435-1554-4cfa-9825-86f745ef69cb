package com.glory.mes.wip.lot.cut;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.mes.wip.future.FutureMerge;
import com.glory.mes.wip.model.Lot;

public class CutForm extends EntityForm {
	private static final Logger logger = Logger.getLogger(CutForm.class);
	//private TableViewer viewer;
	//private IField field;

    private RefTableField fieldStepSate;
	
	private static final String FIELDID_SEPARTOR = "splitSepartor";
	//private CutSection section;
	
	public CutForm(Composite parent, int style, ADTab tab,
			IMessageManager mmng, CutSection section) {
		super(parent, style, tab, mmng);
		//this.section = section;
		super.createForm();
	}

	public CutForm(Composite parent, int style, ADTable table,
			IMessageManager mmng) {
		super(parent, style, table, mmng);
	}

	public CutForm(Composite parent, int style, Object object, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, object, tab, mmng);
	}

	public CutForm(Composite parent, int style, Object object,
			ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}

	@Override
	public void createForm() {}
	
	@Override
	public void loadFromObject() {
		if (object != null) {
			Lot lot = (Lot) object;
			for (IField f : fields.values()) {
				if (!f.getId().equals(FIELDID_SEPARTOR)) {
					Object o = PropertyUtil.getPropertyForIField(object, f.getId());
					f.setValue(o);
				}
			}
//			if (lot.getObjectRrn() != null) {
//				// ȡ��δ������
//			    List<FutureMerge> futureMerges = getADManger().getEntityList(Env.getOrgRrn(), FutureMerge.class, 1, 
//			    		"parentLotRrn = '" + lot.getObjectRrn() + "'", "");
//			    if (futureMerges != null && futureMerges.size() > 0) {
//			    	FutureMerge futureMerge = futureMerges.get(0);
//			    	fieldStepSate.setValue(futureMerge.getStepName());
//			    }
//			}
			refresh();
			setEnabled();
		}
    }

//	public void createTableViewer() {
//		String[] columnsHeaders = new String[] {Message.getString("wip.lot_id"),
//				Message.getString("wip.main_qty"),
//				Message.getString("wip.sub_qty"), 
//				Message.getString("wip.state")};
//		int[] columnsSize = new int[] { 60, 60, 60, 60 };
//
//		Table table = new Table(this, SWT.BORDER | SWT.CHECK | SWT.FULL_SELECTION);
//		table.setHeaderVisible(true);
//		table.setLinesVisible(true);
//		GridData gd = new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1);
//		gd.heightHint = 400;
//		gd.widthHint = 100;
//		table.setLayoutData(gd);
//		table.setHeaderVisible(true);
//		table.setLinesVisible(true);
//		viewer = new TableViewer(table);
//		if (columnsHeaders != null) {
//			for (int i = 0; i < columnsHeaders.length; i++) {
//				TableColumn column;
//				column = new TableColumn(table, SWT.NONE);
//				column.setText(columnsHeaders[i]);
//				column.setResizable(true);
//			}
//		}
//		table.setLayout(new WeightedTableLayout(columnsSize));
//	}
	
//	private class LotLabelProvider extends LabelProvider implements ITableLabelProvider {
//		
//		@Override
//		public Image getColumnImage(Object element, int columnIndex) {
//			return null;
//		}
//		
//		@Override
//		public String getColumnText(Object element, int columnIndex) {
//			if (element instanceof Lot) {
//				Lot lot = (Lot) element;
//				switch (columnIndex) {
//					case 0:
//						return lot.getLotId();
//					case 1: {
//						if (lot.getMainQty() == null) {
//							return "";
//						}
//						return lot.getMainQty().toString();
//					}
//					case 2: {
//						if (lot.getSubQty() == null) {
//							return "";
//						}
//						return lot.getSubQty().toString();
//					}
//					case 3: {
//						return lot.getState();
//					}
//				}
//			}
//			return "";
//		}
//	}
	
//	private class LotContentProvider implements IStructuredContentProvider {
//		@Override
//		public Object[] getElements(Object inputElement) {
//			if (inputElement instanceof List) {
//				return ((List) inputElement).toArray();
//			}
//			return new Object[0];
//		}
//
//		@Override
//		public void dispose() {}
//
//		@Override
//		public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {}
//	}

    public RefTableField getFieldStepSate() {
        return fieldStepSate;
    }

    public void setFieldStepSate(RefTableField fieldStepSate) {
        this.fieldStepSate = fieldStepSate;
    }
}
