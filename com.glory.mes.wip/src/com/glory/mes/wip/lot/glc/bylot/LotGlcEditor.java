package com.glory.mes.wip.lot.glc.bylot;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.StructuredSelection;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.security.password.check.UserPasswordCheckDialog;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.client.SecurityManager;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.CarrierLotCustomComposite;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;

public class LotGlcEditor extends GlcEditor{
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.run.glc.bylot.LotGlcEditor";

	private static final Logger logger = Logger.getLogger(LotGlcEditor.class);
	
	protected static final String CONTROL_LOTID_ENTERPRESSED = "lotQtimeAndComponent-lotId-EnterPressed";
	protected static final String CONTROL_CARRIERID_ENTERPRESSED = "lotQtimeAndComponent-carrierId-EnterPressed";
	
	public static final String FIELD_ENTITY_BASIC = "lotBasicInfo"; 
	public static final String FIELD_ENTITY_PRD = "lotPrdInfo"; 
	public static final String FIELD_TABLE_HOLD = "lotHoldTable"; 
	public static final String FIELD_TABLE_CHILD = "lotChildTable"; 
	public static final String FIELD_TABLE_PARAMETER = "lotParameter"; 
	
	public static final String FIELD_CUSTOM_CARRIERLOT = "lotQtimeAndComponent"; 
	public static final String FIELD_GLC_BASICINFO_PRD = "lotBasicAndPrdInfo"; 
	public static final String FIELD_GLC_QTIME_COMPONENT_HOLD = "lotParamterAndChildAndHold"; 
	
	public GlcFormField lotBasicAndPrdInfoForm, lotParaAndChildCommentForm, lotParamterAndChildAndHoldForm;
	public CustomField lotQtimeAndComponentForm;
	public EntityFormField basicInfoForm, prdProcedureForm;
	public ListTableManagerField holdTableManagerField, childLoTableManagerField, parameterTableManagerField;
	
	public CarrierLotCustomComposite carrierLotCustomComposite;
	
	protected Boolean isCaseSensitive;
	
	public HeaderText txtLotId, txtCarrierId, txtOperator;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		//ע��Ĭ�ϻس��¼�
		form.unsubscribeDefaultEvent(form.getFullTopic(CONTROL_LOTID_ENTERPRESSED));
		form.unsubscribeDefaultEvent(form.getFullTopic(CONTROL_CARRIERID_ENTERPRESSED));
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_LOTID_ENTERPRESSED), this::lotIdEnterpressed);
		subscribeAndExecute(eventBroker, form.getFullTopic(CONTROL_CARRIERID_ENTERPRESSED), this::carrierIdEnterpressed);
		
		lotQtimeAndComponentForm = form.getFieldByControlId(FIELD_CUSTOM_CARRIERLOT, CustomField.class);
		carrierLotCustomComposite = (CarrierLotCustomComposite) lotQtimeAndComponentForm.getCustomComposite();
		
		lotBasicAndPrdInfoForm = form.getFieldByControlId(FIELD_GLC_BASICINFO_PRD, GlcFormField.class);
		basicInfoForm = lotBasicAndPrdInfoForm.getFieldByControlId(FIELD_ENTITY_BASIC, EntityFormField.class);
		prdProcedureForm = lotBasicAndPrdInfoForm.getFieldByControlId(FIELD_ENTITY_PRD, EntityFormField.class);
		
		lotParamterAndChildAndHoldForm = form.getFieldByControlId(FIELD_GLC_QTIME_COMPONENT_HOLD, GlcFormField.class);
		holdTableManagerField = lotParamterAndChildAndHoldForm.getFieldByControlId(FIELD_TABLE_HOLD, ListTableManagerField.class);
		childLoTableManagerField = lotParamterAndChildAndHoldForm.getFieldByControlId(FIELD_TABLE_CHILD, ListTableManagerField.class);
		parameterTableManagerField = lotParamterAndChildAndHoldForm.getFieldByControlId(FIELD_TABLE_PARAMETER, ListTableManagerField.class);
		
		
		txtLotId = carrierLotCustomComposite.getTxtLotId();
		txtCarrierId = carrierLotCustomComposite.getTxtCarrierId();
		txtOperator = carrierLotCustomComposite.getTxtOperator();
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			if ((MesCfMod.isTrackOperatorPassword(Env.getOrgRrn(), sysParamManager) && txtOperator != null) 
					|| (!MesCfMod.isTrackOperator(Env.getOrgRrn(), sysParamManager) && txtOperator != null)) {
				txtOperator.setVisible(false);
				carrierLotCustomComposite.getLblOperatorId().setVisible(false);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
		carrierLotCustomComposite.getLotTableManager().addICheckChangedListener(new ICheckChangedListener() {
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				List<Object> objects = carrierLotCustomComposite.getLotTableManager().getCheckedObject();
				if (objects != null && objects.size() > 0) {
					List<Lot> checkLots = objects.stream().map(o -> ((Lot)o)).collect(Collectors.toList());
					try {
						carrierLotCustomComposite.loadComponentUnits(checkLots);
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
						return;
					}
					Lot lot = checkLots.get(0);
					if (lot != null) {
						lot = searchLot(lot.getLotId());
					}
					setAdObject(lot);
					refresh();
			}	
			}
		});
	}
	
	protected void lotIdEnterpressed(Object obj) {
		String lotId = txtLotId.getText();
		if (!StringUtil.isEmpty(lotId)) {
			if (!isLotIdCaseSensitive()) {
				lotId = lotId.toUpperCase();
			}
			getLotByLotId(lotId);
		}
	}
	
	protected void carrierIdEnterpressed(Object obj) {
		String carrierId = txtCarrierId.getText();
		if (!StringUtil.isEmpty(carrierId)) {
			getLotsByCarrierId(carrierId);
		}
	}
	
	public void setAdObject(Lot lot) {
		try {
			if (lot != null) {	
				try {
					if (lot.getObjectRrn() != null) {
						//������Ϣ
						basicInfoForm.setValue(lot);
						prdProcedureForm.setValue(lot);
						basicInfoForm.refresh();
						prdProcedureForm.refresh();
						//�����Qtime��Ϣ
						SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
						if (MesCfMod.isUseDurable(Env.getOrgRrn(), sysParamManager)) {
							if (carrierLotCustomComposite != null && carrierLotCustomComposite.getLotTableManager() != null) {
								carrierLotCustomComposite.getLotTableManager().update(lot);
							}
						}
						//��ͣ��Ϣ
						LotManager lotManager = Framework.getService(LotManager.class);
						List<LotHold> lotHoldsList = lotManager.getLotHolds(lot.getObjectRrn());
						holdTableManagerField.getListTableManager().setInput(lotHoldsList);
						//������Ϣ
						List<Lot> lotChilds = lotManager.getLotWithChildren(lot.getObjectRrn()).getChildrenLots();
						childLoTableManagerField.getListTableManager().setInput(lotChilds);
						//������Ϣ
						PrdManager prdManager = Framework.getService(PrdManager.class);
						Map<String, Object> paramMap = new HashMap<String, Object>();
						if(lot.getProcessInstanceRrn() != null) {
							paramMap = prdManager.getCurrentParameter(lot.getProcessInstanceRrn());
							List<Lot> lotParameters = new ArrayList<Lot>();
							if(paramMap !=null && paramMap.size() > 0) {
								for (String key : paramMap.keySet()) {
									Lot lotParameter = (Lot)lot.clone();
									lotParameter.setAttribute1(key);
									lotParameter.setAttribute3(String.valueOf(paramMap.get(key)));
									lotParameters.add(lotParameter);
								}
							}
							parameterTableManagerField.getListTableManager().setInput(lotParameters);
						}
				 }
				} catch (Exception e) {
					e.printStackTrace();
				}
			} 
		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotByLotId(String lotId) {
		try {
			Lot lot = searchLot(lotId);
			if (lot != null) {
				List<Lot> lots = new ArrayList<Lot>();
				lots.add(lot);
				
				carrierLotCustomComposite.getLotTableManager().setInput(lots);
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				txtLotId.setText(lotId);
				// Ĭ��ȫѡ
				if (carrierLotCustomComposite.isCheckFlag()) {
					carrierLotCustomComposite.getLotTableManager().setCheckedObject(lot);
				}
				carrierLotCustomComposite.getLotTableManager().refresh();
				setAdObject(lot);
				refresh();
				carrierLotCustomComposite.loadComponentUnits(lot);

				if (txtCarrierId != null) {
					if(!StringUtil.isEmpty(lot.getDurable())) {
						txtCarrierId.setText(lot.getDurable());
					}else {
						txtCarrierId.setText("");
					}
				}
				txtLotId.focusing();
			} else {
				carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
				carrierLotCustomComposite.getLotTableManager().refresh();

				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				txtLotId.setText(lotId);
				
				carrierLotCustomComposite.getLotTableManager().setSelection(new StructuredSelection(new Object[] {new Lot()}));
				
				if (carrierLotCustomComposite.isShowComponentFlag()) {
					carrierLotCustomComposite.getCompTableManager().setInput(Lists.newArrayList());
					carrierLotCustomComposite.getCompTableManager().refresh();
				}
				txtLotId.warning();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
				if (CollectionUtils.isNotEmpty(lots)) {
					// Load Current Step
					Lot lotInfo = searchLot(lots.get(0).getLotId());
					
					carrierLotCustomComposite.getLotTableManager().setInput(lots);
					// Ĭ��ȫѡ
					if (carrierLotCustomComposite.isCheckFlag()) {
						for (Lot lot : lots) {
							carrierLotCustomComposite.getLotTableManager().setCheckedObject(lot);
						}
					}
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					carrierLotCustomComposite.getLotTableManager().refresh();
					
					if (lots != null && lots.size() > 0) {
						setAdObject(lotInfo);
						refresh();
					}
					
					carrierLotCustomComposite.loadComponentUnits(lots);	
					txtLotId.setText(lots.get(0).getLotId());
					txtCarrierId.focusing();
				} else {
					carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
					carrierLotCustomComposite.getLotTableManager().refresh();
				}
			} else {
				carrierLotCustomComposite.getLotTableManager().setInput(new ArrayList<Lot>());
				carrierLotCustomComposite.getLotTableManager().refresh();

				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				
				if (carrierLotCustomComposite.isShowComponentFlag()) {
					carrierLotCustomComposite.getCompTableManager().setInput(Lists.newArrayList());
					carrierLotCustomComposite.getCompTableManager().refresh();
				}
				txtCarrierId.warning();
			}
			
			if (carrierLotCustomComposite.getLotDetailsForm() != null) {
				carrierLotCustomComposite.getLotDetailsForm().setObject(new Lot());
				carrierLotCustomComposite.getLotDetailsForm().loadFromObject();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public Lot searchLot(String lotId) {
		try {
			//���߱�������
			Lot lot = LotProviderEntry.getLotByLine(lotId);
			return lot;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	public List<Lot> searchLotByDurableId(String durableId) {
		try {
			//���ؾ߼������
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Lot> lots = lotManager.getLotsByDurableId(Env.getOrgRrn(), durableId);
			return lots;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	public void refresh() {
		if (txtLotId != null) {
			txtLotId.selectAll();
		}
		if (txtCarrierId != null) {
			txtCarrierId.selectAll();
		}
	}
	
	public void setLotById(String lotId) {
		if (!StringUtil.isEmpty(lotId)) {
			if(txtLotId != null) {
				txtLotId.setText(lotId);
			}
			if (carrierLotCustomComposite != null) {	
				if (carrierLotCustomComposite.getLotTableManager().getInput().size() > 1) {
					getLotsByCarrierId(txtCarrierId.getText());
				} else {
					getLotByLotId(lotId);
				}
			}					
			if (carrierLotCustomComposite == null) {
				Lot lot = new Lot();
				lot = searchLot(lotId);
				setAdObject(lot);
				refresh();
			}
			
		}
	}
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
	
	protected Boolean CheckOperatorOrNoCheck() {
		try {
			SysParameterManager paramManager = Framework.getService(SysParameterManager.class);
			if (MesCfMod.isTrackOperatorPassword(Env.getOrgRrn(), paramManager)) {
				UserPasswordCheckDialog dialog = new UserPasswordCheckDialog(UI.getActiveShell().getDisplay(), Env.getUserName());
				if (UserPasswordCheckDialog.OK == dialog.getReturnCode()) {
					txtOperator.setText(dialog.getUserName());
				}else {
					return false;
				}
			} else {
				String operatorId = txtOperator.getText().trim();
				if (operatorId.length() == 0) {
					UI.showWarning(Message.getString("wip.track_operator_is_null"));
					return false;
				}
				if (!checkOperator(operatorId)) {
					UI.showWarning(Message.getString("wip.track_operator_is_not_exist"));
					return false;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}
	
	protected boolean checkOperator(String operatorStr) {
		try {
			SecurityManager secManager = Framework.getService(SecurityManager.class);
			ADUser user = secManager.getUserByUserName(Env.getOrgRrn(), operatorStr);
			if (user != null) {
				return true;
			}
		} catch (Exception e) {
			
		}
		return false;
	}
}
