package com.glory.mes.wip.lot.run.track.extensionpoints;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtension;
import org.eclipse.core.runtime.IExtensionPoint;
import org.eclipse.core.runtime.Platform;

import com.glory.framework.core.util.StringUtil;

public class TrackEditorExtensionPoint {
	
	private final static Logger logger = Logger.getLogger(TrackEditorExtensionPoint.class);
	
	private static final TrackEditorExtensionPoint instance = new TrackEditorExtensionPoint();
	private static Map<String, TrackEditorConfig> trackDialogConfigs = new HashMap<String, TrackEditorConfig>();
	
    public final static String X_POINT = "com.glory.mes.wip.track.dialogs";
    
    public final static String E_DIALOG = "dialog";
    public final static String A_TYPE = "type";
    
    public final static String E_HEADER = "header";
    public final static String E_BODY = "body";
    public final static String A_XGRID = "xgrid";
    public final static String A_HEIGTHHINT = "heighthint";
    
    public final static String E_HEADERFORM = "headerform";
    public final static String E_BODYFORM = "bodyform";
    public final static String A_FORMID = "id";
    public final static String A_ROWSPAN = "rowspan";
    public final static String A_COLSPAN = "colspan";
    public final static String A_WIDTHHINT = "widthhint";
    public final static String A_FONTNAME = "fontname";
    public final static String A_FONTHIGHT = "fonthight";
    
    public static TrackEditorExtensionPoint getInstance() {
    	return instance;
    }
    
	public static TrackEditorConfig getTrackEditor(String type) {
		return trackDialogConfigs.get(type);
	}

	static {
		IExtensionPoint extensionPoint = Platform.getExtensionRegistry().getExtensionPoint(X_POINT);
		IExtension[] extensions = extensionPoint.getExtensions();
		for (int i = 0; i < extensions.length; i++) {
			IConfigurationElement[] configElements = extensions[i].getConfigurationElements();
			for (int j = 0; j < configElements.length; j++) {
				try {
					if (E_DIALOG.equals(configElements[j].getName())) {
						String type = configElements[j].getAttribute(A_TYPE);
						TrackEditorConfig config = new TrackEditorConfig();
						
						for (IConfigurationElement element : configElements[j].getChildren()) {
							if (E_HEADER.equals(element.getName())) {
								String xgrid = element.getAttribute(A_XGRID);
								if (!StringUtil.isEmpty(xgrid)) {
									try {
										config.setHeaderYGrid(Integer.parseInt(xgrid));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String heigthHint = element.getAttribute(A_HEIGTHHINT);
								if (!StringUtil.isEmpty(heigthHint)) {
									try {
										config.setHeaderHeigthHint(Integer.parseInt(heigthHint));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
							} else if (E_BODY.equals(element.getName())) {
								String xgrid = element.getAttribute(A_XGRID);
								if (!StringUtil.isEmpty(xgrid)) {
									try {
										config.setBodyYGrid(Integer.parseInt(xgrid));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String heigthHint = element.getAttribute(A_HEIGTHHINT);
								if (!StringUtil.isEmpty(heigthHint)) {
									try {
										config.setBodyHeigthHint(Integer.parseInt(heigthHint));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
							} else if (E_HEADERFORM.equals(element.getName())) {
								TrackFormConfig formConfig = new TrackFormConfig();
								String id = element.getAttribute(A_FORMID);
								formConfig.setId(id);
								
								String rowSpan = element.getAttribute(A_ROWSPAN);
								if (!StringUtil.isEmpty(rowSpan)) {
									try {
										formConfig.setRowSpan(Integer.parseInt(rowSpan));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String colSpan = element.getAttribute(A_COLSPAN);
								if (!StringUtil.isEmpty(colSpan)) {
									try {
										formConfig.setColSpan(Integer.parseInt(colSpan));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String heigthHint = element.getAttribute(A_HEIGTHHINT);
								if (!StringUtil.isEmpty(heigthHint)) {
									try {
										formConfig.setHeightHint(Integer.parseInt(heigthHint));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String widthHint = element.getAttribute(A_WIDTHHINT);
								if (!StringUtil.isEmpty(widthHint)) {
									try {
										formConfig.setWidthhint(Integer.parseInt(widthHint));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String fontname = element.getAttribute(A_FONTNAME);
								if (!StringUtil.isEmpty(fontname)) {
									try {
										formConfig.setFontName(fontname);
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String fonthight = element.getAttribute(A_FONTHIGHT);
								if (!StringUtil.isEmpty(fonthight)) {
									try {
										formConfig.setFontHight(Integer.parseInt(fonthight));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								config.getHeaderFormConfigs().add(formConfig);
							} else if (E_BODYFORM.equals(element.getName())) {
								TrackFormConfig formConfig = new TrackFormConfig();
								String id = element.getAttribute(A_FORMID);
								formConfig.setId(id);
								
								String rowSpan = element.getAttribute(A_ROWSPAN);
								if (!StringUtil.isEmpty(rowSpan)) {
									try {
										formConfig.setRowSpan(Integer.parseInt(rowSpan));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String colSpan = element.getAttribute(A_COLSPAN);
								if (!StringUtil.isEmpty(colSpan)) {
									try {
										formConfig.setColSpan(Integer.parseInt(colSpan));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String heigthHint = element.getAttribute(A_HEIGTHHINT);
								if (!StringUtil.isEmpty(heigthHint)) {
									try {
										formConfig.setHeightHint(Integer.parseInt(heigthHint));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String widthHint = element.getAttribute(A_WIDTHHINT);
								if (!StringUtil.isEmpty(widthHint)) {
									try {
										formConfig.setWidthhint(Integer.parseInt(widthHint));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String fontname = element.getAttribute(A_FONTNAME);
								if (!StringUtil.isEmpty(fontname)) {
									try {
										formConfig.setFontName(fontname);
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								String fonthight = element.getAttribute(A_FONTHIGHT);
								if (!StringUtil.isEmpty(fonthight)) {
									try {
										formConfig.setFontHight(Integer.parseInt(fonthight));
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								config.getBodyFormConfigs().add(formConfig);
							}
						}
						config.validate();
						trackDialogConfigs.put(type, config);
					}
				} catch (Exception e){
					logger.error("EdcFormExtensionPoint : init ", e);
				}
			}
		}			
	}
	
	
   
}
