package com.glory.mes.wip.lot.run.byeqp.sorting;

import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.lot.run.byeqp.extensionpoint.IByEqpAction;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpManagerContext;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpManagerDefault;

public class SortingJobByEqpManagerDefault extends ByEqpManagerDefault {
	
	private SortingJobAction action;
	
	@Override
	protected void eqpSelectionChangeAdapter(Object obj) {
		Event event = (Event) obj;
		Equipment equipment = (Equipment) event.getProperty(GlcEvent.PROPERTY_DATA);
		getContext().setCurrentEqp(equipment);
		
		eqpSelectionChanged();
	}
	
	@Override
	protected void init() {
		itemEqpInfo = (ToolItem) form.getButtonByControl(null, ByEqpManagerContext.BUTTON_EQPINFO);
		fieldEqpTree = form.getFieldByControlId(ByEqpManagerContext.FIELD_EQPTREE, CustomField.class);
		fieldLotForm = form.getFieldByControlId(ByEqpManagerContext.FIELD_LOTFORM, GlcFormField.class);
		fieldEqp = form.getFieldByControlId(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  ByEqpManagerContext.FIELD_EQUIPMENT_ID, TextField.class);
		fieldLot = form.getFieldByControlId(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  ByEqpManagerContext.FIELD_LOT_ID, TextField.class);
		fieldCarrier = form.getFieldByControlId(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  ByEqpManagerContext.FIELD_CARRIER_ID, TextField.class);	
		
		IByEqpAction byEqpAction = getContext().getCurrentAction();
		if (byEqpAction != null) {
			byEqpAction.initExtend(getContext().getEqpEditor(), form);
			byEqpAction.subscribeExtend(getContext().getEventBroker(), form);
			byEqpAction.setModelService(modelService);
			byEqpAction.setPartService(partService);
		}
	}
	
	@Override
	protected void clear() {
	}
	
	
	/**
	 * ˢ�������б�
	 * index 1����ˢ��
	 * index 2��ֻˢ��waiting
	 * index 3��ֻˢ��running
	 * @param index
	 */
	@Override
	public void refresh(int index) {
		if (action != null) {
			action.init();
		}
	}
	
	@Override
	protected void addKeyListener() {
	}

	public SortingJobAction getAction() {
		return action;
	}

	public void setAction(SortingJobAction action) {
		this.action = action;
	}

}
