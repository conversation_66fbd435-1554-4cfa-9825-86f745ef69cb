package com.glory.mes.wip.lot.run.manul.movenext;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.edc.extensionpoints.EdcEvent;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.his.LotHis;
import com.glory.mes.wip.lot.history.EdcHisDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class LotHisEdcDialog extends GlcBaseDialog{

    private static final Logger     logger       = Logger.getLogger(LotHisEdcDialog.class);

    private static final String     FIELD_LOTHIS = "LotHis";
    private static final String     BUTTON_SEARCH = "edcDataHisQuery";
    protected EntityFormField   lotInfoFormField, lotReleaseActionFormField;
    protected ListTableManagerField lotHisListTableManagerField;
    protected Lot               lot;
	
    public LotHisEdcDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot){
		super(adFormName, authority, eventBroker);
        this.lot = lot;
        this.setBlockOnOpen(false);
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
        try {
            lotHisListTableManagerField = form.getFieldByControlId(FIELD_LOTHIS, ListTableManagerField.class);

            ADManager adManager = Framework.getService(ADManager.class);
            
            List<LotHis> lotHisList = adManager.getEntityList(Env.getOrgRrn(), LotHis.class, Integer.MAX_VALUE,
                                    " lotRrn=" + lot.getObjectRrn() + " and transType='"
                                                                   + LotStateMachine.TRANS_EDC + "'",
                                    null);
            
            lotHisListTableManagerField.getListTableManager().setInput(lotHisList);

            subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SEARCH), this::detailQueryAdapter);

            
        } catch (Exception e) {
            logger.error("LotHisEdcDialog : Init tablelist", e);
            e.printStackTrace();
        }
	}
	
    protected void detailQueryAdapter(Object object) {
        try {
            Object selectedObject = lotHisListTableManagerField.getListTableManager().getSelectedObject();

            if (selectedObject == null) {
                return;
            }

            LotHis lotHis = (LotHis) selectedObject;

            ADManager adManager = Framework.getService(ADManager.class);
            String condition = " hisSeq = '" + lotHis.getHisSeq() + "'";
            List<EdcData> datas = adManager.getEntityList(Env.getOrgRrn(), EdcData.class, Env.getMaxResult(), condition,
                                                          "");
            EdcSetCurrent edc = new EdcSetCurrent();
            edc.setHistorySeq(lotHis.getHisSeq());
            edc.setItemSetRrn(datas.get(0).getEdcSetRrn());
            edc.setEdcFlag(EdcSetCurrent.FLAG_TEMP);

            EdcEvent event = new EdcEvent();
            event.setLot(lot);

            EdcHisDialog dialog = new EdcHisDialog(UI.getActiveShell(), edc, event, lotHis);
            dialog.open();
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
        }

    }

}
