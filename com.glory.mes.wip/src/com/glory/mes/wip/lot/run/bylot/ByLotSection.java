package com.glory.mes.wip.lot.run.bylot;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.edc.EdcEntry;
import com.glory.edc.client.EDCManager;
import com.glory.edc.collection.EdcSetCurrentComponentDialog;
import com.glory.edc.collection.EdcSetCurrentDialog;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcAQLSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.extensionpoints.WizardPageExtensionPoint;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.core.chain.ChainContext;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.lot.detail.DetailFutureStepForm;
import com.glory.mes.wip.lot.detail.DetailParameterForm;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.lot.run.abort.AbortDialog;
import com.glory.mes.wip.lot.run.abort.AbortWizard;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInDialog;
import com.glory.mes.wip.lot.run.trackin.TrackInWizard;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveContext;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveDialog;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveWizard;
import com.glory.mes.wip.lot.run.trackmove.modal.TrackMoveModalDialog;
import com.glory.mes.wip.lot.run.trackmove.modal.TrackMoveModalWizard;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.track.model.InContext;
import com.glory.framework.core.exception.ExceptionBundle;

public class ByLotSection extends LotSection {

	private static final Logger logger = Logger.getLogger(EntitySection.class);
	
	private static final String KEY_ABORT = "abort";

	protected boolean trackMoveFlag = false;
	protected Step currentStep; 
	
	protected ToolItem itemTrackIn;
	protected ToolItem itemTrackOut;
	protected ToolItem itemTrackMove;
	protected ToolItem itemDcop;
	protected AuthorityToolItem itemAbort;	
	protected ToolItem itemTrackTHold;
	
	protected IEventBroker eventBroker;

	public ByLotSection(ADTable table) {
		super(table,true);
	}
	
	public ByLotSection(ADTable table ,IEventBroker eventBroker) {
		super(table,true);
		this.eventBroker = eventBroker;
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		initAdObject();
		registerAccelerator();
	}

	@Override
	public Lot searchLot(String lotId) {
		try {
			//���߱�������
			Lot lot = LotProviderEntry.getLotByLine(lotId);
			if (lot != null) {
				loadCurrentStep(lot);
				lotChanged(lot);
			}
			return lot;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	protected void loadCurrentStep(Lot lot) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lot.getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			setCurrentStep(step);
		} catch (Exception e) {
			logger.warn("LotSection loadCurrentStep(): Step isn' t exsited!");
		}
	}
	
	@Override
	protected void refreshAdapter() {
		super.refreshAdapter();
		if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
			Lot lot = (Lot) getAdObject();
			loadCurrentStep(lot);
		}
	}
	
	
	@Override
	protected EntityForm getForm(Composite composite, ADTab tab) {
		if ("Parameters".equals(tab.getName())) {
			return new DetailParameterForm(composite, SWT.NONE, tab, form.getMessageManager());
		}else if ("LotTrackFutureStep".equals(tab.getName())) {
			return new DetailFutureStepForm(composite, SWT.NONE, tab, form.getMessageManager(), eventBroker);
		} else {
			return new EntityForm(composite, SWT.NONE, tab, form.getMessageManager());
		}
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemTrackMove(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemTrackIn(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDcop(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		
		createToolItemTrackOut(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);		
		createToolItemAbort(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
//		createToolItemTrackTHold(tBar);
//		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);

	}

	protected void createToolItemTrackTHold(ToolBar tBar) {
		itemTrackTHold = new ToolItem(tBar, SWT.PUSH);
		itemTrackTHold.setText("TrackTHold");
		itemTrackTHold.setImage(SWTResourceCache.getImage("trackin"));
		itemTrackTHold.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				trackTHoldAdapter();
			}
		});
	}

	protected void trackTHoldAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			TrackOutContext context = new TrackOutContext();
			context.setTrackOutType(TrackOutContext.TRACK_OUT_BYLOT);

			Lot lot = (Lot)getAdObject();
			List<Lot> lots = getRunningLotList(lot);
			context.setLots(lots);
			context.setStep(getCurrentStep());
			
			FlowWizard wizard = WizardPageExtensionPoint.getWizardRegistry().get("TrackTHold");
			if (wizard instanceof TrackOutWizard) {
				((TrackOutWizard) wizard).setContext(context);
			}
			TrackOutDialog dialog = new TrackOutDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == TrackOutDialog.FIN) {
				if (((TrackOutWizard) wizard).getContext().getOutLots().size() > 0) {
					Lot trackOutLot = ((TrackOutWizard) wizard).getContext().getOutLots().get(0);
					if (trackOutLot != null){
						if (txtLot != null) {
							txtLot.setText(trackOutLot.getLotId());
						}
						setAdObject(trackOutLot);
					}
				}
				refreshAdapter();
				//ֻ����TrackOut��TrackMoveʱ����Ҫ�������Step�仯
				lotChanged((Lot)getAdObject());
				UI.showInfo(Message.getString("wip.trackout_success"));
			}
			if (txtLot != null) {
				txtLot.selectAll();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void createToolItemTrackIn(ToolBar tBar) {
		itemTrackIn = new ToolItem(tBar, SWT.PUSH);
		itemTrackIn.setText(Message.getString("wip.trackin") + "(F3)");
		itemTrackIn.setImage(SWTResourceCache.getImage("trackin"));
		itemTrackIn.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				trackInAdapter();
			}
		});
	}

	protected void createToolItemTrackOut(ToolBar tBar) {
		itemTrackOut = new ToolItem(tBar, SWT.PUSH);
		itemTrackOut.setText(Message.getString("wip.trackout") + "(F5)");
		itemTrackOut.setImage(SWTResourceCache.getImage("trackout"));
		itemTrackOut.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				trackOutAdapter();
			}
		});
	}

	protected void createToolItemTrackMove(ToolBar tBar) {
		itemTrackMove = new ToolItem(tBar, SWT.PUSH);
		itemTrackMove.setText(Message.getString("wip.trackmove") + "(F2)");
		itemTrackMove.setImage(SWTResourceCache.getImage("trackmove"));
		itemTrackMove.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				trackMoveAdapter();
			}
		});
	}
	
	protected void createToolItemDcop(ToolBar tBar) {
		itemDcop = new ToolItem(tBar, SWT.PUSH);
		itemDcop.setText(Message.getString("wip.dcop") + "(F4)");
		itemDcop.setImage(SWTResourceCache.getImage("dcop"));
		itemDcop.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				dcopAdapter();
			}
		});
	}

//	protected void createToolItemAutoDcop(ToolBar tBar) {
//		itemAutoDcop = new ToolItem(tBar, SWT.PUSH);
//		itemAutoDcop.setText(Message.getString("wip.autodcop"));
//		itemAutoDcop.setImage(SWTResourceCache.getImage("dcop"));
//		itemAutoDcop.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				autoDcopAdapter();
//			}
//		});
//	}
	
	protected void createToolItemAbort(ToolBar tBar) {
		itemAbort = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_ABORT);
		itemAbort.setText(Message.getString("wip.abort") + "(F6)");
		itemAbort.setImage(SWTResourceCache.getImage("abort"));
		itemAbort.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				abortAdapter();
			}
		});
	}

	protected void createToolItemRefresh(ToolBar tBar) {
		itemRefresh = new ToolItem(tBar, SWT.PUSH);
		itemRefresh.setText(Message.getString(ExceptionBundle.bundle.CommonRefresh()) + "(F8)");
		itemRefresh.setImage(SWTResourceCache.getImage("refresh"));
		itemRefresh.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				refreshAdapter();
			}
		});
	}
	
	protected void trackInAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			TrackInContext context = new TrackInContext();
			context.setTrackInType(TrackInContext.TRACK_IN_BYLOT);
			List<Lot> lots = new ArrayList<Lot>();
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
	        if (MesCfMod.isUseDurable(Env.getOrgRrn(), sysParamManager)) {
	        	List<Object> objects = carrierLotComposite.getLotTableManager().getCheckedObject();
				if (objects == null || objects.size() == 0) {
					UI.showError(Message.getString("wip.please_select_lot_first"));
					return;
				}
				List<Lot> checkLots = objects.stream().map(o -> ((Lot)o)).collect(Collectors.toList());
				lots.addAll(checkLots);
	        } else {
	        	Lot lot = (Lot) getAdObject();
	        	lots.add(lot);
	        }

			context.setLots(lots);
			Step step = getCurrentStep();
			context.setStep(step);
			
			FlowWizard wizard = getTrackInWizard(context, step.getTrackInFlow());
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			inContext.setMultiEqp(step.getIsMultiEqp());
			
			LotManager lotManager = Framework.getService(LotManager.class);			
			ChainContext wipContext = lotManager.checkTrackInConstraint(inContext
					, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}

			ChainContext checkAutoFutureMergeContext = lotManager.checkAutoFutureMergeConstraint(inContext
					, Env.getSessionContext());
			if (checkAutoFutureMergeContext.getReturnMessage() != null
					&& checkAutoFutureMergeContext.getReturnMessage().trim().length() > 0) {
				UI.showError(checkAutoFutureMergeContext.getReturnMessage());
			}
			if (checkAutoFutureMergeContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}

			TrackInDialog dialog = new TrackInDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if ((result == Dialog.OK || result == TrackInDialog.FIN) 
					&& context.getReturnCode() == TrackInContext.OK_ID) {
				Lot trackInLot = ((TrackInWizard) wizard).getContext().getTrackInLot();
				if (trackInLot != null) {
					if (txtLot != null) {
						txtLot.setText(trackInLot.getLotId());
					}
					setAdObject(trackInLot);
				}
				refreshAdapter();
			}
			if (txtLot != null) {
				txtLot.selectAll();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	protected TrackInWizard getTrackInWizard(TrackInContext context, String wizardName) {
		context.setOperator1(Env.getUserName());
		TrackInWizard wizard = (TrackInWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setContext(context);
		return wizard;
	}
	
	protected void trackOutAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			TrackOutContext context = new TrackOutContext();
			context.setTrackOutType(TrackOutContext.TRACK_OUT_BYLOT);

			Lot lot = (Lot)getAdObject();
			List<Lot> lots = getRunningLotList(lot);
			context.setLots(lots);

			Step step = getCurrentStep();
			context.setStep(step);
			
			FlowWizard wizard = getTrackOutWizard(context, step.getTrackOutFlow());
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			inContext.setMultiEqp(step.getIsMultiEqp());
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkTrackOutConstraint(inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}

			TrackOutDialog dialog = new TrackOutDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == TrackOutDialog.FIN) {
				if (((TrackOutWizard) wizard).getContext().getOutLots().size() > 0) {
					Lot trackOutLot = ((TrackOutWizard) wizard).getContext().getOutLots().get(0);
					if (trackOutLot != null){
						if (txtLot != null) {
							txtLot.setText(trackOutLot.getLotId());
						}
						setAdObject(trackOutLot);
					}
				}
				refreshAdapter();
				//ֻ����TrackOut��TrackMoveʱ����Ҫ�������Step�仯
				lotChanged((Lot)getAdObject());
				UI.showInfo(Message.getString("wip.trackout_success"));
			}
			if (txtLot != null) {
				txtLot.selectAll();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected TrackOutWizard getTrackOutWizard(TrackOutContext context, String wizardName) {
		context.setOperator1(Env.getUserName());
		TrackOutWizard wizard = (TrackOutWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setContext(context);
		return wizard;
	}
	
	protected List<Lot> getRunningLotList(Lot lot) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		List<Lot> lotList = new ArrayList<Lot>();
		if (lot.getBatchId() != null) {
			List<Lot> clotList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
			for (Lot clot : clotList) {
				clot = lotManager.getRunningLot(clot.getObjectRrn());
				
				if(LotStateMachine.STATE_RUN.equals(clot.getState())){
					lotList.add(clot);
				}
				
			}
		} else {
			lot = lotManager.getRunningLot(lot.getObjectRrn());
			lotList.add(lot);
		}
		return lotList;
	}
	
	protected void trackMoveAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			TrackMoveContext context = new TrackMoveContext();
			context.setTrackMoveType(TrackMoveContext.TRACK_MOVE_DEFAULT);
			List<Lot> lots = new ArrayList<Lot>();
			Lot lot = (Lot) getAdObject();
			lots.add(lot);
			context.setLots(lots);

			Step step = getCurrentStep();
			context.setStep(step);
			
			FlowWizard wizard = getTrackMoveWizard(context, step.getMoveNextFlow());
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkTrackInConstraint(
					inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}

			if (wizard instanceof TrackMoveWizard) {
				TrackMoveDialog dialog = new TrackMoveDialog(UI.getActiveShell(), wizard);
				int result = dialog.open();
				if (result == Dialog.OK || result == TrackMoveDialog.FIN) {
					refreshAdapter();
					//ֻ����TrackOut��TrackMoveʱ����Ҫ�������Step�仯
					lotChanged((Lot)getAdObject());
					UI.showInfo(Message.getString("wip.trackmove_success"));
				}
			} else if (wizard instanceof TrackMoveModalWizard) {
				//�Թ̶����ڵ���ʽ��վ
				((TrackMoveContext)((TrackMoveModalWizard)wizard).getContext()).setTrackMoveType(TrackMoveContext.TRACK_MOVE_MODAL);
				TrackMoveModalDialog dialog = new TrackMoveModalDialog(UI.getActiveShell(), wizard);
				dialog.open();
				refreshAdapter();
			}
			
			if (txtLot != null) {
				txtLot.selectAll();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected TrackOutWizard getTrackMoveWizard(TrackMoveContext context, String wizardName) {
		context.setOperator1(Env.getUserName());
		TrackOutWizard wizard = (TrackOutWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setContext(context);
		return wizard;
	}
	
	protected List<EdcSetCurrent> getEdcSetCurrent() {
		try {
			Lot lot = (Lot) getAdObject();
			EDCManager edcManager = Framework.getService(EDCManager.class);
			return edcManager.getItemSetCurrents(Env.getOrgRrn(), lot.getBatchId(), lot.getObjectRrn(), null);
		} catch (ClientException e) {
			return null;
		} catch (Exception e) {
			return null;
		}
	}
	
	protected void dcopAdapter() {
		try {
			List<EdcSetCurrent> currents = getEdcSetCurrent();
			if (currents != null && currents.size() > 0) {
			    for (EdcSetCurrent current : currents) {
                    AbstractEdcSet itemEdcSet = new AbstractEdcSet();
                    itemEdcSet.setObjectRrn(current.getItemSetRrn());
                    ADManager adManager = Framework.getService(ADManager.class);
                    itemEdcSet = (AbstractEdcSet) adManager.getEntity(itemEdcSet);
                    if (itemEdcSet instanceof EdcAQLSet) {
                        EdcAQLSet edcAQLSet = (EdcAQLSet)itemEdcSet;
                        EDCManager edcManager = Framework.getService(EDCManager.class);
                        edcManager.createAqlSamplingPlanInstance(edcAQLSet, (Lot)getAdObject());
                    }
                }
				if (currents.size() == 1 
						&& !EdcSetCurrent.FLAG_DONE.equals(currents.get(0).getEdcFlag())
						&& !EdcSetCurrent.FLAG_PASS.equals(currents.get(0).getEdcFlag())){
					//��ֻ��һ��EDCʱ����δ���ʱ
					int result = EdcEntry.open(EdcData.EDCFROM_LOT, currents.get(0), null, (Lot)getAdObject());
					if (result == Dialog.OK) {
						refreshAdapter();
					}
				} else {
					boolean flag = false;
					for (EdcSetCurrent current : currents) {
						if (!StringUtil.isEmpty(current.getComponentUnitId())) {
							flag = true;
							break;
						}
					}
					EdcSetCurrentDialog edcSetCurrentDialog = null;
					if (flag) {
						edcSetCurrentDialog = new EdcSetCurrentComponentDialog(UI.getActiveShell(), form, (Lot)getAdObject(), currents);
						
					} else {
						edcSetCurrentDialog = new EdcSetCurrentDialog(UI.getActiveShell(), form, (Lot)getAdObject(), currents);	
					}
					edcSetCurrentDialog.open();
					if (edcSetCurrentDialog.getReturnCode() == Dialog.OK) {
						refreshAdapter();
					}		
				}
			} else {
				UI.showWarning(Message.getString("edc.alert_message"), Message
						.getString("edc.alert_message_title"));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	protected void autoDcopAdapter() {
		try {
			dcopAdapter();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected void abortAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			InContext context = new InContext();

			Lot lot = (Lot)getAdObject();
			List<Lot> lots = getRunningLotList(lot);
			context.setLots(lots);

			Step step = getCurrentStep();
			context.setCurrentStep(step);
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkAbortConstraint(
					context, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
			
			FlowWizard wizard = getAbortWizard(context, step.getAbortFlow());

			AbortDialog dialog = new AbortDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == AbortDialog.FIN) {
				refreshAdapter();
			}
			if (txtLot != null) {
				txtLot.selectAll();
			}
		} catch (Exception e) {
			logger.error("Error at RunningLotSection : abortAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected AbortWizard getAbortWizard(InContext context, String wizardName) {
		AbortWizard wizard = (AbortWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setContext(context);
		return wizard;
	}
	
	public void lotChanged(Lot lot) {
		try {
			if (lot != null && LotStateMachine.STATE_WAIT.equals(lot.getState())) {
				//ֻ����Waitʱ�ż��������TrackIn����TrackMove
				Step step = getCurrentStep();
				if (step.getIsMoveNext()) {
					trackMoveFlag = true;
				} else {
					trackMoveFlag = false;
				}
				if (trackMoveFlag) {
					if (itemTrackMove != null) {
						itemTrackMove.setEnabled(true);
					}
					if (itemTrackIn != null) {
						itemTrackIn.setEnabled(false);
					}
				} else {
					if (itemTrackMove != null) {
						itemTrackMove.setEnabled(false);
					}
					if (itemTrackIn != null) {
						itemTrackIn.setEnabled(true);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public void statusChanged(String newStatus) {
		if (LotStateMachine.STATE_RUN.equals(newStatus)) {
			if (itemTrackIn != null) {
				itemTrackIn.setEnabled(false);
				if (getCurrentStep().getIsMultiEqp()) {
					itemTrackIn.setEnabled(true);
				}
			}
			
			//������ݲɼ��Ƿ���ڡ��Ƿ��Ѿ����
			boolean edcCompFlag = true;
			List<EdcSetCurrent> currents = getEdcSetCurrent();
			if (currents != null && currents.size() > 0){
				if (itemDcop != null) {
					itemDcop.setEnabled(true);
					//itemAutoDcop.setEnabled(true);
				}
				for (EdcSetCurrent current : currents) {
					if (!EdcSetCurrent.FLAG_DONE.equals(current.getEdcFlag())
							&& !EdcSetCurrent.FLAG_PASS.equals(current.getEdcFlag())) {
						//ֻҪ��һ�����ݲɼ�δ���
						edcCompFlag = false;
						break;
					}
				}
			} else {
				if (itemDcop != null) {
					itemDcop.setEnabled(false);
					//itemAutoDcop.setEnabled(false);
				}
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(edcCompFlag);
			}
			if (itemAbort != null) {
				itemAbort.setEnabled(true);
			}
			if (itemTrackMove != null) {
				itemTrackMove.setEnabled(false);
			}
		} else if (LotStateMachine.STATE_WAIT.equals(newStatus)) {
			if (trackMoveFlag) {
				if (itemTrackMove != null) {
					itemTrackMove.setEnabled(true);
				}
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(false);
				}
			} else {
				if (itemTrackMove != null) {
					itemTrackMove.setEnabled(false);
				}
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(true);
				}
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(false);
			}
			if (itemDcop != null) {
				itemDcop.setEnabled(false);
				//itemAutoDcop.setEnabled(false);
			}
			if (itemAbort != null) {
				itemAbort.setEnabled(false);
			}
		} else {
			if (itemTrackIn != null) {
				itemTrackIn.setEnabled(false);
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(false);
			}
			if (itemDcop != null) {
				itemDcop.setEnabled(false);
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(false);
			}
			if (itemTrackMove != null) {
				itemTrackMove.setEnabled(false);
			}
			if (itemAbort != null) {
				itemAbort.setEnabled(false);
			}
		}
		Lot lot = (Lot)getAdObject();
		if (lot != null && Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
			//run Hold״̬���Գ�վ
			if(itemTrackIn != null) {
				itemTrackIn.setEnabled(false);
			}
		}
	}

	protected void registerAccelerator() {
		this.txtLot.addListener(SWT.KeyDown, acceleratorListener());
	}

	protected Listener acceleratorListener() {
		return new Listener() {
			public void handleEvent(Event e) {
				if (getAdObject() != null) {
					switch (e.keyCode) {
					case SWT.F2:
						if (itemTrackMove != null && itemTrackMove.isEnabled()) {
							trackMoveAdapter();
						}
						break;
					case SWT.F3:
						if (itemTrackIn != null && itemTrackIn.isEnabled()) {
							trackInAdapter();
						}
						break;
					case SWT.F4:
						if (itemDcop != null && itemDcop.isEnabled()) {
							dcopAdapter();
						}
						break;
					case SWT.F5:
						if (itemTrackOut != null && itemTrackOut.isEnabled()) {
							trackOutAdapter();
						}
						break;
					case SWT.F6:
						if (itemAbort != null && itemAbort.isEnabled()) {
							abortAdapter();
						}
						break;
					case SWT.F8:
						if (itemRefresh != null && itemRefresh.isEnabled()) {
							refreshAdapter();
						}
						break;
					}
				}
			}
		};
	}

	public Step getCurrentStep() {
		if (currentStep == null) {
			Lot lot = (Lot) getAdObject();
			if (lot != null && lot.getObjectRrn() != null) {
				loadCurrentStep(lot);
			}
		}
		return currentStep;
	}

	public void setCurrentStep(Step currentStep) {
		this.currentStep = currentStep;
	}
	
}
