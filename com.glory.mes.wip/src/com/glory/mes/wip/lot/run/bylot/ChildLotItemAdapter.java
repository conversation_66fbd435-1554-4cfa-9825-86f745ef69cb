package com.glory.mes.wip.lot.run.bylot;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.wip.model.Lot;

public class ChildLotItemAdapter extends List<PERSON>temAdapter<Lot> {
	@Override
	public Color getBackground(Object element, String id) {
		Lot lot = (Lot) element;
		//������ͣ���
    	if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
    		Color color = new Color(Display.getCurrent(), 255, 111, 111);
    		return color;
    	} else {
    		return null;
    	}
	}
}
