package com.glory.mes.wip.lot.run.byeqp.glc;

import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.application.command.OpenEditorCommand;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.wip.custom.depend.ByEqpConsole;
import com.glory.mes.wip.model.Lot;

public abstract class ByEqpManager {
	
	boolean isRegistered = false;
	
	protected ByEqpManagerContext context;
	
	protected IEventBroker eventBroker;
	protected ByEqpEditor byEqpEditor;
	protected GlcForm form;
	protected EPartService partService;
	protected EModelService modelService;
	protected ByEqpConsole console;
	
	protected ToolItem itemEqpInfo;
	protected ToolItem itemTrackIn;
	protected ToolItem itemDcop;
	protected ToolItem itemTrackOut;
	protected ToolItem itemAbort;
	protected ToolItem itemRunRefresh;
	protected ToolItem itemPrepare;
	
	protected GlcFormField fieldLotForm;
	protected CustomField fieldEqpTree;
	protected CustomField fieldRunning;
	protected CustomField fieldWaitting;
	protected TextField fieldEqp;
	protected TextField fieldLot;
	protected TextField fieldCarrier;
	protected BooleanField dispatchField;
	
	protected abstract void byEqpFormActiom();
	
	protected abstract void init();
	
	protected abstract void prepareAdapter(Object obj);
	
	protected abstract void equipmentEnterPressed(Object obj);
	
	protected abstract void carrierEnterPressed(Object obj);
	
	protected abstract void lotEnterPressed(Object obj);
	
	protected abstract void searchLot(Lot lot) throws Exception;
	
	protected abstract void runningSelectionAdaptor(Object obj);
	
	protected abstract void waitingSelectionAdaptor(Object obj);
	
	protected abstract void lotStatusChanged(String state, String holdState);
	
	protected abstract void eqpSelectionChangeAdapter(Object obj);
	
	protected abstract void eqpSelectionChanged();
	
	protected abstract void eqpInfoAdapter(Object obj);
	
	protected abstract void eqpRefreshAdapter(Object obj);
	
	protected abstract void trackInAdapter(Object obj);
	
	protected abstract void abortAdapter(Object obj);
	
	protected abstract void docpAdapter(Object obj);
	
	protected abstract void trackOutAdapter(Object obj);
	
	protected abstract void refresh(int index);
	
	protected abstract void runRefreshAdapter(Object obj);
	
	protected abstract void waitRefreshAdapter(Object obj);
	
	protected abstract void addKeyListener();
	
	protected abstract boolean switchView();
	
	protected abstract void clear();
	
	protected abstract boolean isRTDAvailable();
	
	public ByEqpManager() {
		super();
	}
	
	public boolean isRegistered() {
		return isRegistered;
	}

	public void setRegistered(boolean isRegistered) {
		this.isRegistered = isRegistered;
	}

	public ByEqpManager(ByEqpManagerContext context) {
		this.context = context;
	}
	
	protected void lotListDoubleClickAdaptor(Object obj) {
		try {
			Event event = (Event) obj;
			Object columnValue = event.getProperty(GlcEvent.PROPERTY_TABLE_COLUMN_VALUE);
			String columnName = (String) event.getProperty(GlcEvent.PROPERTY_TABLE_COLUMN_NAME);
			if ("lotId".equals(columnName)) {
				if (columnValue != null) {
					String lotId = DBUtil.toString(columnValue);
					if (!StringUtil.isEmpty(lotId)) {
						ADManager adManager = Framework.getService(ADManager.class);
						List<ADAuthority> authority = adManager.getEntityList(
								Env.getOrgRrn(), ADAuthority.class, Env.getMaxResult(), "name = '" + "Wip.LotDetail" + "'", "");
						if (authority.size() != 1) {
							return;
						}
						authority.get(0).setAttribute1(lotId);
						OpenEditorCommand.open(authority.get(0), context.getPartService(), context.getModelService());
					}
				}
			}
			
			if ("durable".equals(columnName)) {
				if (columnValue != null) {
					String lotId = DBUtil.toString(columnValue);
					if (!StringUtil.isEmpty(lotId)) {
						ADManager adManager = Framework.getService(ADManager.class);
						List<ADAuthority> authority = adManager.getEntityList(
								Env.getOrgRrn(), ADAuthority.class, Env.getMaxResult(), "name = '" + "MM.CarrierActionQuery" + "'", "");
						if (authority.size() != 1) {
							return;
						}
						authority.get(0).setAttribute1(lotId);
						OpenEditorCommand.open(authority.get(0), context.getPartService(), context.getModelService());
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void setTextValue(String equipmentId, String lotId, String carrierId) {
		fieldEqp.setText(equipmentId);
		fieldLot.setText(lotId);
		fieldCarrier.setText(carrierId);
	}
	
	public ByEqpManagerContext getContext() {
		return context;
	}

	public void setContext(ByEqpManagerContext context) {
		this.context = context;
	}

	public CustomField getFieldWaitting() {
		return fieldWaitting;
	}

	public void setFieldWaitting(CustomField fieldWaitting) {
		this.fieldWaitting = fieldWaitting;
	}

	

}
