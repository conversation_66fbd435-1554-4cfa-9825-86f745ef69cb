package com.glory.mes.wip.lot.run.bylot.glc;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotHistoryManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.his.LotHis;
import com.glory.mes.wip.lot.action.LotActionDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.google.common.collect.Lists;

public class LotChangeCommentDialog extends LotActionDialog{

    private static final Logger logger = Logger.getLogger(LotChangeCommentDialog.class);

    private static final String FIELD_LOT_INFO = "lotInfo";
    private static final String FIELD_LOT_COMMENT = "lotComment";
    private static final String FIELD_LOT_COMMENT_HIS = "lotCommentHis";
    protected ListTableManagerField lotCommentHis;
    protected EntityFormField   lotInfoField;
    protected Lot lot;
	protected Event event;
	protected TextField lotCommentField;
	
    private static int DIALOG_WIDTH = 500;
	private static int DIALOG_HEIGHT = 300;
    
    public LotChangeCommentDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot, Event event){
		super(adFormName, authority, eventBroker);
        this.lot = lot;
        this.event = event;
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
        try {
        	LotHistoryManager lotHistoryManager =  Framework.getService(LotHistoryManager.class);
        	List<LotHis> lotHis = lotHistoryManager.getLotHisByLotId(lot.getOrgRrn(), lot.getLotId(), "transType = '" + LotStateMachine.TRANS_ADDCOMMENT + "'");
        	lotInfoField = form.getFieldByControlId(FIELD_LOT_INFO, EntityFormField.class);
        	lotCommentField = lotInfoField.getFieldByControlId(FIELD_LOT_COMMENT, TextField.class);	
        	lotCommentHis = form.getFieldByControlId(FIELD_LOT_COMMENT_HIS, ListTableManagerField.class);
        	if(CollectionUtils.isNotEmpty(lotHis)) {
        		lotCommentHis.getListTableManager().setInput(lotHis);
        	}else {
        		lotCommentHis.getListTableManager().setInput(null);
			}
        	
        	
        	lotInfoField.setValue(lot);
        	lotInfoField.refresh();
        	lotCommentField.setEnabled(true);
        	lotCommentField.setValue(lot.getLotComment());
        	lotCommentField.refresh();
        } catch (Exception e) {
            logger.error("LotReleaseDialog : Init tablelist", e);
            e.printStackTrace();
        }
	}
	
	@Override
	protected void okPressed() {	
		try {
            LotManager lotManager = Framework.getService(LotManager.class);
			//���ú�̨������������
			SessionContext sc = Env.getSessionContext();
			lot = (Lot)lotInfoField.getValue();
			
			lotManager.addLotComments(lot.getLotId(), lot.getLotComment(), sc);
			UI.showInfo(Message.getString("wip.changeId_successed"));// ������ʾ��
			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
				Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT), shellSize.y));
	}

	@Override
	public void initLot() {
	}

	@Override
	public boolean isSupportMulitLot() {
		return false;
	}
}
