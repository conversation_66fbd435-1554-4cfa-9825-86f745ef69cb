package com.glory.mes.wip.lot.run.byeqp.glc;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.custom.depend.ByEqpConsole;
import com.glory.mes.wip.lot.run.byeqp.extensionpoint.IByEqpAction;
import com.glory.mes.wip.lot.run.byeqp.extensionpoint.LotRunByEqpExtensionPoint;
import com.glory.mes.wip.model.Lot;

public class ByEqpManagerContext implements Serializable {

	private static final long serialVersionUID = 4259199558833145784L;
	
	public static final String FIELD_EQPTREE = "eqpTree";
	public static final String FIELD_LOTFORM = "lotForm";
	public static final String FIELD_RUNNINGLOTS = "runningLots";
	public static final String FIELD_WAITTINGLOTS = "waittingLots";
	public static final String FIELD_DISPATCHFLAG = "dispatchFlag";
	public static final String FIELD_EQUIPMENT_ID = "equipmentId";
	public static final String FIELD_CARRIER_ID = "carrierId";
	public static final String FIELD_LOT_ID = "lotId";
	
	public static final String BUTTON_EQPINFO = "eqpinfo";
	public static final String BUTTON_MYREFRESH = "myrefresh";
	public static final String BUTTON_TRACKIN = "trackin";
	public static final String BUTTON_PREPARE = "prepare";
	public static final String BUTTON_INREFRESH = "inrefresh";
	public static final String BUTTON_DOCP = "docp";
	public static final String BUTTON_TRACKOUT = "trackout";
	public static final String BUTTON_ABORT = "abort";
	public static final String BUTTON_RUNREFRESH = "runrefresh";
	
	private ADManager adManager;
	
	private IByEqpAction currentAction = LotRunByEqpExtensionPoint.getMainAction();
	
	private Map<String, String> eqpTypeActionMap;
	
	private ByEqpEditor eqpEditor;
	
	private GlcForm mainGlcForm;
	
	private IEventBroker eventBroker;
	
	private EPartService partService;
	
	private EModelService modelService;
	
	private Equipment currentEqp;
	
	private boolean eqpAvailable = false;
	
	private List<Lot> holdLots;
	
	private  ByEqpConsole console;
	
	public ADManager getAdManager() {
		return adManager;
	}

	public void setAdManager(ADManager adManager) {
		this.adManager = adManager;
	}

	public IByEqpAction getCurrentAction() {
		return currentAction;
	}

	public void setCurrentAction(IByEqpAction currentAction) {
		this.currentAction = currentAction;
	}

	public Map<String, String> getEqpTypeActionMap() {
		return eqpTypeActionMap;
	}

	public void setEqpTypeActionMap(Map<String, String> eqpTypeActionMap) {
		this.eqpTypeActionMap = eqpTypeActionMap;
	}

	public ByEqpEditor getEqpEditor() {
		return eqpEditor;
	}
	
	public void setEqpEditor(ByEqpEditor eqpEditor) {
		this.eqpEditor = eqpEditor;
	}

	public GlcForm getMainGlcForm() {
		return mainGlcForm;
	}

	public void setMainGlcForm(GlcForm mainGlcForm) {
		this.mainGlcForm = mainGlcForm;
	}

	public Equipment getCurrentEqp() {
		return currentEqp;
	}

	public void setCurrentEqp(Equipment currentEqp) {
		this.currentEqp = currentEqp;
	}

	public boolean isEqpAvailable() {
		return eqpAvailable;
	}

	public void setEqpAvailable(boolean eqpAvailable) {
		this.eqpAvailable = eqpAvailable;
	}

	public List<Lot> getHoldLots() {
		return holdLots;
	}

	public void setHoldLots(List<Lot> holdLots) {
		this.holdLots = holdLots;
	}

	public IEventBroker getEventBroker() {
		return eventBroker;
	}

	public void setEventBroker(IEventBroker eventBroker) {
		this.eventBroker = eventBroker;
	}

	public EPartService getPartService() {
		return partService;
	}

	public void setPartService(EPartService partService) {
		this.partService = partService;
	}

	public EModelService getModelService() {
		return modelService;
	}

	public void setModelService(EModelService modelService) {
		this.modelService = modelService;
	}

	public ByEqpConsole getConsole() {
		return console;
	}

	public void setConsole(ByEqpConsole console) {
		this.console = console;
	}
	
}
