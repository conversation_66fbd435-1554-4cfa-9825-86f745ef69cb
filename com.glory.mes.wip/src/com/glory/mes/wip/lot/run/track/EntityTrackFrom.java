package com.glory.mes.wip.lot.run.track;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.graphics.RGB;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.FMessage.MsgType;
import com.glory.framework.base.ui.forms.FMessageManager;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.lot.run.track.forms.ConsoleLogForm;
import com.google.common.collect.Maps;

public class EntityTrackFrom extends TrackForm {
	
	// ��map�����洢��ť�ļ�ֵ�ԣ�����������ð�ťlistener
	protected Map<String, SquareButton> buttonMap = Maps.newHashMap();
	protected LinkedHashMap<String, IField> fields = new LinkedHashMap<String, IField>(10, (float)0.75, false);
	protected FMessageManager mmg = new FMessageManager();
	
	private String consoleTopicName;
	
	
	protected void addField(String key, IField field) {
        fields.put(key, field);
    }

    public void set(String key, Object value) {
        IField field = fields.get(key);
        if (field != null) {
            field.setValue(value);
        }
    }

    public IField getField(String key) {
        IField field = fields.get(key);
        if (field != null) {
            return field;
        }
        return null;
    }
	
    public void refresh() {
        for (IField field : fields.values()) {
        	field.setValue(null);
        	field.refresh();
        }
        if (mmg != null) {
			mmg.removeAllMessages();
		}
    }
	
    public void sendMsgByBroker(Object msg, String topicName) {
		if (broker != null) {
			broker.send(topicName, msg);
		}
	}

	public TextField createText(String id, String label, String value, int limit) {
    	TextField fe = new TextField(id);
        fe.setLabel(label);
        fe.setValue(value != null ? value : ""); 
        fe.setLength(limit);
        fe.setWidth(limit);
        return fe;
    }
	
    public RefTableField createRefTableFieldCombo(String id, String label, String refTableName, boolean autoUpper, Boolean isMandatory) {
    	try {
    		ADManager adManager = (ADManager)Framework.getService(ADManager.class);
    		List<ADRefTable> refTables = adManager.getEntityList(Env.getOrgRrn(), ADRefTable.class, 
    				1, " name = '" + refTableName + "'", "");
    		if (refTables.size() == 0) {
    			return null;
    		}
    		ADRefTable refTable = refTables.get(0);

    		ADTable adTable = adManager.getADTable(refTable.getTableRrn());
    		com.glory.framework.base.ui.nattable.ListTableManager tableManager = new com.glory.framework.base.ui.nattable.ListTableManager(adTable);

    		List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn(), Env.getMaxResult(), refTable.getWhereClause(), refTable.getOrderByClause());
    		if (!isMandatory) {
				String className = adTable.getModelClass();
				list.add((ADBase)Class.forName(className).newInstance());
			}
    		tableManager.setInput(list);
    		
    		return createRefTableFieldCombo(id, label, tableManager, refTable, autoUpper, 32);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}
	
    public RefTableField createRefTableFieldCombo(String id, String label, ListTableManager tableManager, ADRefTable refTable, boolean autoUpper, int limit) {
    	int mStyle = SWT.BORDER;
    	tableManager.setIndexFlag(true);
    	RefTableField fe = new RefTableField(id, tableManager, refTable, mStyle, autoUpper);
        fe.setLabel(label);
        fe.setWidth(limit);
        fe.setReadOnly(true);
        return fe;
    }
    
    /**
	 * �����̨������Ϣ
	 * @param key
	 * @param message
	 * @param type
	 */
	protected void sendMsgToConsole(Object key, String message, MsgType type) {
		mmg.addMessage(key, message, null, type.getIndex());
		sendMsgByBroker(mmg, ConsoleLogForm.TOPIC_NEAME);
	}
	
	/**
	 * �޸İ�ť�Ŀ��á�������״̬
	 * @param buttonId
	 * @param enabled
	 */
	protected void buttonEnable(String buttonId, boolean enabled) {
		SquareButton button = buttonMap.get(buttonId);
		if (button != null) {
			button.setEnabled(enabled);
			button.layout();
		}
	}
	
	/**
	 * �޸�Field�Ŀ��á�������״̬
	 * @param fieldId
	 * @param enabled
	 */
	protected void fieldEnable(String fieldId, boolean enabled) {
		IField field = fields.get(fieldId);
		if (field != null) {
			field.setEnabled(enabled);
			for (Control control : field.getControls()) {
				control.getShell().layout();
			}
		}
	}
	
	protected void handlingExceptions(Exception e) {
		// eclipse����̨����ʾ
		e.printStackTrace();
		// ��վ����̨��ʾ
		if (e instanceof ClientException) {
			ClientException ce = (ClientException) e;
			sendMsgToConsole("Exception", Message.getString(ce.getMessage()), MsgType.MSG_ERROR);
		} else {
			sendMsgToConsole("Exception", Message.getString("common.system_occur_error"), MsgType.MSG_ERROR);
		}
	}
    
    @Override
	public void setFieldFont(String name, int height) {
    	FontData fontData = new FontData(name, height, SWT.CENTER);
		Font font = new Font(Display.getCurrent(), fontData);
		for (IField	 field : fields.values()) {
			Control[] controls = field.getControls();
			if (controls != null) {
				for (Control control : controls) {
					control.setFont(font);
					if (control instanceof Label) {
						control.setForeground(new Color(Display.getCurrent(), new RGB(27, 79, 131), 0));
						control.setBackground(new Color(Display.getCurrent(), new RGB(255, 255, 255), 255));
					}
				}
			}
		}
	}
    
    public String getConsoleTopicName() {
    	consoleTopicName = StringUtil.isEmpty(consoleTopicName) ? UUID.randomUUID().toString() : consoleTopicName;
    	return consoleTopicName;
    }
}
