package com.glory.mes.wip.lot.run.bylot;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.lot.LotEditor;
import com.glory.mes.wip.lot.run.bylot.op.OpByLotSection;

public class ByLotEditor extends LotEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.run.bylot.ByLotEditor";
	
	private static final String TAB_NAME = "Parameters";
	private static final String TAB_NAME_FUTURESTEP = "LotTrackFutureStep";
	
	@Inject
	protected EPartService partService;
	
	@Inject
	protected ESelectionService selectionService;

	@Inject
	protected IEventBroker eventBroker;

	@PostConstruct
	public void postConstruct(Composite parent) {
		super.postConstruct(parent);
		
		ADTable adTable = (ADTable)mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
		createSection(adTable);
		
		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		ScrolledForm form = toolkit.createScrolledForm(parent);
		//form.setLayoutData(new GridData(GridData.FILL_BOTH));
		ManagedForm mform = new ManagedForm(toolkit, form);
		
		Composite body = form.getBody();
		configureBody(body);
		section.createContents(mform, body);
//		mPart.setLabel(Message.getString("wip.bylot.editor_title"));
	}
	
	protected void createSection(ADTable adTable) {
		try {
			ADTab tab = adTable.getTabs().get(0);
			ADTab tab2 = (ADTab)tab.clone();
			ADTab tab3 = (ADTab)tab.clone();
			tab2.setName(TAB_NAME);
			tab2.setLabel_zh(Message.getString("wip.bylot.parameter"));
			tab2.setLabel(TAB_NAME);
			tab2.setSeqNo(Long.valueOf(20));
			
			adTable.getTabs().add(tab2);
			
			tab3.setName(TAB_NAME_FUTURESTEP);
			tab3.setLabel_zh(Message.getString("wip.bylot.futuresteps"));
			tab3.setLabel(TAB_NAME_FUTURESTEP);
			tab3.setSeqNo(Long.valueOf(30));
			
			adTable.getTabs().add(tab3);
			
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
	        if (MesCfMod.isTrackOperator(Env.getOrgRrn(), sysParamManager)) {
				section = new OpByLotSection(adTable);
			} else {
				section = new ByLotSection(adTable, eventBroker);
			}
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
		
}
