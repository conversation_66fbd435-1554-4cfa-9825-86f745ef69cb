package com.glory.mes.wip.lot.run.bybatch;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.core.chain.ChainContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.batch.BatchLotComposite;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.bylot.ByLotSection;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;

public class ByBatchSection extends ByLotSection {

	public BatchLotComposite batchLotComposite;
	
	public ByBatchSection(ADTable table) {
		super(table);
	} 

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		
		batchLotComposite = new BatchLotComposite(client, SWT.NONE, false, false);
		batchLotComposite.setTableHeigthHint(300);
		batchLotComposite.createPartControl();
		
		batchLotComposite.getLotTableManager().addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection) event.getSelection();
				Lot lot = (Lot) selection.getFirstElement();
				setAdObject(lot);
				refresh();
			}
		});
	}
	
	@Override
	protected void trackInAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			TrackInContext context = new TrackInContext();

			Lot lot = (Lot)this.getAdObject();
			if (lot == null) {
				UI.showError(Message.getString("wip.trackin_lot_is_null"));
				return;
			}
			context.setTrackInLot(lot);
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Lot> lots = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
			context.setLots(lots);
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lots.get(0).getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(Env.getUserName());
			
			ChainContext wipContext = lotManager.checkTrackInConstraint(inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
			
			ChainContext checkAutoFutureMergeContext = lotManager.checkAutoFutureMergeConstraint(inContext
					, Env.getSessionContext());
			if (checkAutoFutureMergeContext.getReturnMessage() != null
					&& checkAutoFutureMergeContext.getReturnMessage().trim().length() > 0) {
				UI.showError(checkAutoFutureMergeContext.getReturnMessage());
			}
			if (checkAutoFutureMergeContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
			
			FlowWizard wizard = getTrackInWizard(context, step.getTrackInFlow());
			TrackInDialog dialog = new TrackInDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if ((result == Dialog.OK || result == TrackInDialog.FIN) 
					&& context.getReturnCode() == TrackInContext.OK_ID) {
				batchLotComposite.refresh();
				lots = (List)batchLotComposite.getLotTableManager().getInput();
				if (!lots.isEmpty()) {
					setAdObject(lots.get(0));
				}
				refreshAdapter();
			}
			if (txtLot != null) {
				txtLot.selectAll();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	 protected void refreshAdapter() {
		 batchLotComposite.refresh();
	 }
	

	protected void registerAccelerator() {
		this.batchLotComposite.addListener(SWT.KeyDown, acceleratorListener());
	}
}
