package com.glory.mes.wip.lot.run.track.forms;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.editor.ListEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.lot.run.track.TrackContext;
import com.glory.mes.wip.lot.run.track.TrackDialog;
import com.glory.mes.wip.lot.run.track.TrackForm;
import com.glory.mes.wip.lot.run.track.extensionpoints.ITrackForm;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.model.QtyUnit;

public class ScrapQtyUnitForm extends TrackForm {

	private static String TABLE_NAME = "WIPTrackOutScrapQty";
	
	protected ListEditorTableManager tableManager;
	protected ManagedForm mform;
	protected TableViewer viewer;
	
	protected ListTableManager tableManager0, tableManager1;
	protected XCombo combo0, combo1;
	
	public Composite createForm(Composite parent) {	
		try {	
			GridLayout layout = new GridLayout(1, false);
			layout.verticalSpacing = 0;
			layout.horizontalSpacing = 0;
			layout.marginWidth = 0;
			layout.marginHeight = 0;
				
			Group scrapGroup = new Group(parent, SWT.NONE);
			scrapGroup.setText("������Ϣ");
			scrapGroup.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
			scrapGroup.setLayout(layout);
			scrapGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
			
			createViewerComponent(scrapGroup);		
			createInputComponent(scrapGroup);
			
			return scrapGroup;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;

	}
	
	protected void createViewerComponent(Composite composite) {
		ADManager adManager;
		try {
			adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new ListEditorTableManager(adTable, true);
			tableManager.setIndexFlag(true);
			tableManager.newViewer(composite);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createInputComponent(Composite composite) {
		try {
//			Composite inputComposite = new Composite(composite, SWT.BORDER);
//			inputComposite.setLayout(new GridLayout(2, false));
//			inputComposite.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
//			inputComposite.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
//			inputComposite.setBackgroundMode(SWT.INHERIT_NONE);
//			
//			FormToolkit toolkit = new FormToolkit(Display.getCurrent().getActiveShell().getDisplay());
//			ADManager adManager = Framework.getService(ADManager.class);
//						
//			toolkit.createLabel(inputComposite, Message.getString("wip.lot_id"), SWT.NULL);					
//			ADTable adTable0  = adManager.getADTable(Env.getOrgRrn(), "WIPLot");	    		
//	    	tableManager0 = new ListTableManager(adTable0);
//			combo0 = new XCombo(inputComposite, tableManager0, "lotId", "lotId", SWT.BORDER, false);
//			combo0.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
//		    	   			
//			toolkit.createLabel(inputComposite, Message.getString("wip.scrap_code"), SWT.NULL);				
//			ADTable adTable1  = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);	    		
//	    	tableManager1 = new ListTableManager(adTable1);
//			combo1 = new XCombo(inputComposite, tableManager1, "actionCode", "actionCode", SWT.BORDER, false);
//			combo1.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
//	  	      
//		    toolkit.createLabel(inputComposite, "����", SWT.NULL);
//		    final Text text = toolkit.createText(inputComposite, "");
//		    text.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
//		    
//		    GridData gd = new GridData(GridData.FILL_HORIZONTAL);		
//			gd.horizontalAlignment = SWT.RIGHT;
//			gd.grabExcessHorizontalSpace = true;
//			gd.horizontalSpan = 2;
//			Composite btnComposite = new Composite(inputComposite, SWT.NONE);
//			btnComposite.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
//			btnComposite.setBackgroundMode(SWT.INHERIT_NONE);
//			btnComposite.setLayout(new GridLayout(2, false));
//			btnComposite.setLayoutData(gd);
//			Button addBtn = toolkit.createButton(btnComposite, "����", SWT.NONE);
//			addBtn.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
//			addBtn.addSelectionListener(new SelectionAdapter() {
//				public void widgetSelected(SelectionEvent event) {
//					try {
//						List<QtyUnit> qtyUnits = (List<QtyUnit>)(List<? extends Object>)tableManager.getInput();
//						List<QtyUnit> lines = new ArrayList<QtyUnit>();
//						for (QtyUnit qtyUnit : qtyUnits) {
//							lines.add(qtyUnit);
//						}	
//						QtyUnit newQtyUnit = new QtyUnit();
//						newQtyUnit.setActionCode(combo1.getText());
//						newQtyUnit.setMainQty(BigDecimal.valueOf(Long.valueOf(text.getText())));
//						lines.add(newQtyUnit);
//						tableManager.setInput(lines);
//					} catch (Exception e) {
//						e.printStackTrace();
//					}
//				}
//			});
//			
//			Button deleteBtn = toolkit.createButton(btnComposite, "ɾ��", SWT.NONE);
//			deleteBtn.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
//			deleteBtn.addSelectionListener(new SelectionAdapter() {
//				public void widgetSelected(SelectionEvent event) {
//					try {
//						List<QtyUnit> qtyUnits = (List<QtyUnit>)(List<? extends Object>)tableManager.getInput();
//						List<QtyUnit> lines = new ArrayList<QtyUnit>();
//						for(QtyUnit qtyUnit : qtyUnits){
//							lines.add(qtyUnit);
//						}				
//						List<Object> os = tableManager.getCheckedObject();
//						if (os.size() != 0) {
//							for (Object o : os) {
//								QtyUnit line = (QtyUnit) o;
//								lines.remove(line);
//							}
//						}
//						tableManager.setInput(lines);
//					} catch (Exception e) {
//						e.printStackTrace();
//					}
//				}
//			});
//			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	//@Override
	public void lotChanged(Object sender, List<Lot> lots) {
		try {
			if (lots != null && lots.size() > 0) {
				tableManager0.setInput(lots);
				tableManager0.refresh();
				ADManager manager = Framework.getService(ADManager.class);
				Step step = new Step();
				step.setObjectRrn(lots.get(0).getStepRrn());
				step = (Step) manager.getEntity(step);
				if (step == null) {
					TrackDialog dialog = (TrackDialog) sender;
					dialog.writeConsoleMessage(Message.getString("wip.step_is_null"), TrackDialog.LOGGER_LEVEL_ERROR);				
				}
			
				String scrapCode = step.getScrapCodeSrc() != null ? step.getScrapCodeSrc() : "ScrapCode";
				List<ADURefList> list = manager.getEntityList(Env.getOrgRrn(), ADURefList.class, 
						Env.getMaxResult(), "referenceName = '" + scrapCode + "' ", "");
				List<QtyUnit> scrapCodeList = new ArrayList<QtyUnit>();
				for (ADURefList ref : list) {
					QtyUnit scrap = new QtyUnit();
					scrap.setActionCode(ref.getText());
					scrap.setDescription(ref.getDescription());
					scrapCodeList.add(scrap);
				}
				tableManager1.setInput(scrapCodeList);	
				tableManager1.refresh();
			} else {
				tableManager0.setInput(null);
				tableManager0.refresh();
				tableManager1.setInput(null);
				tableManager1.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}			
	}
	
	public TrackContext saveToObject(TrackContext trackContext) {		
		try {
			List<ProcessUnit> scrapUnits = new ArrayList<ProcessUnit>();
			for (Object unit : tableManager.getInput()) {
				QtyUnit qtyUnit = (QtyUnit)unit;
				if (qtyUnit.getMainQty() != null) {
					qtyUnit.setEquipmentId(trackContext.getCurrentLot().getEquipmentId());
					scrapUnits.add(qtyUnit);
				}
			}
			
		} catch (ClientException e) {
			throw new ClientException(e);
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return trackContext;
	}

}
