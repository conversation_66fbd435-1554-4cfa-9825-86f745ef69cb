package com.glory.mes.wip.lot.run.trackin.subcapa;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.swt.widgets.Composite;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.ras.eqp.CapaSub;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.eqp.subcapa.SubCapaEquipmentComponent;
import com.glory.mes.ras.eqp.subcapa.SubCapaEquipmentForm;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;

public class SelectEquipmentSubCapaForm extends SubCapaEquipmentForm {

	public Lot lot;
	
	public SelectEquipmentSubCapaForm(Composite parent, List<CapaSub> subCapas, Lot lot) {
		super(parent, subCapas);
		this.lot = lot;
	}

	public List<Equipment> setUnitEquipment() throws Exception {
		List<Equipment> allEquipments = new ArrayList<Equipment>();
		LotManager manager = Framework.getService(LotManager.class);
		for (Composite unit : units) {
			SubCapaEquipmentComponent subUnit = (SubCapaEquipmentComponent)unit;
			//��ÿ������豸,ֻ�����豸״̬,�����ǹ�������,�豸Ԥ������������
			List<Equipment> availableEqps = manager.getAvailableEquipments(lot, subUnit.getSubCapa().getSubCapaRrn(), Env.getSessionContext()); 
			
			List<Equipment> showEqps =  availableEqps.stream().filter(p -> p.getIsAvailable() == true).collect(Collectors.toList());
			subUnit.setAvailableEquipment(showEqps);
			allEquipments.addAll(availableEqps);
		}
		return allEquipments;
	}
	
}
