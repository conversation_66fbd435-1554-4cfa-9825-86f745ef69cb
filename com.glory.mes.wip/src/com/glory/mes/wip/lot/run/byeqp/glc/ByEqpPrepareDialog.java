package com.glory.mes.wip.lot.run.byeqp.glc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.osgi.service.event.Event;

import com.glory.common.fel.common.StringUtils;
import com.glory.edc.client.EDCManager;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcItemSet;
import com.glory.edc.model.EdcItemSetLine;
import com.glory.edc.model.EdcTecn;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.FMessage.MsgType;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.FieldType;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.validator.DataType;
import com.glory.framework.base.ui.validator.ValidatorFactory;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.ConstraintManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.LotPrepareManager;
import com.glory.mes.wip.custom.LotListComposite;
import com.glory.mes.wip.lot.run.extensionpoints.OperationActionExtensionPoint;
import com.glory.mes.wip.lot.run.extensionpoints.OperationContext;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotPrepare;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.util.ByEqpUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class ByEqpPrepareDialog extends GlcBaseDialog {
	
	private static final Logger logger = Logger.getLogger(ByEqpPrepareDialog.class);
	
	public static final String FIELD_LEFTFROM = "leftFrom";
	protected static final String FIELD_LOTLIST = "lotList";
	public static final String FIELD_PREPARELIST = "prepareList";
	
	public static final String FIELD_JOBID = "jobId";
	public static final String FIELD_COMPONENTID = "componentId";
	
	private static final String BUTTON_PREPARE_CREATE = "preparecreate";
	private static final String BUTTON_PPID_CREATE = "ppidcreate";
	private static final String BUTTON_PREPARE_DELETE = "preparedelete";
	private static final String BUTTON_PREPARE_MODIFY = "preparemodify";
	private static final String BUTTON_CHOOSE_WAFER = "choosewafer";
	
	private CustomField lotListField;
	private ListTableManagerField prepareField;
	private TextField leftComponentIdField;
	private TextField rightComponentIdField;
	
	protected SquareButton ppidCreate;
	protected SquareButton prepareCreate;
	
	protected SquareButton leftSample;
	protected SquareButton rightSample;
	private String[] samplingConfig;
	
	private Equipment equipment;
	
	private Consumer<ByEqpPrepareDialog> closeAdaptor;
	
	private boolean isRTDAvailable = false;
	
	public boolean isCheckBatchControl = true;
	
	public ByEqpPrepareDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
	}
	
	protected void createFormAction(GlcForm form) {
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_PREPARE_CREATE), this::createAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_PPID_CREATE), this::ppidCreateAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_CHOOSE_WAFER), this::leftSample);
		
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PREPARE_DELETE), this::removeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_PREPARE_MODIFY), this::modifyAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CHOOSE_WAFER), this::rightSample);
		
		lotListField = form.getFieldByControlId(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_LOTLIST, CustomField.class);
		prepareField = form.getFieldByControlId(FIELD_PREPARELIST, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, lotListField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::leftTableSelectionChangeAdapter);
		subscribeAndExecute(eventBroker, prepareField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::rightTableSelectionChangeAdapter);
		
		leftComponentIdField = form.getFieldByControlId(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_COMPONENTID, TextField.class);
		rightComponentIdField = form.getFieldByControlId(FIELD_COMPONENTID, TextField.class);
		
		leftSample = (SquareButton) form.getButtonByControl(FIELD_LEFTFROM, BUTTON_CHOOSE_WAFER);
		leftSample.setEnabled(false);
		
		rightSample = (SquareButton) form.getButtonByControl(null, BUTTON_CHOOSE_WAFER);
		rightSample.setEnabled(false);
		
		ppidCreate = (SquareButton) form.getButtonByControl(FIELD_LEFTFROM, BUTTON_PPID_CREATE);
		ppidCreate.setEnabled(false);
		
		prepareCreate = (SquareButton) form.getButtonByControl(FIELD_LEFTFROM, BUTTON_PREPARE_CREATE);
		prepareCreate.setEnabled(true);
		
		this.equipment = (Equipment) propValues.get("leftFrom-equipmentInfo");
		
		// ����Ƿ���ѡ��PPID
		checkSelectEqpPPID();
	}
	
	private void checkSelectEqpPPID() {
		try {
			ppidCreate.setEnabled(false);
			prepareCreate.setEnabled(true);
			
			RASManager rasManager = Framework.getService(RASManager.class);
			List<Equipment> subEqps = rasManager.getSubEquipments(Env.getOrgRrn(), equipment.getEquipmentId());
			
			if (CollectionUtils.isNotEmpty(subEqps)) {
				String subEqpCategory = subEqps.get(0).getCategory();
				if (Equipment.CATEGORY_CHAMBER.equals(subEqpCategory)) {
					ppidCreate.setEnabled(true);
					prepareCreate.setEnabled(false);
					
					ppidCreate.setData(1L);
				}
			}
			
		} catch (Exception e) {
			logger.error("checkSelectEqpPPID() Failed !", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	
	private void leftSample(Object obj) {
		sampleAdapter(1);
	}
	
	private void rightSample(Object obj) {
		sampleAdapter(2);
	}
	
	private void ppidCreateAdapter(Object obj) {
		try {
			form.getMessageManager().removeAllMessages();
			
			TextField leftJobIdField = form.getFieldByControlId(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_JOBID, TextField.class);
			ListTableManager tableManager = ((LotListComposite)lotListField.getCustomComposite()).getTableManager();
			List<Object> objs = tableManager.getCheckedObject();
			
			if (CollectionUtils.isNotEmpty(objs)) {
				// �������²�ѯ���µ�PPID��Reticle
				List<Lot> lots = objs.stream().map(o -> ((Lot)o)).collect(Collectors.toList());
				
				List<Lot> batchLots = (List<Lot>) tableManager.getInput();
				List<Lot> batchLotYets = Lists.newArrayList();
				Optional<Lot> batchLot = lots.stream().filter(lot -> StringUtils.isNotEmpty(lot.getBatchId())).findFirst();
				if (batchLot.isPresent()) {
					Lot source = batchLot.get();
					batchLotYets = batchLots.stream().filter(lot -> StringUtils.isNotEmpty(lot.getBatchId()) && source.getBatchId().equals(lot.getBatchId())).collect(Collectors.toList());
				}
				lots.addAll(batchLotYets);
				lots = lots.stream().distinct().collect(Collectors.toList());
				
				for (Lot lot : lots) {	
					LotManager lotManager = Framework.getService(LotManager.class);
					
					// ����ȡ����Reticle��Logic Recipe
					List<Lot> checkList = Lists.newArrayList(lot);
					if (!StringUtil.isEmpty(lot.getBatchId())) {
						checkList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
					}
					Lot lotRecipeMask = ByEqpUtil.getEquipmentRecipeAndReticle(checkList, equipment);
					String mask = DBUtil.toString(lotRecipeMask.getMask());
					String logicRecipe = DBUtil.toString(lotRecipeMask.getRecipeName());
					
					//У���豸��LogicRecipe��PPID�Ƿ�ƥ�䣬recipe�Ƿ�Ϊhold
					if(!StringUtil.isEmpty(lotRecipeMask.getRecipeName())) {
						lotManager.validRecipeEquipment(Env.getOrgRrn(), equipment.getEquipmentId(), lotRecipeMask.getRecipeName(), lotRecipeMask.getEquipmentRecipe());
					}		
					
					lot.setEquipmentMask(lotRecipeMask.getEquipmentMask());
					lot.setEquipmentRecipe(lotRecipeMask.getEquipmentRecipe());
					String lotMask =  DBUtil.toString(lot.getMask());
					String lotLogicRecipe = DBUtil.toString(lot.getRecipeName());
					// ������ֲ�һ�£���Ҫ����Աȷ���Ƿ�ʹ�õ����µ�Reticle��Recipe��ˢ�½������ܻ��һ�£�
					if (!Objects.equals(mask, lotMask)) {
						if (!UI.showConfirm(String.format(
								Message.getString("wip.lot_run_check_reticle"), lotMask, mask, lot.getLotId()))) {
							return;
						}
					}
					if (!Objects.equals(logicRecipe, lotLogicRecipe)) {
						if (!UI.showConfirm(String.format(
								Message.getString("wip.lot_run_check_recipe"), lotLogicRecipe, logicRecipe, lot.getLotId()))) {
							return;
						}
					}
					
					// ��������Ҫ���¸�ֵ
					lot.setMask(mask);
					lot.setRecipeName(logicRecipe);
				}
				
				LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
				String jobId = leftJobIdField.getText();
				// ���JobID�Ƿ��ظ�
				if (!StringUtil.isEmpty(jobId)) {
					if (!ValidatorFactory.isValid(DataType.INTEGER, leftJobIdField.getText())) {
						ADField adField = (ADField) leftJobIdField.getADField();
						form.getMessageManager().addMessage(adField.getName() + "common.isvalid", 
								String.format(Message.getString("common.isvalid"), I18nUtil.getI18nMessage(adField, "label"), DataType.INTEGER), null,
								MsgType.MSG_ERROR.getIndex(), leftJobIdField.getControls()[leftJobIdField.getControls().length - 1]);
						return;
					}
					List<LotPrepare> prepares = prepareManager.getPrepareJobs(Env.getOrgRrn(), equipment.getEquipmentId(), jobId, false);
					if (CollectionUtils.isNotEmpty(prepares)) {
						UI.showError(Message.getString("wip.lot_prepare_jobid_exist"));
						return;
					}
				}
				
				OperationContext context = new OperationContext();
				context.setParentObject(this);
				context.setLots(lots);
				context.setData(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_JOBID, leftJobIdField);
				context.setEquipments(Lists.newArrayList(equipment));
				
				long checkData = DBUtil.toLong(ppidCreate.getData());
				if (1 == checkData) {
					context = OperationActionExtensionPoint.executeOperationAction(context, OperationActionExtensionPoint.CHECK_POINT_MULTI_CHAMBER_PREPARE_CREATE);
					if (context.isIgnoreStandardMethod()) {
						return ;
					}
				} else if (2 == checkData) {
					context = OperationActionExtensionPoint.executeOperationAction(context, OperationActionExtensionPoint.CHECK_POINT_FURNACE_PREPARE_CREATE);
					if (context.isIgnoreStandardMethod()) {
						return ;
					}
				}
				
				//��IsNotCheckBatchControl���ش�ʱ����̨����У��EquipmentBatchControl���˹�Prepare��ʱ���Ϊǰ̨У�飬У�鲻ͨ����������û���ѡ���Ƿ�ǿ��PrePare
				Boolean isCreateLotPrepare = true;

		        if (isCheckBatchControl) {
		        	try {
		        		ConstraintManager constraintManager = Framework.getService(ConstraintManager.class);
		        		constraintManager.checkEquipmentCapacity(Env.getOrgRrn(), Arrays.asList(equipment), lots, true);
					} catch (ClientParameterException e) {
						String message = String.format(Message.getString(e.getErrorCode()), e.getParameters()[0], e.getParameters()[1])
								+ Message.getString("wip_whether_to_continue_creating");
						if(UI.showConfirm(message)) {
							isCreateLotPrepare = true;
						} else {
							isCreateLotPrepare = false;
						}
					}
		        }
				
		        if(isCreateLotPrepare) {
		        	prepareManager.createLotJobPrepare(equipment.getEquipmentId(), jobId, lots, Env.getSessionContext());
		        	UI.showInfo(Message.getString("wip.byeqp_create_prepare_job_success"));
		        	leftJobIdField.setValue(null);
		        	leftJobIdField.refresh();
		        }
			}
			
		} catch (Exception e) {
			logger.error("ppidCreateAdapter() Failed !", e);
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			try {
				updateList();
			} catch (Exception e) {
				logger.error("ppidCreateAdapter() updateList Failed !", e);
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}

	private void createAdapter(Object obj) {
		try {
			form.getMessageManager().removeAllMessages();
			
			TextField leftJobIdField = form.getFieldByControlId(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_JOBID, TextField.class);
			ListTableManager tableManager = ((LotListComposite)lotListField.getCustomComposite()).getTableManager();
			List<Object> objs = tableManager.getCheckedObject();
			
			if (CollectionUtils.isNotEmpty(objs)) {
				// �������²�ѯ���µ�PPID��Reticle
				List<Lot> lots = objs.stream().map(o -> ((Lot)o)).collect(Collectors.toList());
				
				List<Lot> batchLots = (List<Lot>) tableManager.getInput();
				List<Lot> batchLotYets = Lists.newArrayList();
				Optional<Lot> batchLot = lots.stream().filter(lot -> StringUtils.isNotEmpty(lot.getBatchId())).findFirst();
				if (batchLot.isPresent() && equipment.getIsBatch()) {
					Lot source = batchLot.get();
					batchLotYets = batchLots.stream().filter(lot -> StringUtils.isNotEmpty(lot.getBatchId()) && source.getBatchId().equals(lot.getBatchId())).collect(Collectors.toList());
				}
				lots.addAll(batchLotYets);
				lots = lots.stream().distinct().collect(Collectors.toList());
				
				for (Lot lot : lots) {	
					LotManager lotManager = Framework.getService(LotManager.class);
					
					// ����ȡ����Reticle��Logic Recipe
					List<Lot> checkList = Lists.newArrayList(lot);
					if (!StringUtil.isEmpty(lot.getBatchId())) {
						checkList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
					}
					Lot lotRecipeMask = ByEqpUtil.getEquipmentRecipeAndReticle(checkList, equipment);
					String mask = DBUtil.toString(lotRecipeMask.getMask());
					String logicRecipe = DBUtil.toString(lotRecipeMask.getRecipeName());
					
					
					//У���豸��LogicRecipe��PPID�Ƿ�ƥ�䣬recipe�Ƿ�Ϊhold
					if(!StringUtil.isEmpty(lotRecipeMask.getRecipeName())) {
						lotManager.validRecipeEquipment(Env.getOrgRrn(), equipment.getEquipmentId(), lotRecipeMask.getRecipeName(), lotRecipeMask.getEquipmentRecipe());
					}		
					
					lot.setEquipmentMask(lotRecipeMask.getEquipmentMask());
					lot.setEquipmentRecipe(lotRecipeMask.getEquipmentRecipe());
					String lotMask =  DBUtil.toString(lot.getMask());
					String lotLogicRecipe = DBUtil.toString(lot.getRecipeName());
					// ������ֲ�һ�£���Ҫ����Աȷ���Ƿ�ʹ�õ����µ�Reticle��Recipe��ˢ�½������ܻ��һ�£�
					if (!Objects.equals(mask, lotMask)) {
						if (!UI.showConfirm(String.format(
								Message.getString("wip.lot_run_check_reticle"), lotMask, mask, lot.getLotId()))) {
							return;
						}
					}
					if (!Objects.equals(logicRecipe, lotLogicRecipe)) {
						if (!UI.showConfirm(String.format(
								Message.getString("wip.lot_run_check_recipe"), lotLogicRecipe, logicRecipe, lot.getLotId()))) {
							return;
						}
					}
					
					// ��������Ҫ���¸�ֵ
					lot.setMask(mask);
					lot.setRecipeName(logicRecipe);
				}
				
				LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
				String jobId = leftJobIdField.getText();
				// ���JobID�Ƿ��ظ�
				if (!StringUtil.isEmpty(jobId)) {
					if (!ValidatorFactory.isValid(DataType.INTEGER, leftJobIdField.getText())) {
						ADField adField = (ADField) leftJobIdField.getADField();
						form.getMessageManager().addMessage(adField.getName() + "common.isvalid", 
								String.format(Message.getString("common.isvalid"), I18nUtil.getI18nMessage(adField, "label"), DataType.INTEGER), null,
								MsgType.MSG_ERROR.getIndex(), leftJobIdField.getControls()[leftJobIdField.getControls().length - 1]);
						return;
					}
					List<LotPrepare> prepares = prepareManager.getPrepareJobs(Env.getOrgRrn(), equipment.getEquipmentId(), jobId, false);
					if (CollectionUtils.isNotEmpty(prepares)) {
						UI.showError(Message.getString("wip.lot_prepare_jobid_exist"));
						return;
					}
				}
				
				OperationContext context = new OperationContext();
				context.setParentObject(this);
				context.setLots(lots);
				context.setData(FIELD_LEFTFROM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_JOBID, leftJobIdField);
				context.setEquipments(Lists.newArrayList(equipment));
				context = OperationActionExtensionPoint.executeOperationAction(context, OperationActionExtensionPoint.CHECK_POINT_PREPARE_CREATE);
				if (context.isIgnoreStandardMethod()) {
					return ;
				}
				
				//��IsNotCheckBatchControl���ش�ʱ����̨����У��EquipmentBatchControl���˹�Prepare��ʱ���Ϊǰ̨У�飬У�鲻ͨ����������û���ѡ���Ƿ�ǿ��PrePare
				Boolean isCreateLotPrepare = true;

		        if (isCheckBatchControl) {
		        	try {
		        		ConstraintManager constraintManager = Framework.getService(ConstraintManager.class);
		        		constraintManager.checkEquipmentCapacity(Env.getOrgRrn(), Arrays.asList(equipment), lots, true);
					} catch (ClientParameterException e) {
						String message = String.format(Message.getString(e.getErrorCode()), e.getParameters()[0], e.getParameters()[1])
								+ Message.getString("wip_whether_to_continue_creating");
						if(UI.showConfirm(message)) {
							isCreateLotPrepare = true;
						} else {
							isCreateLotPrepare = false;
						}
					}
		        }
		        
		        if(isCreateLotPrepare) {
		        	prepareManager.createLotJobPrepare(equipment.getEquipmentId(), jobId, lots, Env.getSessionContext());
		        	UI.showInfo(Message.getString("wip.byeqp_create_prepare_job_success"));
		        	updateList();
		        	leftJobIdField.setValue(null);
		        	leftJobIdField.refresh();
		        }
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : createAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	private void removeAdapter(Object obj) {
		try {
			form.getMessageManager().removeAllMessages();
			List<Object> objs = prepareField.getListTableManager().getCheckedObject();
			if (CollectionUtils.isNotEmpty(objs)) {
				LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
				List<LotPrepare> lotPrepares = objs.stream().map(o -> ((LotPrepare) o)).collect(Collectors.toList());
				
				List<String> jobIds = lotPrepares.stream().map(LotPrepare::getJobId).distinct().collect(Collectors.toList());
				String jobIdsStr = jobIds.stream().collect(Collectors.joining(","));
				if (UI.showConfirm(String.format(Message.getString("wip.byeqp_sure_cancel_jobs"), jobIdsStr))) {
					OperationContext context = new OperationContext();
					context.setParentObject(this);
					context.setEquipments(Lists.newArrayList(equipment));
					context.setData(FIELD_JOBID, jobIds);
					context = OperationActionExtensionPoint.executeOperationAction(context, OperationActionExtensionPoint.CHECK_POINT_PREPARE_REMOVE);
					if (context.isIgnoreStandardMethod()) {
						return ;
					}
					prepareManager.cancelLotJobPrepare(equipment.getEquipmentId(), jobIds, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
					updateList();
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : removeAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	public void updateList() throws Exception {
		LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
		List<LotPrepare> lotPrepares = prepareManager.getPrepareJobs(
				Env.getOrgRrn(), equipment.getEquipmentId(), null, true);
		prepareField.setValue(lotPrepares);
		prepareField.refresh();
		
		// ˢ�������б�������֤�б��е����������µ�
		ListTableManager tableManager = ((LotListComposite)lotListField.getCustomComposite()).getTableManager();
		LotManager lotManager = Framework.getService(LotManager.class);
		
		List<Lot> waittingLots = Lists.newArrayList();
		if (isRTDAvailable) {
			// RTD�Ĳ�ѯ
			waittingLots = ByEqpUtil.getRtdLots(equipment, false);
		} else {
			waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), equipment.getObjectRrn(),
					LotStateMachine.STATE_WAIT, Env.getSessionContext());
		}
		
		waittingLots = ByEqpUtil.filterWaitingLots(waittingLots, equipment);
		
		tableManager.setInput(waittingLots);
	}
	
	private void modifyAdapter(Object obj) {
		try {
			form.getMessageManager().removeAllMessages();
			List<Object> objs = prepareField.getListTableManager().getCheckedObject();
			if (CollectionUtils.isNotEmpty(objs)) {
				// ���ȼ��ֻ��ͬʱ�޸�һ��Job
				Set<String> modifyJobIds = objs.stream().map(o -> ((LotPrepare)o).getJobId()).collect(Collectors.toSet());
				if (modifyJobIds.size() > 1) {
					UI.showInfo(Message.getString("wip.lot_prepare_jobid_modify_one"));
					return;
				}
				
				LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
				
				TextField rightJobIdField = form.getFieldByControlId(FIELD_JOBID, TextField.class);
				if (!ValidatorFactory.isValid(DataType.INTEGER, rightJobIdField.getText())) {
					ADField adField = (ADField) rightJobIdField.getADField();
					form.getMessageManager().addMessage(adField.getName() + "common.isvalid", 
							String.format(Message.getString("common.isvalid"), I18nUtil.getI18nMessage(adField, "label"), DataType.INTEGER), null,
							MsgType.MSG_ERROR.getIndex(), rightJobIdField.getControls()[rightJobIdField.getControls().length - 1]);
					return;
				}
				String jobId = rightJobIdField.getText();
				// ����޸ĵ���������Ƿ��Ѿ�����
				if (!StringUtil.isEmpty(jobId)) {
					List<LotPrepare> prepares = prepareManager
							.getPrepareJobs(Env.getOrgRrn(), equipment.getEquipmentId(), jobId, false);
					if (CollectionUtils.isNotEmpty(prepares)) {
						UI.showError(Message.getString("wip.lot_prepare_jobid_exist"));
						return;
					}
				} else {
					UI.showInfo(Message.getString("wip.lot_prepare_jobid_modify_input"));
					return;
				}
				
				List<LotPrepare> lotPrepares = objs.stream().map(o -> ((LotPrepare) o)).collect(Collectors.toList());
				
				OperationContext context = new OperationContext();
				context.setParentObject(this);
				context.setData(FIELD_PREPARELIST, lotPrepares);
				context.setData(FieldType.TEXT + FIELD_JOBID, rightJobIdField);
				context.setData(FIELD_JOBID, jobId);
				context.setEquipments(Lists.newArrayList(equipment));
				context = OperationActionExtensionPoint.executeOperationAction(context, OperationActionExtensionPoint.CHECK_POINT_PREPARE_CHANGE);
				if (context.isIgnoreStandardMethod()) {
					return ;
				}
				
				prepareManager.changePrepareId(lotPrepares, jobId, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
				updateList();
				rightJobIdField.setValue(null);
				rightJobIdField.refresh();
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : modifyAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	private void leftTableSelectionChangeAdapter(Object obj) {
		leftSample.setEnabled(false);
		Event event = (Event) obj;
		Lot lot = (Lot)event.getProperty(GlcEvent.PROPERTY_DATA);
		if (lot != null) {
			edit(lot);
			if (StringUtils.isNotEmpty(samplingConfig[2])) {
				leftSample.setEnabled(true);
				leftComponentIdField.setValue(getLastSamplingComponentIds(lot));
			} else {
				leftComponentIdField.setValue(null);
			}
		} else {
			leftComponentIdField.setValue(null);
		}
		leftComponentIdField.refresh();		
	}
	
	private void rightTableSelectionChangeAdapter(Object obj) {
		rightSample.setEnabled(false);
		Event event = (Event) obj;
		LotPrepare lotPrepare = (LotPrepare)event.getProperty(GlcEvent.PROPERTY_DATA);
		if (lotPrepare != null) {
			edit(lotPrepare.getLot());
			if (StringUtils.isNotEmpty(samplingConfig[2])) {
				rightSample.setEnabled(true);
				rightComponentIdField.setValue(getLastSamplingComponentIds(lotPrepare.getLot()));
			} else {
				rightComponentIdField.setValue(null);
			}
		} else {
			rightComponentIdField.setValue(null);
		}
		rightComponentIdField.refresh();
	}
	
	private void edit(Lot lot) {
		try {
			samplingConfig = new String[4];//��ά���飬�ֱ���ComponentSamplingPlan/sampleSize/edcSetName/lotRrn
			if (ProcessUnit.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType()) && 
					(LotStateMachine.STATE_WAIT.equals(lot.getState()) || LotStateMachine.STATE_DISP.equals(lot.getState()))) {
				LotManager lotManager = Framework.getService(LotManager.class);	
				List<AbstractEdcSet> edcSets = lotManager.getEdcSets(lot, null, true, Env.getSessionContext());
				if (CollectionUtils.isNotEmpty(edcSets)) {
					samplingConfig[2] = edcSets.get(0).getName();
					samplingConfig[3] = String.valueOf(lot.getObjectRrn());
					for (AbstractEdcSet edcSet : edcSets) {
	    				if (edcSet instanceof EdcItemSet) {
	    					if (!StringUtil.isEmpty(edcSet.getComponentSamplingPlan())) {
	    						samplingConfig[0] = edcSet.getComponentSamplingPlan();
	    					}
	    					List<EdcItemSetLine> edcSetLines = ((EdcItemSet)edcSet).getItemSetLines();
							for (EdcItemSetLine edcSetLine : edcSetLines) {
								if (edcSetLine.getComp() != null) {
									samplingConfig[1] = String.valueOf(edcSetLine.getSampleSize());
	    							return;
								}
							}
	    				}
	    			}
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : edit() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	protected void sampleAdapter(int refreshPart) {
		try {
			ChangeLotComponentSamplingDialog dialog = new ChangeLotComponentSamplingDialog("WIPLotSampling", null, eventBroker);
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLotWithComponentOrderByPosition(Long.valueOf(samplingConfig[3]), true);
			EDCManager edcManager = Framework.getService(EDCManager.class);
			List<EdcTecn> edcTecns = edcManager.getTecnByLotCurrentStep(Env.getOrgRrn(), lot.getLotId(), lot.getStepName());
			
			Map<String, Object> propValues = Maps.newHashMap();
			propValues.put(ChangeLotComponentSamplingDialog.FIELD_COMPONENT_LIST, (List)lot.getSubProcessUnit());
			BigDecimal sampleSize = BigDecimal.valueOf(Long.valueOf(samplingConfig[1]));
			if (sampleSize.compareTo(lot.getMainQty()) != -1) {
				sampleSize = lot.getMainQty();
			}
			propValues.put(ChangeLotComponentSamplingDialog.FIELD_SAMPLE_SIZE, sampleSize);
			dialog.setPropValues(propValues);
			if (CollectionUtils.isNotEmpty(edcTecns)) {
				dialog.setSamplingComponentIds(edcTecns.get(0).getComponentIdList());
			}
			lot.setEdcSetName(DBUtil.toString(samplingConfig[2]));
			lot.setAttribute1(DBUtil.toString(samplingConfig[0]));
			dialog.setLot(lot);
			dialog.setCloseAdaptor(new Consumer<ChangeLotComponentSamplingDialog>() {
				
				@Override
				public void accept(ChangeLotComponentSamplingDialog t) {
					if (refreshPart == 1) {
						leftComponentIdField.setValue(getLastSamplingComponentIds(lot));
						leftComponentIdField.refresh();
					} else {
						rightComponentIdField.setValue(getLastSamplingComponentIds(lot));
						rightComponentIdField.refresh();
					}
				}
			});
			dialog.open();
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : sampleAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	private String getLastSamplingComponentIds(Lot lot) {
		try {
			EDCManager edcManager = Framework.getService(EDCManager.class);
			List<EdcTecn> edcTecns = edcManager.getTecnByLotCurrentStep(Env.getOrgRrn(), lot.getLotId(), lot.getStepName());
			if (CollectionUtils.isNotEmpty(edcTecns)) {
				return edcTecns.get(0).getComponentList();
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpPrepareDialog : getLastSamplingComponentIds() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
		return null;
	}

	public boolean isRTDAvailable() {
		return isRTDAvailable;
	}

	public void setRTDAvailable(boolean isRTDAvailable) {
		this.isRTDAvailable = isRTDAvailable;
	}
	
	public boolean isCheckBatchControl() {
		return isCheckBatchControl;
	}

	public void setCheckBatchControl(boolean isCheckBatchControl) {
		this.isCheckBatchControl = isCheckBatchControl;
	}

	@Override
	protected void createButtonsForButtonBar(Composite parent) {	
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonClose()), false, null);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);

	}
	
	@Override
	public boolean close() {
		if (closeAdaptor != null) {
			try {
				closeAdaptor.accept(this);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return super.close();
	}

	public Consumer<ByEqpPrepareDialog> getCloseAdaptor() {
		return closeAdaptor;
	}

	public void setCloseAdaptor(Consumer<ByEqpPrepareDialog> closeAdaptor) {
		this.closeAdaptor = closeAdaptor;
	}

	public ListTableManagerField getPrepareField() {
		return prepareField;
	}

	public void setPrepareField(ListTableManagerField prepareField) {
		this.prepareField = prepareField;
	}

}
