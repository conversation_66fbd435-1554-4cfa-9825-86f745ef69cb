package com.glory.mes.wip.lot.run.track.forms;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Group;

import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.StepAttribute;
import com.glory.mes.wip.lot.LotStepAttributeForm;
import com.glory.mes.wip.lot.run.track.TrackContext;
import com.glory.mes.wip.lot.run.track.TrackForm;
import com.glory.mes.wip.lot.run.track.extensionpoints.ITrackForm;
import com.glory.mes.wip.model.Lot;

public class StepAttributeForm extends TrackForm {

	protected LotStepAttributeForm attributeForm;
	
	public StepAttributeForm() {}
	
	public Composite createForm(Composite parent) {		
		try {
//			if (this.step != null) {
//				PrdManager prdManager = Framework.getService(PrdManager.class);
//				List<StepAttribute> stepAttributes = 
//						prdManager.getStepAttribute(this.step.getObjectRrn(), transType);
//				if (stepAttributes != null && stepAttributes.size() > 0) {
//					
//					GridLayout layout = new GridLayout();
//					layout.numColumns = 1;
//					layout.marginHeight = 0;
//					layout.marginWidth = 0;
//					
//					GridData gd = new GridData(GridData.FILL_BOTH);
//					
//					Group attribute = new Group(parent, SWT.NONE);
//					attribute.setText(Message.getString("common.attribute"));
//					attribute.setLayout(layout);
//					attribute.setLayoutData(gd);
//					attribute.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
//					
//					attributeForm = new LotStepAttributeForm(attribute, 
//							SWT.BORDER, null, null, new ArrayList<StepAttribute>(), this.equipment);
//					attributeForm.setGridY(1);
//					attributeForm.createForm();
//					attributeForm.setLayoutData(gd);	
//					
//					return attribute;
//				}
//			}
		} catch (ClientException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
		
	public TrackContext saveToObject(TrackContext trackContext) {	
		try {
			boolean saveFlag = true;					
			if (!attributeForm.saveToObject()) {
				saveFlag = false;
			}					
			if (saveFlag) {
				trackContext.setLotAttributeValues(attributeForm.getAttributeValues());	
			} else {
				throw new ClientException("attribute����ֵ���ʧ�ܣ�");
			}
		} catch (ClientException e) {
			throw new ClientException(e);
		} catch (Exception e) {
			e.printStackTrace();
		}		 
		return trackContext;
	}
	
	public boolean validate() {
		return true;
	}
	
	//@Override
	public void lotChanged(Object sender, List<Lot> lots) {
		try {
			if (lots != null && lots.size() > 0) {
				if (attributeForm != null) {
//					PrdManager prdManager = Framework.getService(PrdManager.class);
//					List<StepAttribute> stepAttributes = 
//							prdManager.getStepAttribute(lots.get(0).getStepRrn(), transType);
//					if (stepAttributes != null && stepAttributes.size() > 0) {
//						attributeForm.setAttributes((List) stepAttributes);
//						attributeForm.setLot(lots.get(0));
//					}
					
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}	
		
	}
}
