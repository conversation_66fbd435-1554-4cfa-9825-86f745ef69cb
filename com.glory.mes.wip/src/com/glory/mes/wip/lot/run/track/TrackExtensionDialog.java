package com.glory.mes.wip.lot.run.track;

import java.awt.Toolkit;

import javax.inject.Inject;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.dialog.ExtensionDialog;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.util.UI;
import com.glory.mes.wip.lot.run.track.extensionpoints.TrackEditorConfig;
import com.glory.mes.wip.lot.run.track.extensionpoints.TrackEditorExtensionPoint;

public class TrackExtensionDialog extends ExtensionDialog {
	
	@Inject
    IEventBroker eventBroker;
	
	public TrackExtensionDialog() {
	}
	
	@Override
	protected Control buildView(Composite parent) {
		
		TrackEditorConfig editorConfig = TrackEditorExtensionPoint.getTrackEditor(getTableId());
		if (editorConfig == null) {
			UI.showError("Editor config is null, track type is " + getTableId()); 
		}
		
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		parent.setLayout(layout);
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		FFormToolKit toolkit = new FFormToolKit(parent.getDisplay());
		
		createHeader(parent, editorConfig, toolkit);
		createBody(parent, editorConfig, toolkit);
		
		return parent;
	}
	
	@Override
	protected Control createOkCancelButtons(Composite parent) {
		// TODO Auto-generated method stub
		return null;
	}
	
	protected void createHeader(Composite parent, TrackEditorConfig editorConfig, FFormToolKit toolkit) {
		HeaderComposite header = new HeaderComposite(parent, editorConfig, eventBroker, SWT.NONE, toolkit);
	}
	
	protected void createBody(Composite parent, TrackEditorConfig editorConfig, FFormToolKit toolkit) {
		BodyComposite body = new BodyComposite(parent, editorConfig, eventBroker, SWT.NONE, toolkit);
	}
	
	@Override
	protected Point getInitialSize() {
		int width = Toolkit.getDefaultToolkit().getScreenSize().width;
		int height = Toolkit.getDefaultToolkit().getScreenSize().height;
		
		return new Point(width/2, height/2);
	}

}
