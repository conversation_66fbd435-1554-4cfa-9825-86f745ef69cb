package com.glory.mes.wip.lot.run.bylot;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.nattable.TableViewerManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;

public class RunByLotQueryTableManager extends ListTableManager {

	public RunByLotQueryTableManager() {	
	}
	
	public RunByLotQueryTableManager(ADTable adTable) {
		super(adTable);
	}
	
	public RunByLotQueryTableManager(ADTable adTable, boolean checkFlag) {
		super(adTable, checkFlag);
	}
	
	public RunByLotQueryTableManager(TableViewerManager tableManager) {
		super(tableManager);
	}
	
	
	@Override
    public ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        try {
	        factory.registerAdapter(Object.class, new RunByLotQueryItemAdapter());
        } catch (Exception e){
        	e.printStackTrace();
        }
        return factory;
    }

}
