package com.glory.mes.wip.lot.run.bylot;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.model.Lot;

public class AddCommentDialog extends EntityDialog {

	public AddCommentDialog(ADTable table, ADBase adObject) {
		super(table, adObject);
	}
	 
	 protected boolean saveAdapter() {
		 try {
			 managedForm.getMessageManager().removeAllMessages();
				if (getAdObject() != null) {					
					boolean saveFlag = true;
					for (IForm detailForm : getDetailForms()) {
						if (!detailForm.saveToObject()) {
							saveFlag = false;
						}
					}
					if (saveFlag) {
						for (IForm detailForm : getDetailForms()) {
							PropertyUtil.copyProperties(getAdObject(), detailForm
									.getObject(), detailForm.getCopyProperties());
						}
						Lot lot = (Lot) getAdObject();				
						setAdObject(lot);
						return true;
					}
				}
		 } catch (Exception e) {
			 ExceptionHandlerManager.asyncHandleException(e);
		 } 
		return false;
	 }
}
	    
