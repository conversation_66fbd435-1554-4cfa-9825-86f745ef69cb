package com.glory.mes.wip.lot.run.abort.multieqp;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.wizard.IWizardPage;

import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.abort.AbortWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.track.model.InContext;
import com.google.common.collect.Lists;

public class MultiEqpAbortWizard extends AbortWizard {
	
	public static String ABORT_BYEQP = "MultiEqpAbort";
	private static final Logger logger = Logger.getLogger(AbortWizard.class);
	protected String operator1;

	public MultiEqpAbortWizard() {
	}

	public MultiEqpAbortWizard(InContext context) {
		super(ABORT_BYEQP);
		this.setContext(context);
	}

	@Override
	public boolean performFinish() {
		try {
			InContext context = (InContext) this.getContext();
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Lot> outLots = context.getLots();
			Lot realLot = lotManager.getLotByLotId(Env.getOrgRrn(), outLots.get(0).getLotId());
			
			List<LotAction> actions = context.getActions();
			if (CollectionUtils.isNotEmpty(actions)) {
				actions.forEach(a -> a.setLotRrn(realLot.getObjectRrn()));
				context.setActions(actions);
			}
			
			List<ProcessUnit> units = Lists.newArrayList();
			for (Lot lot : outLots) {
				// ���ҳ�վ�ļ�¼
				units.addAll(lot.getSubProcessUnit());
			}
			
			realLot.setSubProcessUnit(units);
			if (!StringUtil.isEmpty(operator1)) {
				realLot.setOperator1(operator1);
	    	} else {
	    		realLot.setOperator1(Env.getUserName());
	    	}
			
			context.setLots(Lists.newArrayList(realLot));
			context.setOperator1(realLot.getOperator1());
			context.setMultiEqp(true);
			lotManager.abortLotPartialMultiEqp(context, units, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.abort_success"));
			return true;
		} catch (Exception e) {
			logger.error("MultiEqpAbortWizard : performFinish", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return false;
	}
	
	public boolean validateStepCategory() {
		if (context.getCurrentStep() != null && !StringUtil.isEmpty(context.getCurrentStep().getUseCategory())) {
			if (Step.USE_CATEGORY_PROCESS.equals(context.getCurrentStep().getUseCategory())
					|| Step.USE_CATEGORY_MEASURE.equals(context.getCurrentStep().getUseCategory())
					|| Step.USE_CATEGORY_BOTH.equals(context.getCurrentStep().getUseCategory())) {
				//��׼UseCategory,������
				return true;
			} else {
				if (!context.getCurrentStep().getUseCategory().equals(context.getUseCategory())) {
					//����UseCategory,����У�鴫ֵ�Ƿ�һ��
					//��ʾ(���������ҵ����ʹ���ض�����)
					UI.showError(Message.getString("wip.track_special_category"));
					return false;
				}
			}
		} 
		return true;
	}

	@Override
	public IWizardPage getStartingPage() {
		return startPage;
	}

	public void setContext(InContext context) {
		this.context = context;
	}

	public InContext getContext() {
		return context;
	}

	public String getOperator1() {
		return operator1;
	}

	public void setOperator1(String operator1) {
		this.operator1 = operator1;
	}
}
