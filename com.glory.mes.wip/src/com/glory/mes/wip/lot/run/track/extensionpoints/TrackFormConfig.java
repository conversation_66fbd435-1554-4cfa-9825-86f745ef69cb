package com.glory.mes.wip.lot.run.track.extensionpoints;

public class TrackFormConfig {

	private String id;

	private int rowSpan;
	
	private int colSpan;
	
	private Integer heightHint;
	
	private Integer widthHint;
	
	private String fontName;
	
	private Integer fontHight;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public int getRowSpan() {
		return rowSpan;
	}

	public void setRowSpan(int rowSpan) {
		this.rowSpan = rowSpan;
	}

	public int getColSpan() {
		return colSpan;
	}

	public void setColSpan(int colSpan) {
		this.colSpan = colSpan;
	}

	public Integer getHeightHint() {
		return heightHint;
	}

	public void setHeightHint(Integer heightHint) {
		this.heightHint = heightHint;
	}

	public Integer getWidthHint() {
		return widthHint;
	}

	public void setWidthhint(Integer widthHint) {
		this.widthHint = widthHint;
	}
	
	public void setFontName(String fontName) {
		this.fontName = fontName;
	}
	
	public String getFontName() {
		return fontName;
	}
	
	public void setFontHight(Integer fontHight) {
		this.fontHight = fontHight;
	}
	
	public Integer getFontHight() {
		return fontHight;
	}
	
}
