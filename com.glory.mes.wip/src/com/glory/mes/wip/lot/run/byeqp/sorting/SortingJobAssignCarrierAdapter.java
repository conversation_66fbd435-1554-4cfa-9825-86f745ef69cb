package com.glory.mes.wip.lot.run.byeqp.sorting;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.wip.model.ComponentUnit;

public class SortingJobAssignCarrierAdapter extends ListItemAdapter<ComponentUnit>{
	
	@Override
	public Color getForeground(Object element, String id) {
		ComponentUnit componentUnit = (ComponentUnit) element;
    	if ("Y".equals(componentUnit.getAttribute1())) {
    		Color color = new Color(Display.getCurrent(), 128, 128, 128);
    		return color;
    	}
		return super.getForeground(element, id);
	}
}
