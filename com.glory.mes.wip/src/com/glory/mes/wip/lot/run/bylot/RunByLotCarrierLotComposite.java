package com.glory.mes.wip.lot.run.bylot;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.custom.CarrierLotCustomComposite;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;

public class RunByLotCarrierLotComposite extends CarrierLotCustomComposite {
	
	private static final String TABLE_NAME = "WIPRunByLotCarrier";
	private static final String COMP_TABLE_NAME = "WIPRunByLotComponentUnit";
	private Boolean isCaseSensitive;
	
	protected LotSection lotSection;

	public RunByLotCarrierLotComposite(boolean checkFlag) {
		super(checkFlag);
		setLotTableName(TABLE_NAME);
		setCompTableName(COMP_TABLE_NAME);
		setShowComponentFlag(true);
		setLotListAutoSize(true);
		
		// ��ֹ��ָ��
		CustomField customField = new CustomField(
				"RunByLotCarrierLotComposite", "RunByLotCarrierLotComposite", buttons);
		setField(customField);
	}
	
	public RunByLotCarrierLotComposite(LotSection lotSection, Composite parent, int style, boolean checkFlag, boolean showLotFlag, boolean showDetailFlag, boolean showOperatorFlag) {
		this(checkFlag, showLotFlag, showDetailFlag, showOperatorFlag);
		this.lotSection = lotSection;
	}
	
	public RunByLotCarrierLotComposite(boolean checkFlag, boolean showLotFlag, boolean showDetailFlag, boolean showOperatorFlag) {
		this(checkFlag);
		this.checkFlag = checkFlag;
		this.showLotFlag = showLotFlag;
		this.showDetailFlag = showDetailFlag;
		this.showOperatorFlag = showOperatorFlag;
	}
	
	public RunByLotCarrierLotComposite(LotSection lotSection, boolean checkFlag, boolean showLotFlag, 
			boolean showDetailFlag, boolean showOperatorFlag, boolean showComponentFlag) {
		this(checkFlag);
		this.lotSection = lotSection;
		this.checkFlag = checkFlag;
		this.showLotFlag = showLotFlag;
		this.showDetailFlag = showDetailFlag;
		this.showOperatorFlag = showOperatorFlag;
		this.showComponentFlag = showComponentFlag;
	}
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		Composite composite = super.createForm(toolkit, parent);
		try {
			txtLotId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					// �س��¼�
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						Text tLotId = ((Text) event.widget);
						String lotId = tLotId.getText();
						if (!StringUtil.isEmpty(lotId)) {
							if (!isLotIdCaseSensitive()) {
								lotId = lotId.toUpperCase();
							}
							getLotByLotId(lotId);
						}
					}
				}
			});

			if (showOperatorFlag) {
				txtOperator.addKeyListener(new KeyAdapter() {
					@Override
					public void keyPressed(KeyEvent event) {

					}

				});
				txtOperator.addFocusListener(new FocusListener() {
					public void focusGained(FocusEvent e) {
					}

					public void focusLost(FocusEvent e) {
					}
				});
			}

			txtCarrierId.addKeyListener(new KeyAdapter() {
				@Override
				public void keyPressed(KeyEvent event) {
					// �س��¼�
					if (event.keyCode == SWT.CR || event.keyCode == SWT.KEYPAD_CR) {
						String carrierId = ((Text) event.widget).getText();
						if (!StringUtil.isEmpty(carrierId)) {
							getLotsByCarrierId(carrierId);
						}
					}
				}
			});
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
		return composite;
	}
	


	public void getLotByLotId(String lotId) {
		try {
			Lot lot = lotSection.searchLot(lotId);
			if (lot != null) {
				List<Lot> lots = new ArrayList<Lot>();
				lots.add(lot);
				
				lotTableManager.setInput(lots);
				
				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				txtLotId.setText(lotId);
				lotTableManager.refresh();
				// Ĭ��ȫѡ
				if (checkFlag) {
					lotTableManager.setCheckedObject(lot);
				}
				lotSection.setAdObject(lot);
				lotSection.refresh();
				loadComponentUnits(lot);
				if(!StringUtil.isEmpty(lot.getDurable())) {
					txtCarrierId.setText(lot.getDurable());
				}else {
					txtCarrierId.setText("");
				}
				txtLotId.focusing();
				txtLotId.selectAll();
			} else {
				lotTableManager.setInput(new ArrayList<Lot>());
				lotTableManager.refresh();

				txtLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				txtLotId.setText(lotId);
				
				lotTableManager.setSelection(new StructuredSelection(new Object[] {new Lot()}));
				
				if (showComponentFlag) {
					compTableManager.setInput(Lists.newArrayList());
					compTableManager.refresh();
				}
				
				txtLotId.warning();
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void getLotsByCarrierId(String carrierId) {
		try {
			DurableManager durableManager = Framework.getService(DurableManager.class);

			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);
			
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);
				if (CollectionUtils.isNotEmpty(lots)) {
					// Load Current Step
					Lot lotInfo = lotSection.searchLot(lots.get(0).getLotId());
					
					lotTableManager.setInput(lots);
					// Ĭ��ȫѡ
					if (checkFlag) {
						for (Lot lot : lots) {
							lotTableManager.setCheckedObject(lot);
						}
					}
					txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					lotTableManager.refresh();
					
					if (lots != null && lots.size() > 0) {
						lotSection.setAdObject(lotInfo);
						lotSection.refresh();
					}
					
					loadComponentUnits(lots);	
					txtLotId.setText(lots.get(0).getLotId());
					txtCarrierId.focusing();
				} else {
					lotTableManager.setInput(new ArrayList<Lot>());
					lotTableManager.refresh();
				}
				txtCarrierId.selectAll();
			} else {
				lotTableManager.setInput(new ArrayList<Lot>());
				lotTableManager.refresh();

				txtCarrierId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
				txtCarrierId.warning();
				if (showComponentFlag) {
					compTableManager.setInput(Lists.newArrayList());
					compTableManager.refresh();
				}
			}
			
			if (lotDetailsForm != null) {
				lotDetailsForm.setObject(new Lot());
				lotDetailsForm.loadFromObject();
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public boolean isLotIdCaseSensitive() {
		if (isCaseSensitive == null) {
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				isCaseSensitive = MesCfMod.isLotIdCaseSensitive(Env.getOrgRrn(), sysParamManager);
			} catch (Exception e) {
				isCaseSensitive = false;
				e.printStackTrace();
			}
		}
		return isCaseSensitive;
	}
}
