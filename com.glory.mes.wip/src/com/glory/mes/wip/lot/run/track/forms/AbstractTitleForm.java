package com.glory.mes.wip.lot.run.track.forms;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.RGB;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.wip.lot.run.track.EntityTrackFrom;

public abstract class AbstractTitleForm extends EntityTrackFrom {
	
	protected String fontName = "΢���ź�";
	
	public AbstractTitleForm() {
	}
	
	/**
	 * ��ȡ������<br/>
	 * ��ʹ��Message.getString(key)
	 * @return Step ����
	 */
	public abstract String getStepName();

	@Override
	public Composite createForm(Composite parent) {
		Composite titleBody = new Composite(parent, SWT.NONE);
		titleBody.setLayout(new GridLayout(2, false));
		titleBody.setBackground(new Color(Display.getCurrent(), new RGB(239, 239, 239), 255));

		Composite composite = new Composite(titleBody, SWT.NONE);
		composite.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		composite.setLayout(new GridLayout(1, true));
		
		Font titlefont = new Font(Display.getCurrent(), fontName, DPIUtil.autoScaleUpUsingNativeDPI(24), SWT.NORMAL);
		Label step = new Label(composite, SWT.NONE);
		step.setText(getStepName());
		step.setFont(titlefont);
		step.setForeground(new Color(Display.getCurrent(), new RGB(27, 79, 131), 0));

		Composite detils = new Composite(titleBody, SWT.NONE);
		detils.setLayoutData(new GridData(GridData.HORIZONTAL_ALIGN_FILL));
		detils.setLayout(new GridLayout(1, false));

		Font right = new Font(Display.getCurrent(), fontName, DPIUtil.autoScaleUpUsingNativeDPI(16), SWT.NORMAL);
		Label user = new Label(detils, SWT.NONE);
		user.setFont(right);
		user.setForeground(new Color(Display.getCurrent(), new RGB(27, 79, 131), 0));
		
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADUser adUser = new ADUser();
			adUser.setObjectRrn(Env.getUserRrn());
			adUser = (ADUser) adManager.getEntity(adUser);
			
			user.setText(getUserText(adUser.getUserName(), adUser.getDescription()));
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		user.setFont(right);
		return titleBody;
	}
	
	protected String getUserText(String userName, String userNameZH) {
		StringBuilder builder = new StringBuilder(Message.getString("common.username"));
		builder.append(": ").append(userName).append("  ").append(Message.getString("wip.operator")).append(": ")
				.append(userNameZH);
		return builder.toString();
	}
}
