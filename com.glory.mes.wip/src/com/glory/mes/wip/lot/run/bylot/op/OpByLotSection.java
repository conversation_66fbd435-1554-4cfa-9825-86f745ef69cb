package com.glory.mes.wip.lot.run.bylot.op;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.security.password.check.UserPasswordCheckDialog;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.client.SecurityManager;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.wip.lot.run.abort.AbortWizard;
import com.glory.mes.wip.lot.run.bylot.ByLotSection;
import com.glory.mes.wip.lot.run.bylot.RunByLotCarrierLotComposite;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInWizard;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;

public class OpByLotSection extends ByLotSection {

	private static final Logger logger = Logger.getLogger(OpByLotSection.class);
	
	public HeaderText txtOperator;
	public Label lblOperatorId;
	
	public OpByLotSection(ADTable table) {
		super(table);
	}

	@Override
	protected void createSectionTitle(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.verticalAlignment = SWT.TOP;
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
	        if (MesCfMod.isUseDurable(Env.getOrgRrn(), sysParamManager)) {
	        	//���ʹ���ؾ�
				carrierLotComposite = new RunByLotCarrierLotComposite(this, client, SWT.NONE, checkFlag, showLotFlag, showDetailFlag, true);
				carrierLotComposite.createForm(toolkit, client);
				
				carrierLotComposite.getLotTableManager().addSelectionChangedListener(new ISelectionChangedListener() {
					@Override
					public void selectionChanged(SelectionChangedEvent event) {
						StructuredSelection selection = (StructuredSelection) event.getSelection();
						Lot lot = (Lot) selection.getFirstElement();
						setAdObject(lot);
						refresh();
					}
				});
				txtLot = carrierLotComposite.getTxtLotId();
				txtOperator = carrierLotComposite.getTxtOperator();
				lblOperatorId = carrierLotComposite.getLblOperatorId();
				if (MesCfMod.isTrackOperatorPassword(Env.getOrgRrn(), sysParamManager)) {
					txtOperator.setVisible(false);
					lblOperatorId.setVisible(false);
				}
	        } else {
	        	Composite top = toolkit.createComposite(client);
	    		top.setLayout(new GridLayout(5, false));
	    		top.setLayoutData(gd);
	    		
	    		Label label = toolkit.createLabel(top, Message.getString("wip.lot_id"));
	    		label.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_LARGE));
	    		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
	    		txtLot = new HeaderText(top, SWTResourceCache.getImage("header-text-lot"));
	    		txtLot.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_LARGE));
	    		txtLot.setTextLimit(32);
	    		txtLot.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
	    		txtLot.addKeyListener(new KeyAdapter() {
	    			@Override
	    			public void keyPressed(KeyEvent event) {
	    				Text tLotId = ((Text) event.widget);
	    				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
	    				switch (event.keyCode) {
	    				case SWT.CR:
	    				case SWT.KEYPAD_CR:
	    					Lot lot = null;
	    					String lotId = tLotId.getText();
	    					if (!isLotIdCaseSensitive()) {
								lotId = lotId.toUpperCase();
							}
	    					tLotId.setText(lotId);
	    					lot = searchLot(lotId);
	    					tLotId.selectAll();
	    					if (lot == null) {
	    						tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
	    						try {
	    	        				setAdObject(createAdObject());		        			
	    	        			} catch(Exception en) {
	    	        				logger.error("createADObject error at searchEntity Method!");
	    	        			}
	    						txtLot.warning();
	    					} else {
	    						setAdObject(lot);
	    						txtOperator.setFocus();
	    						txtLot.focusing();
	    					}
	    					refresh();
	    					
	    					break;
	    				}
	    			}
	
	    		});
	    		txtLot.addFocusListener(new FocusListener() {
	    			public void focusGained(FocusEvent e) {
	    			}
	
	    			public void focusLost(FocusEvent e) {
	    				Text tLotId = ((Text) e.widget);
	    				String lotId = tLotId.getText();
						if (!isLotIdCaseSensitive()) {
							lotId = lotId.toUpperCase();
						}
	    				tLotId.setText(lotId);
	    			}
	    		});
	    		
	    		label = toolkit.createLabel(top, Message.getString("wip.operator"));
	    		label.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_LARGE));
	    		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
	    		txtOperator = new HeaderText(top, SWTResourceCache.getImage("header-text-op"));
	    		txtOperator.setFont(SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_LARGE));
	    		txtOperator.setBackground(new Color(Display.getCurrent(), 255, 255, 255));
	    		txtOperator.setTextLimit(32);
	    		txtOperator.addKeyListener(new KeyAdapter() {
	    			@Override
	    			public void keyPressed(KeyEvent event) {
	    				
	    			}
	
	    		});
	    		txtOperator.addFocusListener(new FocusListener() {
	    			public void focusGained(FocusEvent e) {
	    			}
	
	    			public void focusLost(FocusEvent e) {
	    			}
	    		});
	    		
	    		Composite right = toolkit.createComposite(top);
	    		GridLayout layout = new GridLayout(2, false);
	    		right.setLayout(layout);
	    		gd = new GridData(GridData.FILL_HORIZONTAL);
	    		gd.horizontalAlignment = SWT.END;
	    		gd.grabExcessHorizontalSpace = true;
	    		right.setLayoutData(gd);
	        }
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}	
	}
	
	protected Boolean CheckOperatorOrNoCheck() {
		try {
			SysParameterManager paramManager = Framework.getService(SysParameterManager.class);
			if (MesCfMod.isTrackOperatorPassword(Env.getOrgRrn(), paramManager)) {
				UserPasswordCheckDialog dialog = new UserPasswordCheckDialog(UI.getActiveShell().getDisplay(), Env.getUserName());
				if (UserPasswordCheckDialog.OK == dialog.getReturnCode()) {
					txtOperator.setText(dialog.getUserName());
				}else {
					return false;
				}
			} else {
				String operatorId = txtOperator.getText().trim();
				if (operatorId.length() == 0) {
					UI.showWarning(Message.getString("wip.track_operator_is_null"));
					return false;
				}
				if (!checkOperator(operatorId)) {
					UI.showWarning(Message.getString("wip.track_operator_is_not_exist"));
					return false;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}
	
	protected void trackInAdapter() {
		if(!CheckOperatorOrNoCheck()) {
			return;
		}
		super.trackInAdapter();
	}
	
	protected void trackOutAdapter() {
		if(!CheckOperatorOrNoCheck()) {
			return;
		}
		super.trackOutAdapter();
	}
	
	protected void abortAdapter() {
		if(!CheckOperatorOrNoCheck()) {
			return;
		}
		super.abortAdapter();
	}
	
	protected void trackMoveAdapter() {
		if(!CheckOperatorOrNoCheck()) {
			return;
		}
		super.trackMoveAdapter();
	}
	
	@Override
	protected TrackInWizard getTrackInWizard(TrackInContext context, String wizardName) {
		TrackInWizard wizard = super.getTrackInWizard(context, wizardName);
		context = wizard.getContext();
		context.setOperator1(txtOperator.getText().trim());
		wizard.setContext(context);
		return wizard;
	}
	
	@Override
	protected TrackOutWizard getTrackOutWizard(TrackOutContext context, String wizardName) {
		TrackOutWizard wizard = super.getTrackOutWizard(context, wizardName);
		context = wizard.getContext();
		context.setOperator1(txtOperator.getText().trim());
		wizard.setContext(context);
		return wizard;
	}
	
	@Override
	protected AbortWizard getAbortWizard(InContext context, String wizardName) {
		AbortWizard wizard = super.getAbortWizard(context, wizardName);
		context = wizard.getContext();
		wizard.setContext(context);
		wizard.setOperator1(txtOperator.getText().trim());
		return wizard;
	}
	
	@Override
	protected TrackOutWizard getTrackMoveWizard(TrackMoveContext context, String wizardName) {
		TrackOutWizard wizard = super.getTrackMoveWizard(context, wizardName);
		TrackOutContext outContext = wizard.getContext();
		outContext.setOperator1(txtOperator.getText().trim());
		wizard.setContext(outContext);
		return wizard;
	}
	
	@Override
	protected void registerAccelerator() {
		super.registerAccelerator();
		this.txtOperator.addListener(SWT.KeyDown, acceleratorListener());
	}
	
	protected boolean checkOperator(String operatorStr) {
		try {
			SecurityManager secManager = Framework.getService(SecurityManager.class);
			ADUser user = secManager.getUserByUserName(Env.getOrgRrn(), operatorStr);
			if (user != null) {
				return true;
			}
		} catch (Exception e) {
			
		}
		return false;
	}
	
	protected boolean checkUserPassword(String userName) {
		try {
			SysParameterManager paramManager = Framework.getService(SysParameterManager.class);
			if (MesCfMod.isTrackOperatorPassword(Env.getOrgRrn(), paramManager)) {
				UserPasswordCheckDialog dialog = new UserPasswordCheckDialog(UI.getActiveShell().getDisplay(), userName);
				if (UserPasswordCheckDialog.OK == dialog.getReturnCode()) {
					return true;
				}
			} else {
				return true;
			}
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return false;
	}
	
}
