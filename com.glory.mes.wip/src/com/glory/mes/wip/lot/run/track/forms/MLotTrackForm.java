package com.glory.mes.wip.lot.run.track.forms;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.forms.FMessage.MsgType;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.mes.mm.client.MMManager;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.track.EntityTrackFrom;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.track.model.InContext;

public class MLotTrackForm extends EntityTrackFrom {

	protected final static String FIELD_LOT = "Lot";
	protected final static String FIELD_EQP = "Eqp";
	protected final static String FIELD_MLOT = "MLot";

	protected SquareButton btnRest;
	protected SquareButton btnPass;

	public Equipment equipment;
	public Lot lot;
	public MLot mLot;

	@Override
	public Composite createForm(Composite parent) {
		Composite body = toolKit.createComposite(parent);
		body.setLayout(new GridLayout(1, false));
		body.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite fieldBody = toolKit.createComposite(body);
		GridLayout gridLayout = new GridLayout(2, false);
		gridLayout.marginTop = 14;
		fieldBody.setLayout(gridLayout);
		GridData gridData = new GridData(GridData.FILL_BOTH);
		gridData.horizontalAlignment = GridData.CENTER;
		gridData.verticalAlignment = GridData.CENTER;
		fieldBody.setLayoutData(gridData);

		createEqpIdField();
		createMLotField();
		createLotIdField();
		
		createContent(fieldBody, toolKit);
		createButton(fieldBody, toolKit);

		((TextField) getField(FIELD_EQP)).getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tEqoId = ((Text) event.widget);
				tEqoId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					txtEqpCREvent(tEqoId);
					break;
				}
			}
		});
		
		((TextField) getField(FIELD_MLOT)).getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tEqoId = ((Text) event.widget);
				tEqoId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					txtMLotCREvent(tEqoId);
					break;
				}
			}
		});

		((TextField) getField(FIELD_LOT)).getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					txtLotCREvent(tLotId);
					break;
				}
			}
		});
		return body;
	}

	public void createEqpIdField() {
		TextField txtLot = createText("", "�豸", "", 32);
		addField(FIELD_EQP, txtLot);
	}

	public void createLotIdField() {
		TextField txtLot = createText("", "FPC", "", 32);
		addField(FIELD_LOT, txtLot);
	}

	public void createMLotField() {
		TextField txtLot = createText("", "��������", "", 32);
		addField(FIELD_MLOT, txtLot);
	}

	private void createContent(Composite parent, FormToolkit toolkit) {
		fields.values().stream().forEach(field -> field.createContent(parent, toolkit));
	}

	private void createButton(Composite parent, FormToolkit toolkit) {
		Composite bodyBtn = toolkit.createComposite(parent, SWT.NONE);
		GridData data = new GridData(GridData.FILL_HORIZONTAL);
		data.horizontalSpan = 2;
		bodyBtn.setLayoutData(data);
		bodyBtn.setLayout(new GridLayout(2, true));

		btnPass = UIControlsFactory.createButton(bodyBtn, UIControlsFactory.BUTTON_DEFAULT);
		btnPass.setText("��վ");
		FontData fontData = new FontData("΢���ź�", 20, SWT.CENTER);
		Font font = new Font(Display.getCurrent(), fontData);
		btnPass.setFont(font);
		btnPass.setLayoutData(new GridData(GridData.CENTER));
		btnPass.setSize(200, 100);

		btnPass.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				txtLotCREvent((Text) getField(FIELD_LOT).getControls()[1]);
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
			}
		});

		btnRest = UIControlsFactory.createButton(bodyBtn, UIControlsFactory.BUTTON_DEFAULT);
		btnRest.setFont(font);
		btnRest.setText("����");
		btnRest.setSize(200, 100);

		btnRest.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				refresh();
			}
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
			}
		});
	}

	/**
	 * �豸ID�Ļس��¼�������
	 * 
	 * @param text
	 */
	protected void txtEqpCREvent(Text text) {
		mmg.removeAllMessages();
		String txtEqp = text.getText();
		try {
			equipment = null;
			RASManager rasManager = Framework.getService(RASManager.class);
			equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), txtEqp);
		} catch (Exception e) {
			mmg.addMessage("Eqp", e.getMessage(), null, MsgType.MSG_ERROR.getIndex(),
					getField(FIELD_EQP).getControls()[getField(FIELD_EQP).getControls().length - 1]);
			broker.send(ConsoleLogForm.TOPIC_NEAME, mmg);
		}
		if (equipment == null) {
			text.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
		}
		if (equipment != null && equipment.getObjectRrn() != null) {
			getField(FIELD_EQP).setValue(equipment.getEquipmentId());
			getField(FIELD_EQP).setEnabled(false);
			((TextField) getField(FIELD_MLOT)).getTextControl().setFocus();
		}
	}

	/**
	 * ����ID�Ļس��¼�������
	 * 
	 * @param text
	 */
	protected void txtLotCREvent(Text text) {
		mmg.removeAllMessages();
		String txtLotId = text.getText();
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), txtLotId);
			if (lot != null && lot.getObjectRrn() != null) {
				this.lot = lot;
				passAdapter();
			} else {
				text.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
			}
		} catch (Exception e) {
			mmg.addMessage("Lot", e.getMessage(), null, MsgType.MSG_ERROR.getIndex(),
					getField(FIELD_LOT).getControls()[getField(FIELD_LOT).getControls().length - 1]);
			broker.send(ConsoleLogForm.TOPIC_NEAME, mmg);
		}
	}
	
	/**
	 * ��������ID�Ļس��¼�������
	 * @param text
	 */
	protected void txtMLotCREvent(Text text) {
		mmg.removeAllMessages();
		String txtMLotId = text.getText();
		try {
			mLot = null;
			MMManager mmManager = Framework.getService(MMManager.class);
			mLot = mmManager.getMLotByMLotId(Env.getOrgRrn(), txtMLotId);
		} catch (Exception e) {
			mmg.addMessage("MLot", e.getMessage(), null, MsgType.MSG_ERROR.getIndex(),
					getField(FIELD_MLOT).getControls()[getField(FIELD_MLOT).getControls().length - 1]);
			broker.send(ConsoleLogForm.TOPIC_NEAME, mmg);
		}
		if (mLot != null && mLot.getObjectRrn() != null) {
			((TextField) getField(FIELD_LOT)).getTextControl().setFocus();
		} else {
			text.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
		}
	}

	/**
	 * ��վǰ��У��
	 * 
	 * @return
	 */
	public boolean validate() {
		if (equipment == null) {
			return false;
		}
		if (mLot == null) {
			((TextField) getField(FIELD_MLOT)).getTextControl().setFocus();
			return false;
		}
		if (lot == null) {
			((TextField) getField(FIELD_LOT)).getTextControl().setFocus();
			return false;
		}
		return true;
	}

	/**
	 * ��վ�¼�
	 */
	protected void passAdapter() {
		if (validate()) {
			try {
				InContext inContext = new InContext();
				List<Lot> lots = new ArrayList<>();
				lot.setEquipmentId(equipment.getEquipmentId());
				lots.add(lot);
				List<Equipment> equipments = new ArrayList<>();
				equipments.add(equipment);
				inContext.setLots(lots);
				inContext.setEquipments(equipments);
				inContext.setOperator1(Env.getUserName());
				
				List<MLot> mLots = new ArrayList<>();
				mLots.add(mLot);
				
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.trackMoveAssembly(inContext, mLots, false, Env.getSessionContext());
				
				mmg.addMessage("TrackMove", lot.getLotId() + " pass!", null, MsgType.MSG_INFORMATION.getIndex());
				broker.send(ConsoleLogForm.TOPIC_NEAME, mmg);
			} catch (Exception e) {
				mmg.addMessage("TrackMove", e.getMessage(), null, MsgType.MSG_ERROR.getIndex(),
						getField(FIELD_LOT).getControls()[getField(FIELD_LOT).getControls().length - 1]);
				broker.send(ConsoleLogForm.TOPIC_NEAME, mmg);
			}
		}
		lot = null;
		Text txtLot = ((TextField) getField(FIELD_LOT)).getTextControl();
		txtLot.setText("");
	}

	@Override
	public void refresh() {
		super.refresh();
		getField(FIELD_EQP).setEnabled(true);
		((TextField) getField(FIELD_EQP)).getTextControl().setFocus();
		equipment = null;
		lot = null;
		mLot = null;
	}

	@Override
	public void setFieldFont(String name, int height) {
		FontData fontData = new FontData(name, height, SWT.CENTER);
		Font font = new Font(Display.getCurrent(), fontData);
		for (IField field : fields.values()) {
			Control[] controls = field.getControls();
			for (Control control : controls) {
				control.setFont(font);
			}
		}
		btnPass.setFont(font);
		btnRest.setFont(font);
	}
}
