package com.glory.mes.wip.lot.run.track;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADEditor;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.util.UI;
import com.glory.mes.wip.lot.run.track.extensionpoints.TrackEditorConfig;
import com.glory.mes.wip.lot.run.track.extensionpoints.TrackEditorExtensionPoint;

public class TrackEditor {

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.run.track.TrackEditor";
	
	@Inject
	protected MPart mPart;
	
	@Inject
    IEventBroker eventBroker;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		ADEditor adEditor = (ADEditor)mPart.getTransientData().get(CommandParameter.PARAM_ADEDITOR);
		String trackType = adEditor.getPARAM2();
		TrackEditorConfig editorConfig = TrackEditorExtensionPoint.getTrackEditor(trackType);
		if (editorConfig == null) {
			UI.showError("Editor config is null, track type is " + trackType); 
			return;
		}
		
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		parent.setLayout(layout);
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		FFormToolKit toolkit = new FFormToolKit(parent.getDisplay());
		
		createHeader(parent, editorConfig, toolkit);
		createBody(parent, editorConfig, toolkit);
	}

	protected void createHeader(Composite parent, TrackEditorConfig editorConfig, FFormToolKit toolkit) {
		HeaderComposite header = new HeaderComposite(parent, editorConfig, eventBroker, SWT.NONE, toolkit);
	}
	
	protected void createBody(Composite parent, TrackEditorConfig editorConfig, FFormToolKit toolkit) {
		BodyComposite body = new BodyComposite(parent, editorConfig, eventBroker, SWT.NONE, toolkit);
	}
	
	
}
