package com.glory.mes.wip.lot.run.track.extensionpoints;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtension;
import org.eclipse.core.runtime.IExtensionPoint;
import org.eclipse.core.runtime.Platform;

public class TrackFormExtensionPoint {
	
	private final static Logger logger = Logger.getLogger(TrackFormExtensionPoint.class);
	
	private static final TrackFormExtensionPoint instance = new TrackFormExtensionPoint();
	private static Map<String, ITrackForm> trackForms = new HashMap<String, ITrackForm>();
	
    public final static String X_POINT = "com.glory.mes.wip.track.forms";
    public final static String A_TYPE = "type";
    public final static String A_CLASS = "class";
    
    public static TrackFormExtensionPoint getInstance() {
    	return instance;
    }
    
	public static ITrackForm getTrackForm(String type) {
		return trackForms.get(type);
	}

	static {
		IExtensionPoint extensionPoint = Platform.getExtensionRegistry().getExtensionPoint(X_POINT);
		IExtension[] extensions = extensionPoint.getExtensions();
		for (int i = 0; i < extensions.length; i++) {
			IConfigurationElement[] configElements = extensions[i].getConfigurationElements();
			for (int j = 0; j < configElements.length; j++) {
				try {
					String type = configElements[j].getAttribute(A_TYPE);
					ITrackForm Form = (ITrackForm)configElements[j].createExecutableExtension(A_CLASS);
					trackForms.put(type, Form);
				} catch (Exception e){
					logger.error("EdcFormExtensionPoint : init ", e);
				}
			}
		}			
	}
	
	
   
}
