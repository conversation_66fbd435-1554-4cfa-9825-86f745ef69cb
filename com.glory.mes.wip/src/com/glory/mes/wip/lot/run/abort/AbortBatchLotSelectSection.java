package com.glory.mes.wip.lot.run.abort;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.viewers.CheckboxTableViewer;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IManagedForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.mes.wip.model.Lot;

public class AbortBatchLotSelectSection {

	protected IManagedForm form;
	protected BatchLotSelectPage parentPage;
	protected CheckboxTableViewer viewer;
	protected ADTable adTable;
	protected Button btKeepBatch;
	
	public AbortBatchLotSelectSection() {}
	
	public AbortBatchLotSelectSection(ADTable table, BatchLotSelectPage parentPage) {
		this.adTable = table;
		this.parentPage = parentPage;
	}
	
	public void createContents(IManagedForm form, Composite parent) {
		this.form = form;
	}
	
	protected List<Lot> filterRunLots(List<Lot> lots) {
		List<Lot> runLots = new ArrayList<Lot>();
		for (Lot lot : lots) {
			if (!Lot.HOLDSTATE_ON.equals(lot.getHoldState()))
				runLots.add(lot);
		}
		return runLots;
	}
	
	public List<Lot> getSelectedLots() {
		if(viewer != null) {
			List<Lot> selectedLots = new ArrayList<Lot>();
			Object[] objs = viewer.getCheckedElements();
			for (int i = 0; i < objs.length; i++) {
				Lot lot = (Lot)objs[i];
				selectedLots.add(lot);
			}
			return selectedLots;
		}
		return null;
	}
	
}
