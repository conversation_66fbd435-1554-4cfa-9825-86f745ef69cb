package com.glory.mes.wip.lot.run.abort;

import java.util.ArrayList;
import java.util.List;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.CheckboxTableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;

public class BatchLotSelectSection {
	private static final Logger logger = Logger.getLogger(BatchLotSelectSection.class);

	protected IManagedForm form;
	protected BatchLotSelectPage parentPage;
	protected BatchLotTableManager tableManager;
	protected CheckboxTableViewer viewer;
	protected ADTable adTable;
	protected Button btKeepBatch;

	public BatchLotSelectSection() {}

	public BatchLotSelectSection(ADTable table, BatchLotSelectPage parentPage) {
		this.adTable = table;
		this.parentPage = parentPage;
	}

	public void createContents(IManagedForm form, Composite parent) {
		this.form = form;
		this.createSectionContent(parent);
	}
	
	protected void createSectionContent(Composite parent) {
		final FormToolkit toolkit = form.getToolkit();
		tableManager = new BatchLotTableManager(adTable);
		tableManager.addStyle(SWT.CHECK);
		viewer = (CheckboxTableViewer)tableManager.createViewer(parent, toolkit);
		// �����Ƿ���Batch��Check Box
//		Composite comp = toolkit.createComposite(parent, SWT.BORDER);
		Composite comp = new Composite(parent, SWT.NULL);
		GridLayout layout = new GridLayout();
		layout.marginHeight = 10;
		layout.marginWidth = 5;
		comp.setLayout(layout);
		comp.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
//		btKeepBatch = toolkit.createButton(comp, Message.getString("wip.is_residence"), SWT.CHECK);
		btKeepBatch = new Button(comp, SWT.CHECK);
		btKeepBatch.setText(Message.getString("wip.is_residence"));
		form.getForm().updateToolBar();

		initTableContent();
	}
	
	protected void initTableContent() {
		try {
			List<Lot> lots = ((TrackOutWizard)parentPage.getWizard()).getContext().getLots();
			if(lots != null && lots.size() > 0) {
//				if (lots.size() == 1) {
//					Lot lot = lots.get(0);
//					LotManager lotManager = Framework.getService(LotManager.class);
//					List<Lot> batchLots = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchRrn());
//					viewer.setInput(filterRunLots(batchLots));
//				} else {
//					viewer.setInput(filterRunLots(lots));
//				}
				viewer.setInput(filterRunLots(lots));
				tableManager.updateView(viewer);
				viewer.setAllChecked(true);
			}
		} catch (Exception e) {
			logger.error("Error at BatchLotSelectSection : initTableContent() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
        	return;
		}
	}
	
	protected List<Lot> filterRunLots(List<Lot> lots) {
		List<Lot> runLots = new ArrayList<Lot>();
		for(Lot lot : lots) {
			if (!Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
				runLots.add(lot);
			}
		}
		return runLots;
	}
	
	public List<Lot> getSelectedLots() {
		if(viewer != null) {
			List<Lot> selectedLots = new ArrayList<Lot>();
			Object[] objs = viewer.getCheckedElements();
			for(int i = 0; i < objs.length; i++) {
				Lot lot = (Lot)objs[i];
				selectedLots.add(lot);
			}
			return selectedLots;
		}
		return null;
	}

	public boolean getIsKeepBatch() {
		return btKeepBatch.getSelection();
	}

}
