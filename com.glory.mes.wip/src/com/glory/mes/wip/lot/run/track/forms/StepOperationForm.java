package com.glory.mes.wip.lot.run.track.forms;

import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.events.MouseEvent;

public class StepOperationForm extends AbstractTableForm {

	@Override
	public String getTableName() {
		return "WIPTrackInStepOperation";
	}

	@Override
	public void addTableViewerListener() {
		tableViewerManager.addDoubleClickListener(new IMouseAction() {
			
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				Object object = tableViewerManager.getSelectedObject();
				doubleClickAdapter(object);
			}
		});
	}
	
	protected void doubleClickAdapter(Object object) {
		
	}

	@Override
	public String getGroupName() {
		return "����ָʾ";
	}

}
