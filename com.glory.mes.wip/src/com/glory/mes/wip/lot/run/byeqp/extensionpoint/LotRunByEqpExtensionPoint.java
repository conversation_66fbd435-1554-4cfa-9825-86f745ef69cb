package com.glory.mes.wip.lot.run.byeqp.extensionpoint;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtension;
import org.eclipse.core.runtime.IExtensionPoint;
import org.eclipse.core.runtime.Platform;

import com.glory.framework.core.util.DBUtil;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpManager;
import com.google.common.collect.Maps;

public class LotRunByEqpExtensionPoint {
	
	private final static Logger logger = Logger.getLogger(LotRunByEqpExtensionPoint.class);
	
	private static final LotRunByEqpExtensionPoint instance = new LotRunByEqpExtensionPoint();
	
	private static Map<String, IByEqpAction> byEqpActionRegistry = new HashMap<String, IByEqpAction>();
	private static IByEqpAction defaultAction;
	private static IByEqpAction customerAction;
	
    public final static String X_POINT = "com.glory.mes.wip.byeqp.glc";
    
    public final static String E_MANAGER = "manager";
    public final static String A_NAME = "name";
    public final static String A_CLASS = "class";
    public final static String M_CLASS = "managerClass";
    public final static String A_IS_DEFAULT = "isDefault";
    public final static String A_IS_CUSTOMER = "isCustomer";
    
    private static Map<String, ByEqpManager> managerMap = Maps.newHashMap();
    
	static {
		IExtensionPoint extensionPoint = Platform.getExtensionRegistry().getExtensionPoint(X_POINT);
		IExtension[] extensions = extensionPoint.getExtensions();
		for (int i = 0; i < extensions.length; i++) {
			try {
				IConfigurationElement[] configElements = extensions[i].getConfigurationElements();
				for (int j = 0; j < configElements.length; j++) {
					if (E_MANAGER.equals(configElements[j].getName())) {
						String name = configElements[j].getAttribute(A_NAME);
						String defStr = configElements[j].getAttribute(A_IS_DEFAULT);
						String cusStr = configElements[j].getAttribute(A_IS_CUSTOMER);
						
						IByEqpAction action = (IByEqpAction)configElements[j].createExecutableExtension(A_CLASS);
						boolean isDefault = DBUtil.toBoolean(defStr);
						if (isDefault) {
							setDefaultAction(action);
						}
						
						boolean isCustomer = DBUtil.toBoolean(cusStr);
						if (isDefault && isCustomer) {
							setCustomerAction(action);
						}
						
						
						if (isCustomer || !byEqpActionRegistry.containsKey(name)) {
							ByEqpManager manager = (ByEqpManager)configElements[j].createExecutableExtension(M_CLASS);
							if (!managerMap.containsKey(manager.getClass().getName())) {
								managerMap.put(manager.getClass().getName(), manager);
								action.setManager(manager);
							} else {
								action.setManager(managerMap.get(manager.getClass().getName()));
							}
							
							byEqpActionRegistry.put(name, action);
						}
					}
				}
			} catch (Exception e) {
				logger.error("LotRunByEqpExtensionPoint : init ", e);
			}
		}			
	}
	
	public static IByEqpAction getMainAction() {
		return getCustomerAction() == null ? getDefaultAction()  : getCustomerAction();
	}
    
    public static IByEqpAction getDefaultAction() {
		return defaultAction;
	}

    private static void setDefaultAction(IByEqpAction defaultAction) {
		LotRunByEqpExtensionPoint.defaultAction = defaultAction;
	}

	public static IByEqpAction getCustomerAction() {
		return customerAction;
	}

	public static void setCustomerAction(IByEqpAction customerAction) {
		LotRunByEqpExtensionPoint.customerAction = customerAction;
	}

	public static LotRunByEqpExtensionPoint getInstance() {
    	return instance;
    }

	public static Map<String, IByEqpAction> getByEqpActionRegistry() {
		return byEqpActionRegistry;
	}
	
}
