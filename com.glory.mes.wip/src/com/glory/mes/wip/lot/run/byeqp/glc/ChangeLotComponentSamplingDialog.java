package com.glory.mes.wip.lot.run.byeqp.glc;

import java.util.Date;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.edc.client.EDCManager;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTecn;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.UUIDUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class ChangeLotComponentSamplingDialog extends GlcBaseDialog {
	
	private static final Logger logger = Logger.getLogger(ChangeLotComponentSamplingDialog.class);
	
	public static final String FIELD_COMPONENT_LIST = "componentList";
	public static final String FIELD_SAMPLE_SIZE = "sampleSize";
	private static final String CONTEXT_NAME = "EDCTECN";
	
	private TextField textField;
	private ListTableManagerField listTableManagerField;
	private List<String> samplingComponentIds;
	private Object defaultSamplingSize;
	private Lot lot;
	private Consumer<ChangeLotComponentSamplingDialog> closeAdaptor;
	
	public ChangeLotComponentSamplingDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
	}
	
	protected void createFormAction(GlcForm form) {
		textField = form.getFieldByControlId(FIELD_SAMPLE_SIZE, TextField.class);
		defaultSamplingSize = propValues.get(ChangeLotComponentSamplingDialog.FIELD_SAMPLE_SIZE);
		listTableManagerField = form.getFieldByControlId(FIELD_COMPONENT_LIST, ListTableManagerField.class);
		listTableManagerField.getListTableManager().addICheckChangedListener(new ICheckChangedListener() {
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				try {
					textField.setValue(listTableManagerField.getListTableManager().getCheckedObject().size());
					textField.refresh();
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
					return;
				}
			} 
		});
		List<Object> objects = (List<Object>) listTableManagerField.getListTableManager().getInput();
		if (samplingComponentIds != null) {
			for (Object object : objects) {
				ComponentUnit compositeUnit = (ComponentUnit) object;
				if (samplingComponentIds.contains(compositeUnit.getComponentId())) {
					listTableManagerField.getListTableManager().setCheckedObject(compositeUnit);
				}
			}
			textField.setValue(listTableManagerField.getListTableManager().getCheckedObject().size());
			textField.refresh();
		}
	}

	@Override
	protected void okPressed() {
		try {
			form.getMessageManager().removeAllMessages();
			
			ListTableManager tableManager = listTableManagerField.getListTableManager();
			List<Object> objs = tableManager.getCheckedObject();
			if (CollectionUtils.isNotEmpty(objs)) {
				if (Integer.valueOf(textField.getValue().toString()) != objs.size()) {
					UI.showInfo(Message.getString("wip.sampling_mainqty_is_not_equal_selected_component_quantity"));
					return;
				}
				if (!ObjectUtils.equals(defaultSamplingSize, textField.getValue())) {
					if (!UI.showConfirm(Message.getString("wip.sampling_mainqty_is_not_equal_edc_quantity"))) {
						return;
					}
				}
				
				EDCManager edcManager = Framework.getService(EDCManager.class);
				List<EdcTecn> tecns = edcManager.getTecnByLotCurrentStep(Env.getOrgRrn(), lot.getLotId(), lot.getStepName());
				EdcTecn edcTecn = new EdcTecn();
				if (CollectionUtils.isNotEmpty(tecns)) {
					edcTecn = tecns.get(0);
				} else {
					edcTecn.setOrgRrn(Env.getOrgRrn());
					edcTecn.setCreatedBy(Env.getUserName());
					edcTecn.setUpdatedBy(Env.getUserName());
					edcTecn.setName(UUIDUtil.base58Uuid());
					edcTecn.setVersion(1l);
					edcTecn.setStatus(VersionControl.STATUS_ACTIVE);
					edcTecn.setActiveTime(new Date());
					edcTecn.setActiveUser(Env.getUserName());		
					edcTecn.setActionType(EdcTecn.ACTION_TYPE_CURRENT_STEP);	
					edcTecn.setEcnType(EdcTecn.ECN_TYPE_EDC);
//					edcTecn.setSubgroupPlanName(lot.getAttribute1().toString());
					edcTecn.setLotId(lot.getLotId());
					edcTecn.setStepName(lot.getStepName());
				}
				edcTecn.setEdcName(lot.getEdcSetName());
				edcTecn.setItem(Long.valueOf(objs.size()));
				edcTecn.setComponentList(objs.stream().map(x -> {return (ComponentUnit)x;}).map(ComponentUnit::getComponentId).collect(Collectors.joining(";")));
				edcTecn.setCompSLotSrc(objs.stream().map(x -> {return (ComponentUnit)x;}).map(ComponentUnit::getPosition).collect(Collectors.joining(";")));
				
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.addLotEdcTecnAtCurrent(lot.getLotId(), Lists.newArrayList(edcTecn), EdcSetCurrent.TEST_TYPE_NORMAL, true, true, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
				super.okPressed();
			}
		} catch (Exception e) {
			logger.error("Error at ChangeLotComponentSamplingDialog : okAdapter() ", e);
			ExceptionHandlerManager.syncHandleException(e);
		}
	}
	
	public List<String> getSamplingComponentIds() {
		return samplingComponentIds;
	}

	public void setSamplingComponentIds(List<String> samplingComponentIds) {
		this.samplingComponentIds = samplingComponentIds;
	}

	public Lot getLot() {
		return lot;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}
	
	@Override
	public boolean close() {
		if (closeAdaptor != null) {
			try {
				closeAdaptor.accept(this);
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		return super.close();
	}

	public Consumer<ChangeLotComponentSamplingDialog> getCloseAdaptor() {
		return closeAdaptor;
	}

	public void setCloseAdaptor(Consumer<ChangeLotComponentSamplingDialog> closeAdaptor) {
		this.closeAdaptor = closeAdaptor;
	}
}
