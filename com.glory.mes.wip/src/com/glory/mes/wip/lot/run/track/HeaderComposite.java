package com.glory.mes.wip.lot.run.track;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Rectangle;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.mes.wip.lot.run.track.extensionpoints.ITrackForm;
import com.glory.mes.wip.lot.run.track.extensionpoints.TrackEditorConfig;
import com.glory.mes.wip.lot.run.track.extensionpoints.TrackFormConfig;
import com.glory.mes.wip.lot.run.track.extensionpoints.TrackFormExtensionPoint;

public class HeaderComposite extends Composite {

	private TrackEditorConfig config;
	private FFormToolKit toolKit;
	private IEventBroker broker;
	
	public HeaderComposite(Composite parent, TrackEditorConfig config, int style) {
		super(parent, style);
		this.config = config;
		createForm();
	}
	
	public HeaderComposite(Composite parent, TrackEditorConfig editorConfig, IEventBroker eventBroker, int style,
			FFormToolKit toolkit) {
		super(parent, style);
		this.broker = eventBroker;
		this.config = editorConfig;
		this.toolKit = toolkit;
		createForm();
	}

	public void createForm() {
		GridLayout layout = new GridLayout(config.getHeaderYGrid(), true);
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(layout);
		
		// ��ȡ��ǰ��Ļ�ķֱ���
		Rectangle area = Display.getDefault().getClientArea();
		int resolution = area.height;
		GridData gd = new GridData(SWT.FILL, SWT.FILL, true, false);
		if (config.getHeaderHeigthHint() != null) {
			gd.heightHint = resolution * 7 / 100;
		}
		setLayoutData(gd);
		
		for (TrackFormConfig formConfig : config.getHeaderFormConfigs()) {
			ITrackForm trackForm = TrackFormExtensionPoint.getTrackForm(formConfig.getId());
			if (trackForm != null) {
				Composite component = trackForm.createForm(this);
				gd = new GridData(SWT.FILL, SWT.FILL, true, true);
				gd.horizontalSpan = formConfig.getColSpan();
				gd.verticalSpan = formConfig.getRowSpan();
				if (formConfig.getHeightHint() != null) {
					gd.heightHint = formConfig.getHeightHint();
				}
				if (formConfig.getWidthHint() != null) {
					gd.widthHint = formConfig.getWidthHint();
				}
				component.setLayoutData(gd);
			}
		}
	}
	
}
