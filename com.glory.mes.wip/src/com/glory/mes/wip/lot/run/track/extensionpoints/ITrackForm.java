package com.glory.mes.wip.lot.run.track.extensionpoints;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.mes.wip.lot.run.track.TrackContext;

public interface ITrackForm {
	
	public void initialize(TrackContext context);
	
	public Composite createForm(Composite parent);
	
	public void load();
	
	public TrackContext saveToObject();
	
	public boolean validate();
	
	public void setBroker(IEventBroker broker);
	
	public void setToolKit(FFormToolKit toolKit);
	
	public void setFieldFont(String name, int height);
	
}
