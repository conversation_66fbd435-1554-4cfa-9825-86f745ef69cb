package com.glory.mes.wip.lot.run.track.forms;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.TableViewerManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Operation;
import com.glory.mes.wip.lot.run.track.TrackContext;
import com.glory.mes.wip.lot.run.track.TrackForm;

public abstract class AbstractTableForm extends TrackForm {

	protected TableViewerManager tableViewerManager;
	
	public AbstractTableForm() {}
	
	/**
	 * ����ADTable Name
	 * @return ADTable Name
	 */
	public abstract String getTableName();
	
	/**
	 * ��ȡGroup��<br/>
	 * ��ʹ��Message.getString(key)
	 * @return Group ����
	 */
	public abstract String getGroupName();
	
	/**
	 * ���������¼�����
	 */
	public abstract void addTableViewerListener();

	public Composite createForm(Composite parent) {
		Group operationGroup = new Group(parent, SWT.NONE);
		operationGroup.setText(getGroupName());
		operationGroup.setLayout(new GridLayout(1, true));
		operationGroup.setFont(new Font(Display.getCurrent(), "����", DPIUtil.autoScaleUpUsingNativeDPI(15), SWT.BOLD));
		GridData gd = new GridData(SWT.FILL, SWT.FILL, false, true);
		operationGroup.setLayoutData(gd);
		operationGroup.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
        
        try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), getTableName());
			
			tableViewerManager = new TableViewerManager(adTable);
			tableViewerManager.newViewer(operationGroup);
			addTableViewerListener();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return operationGroup;
	}

	public TrackContext saveToObject(TrackContext trackContext) {
		return trackContext;
	}
	
	public void setValue(List<Operation> newOperations) {
		tableViewerManager.setInput(newOperations);
	}
	
	public boolean validate() {
		return true;
	}
	
	@Override
	public void setFieldFont(String name, int height) {
		super.setFieldFont(name, height);
		tableViewerManager.setFont(new Font(Display.getCurrent(), name, DPIUtil.autoScaleUpUsingNativeDPI(12), SWT.BOLD));
		tableViewerManager.setSelectFont(new Font(Display.getCurrent(), name, DPIUtil.autoScaleUpUsingNativeDPI(13), SWT.NORMAL));
		tableViewerManager.setHeaderFont(new Font(Display.getCurrent(), name, DPIUtil.autoScaleUpUsingNativeDPI(14), SWT.BOLD));
		tableViewerManager.setSelectHeaderFont(new Font(Display.getCurrent(), name, DPIUtil.autoScaleUpUsingNativeDPI(14), SWT.NORMAL));
		
	}
	
}
