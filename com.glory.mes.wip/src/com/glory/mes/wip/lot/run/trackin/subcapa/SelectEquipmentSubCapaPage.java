package com.glory.mes.wip.lot.run.trackin.subcapa;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Group;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.eqp.CapaSub;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.eqp.subcapa.SubCapaEquipmentForm;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.run.bylot.RunWizard;
import com.glory.mes.wip.lot.run.bylot.RunWizardContext;
import com.glory.mes.wip.lot.run.trackin.SelectEquipmentPage;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.model.Lot;

/**
 * ��ѡ���豸�Ļ�����,������������ѡ�����豸
 * ���������趨Ϊ������ѡ
 * ����Ǳ���,�����ѡ���Ӧ�����豸
 * ����ǿ�ѡ,�����豸���Ǳ���ѡ��
 */
public class SelectEquipmentSubCapaPage extends SelectEquipmentPage {
	
	public SubCapaEquipmentForm subCapaform;
	
	@Override
	public void createControl(Composite parent) {
		try {
			RunWizardContext context = (RunWizardContext)((RunWizard) getWizard()).getContext();
			step = context.getStep();
			if (step == null || step.getCapability() == null) {
				UI.showError(Message.getString("wip.step_capability_is_null"));
				context.setReturnCode(TrackInContext.FAILED_ID);
				if (this.getShell() != null && !this.getShell().isDisposed()) {
					this.getShell().dispose();
				}
				return;
			}
			//�ڰ��豸��ҵʱ�������豸
			List<Equipment> selectedEqps = context.getSelectEquipments();
			List<Equipment> availableEqps = null;
			
			//���û��ѡ���豸
			if (selectedEqps == null || selectedEqps.size() == 0) {
				LotManager manager = Framework.getService(LotManager.class);
			    availableEqps = manager.getAvailableEquipments(lot, Env.getSessionContext());
			    orderByEqpId(availableEqps);
			}
			
			if (!step.getIsRequireEqp()) {
				//������Ǳ����豸����û�п����豸����ֱ����ת���¸�ҳ��
				if (availableEqps == null || availableEqps.size() == 0) {
					((FlowWizard) this.getWizard()).getDialog().skipPressed();
					return;
				}
			} else {
				//���豸���������ҳ��ѡ��			
				canFlipNextPage = false;
			}
			
			toolkit = new FormToolkit(Display.getCurrent());
			Composite composite = new Composite(parent, SWT.NONE);
			GridLayout layout = new GridLayout();
			layout.numColumns = 1;
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			composite.setLayout(layout);
			GridData gd = new GridData(SWT.CENTER, SWT.CENTER, true, false);
			composite.setLayoutData(gd);

			createContent(composite, availableEqps, selectedEqps);
			setControl(composite);
			setPageTitle();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createContent(Composite composite, List<Equipment> availableEqps, List<Equipment> selectedEqps) {
		if (availableEqps != null) {
			createContent(composite, availableEqps);
		} else {
			createContent(composite, selectedEqps);
		}
	}
	
	protected void createContent(Composite composite, List<Equipment> availableEqps) {
		Composite parent = toolkit.createComposite(composite, SWT.NONE);
		parent.setLayout(new GridLayout(1, true));
		parent.setLayoutData(new GridData(GridData.FILL_BOTH));

		Group tabGroup = new Group(parent, SWT.NONE);
		tabGroup.setText(Message.getString("wip.trackin_eqp_list"));
		tabGroup.setBackground(new Color(null, 255, 255, 255));
		tabGroup.setLayout(new GridLayout(1, true));
		tabGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_EQUIPMENT);
			selectEqpTableManager = new ListTableManager(adTable);
			List<Equipment> showEqps =  availableEqps.stream().filter(p -> p.getIsAvailable() == true).collect(Collectors.toList());
			selectEqpTableManager.newViewer(tabGroup);
			selectEqpTableManager.setInput(showEqps);
			selectEqpTableManager.getNatTable().setLayoutData(new GridData(GridData.FILL_BOTH));
			selectEqpTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					Object selectObj = event.getSelection();
					if (selectObj != null) {
						canFlipNextPage = true;
					}
					((RunWizard)getWizard()).getDialog().updateButtons();
				}
			});
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
		availableEqps = createSubCapaEquipment(parent, availableEqps);
		
		createInvaliableEqpGroup(parent, availableEqps);
	}
	
	protected List<Equipment> createSubCapaEquipment(Composite composite, List<Equipment> availableEqps) {						
		try {
			RunWizardContext context = (RunWizardContext)((RunWizard) getWizard()).getContext();
			ADManager adManager = Framework.getService(ADManager.class);
			//���������
			List<CapaSub> subCapas = adManager.getEntityList(Env.getOrgRrn(), CapaSub.class, Env.getMaxResult(), " capaRrn = " + context.getStep().getCapability(), "");
			
			if (subCapas != null && subCapas.size() > 0) {				
				Group subTabGroup = new Group(composite, SWT.NONE);
				subTabGroup.setText(Message.getString("wip.trackin_child_eqp_list"));
				subTabGroup.setBackground(new Color(null, 255, 255, 255));
				subTabGroup.setLayout(new GridLayout(subCapas.size(), true));
				subTabGroup.setLayoutData(new GridData(GridData.FILL_BOTH));
				
				subCapaform = new SelectEquipmentSubCapaForm(subTabGroup, subCapas, context.getLots().get(0));		
				subCapaform.createContent();
				availableEqps.addAll(subCapaform.setUnitEquipment());
			}
		
		} catch (Exception e) {
			e.printStackTrace();
		}	
		return availableEqps;
	}
	
	@Override
	public String doNext() {
		try {
			RunWizardContext context = (RunWizardContext)((RunWizard) getWizard()).getContext();
			List<Equipment> equipments = getSelectedEquipments();
			if (equipments == null || equipments.size() == 0) {
				if (!context.getStep().getIsRequireEqp()) {
					return getDefaultDirect();
				}
				throw new ClientException("wip.must_select_eqp");
			}
			context.setSelectEquipments(equipments);
			if (context.getStep().getIsMultiEqp()) {
				//��֧�ֶ��豸
				throw new ClientException("wip.trackin_unsupport_mulit_eqp");
			} else {
				Equipment equipment = equipments.get(0);
				if (equipment != null) {
					List<Lot> lots = context.getLots();
					for (Lot lot : lots) {
						lot.setEquipmentId(equipment.getEquipmentId());
					}
				}
				
				List<Equipment> allEquipments = new ArrayList<Equipment>();
				if (subCapaform == null) {
					throw new ClientException("wip.eqp_must_input_sub_capa");
				}
				Map<CapaSub, List<Equipment>> map = subCapaform.getSubCapaEquipments();
				for (CapaSub capaSub : map.keySet()) {
					if (capaSub.getIsRequired()) {
						if (map.get(capaSub) == null || map.get(capaSub).size() == 0) {
							throw new ClientException("wip.must_select_eqp");
						}
					}
					if (!capaSub.getIsMulti()) {
						if (map.get(capaSub) != null && map.get(capaSub).size() > 1) {
							throw new ClientException("wip.trackin_unsupport_mulit_eqp");
						}
					}
					allEquipments.addAll(map.get(capaSub));
	 			}
				equipment.setSubEquipments(allEquipments);
			}
			
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return null;
		}
		return getDefaultDirect();
	}
	
}
