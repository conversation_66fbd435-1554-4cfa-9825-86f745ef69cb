package com.glory.mes.wip.lot.run.track.forms;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Group;

import com.glory.framework.base.ui.util.Message;
import com.glory.mes.wip.lot.run.track.TrackForm;

public class LotInfoForm extends TrackForm {

    
    @Override
    public Composite createForm(Composite parent) {
		Group form = new Group(parent, SWT.NONE);
		form.setText(Message.getString("wip.lot_info"));
		form.setLayout(new GridLayout(1, false));
		form.setBackground(parent.getBackground());
		GridData gd = new GridData(GridData.FILL_BOTH);
		form.setLayoutData(gd);
		
		
		return form;
	}

}
