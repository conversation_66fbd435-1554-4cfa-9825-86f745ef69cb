package com.glory.mes.wip.lot.run.byeqp.extensionpoint;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;

import com.glory.framework.activeentity.model.ADForm;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpEditor;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpManager;

public interface IByEqpAction {
	
	/**
	 * ��ȡ��Action����
	 * @return
	 */
	String getActionName();
	
	/**
	 * ��ȡ��̬������
	 * @return
	 */
	String getADFormName();
	
	/**
	 * ��ȡ��̬��
	 * @return
	 */
	ADForm getADForm();
	
	/**
	 * ��ʼ����չ����
	 * @param byEqpEditor
	 */
	void initExtend(ByEqpEditor byEqpEditor, GlcForm form);
	
	/**
	 * �¼�������չ
	 * @param byEqpEditor
	 */
	void subscribeExtend(IEventBroker eventBroker, GlcForm form);
	
	/**
	 * �¼�ȡ�����ģ�����Ҫȡ��
	 */
	void unSubscribeExtend();
	
	
	
	void setPartService(EPartService partService);

	void setModelService(EModelService modelService);
	
	ByEqpManager getManager();
	
	void setManager(ByEqpManager managerDefault);
}
