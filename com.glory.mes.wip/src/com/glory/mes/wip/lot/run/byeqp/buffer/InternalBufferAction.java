package com.glory.mes.wip.lot.run.byeqp.buffer;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.swt.events.DisposeEvent;
import org.eclipse.swt.events.DisposeListener;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADForm;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.LotListComposite;
import com.glory.mes.wip.lot.run.byeqp.extensionpoint.IByEqpAction;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpEditor;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpManager;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotEquipmentUnit;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.util.ByEqpUtil;
import com.google.common.collect.Maps;

/**
 * ��internal buffer�Ļ�̨��֧�ֲ鿴buffer��Ϣ
 * <AUTHOR>
 *
 */
public class InternalBufferAction implements IByEqpAction {
	
	protected Set<EventHandler> subscribleHandlers = new HashSet<EventHandler>();
	
	private static final Logger logger = Logger.getLogger(InternalBufferAction.class);
	
	public static final String ACTION_NAME = "ByEqpInternalBufferAction";
	public static final String ADFORM_NAME = "WIPLotByEqpInternalBuffer";
	
	private static final String FIELD_LOTFORM = "lotForm";
	private static final String FIELD_WAITTINGLOTS = "waittingLots";
	private static final String FIELD_RUNNINGLOTS = "runningLots";
	
	private static final String BUTTON_FURANCE_TRACKIN = "furanceTrackIn";
	private static final String BUTTON_DUMMY_TRACKIN = "dummyTrackIn";
	private static final String BUTTON_INCOMINGLOT = "inComingLot";
	private static final String BUTTON_BUFFERINFO = "bufferInfo";
	
	private ToolItem itemFuranceTrackIn;
	private ToolItem itemDummyTrackIn;
	private ToolItem itemInComingLot;
	private ToolItem itemBuffer;
	
	private ByEqpEditor byEqpEditor;
	private IEventBroker eventBroker;

	private ByEqpManager manager;
	
	@Override
	public ADForm getADForm() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			return adManager.getADForm(Env.getOrgRrn(), ADFORM_NAME);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return null;
		}
	}

	@Override
	public void initExtend(ByEqpEditor byEqpEditor, GlcForm form) {
		this.byEqpEditor = byEqpEditor;
		
		itemFuranceTrackIn = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_FURANCE_TRACKIN);
		itemDummyTrackIn = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_DUMMY_TRACKIN);
		itemInComingLot = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_INCOMINGLOT);
		itemBuffer = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_BUFFERINFO);

		itemFuranceTrackIn.setEnabled(false);
		itemDummyTrackIn.setEnabled(false);
		
		unSubscribeExtend();
	}

	@Override
	public void subscribeExtend(IEventBroker eventBroker, GlcForm form) {
		this.eventBroker = eventBroker;
		
		GlcFormField glcFormField = form.getFieldByControlId(FIELD_LOTFORM, GlcFormField.class);
		CustomField waitingLotListTableManagerField = glcFormField.getFieldByControlId(FIELD_WAITTINGLOTS, CustomField.class);
		waitingLotListTableManagerField.unsubscribeDefaultEvent(form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_WAITTINGLOTS, GlcEvent.EVENT_SELECTION_CHANGED));
		CustomField runingLotListTableManagerField = glcFormField.getFieldByControlId(FIELD_RUNNINGLOTS, CustomField.class);
		runingLotListTableManagerField.unsubscribeDefaultEvent(form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_RUNNINGLOTS, GlcEvent.EVENT_SELECTION_CHANGED));
		
		EventHandler handler1 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_FURANCE_TRACKIN), this::furanceTrackInAdapter);
		subscribleHandlers.add(handler1);
		EventHandler handler2 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_DUMMY_TRACKIN), this::dummyTrackInAdapter);
		subscribleHandlers.add(handler2);	
		EventHandler handler3 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_INCOMINGLOT), this::inComingLotAdapter);
		subscribleHandlers.add(handler3);
		EventHandler handler4 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_BUFFERINFO), this::bufferAdapter);
		subscribleHandlers.add(handler4);
		EventHandler handler5 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_WAITTINGLOTS, GlcEvent.EVENT_SELECTION_CHANGED), this::waitingSelectionAdaptor);
		subscribleHandlers.add(handler5);
		EventHandler handler6 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_RUNNINGLOTS, GlcEvent.EVENT_SELECTION_CHANGED), this::runningSelectionAdaptor);
		subscribleHandlers.add(handler6);
	}	
	
	/**
	 * Waiting�����б��ѡ���¼�
	 * @param obj
	 */
	public void waitingSelectionAdaptor(Object obj) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			
			Event event = (Event) obj;
			Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lot != null) {
				byEqpEditor.lotStatusChanged(lot.getState(), lot.getHoldState());				
				if (!byEqpEditor.isEqpAvailable()) {
					itemDummyTrackIn.setEnabled(false);
					itemFuranceTrackIn.setEnabled(false);		
				} else {
					if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
						itemDummyTrackIn.setEnabled(false);
						itemFuranceTrackIn.setEnabled(false);
					} else if (LotStateMachine.STATE_WAIT.equals(lot.getState()) || LotStateMachine.STATE_DISP.equals(lot.getState())) {
						itemDummyTrackIn.setEnabled(true);
						itemFuranceTrackIn.setEnabled(true);
					} else if (LotStateMachine.STATE_RUN.equals(lot.getState())) {
						itemDummyTrackIn.setEnabled(false);
						itemFuranceTrackIn.setEnabled(false);
					} else {
						itemDummyTrackIn.setEnabled(false);
						itemFuranceTrackIn.setEnabled(false);
					}
				}	
							
				if (byEqpEditor.getConsole() != null && byEqpEditor.getCurrentEqp() != null) {
					/*//�����豸recipe
					List<Lot> lots = lotManager.calculatePPID(Lists.newArrayList(lot), byEqpEditor.getCurrentEqp(), Env.getSessionContext());
					if (CollectionUtils.isNotEmpty(lots)) {
						if (StringUtil.isEmpty(lots.get(0).getEquipmentRecipe())) {
							byEqpEditor.getConsole().info("PPID: Not Found !");
						} else {
							byEqpEditor.getConsole().info("PPID: " + lot.getEquipmentRecipe());
						}
					} else {
						byEqpEditor.getConsole().info("PPID: Not Found !");
					}
					
					//����̨��ʾ�Ƽ�recitle
					if (!StringUtil.isEmpty(lot.getMask())) {
						try {			
							String maskStr[] = lotManager.getLotEquipmentReticle(byEqpEditor.getCurrentEqp().getEquipmentId(), lot, true, true);
							if (maskStr != null && maskStr.length > 0) {	
								byEqpEditor.getConsole().info(Message.getString("ras.recommend.reticle_id")+": "+maskStr[0]);
							}										
						} catch (Exception e) {
							byEqpEditor.getConsole().error(Message.getString(e.toString()));
						}
					}*/
					ByEqpUtil.noticeEquipmentRecipeAndReticle(lot, byEqpEditor.getCurrentEqp(), byEqpEditor.getConsole());
				}
			} else {
				itemDummyTrackIn.setEnabled(false);
				itemFuranceTrackIn.setEnabled(false);
			}
			
			ListTableManager tableManager = 
					((LotListComposite)byEqpEditor.getFieldWaitting().getCustomComposite()).getTableManager();
			List<Object> objects = tableManager.getCheckedObject();
			List<Lot> lots = objects.stream().map(o -> (Lot)o).collect(Collectors.toList());
			Optional<Lot> f = lots.stream().filter(r -> Lot.HOLDSTATE_ON.equals(r.getHoldState())).findFirst();
			if (f.isPresent()) {
				itemDummyTrackIn.setEnabled(false);
				itemFuranceTrackIn.setEnabled(false);
			} else {
				itemDummyTrackIn.setEnabled(true);
				itemFuranceTrackIn.setEnabled(true);
			}	
			
			List<String> dummyLotTypes = lotManager.getDummyLotTypes(Env.getOrgRrn());
			List<Lot> dummyLots = lots.stream().filter(r -> dummyLotTypes.contains(r.getLotType())).collect(Collectors.toList());
			if (lots.size() > 0) {
				if (CollectionUtils.isEmpty(dummyLots)) {		
					itemFuranceTrackIn.setEnabled(true);
					itemDummyTrackIn.setEnabled(false);
				} else if (dummyLots.size() == lots.size()) {
					itemFuranceTrackIn.setEnabled(false);
					itemDummyTrackIn.setEnabled(true);		
				} else {
					itemDummyTrackIn.setEnabled(false);
					itemFuranceTrackIn.setEnabled(false);
				}
			} else {
				itemDummyTrackIn.setEnabled(false);
				itemFuranceTrackIn.setEnabled(false);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}		
	}
	
	
	/**
	 * Running�����б��ѡ���¼�
	 * @param obj
	 */
	protected void runningSelectionAdaptor(Object obj) {
		byEqpEditor.runningSelectionAdaptor(obj);
		itemFuranceTrackIn.setEnabled(false);
		itemDummyTrackIn.setEnabled(false);
	}
	
	/**
	 * ��׼��վ���������ʻ�Dummy�ô˽�վ����
	 * @param obj
	 */
	private void furanceTrackInAdapter(Object obj) {
		try {
			if (byEqpEditor.getCurrentEqp() == null || !byEqpEditor.isEqpAvailable()) {				
				return;
			}

			LotListComposite composite = (LotListComposite) byEqpEditor.getFieldWaitting()
					.getCustomComposite();
			
			List<Lot> lots = (List)composite.getCheckedObjects();
			if (CollectionUtils.isEmpty(lots)) {
				UI.showError(Message.getString("common.byEquipment_check_trackIn_lot"));
				return;
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
			List<String> dummyLotTypes = lotManager.getDummyLotTypes(Env.getOrgRrn());
			for (Lot lot : lots) {
				if (CollectionUtils.isNotEmpty(dummyLotTypes) && dummyLotTypes.contains(lot.getLotType())) {
					UI.showError(Message.getString("wip.lot_byeqp_furance_trackin_no_dummy_lot"));
					return;
				}
			}
			
			byEqpEditor.trackInAdapter(obj);
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : trackInAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}	
	}
	
	private void inComingLotAdapter(Object obj) {
		try {
			if (byEqpEditor.getCurrentEqp() == null) {
				return;
			}
			
			InComingLotDialog dialog = new InComingLotDialog("WIPInComingLotInfoForm", null, eventBroker);
			Map<String, Object> propValues = Maps.newHashMap();
			propValues.put("mainEqp", byEqpEditor.getCurrentEqp());
			dialog.setPropValues(propValues);
			dialog.open();
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : eqpInfoAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}	
	}
	
	private void bufferAdapter(Object obj) {
		try {
			if (byEqpEditor.getCurrentEqp() == null) {
				return;
			}
			RASManager rasManager = Framework.getService(RASManager.class);
			List<Equipment> internalBuffers = rasManager.getInternalBufferByParentEqp(
					Env.getOrgRrn(), byEqpEditor.getCurrentEqp().getObjectRrn(), null);
			
			if (CollectionUtils.isEmpty(internalBuffers)) {
				UI.showInfo(Message.getString("wip.main_eqp_buffer_not_exist"));
				return;
			}
			
			InternalBufferDialog dialog = new InternalBufferDialog("WIPByEqpBufferDialog", null, eventBroker);
			Map<String, Object> propValues = Maps.newHashMap();
			LotManager lotManager = Framework.getService(LotManager.class);
			List<LotEquipmentUnit> eqpUnits = lotManager.getLotEquipmentUnits(
					Env.getOrgRrn(), null, byEqpEditor.getCurrentEqp().getEquipmentId(), true);
			
			propValues.put("rackList", eqpUnits);
			propValues.put("mainEqp", byEqpEditor.getCurrentEqp());
			propValues.put("buffer", internalBuffers.get(0));
			dialog.setPropValues(propValues);
			dialog.open();
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : eqpInfoAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * Dummy��վ
	 * @param obj
	 */
	private void dummyTrackInAdapter(Object obj) {
		try {
			Event event = (Event) obj;
			String operator1 = Env.getUserName();
			if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}
			if (byEqpEditor.getCurrentEqp() == null || !byEqpEditor.isEqpAvailable()) {				
				return;
			}

			LotListComposite composite = (LotListComposite) byEqpEditor.getFieldWaitting()
					.getCustomComposite();

			List<Lot> lots = (List)composite.getCheckedObjects();
			if (CollectionUtils.isEmpty(lots)) {
				UI.showError(Message.getString("common.byEquipment_check_trackIn_lot"));
				return;
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
			List<String> dummyLotTypes = lotManager.getDummyLotTypes(Env.getOrgRrn());
			for (Lot lot : lots) {
				lot.setEquipmentId(byEqpEditor.getCurrentEqp().getEquipmentId());
				
				if (CollectionUtils.isEmpty(dummyLotTypes) || !dummyLotTypes.contains(lot.getLotType())) {
					UI.showError(Message.getString("wip.lot_byeqp_buffer_rack_dummy_only"));
					return;
				}
			}

			if (!byEqpEditor.getCurrentEqp().getIsBatch() && lots.size() > 1) {
				UI.showError(Message.getString("wip.trackin_cannot_batch"));
				return;
			}

			TrackInContext context = new TrackInContext();
			context.setTrackInType(TrackInContext.TRACK_IN_BYEQP);
			List<Equipment> equipments = new ArrayList<Equipment>();
			ADManager adManager = Framework.getService(ADManager.class);
			Equipment currentEqp = (Equipment) adManager.getEntity(byEqpEditor.getCurrentEqp());
			byEqpEditor.setCurrentEqp(currentEqp);
			equipments.add(currentEqp);
			context.setSelectEquipments(equipments);
			context.setLots(lots);
			context.setOperator1(operator1);

			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lots.get(0).getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);
				
			//ֱ�ӽ�վ
			for (Lot lot : context.getLots()) {
				lot.setOperator1(context.getOperator1());
			}

			if (context.getStep().getIsRequireEqp() && context.getSelectEquipments() != null
					&& context.getSelectEquipments().size() > 0) {
				Equipment equipment = context.getSelectEquipments().get(0);
				for (Lot lot : context.getLots()) {
					if (lot.getEquipmentId() == null) {
						lot.setEquipmentId(equipment.getEquipmentId());
					}
				}
			}
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setAttributeValues(context.getLotAttributeValues());
			inContext.setEquipments(context.getSelectEquipments());
			inContext.setOperator1(context.getOperator1());
			inContext.setCheckEquipmentCapa(false);
			inContext.setCheckMaterial(false);
			inContext.setCheckTool(false);
			
			lotManager.trackIn(inContext, Env.getSessionContext());
			UI.showInfo(Message.getString("wip.trackin_success"));
			byEqpEditor.refresh(1);
			
//			FlowWizard wizard = WizardPageExtensionPoint.getWizardRegistry().get(step.getTrackInFlow());
//			if (wizard instanceof TrackInWizard) {
//				((TrackInWizard) wizard).setContext(context);
//			}
//			TrackInDialog dialog = new TrackInDialog(Display.getCurrent().getActiveShell(), wizard);
//			int result = dialog.open();
//			if ((result == Dialog.OK || result == TrackInDialog.FIN)
//					&& context.getReturnCode() == TrackInContext.OK_ID) {
//				if (byEqpEditor.getConsole() != null) {
//					String message = "";
//					for (Lot slot : context.getLots()) {
//						if (message.isEmpty()) {
//							message += "[" + slot.getLotId() + "]";
//						} else {
//							message += ";[" + slot.getLotId() + "]";
//						}
//					}
//					message += " TrackIn Successful! ";
//					byEqpEditor.getConsole().info(message);
//				}
//				byEqpEditor.refresh(1);
//			}
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : trackInAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	public void unSubscribeExtend() {
		itemFuranceTrackIn.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
		itemDummyTrackIn.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
		itemInComingLot.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
		itemBuffer.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
	}

	@Override
	public String getADFormName() {
		return ADFORM_NAME;
	}

	@Override
	public String getActionName() {
		return ACTION_NAME;
	}

	@Override
	public void setPartService(EPartService partService) {
		
	}

	@Override
	public void setModelService(EModelService modelService) {
		
	}

	@Override
	public ByEqpManager getManager() {
		return manager;
	}

	@Override
	public void setManager(ByEqpManager manager) {
		this.manager = manager;
	}

}
