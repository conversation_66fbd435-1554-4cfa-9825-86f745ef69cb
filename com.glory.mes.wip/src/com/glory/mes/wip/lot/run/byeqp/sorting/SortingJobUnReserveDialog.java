package com.glory.mes.wip.lot.run.byeqp.sorting;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.wip.client.SortingManager;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class SortingJobUnReserveDialog extends GlcBaseDialog{

	private static final String FIELD_SELECTJOBS = "selectJobs";
	private static final String ACTION_TYPE_UNRESERVE = "unReserve";

	private ListTableManagerField listTableManagerField;
	
	private LotSortingJob sortingJob;
	private String actionType;
	
	public SortingJobUnReserveDialog(String adFormName, String authority, IEventBroker eventBroker, LotSortingJob lotSortingJob, String actionType) {
		super(adFormName, authority, eventBroker);
		this.sortingJob = lotSortingJob;
		this.actionType = actionType;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		listTableManagerField = form.getFieldByControlId(FIELD_SELECTJOBS, ListTableManagerField.class);
		
		init();
	}

	private void init() {
		try {
			SortingManager sortingManager = Framework.getService(SortingManager.class);
			List<LotSortingJob> sortingJobs = sortingManager.getSortingJobByBatch(Env.getOrgRrn(), sortingJob.getBatchId());
			//��������ɵ�Sorter����
			sortingJobs = sortingJobs.stream().filter(s -> !LotSortingJob.STATE_DONE.equals(s.getJobState())).collect(Collectors.toList());
			listTableManagerField.getListTableManager().setInput(sortingJobs);
			
			for(LotSortingJob sortingJob : sortingJobs) {
				listTableManagerField.getListTableManager().setCheckedObject(sortingJob);
			}
			
			if(ACTION_TYPE_UNRESERVE.equals(actionType)) {
				CheckBoxTableViewerManager boxFixEditorTableManager = (CheckBoxTableViewerManager) listTableManagerField.getListTableManager().getTableManager();
				boxFixEditorTableManager.addICheckChangedListener(new ICheckChangedListener() {
					@Override
					public void checkChanged(List<Object> eventObjects, boolean checked) {
						if(!checked) {
							eventObjects.stream().forEach(e -> {
								listTableManagerField.getListTableManager().setCheckedObject(e);
							});
						}
					}
				});
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected Point getInitialSize() {
		return new Point(1200,800);
	}
	
	@Override
	protected void okPressed() {
		try { 
			SortingManager sortingManager = Framework.getService(SortingManager.class);
			DurableManager durableManager = Framework.getService(DurableManager.class);
			List<Object> sortingJobs = listTableManagerField.getListTableManager().getCheckedObject();
			String lots = null;
			if(CollectionUtils.isEmpty(sortingJobs)) {
				UI.showInfo(Message.getString("wip.no_sorter_job_to_complete"));
				return;
			}	
			List<LotSortingJob> lotSortingJobs = sortingJobs.stream().map(s ->(LotSortingJob)s).collect(Collectors.toList());
			if(ACTION_TYPE_UNRESERVE.equals(actionType)) {
				sortingManager.unReserveSortingJobs(lotSortingJobs, Env.getSessionContext());
			} else {
				if (UI.showConfirm(Message.getString("common.submit_confirm"))) {
					lotSortingJobs.forEach(lotSortingJob ->{
						durableManager.checkCarrierDirty(Env.getSessionContext().getOrgRrn(), Lists.newArrayList(lotSortingJob.getToDurableId()), true);
					});
                    
	        		List<LotSortingJob> sortingJobList = sortingManager.completeLotSortingJobByJobType(lotSortingJobs, Env.getSessionContext());  
	        		if(LotSortingJob.ACTION_TYPE_SPLIT.equals(lotSortingJobs.get(0).getActionType())) {
	        			for(LotSortingJob job : sortingJobList) {
	        				job.setLotId("SubLot :" + job.getLotId());
	        			}
	        			lots = sortingJobList.stream().map(LotSortingJob :: getLotId).collect(Collectors.joining(";"));
	        		}
	        	}
			}
			
			if(ACTION_TYPE_UNRESERVE.equals(actionType)) {
        		UI.showInfo(Message.getString("common.unreserve_success"));
			}else {
        		if(LotSortingJob.ACTION_TYPE_SPLIT.equals(lotSortingJobs.get(0).getActionType())) {
        			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()) + lots);
        		} else {
        			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
        		}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		super.okPressed();
	}
}
