package com.glory.mes.wip.lot.run.bylot;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotAttributeValue;

public class RunWizardContext  /* implements IWizardContext*/{
	
	public static int OK_ID = 0;
	public static int FAILED_ID = 1;

	private int returnCode;
	
	//����ITrackInCheckУ��,���У��δͨ����δfail
	private int checkCode = OK_ID;

	//TrackIn��TrackOut��TrackMove������ʱѡ�л������������
	private List<Lot> lots;
	
	private List<MLot> mLots;
	
	private List<WorkOrderBomLine> bomLines;
	
	private Step step;
	private List<Equipment> selectEquipments;
	private List<LotAttributeValue> lotAttributeValues;
	private Map<String, List<ComponentUnit>> componentUnitMap = new HashMap<String, List<ComponentUnit>>();
	
	private String operator1;
	private String operator2;
	
	//��ǰ������Ӧ��UseCategory,�ɽ��洫��
	private String useCategory;

	public void setLots(List<Lot> lots) {
		this.lots = lots;
	}

	public List<Lot> getLots() {
		return lots;
	}

	public List<MLot> getmLots() {
		return mLots;
	}

	public void setmLots(List<MLot> mLots) {
		this.mLots = mLots;
	}

	public List<WorkOrderBomLine> getBomLines() {
		return bomLines;
	}

	public void setBomLines(List<WorkOrderBomLine> bomLines) {
		this.bomLines = bomLines;
	}

	public void setStep(Step step) {
		this.step = step;
	}

	public Step getStep() {
		return step;
	}

	public void setSelectEquipments(List<Equipment> selectEquipments) {
		this.selectEquipments = selectEquipments;
	}

	public List<Equipment> getSelectEquipments() {
		return selectEquipments;
	}
	
	public String getCategory() {
		return "";
	}
	
	public List<LotAttributeValue> getLotAttributeValues() {
		return lotAttributeValues;
	}

	public void setLotAttributeValues(List<LotAttributeValue> lotAttributeValues) {
		this.lotAttributeValues = lotAttributeValues;
	}

	public Map<String, List<ComponentUnit>> getComponentUnitMap() {
		return componentUnitMap;
	}

	public void setComponentUnitMap(Map<String, List<ComponentUnit>> componentUnitMap) {
		this.componentUnitMap = componentUnitMap;
	}

	//��ǰTrack out��Movement Next����һ�����Σ���getLots().size() == 1
	//�����Ե�һ��Lot�ж��Ƿ���process unit
	public boolean isProcessUnitable() {
		if(lots != null && lots.size() > 0) {
			return lots.get(0).getSubProcessUnit() == null 
				|| lots.get(0).getSubProcessUnit().size() == 0 ? false : true;
		}
		return false;
	}

	public void setReturnCode(int returnCode) {
		this.returnCode = returnCode;
	}

	public int getReturnCode() {
		return returnCode;
	}

	public int getCheckCode() {
		return checkCode;
	}

	public void setCheckCode(int checkCode) {
		this.checkCode = checkCode;
	}
	
	public String getOperator1() {
		return operator1;
	}

	public void setOperator1(String operator1) {
		this.operator1 = operator1;
	}

	public String getOperator2() {
		return operator2;
	}

	public void setOperator2(String operator2) {
		this.operator2 = operator2;
	}
	
	public String getUseCategory() {
		return useCategory;
	}

	public void setUseCategory(String useCategory) {
		this.useCategory = useCategory;
	}
}
