package com.glory.mes.wip.lot.run.bylot.glc;

import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;

import com.glory.edc.extensionpoints.EdcEvent;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotHistoryManager;
import com.glory.mes.wip.his.LotHis;
import com.glory.mes.wip.lot.history.EdcHisDialog;
import com.glory.mes.wip.lot.history.WIPScrapSBDHisViewDialog;
import com.glory.mes.wip.lot.history.WIPSplitLotInfoDialog;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;

public class LotHisQueryDialog extends GlcBaseDialog{

	private static final String FIELD_LOTHIS_TABLE = "lotHisList";
	private static final String BUTTON_DETAIL_SEARCH = "detailSearch";
	
	protected ListTableManagerField listTableManagerField;
	private Lot lot;
	
	public LotHisQueryDialog(Lot lot, String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
		this.lot = lot;
		this.setBlockOnOpen(false);
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		listTableManagerField = form.getFieldByControlId(FIELD_LOTHIS_TABLE, ListTableManagerField.class);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DETAIL_SEARCH), this::detailAdapter);
		
		init();
	}
	
	private void init() {
		try {
			LotHistoryManager lotHistoryManager = Framework.getService(LotHistoryManager.class);
			List<LotHis> lotHis = lotHistoryManager.getLotHisByLotId(lot.getOrgRrn(), lot.getLotId(), "");
			listTableManagerField.getListTableManager().setInput(lotHis);
			
			listTableManagerField.getListTableManager().addDoubleClickListener(new IMouseAction() {
				@Override
				public void run(NatTable arg0, MouseEvent arg1) {
					detailAdapter(null);
				}				
			});
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void detailAdapter(Object obj) {
		try {
			Shell parent = UI.getActiveShell();
			Object ob = listTableManagerField.getListTableManager().getSelectedObject();
			if (null == ob) {
				UI.showError(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			LotHis line = (LotHis) ob;
			ADManager manager = Framework.getService(ADManager.class);
			LotHis lothis = new LotHis();
			lothis = (LotHis) manager.getEntity(line);
			if (LotStateMachine.TRANS_SCRAPLOT.equals(lothis.getTransType())
					|| LotStateMachine.TRANS_UNSCRAPLOT.equals(lothis.getTransType())) {
				WIPScrapSBDHisViewDialog dialog = new WIPScrapSBDHisViewDialog("WIPScrapSBDHisViewDialog", null, eventBroker, lothis);
				dialog.open();

			} else if (LotStateMachine.TRANS_SPLITLOT.equals(lothis.getTransType())
					|| LotStateMachine.TRANS_MERGELOT.equals(lothis.getTransType())
					|| LotStateMachine.TRANS_SPLITOUT.equals(lothis.getTransType())
					|| LotStateMachine.TRANS_MERGEIN.equals(lothis.getTransType())) {
				WIPSplitLotInfoDialog dialog = new WIPSplitLotInfoDialog("WIPSplitLotInfoDialog", null, eventBroker, lothis);
				dialog.open();

			} else if (LotStateMachine.TRANS_EDC.equals(lothis.getTransType())) {
				edcAdapter(lothis);
			} else {
				UI.showInfo(Message.getString("wip.lotHis_NoTranType"));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected void edcAdapter(LotHis lothis) {
		try {
			Lot lot = new Lot();
			lot.setObjectRrn(lothis.getLotRrn());
			ADManager adManager = Framework.getService(ADManager.class);
			lot = (Lot)adManager.getEntity(lot);
			String condition=" hisSeq = '"+lothis.getHisSeq()+"'";
			List<EdcData> datas=adManager.getEntityList(Env.getOrgRrn(), 
					EdcData.class, Env.getMaxResult(), condition, "");
			EdcSetCurrent edc = new EdcSetCurrent();
			edc.setHistorySeq(lothis.getHisSeq());
			edc.setItemSetRrn(datas.get(0).getEdcSetRrn());
			edc.setEdcFlag(EdcSetCurrent.FLAG_TEMP);
			
			EdcEvent event = new EdcEvent();
			event.setLot(lot);
			
			EdcHisDialog d = new EdcHisDialog(
						Display.getCurrent().getActiveShell(), edc, event, lothis);
				if (d == null)
					return;
				if (d.open() == Dialog.OK) {
				}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
	protected void okPressed() {
		super.okPressed();
	}
	
	@Override
	protected void cancelPressed() {
		super.cancelPressed();
	}
	
	@Override
	 protected Point getInitialSize() {
	    return new Point(1600,800);
	 }
	
}
