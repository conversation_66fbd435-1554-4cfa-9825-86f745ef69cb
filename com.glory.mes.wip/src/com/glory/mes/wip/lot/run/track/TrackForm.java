package com.glory.mes.wip.lot.run.track;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Rectangle;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.mes.wip.lot.run.track.TrackContext;
import com.glory.mes.wip.lot.run.track.extensionpoints.ITrackForm;

public class TrackForm implements ITrackForm {

	protected TrackContext context;
	
	protected IEventBroker broker;
	
	protected FFormToolKit toolKit;
	
	@Override
	public void initialize(TrackContext context) {
		this.context = context;
	}

	@Override
	public Composite createForm(Composite parent) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public TrackContext saveToObject() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean validate() {
		// TODO Auto-generated method stub
		return false;
	}
	
	@Override
	public void setBroker(IEventBroker broker) {
		this.broker = broker;
	}
	
	@Override
	public void setToolKit(FFormToolKit toolKit) {
		this.toolKit = toolKit;
	}
	
	@Override
	public void setFieldFont(String name, int height) {}

	@Override
	public void load() {}
}
