package com.glory.mes.wip.lot.run.byeqp.glc;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.RGB;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;

import com.glory.edc.EdcEntry;
import com.glory.edc.client.EDCManager;
import com.glory.edc.collection.EdcSetCurrentComponentDialog;
import com.glory.edc.collection.EdcSetCurrentDialog;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.edc.model.EdcTecn;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADForm;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.extensionpoints.WizardPageExtensionPoint;
import com.glory.framework.base.ui.forms.field.BooleanField;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.core.chain.ChainContext;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.client.RASManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.model.state.RasState;
import com.glory.mes.ras.port.Port;
import com.glory.mes.wip.Activator;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.LotPrepareManager;
import com.glory.mes.wip.custom.ByEqpEqpTreeCustomComposite;
import com.glory.mes.wip.custom.LotListComposite;
import com.glory.mes.wip.lot.contextmenu.LotContextAction;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.lot.run.abort.AbortDialog;
import com.glory.mes.wip.lot.run.abort.AbortWizard;
import com.glory.mes.wip.lot.run.byeqp.extensionpoint.DefaultAction;
import com.glory.mes.wip.lot.run.byeqp.extensionpoint.IByEqpAction;
import com.glory.mes.wip.lot.run.byeqp.extensionpoint.LotRunByEqpExtensionPoint;
import com.glory.mes.wip.lot.run.extensionpoints.OperationActionExtensionPoint;
import com.glory.mes.wip.lot.run.extensionpoints.OperationContext;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInDialog;
import com.glory.mes.wip.lot.run.trackin.TrackInWizard;
import com.glory.mes.wip.lot.run.trackin.extensionpoints.TrackInCheckExtensionPoint;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotBatchJob;
import com.glory.mes.wip.model.LotPrepare;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.track.model.InContext;
import com.glory.mes.wip.util.ByEqpUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

public class ByEqpManagerDefault extends ByEqpManager {
	
	private static final Logger logger = Logger.getLogger(ByEqpManagerDefault.class);
	
	@Override
	protected void byEqpFormActiom() {
		this.byEqpEditor = getContext().getEqpEditor();
		this.form = getContext().getMainGlcForm();
		this.partService = getContext().getPartService();
		this.modelService = getContext().getModelService();
		this.eventBroker = getContext().getEventBroker();
		this.console = getContext().getConsole();
		
		init();
		addKeyListener();
	}

	@Override
	protected void init() {
		itemEqpInfo = (ToolItem) form.getButtonByControl(null, ByEqpManagerContext.BUTTON_EQPINFO);
		itemTrackIn = (ToolItem) form.getButtonByControl(ByEqpManagerContext.FIELD_LOTFORM, ByEqpManagerContext.BUTTON_TRACKIN);
		itemDcop = (ToolItem) form.getButtonByControl(ByEqpManagerContext.FIELD_LOTFORM, ByEqpManagerContext.BUTTON_DOCP);
		itemTrackOut = (ToolItem) form.getButtonByControl(ByEqpManagerContext.FIELD_LOTFORM, ByEqpManagerContext.BUTTON_TRACKOUT);
		itemAbort = (ToolItem) form.getButtonByControl(ByEqpManagerContext.FIELD_LOTFORM, ByEqpManagerContext.BUTTON_ABORT);
		itemRunRefresh = (ToolItem) form.getButtonByControl(ByEqpManagerContext.FIELD_LOTFORM, ByEqpManagerContext.BUTTON_RUNREFRESH);
		itemPrepare = (ToolItem) form.getButtonByControl(ByEqpManagerContext.FIELD_LOTFORM, ByEqpManagerContext.BUTTON_PREPARE);

		itemEqpInfo.setEnabled(false);
		itemDcop.setEnabled(false);
		itemTrackOut.setEnabled(false);
		if (itemTrackIn != null) {
			itemTrackIn.setEnabled(false);
		}	
		itemAbort.setEnabled(false);
		itemPrepare.setEnabled(false);

		dispatchField = form.getFieldByControlId(
				ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  ByEqpManagerContext.FIELD_DISPATCHFLAG, BooleanField.class);
		if (dispatchField != null) {
			dispatchField.addValueChangeListener(new IValueChangeListener() {
				
				@Override
				public void valueChanged(Object sender, Object newValue) {
					refresh(2);
				}
			});
		}
		
		fieldEqpTree = form.getFieldByControlId(ByEqpManagerContext.FIELD_EQPTREE, CustomField.class);
		fieldLotForm = form.getFieldByControlId(ByEqpManagerContext.FIELD_LOTFORM, GlcFormField.class);

		fieldRunning = form.getFieldByControlId(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  ByEqpManagerContext.FIELD_RUNNINGLOTS, CustomField.class);
		fieldWaitting = form.getFieldByControlId(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + ByEqpManagerContext.FIELD_WAITTINGLOTS, CustomField.class);
		fieldEqp = form.getFieldByControlId(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  ByEqpManagerContext.FIELD_EQUIPMENT_ID, TextField.class);
		fieldLot = form.getFieldByControlId(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  ByEqpManagerContext.FIELD_LOT_ID, TextField.class);
		fieldCarrier = form.getFieldByControlId(ByEqpManagerContext.FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR +  ByEqpManagerContext.FIELD_CARRIER_ID, TextField.class);	
		
		IByEqpAction byEqpAction = getContext().getCurrentAction();
		if (byEqpAction != null) {
			byEqpAction.initExtend(getContext().getEqpEditor(), form);
			byEqpAction.subscribeExtend(getContext().getEventBroker(), form);
			byEqpAction.setModelService(modelService);
			byEqpAction.setPartService(partService);
		}
		
		if (fieldEqp != null && getContext().getCurrentEqp() != null) {
			fieldEqp.setValue(getContext().getCurrentEqp().getEquipmentId());
			fieldEqp.refresh();
		}
	}

	/**
	 * ��ҵ׼��
	 * @param obj
	 */
	@Override
	protected void prepareAdapter(Object obj) {
		Equipment currentEqp = getContext().getCurrentEqp();
		boolean eqpAvailable = getContext().isEqpAvailable();
		if (currentEqp != null && eqpAvailable) {
			try {
				OperationContext context = new OperationContext();
				context.setParentObject(this);
				context.setEquipments(Lists.newArrayList(currentEqp));
				context = OperationActionExtensionPoint.executeOperationAction(context, OperationActionExtensionPoint.CHECK_POINT_PREPARE_DIALOG);
				if (context.isIgnoreStandardMethod()) {
					return ;
				}
				
				// ��ҵ׼��Dialog
				ByEqpPrepareDialog dialog = new ByEqpPrepareDialog("WIPLotPrepare", null, eventBroker);
				dialog.setRTDAvailable(isRTDAvailable());
				Map<String, Object> propValues = Maps.newHashMap();
				// ��ҵ׼����¼
				LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
				List<LotPrepare> lotPrepares = prepareManager.getPrepareJobs(
						Env.getOrgRrn(), currentEqp.getEquipmentId(), null, true);
				propValues.put("prepareList", lotPrepares);
				// �豸��Ϣ
				propValues.put("leftFrom-equipmentInfo", currentEqp);
				
				// ��������ҵ׼��������
				LotManager lotManager = Framework.getService(LotManager.class);
				List<Lot> waittingLots = Lists.newArrayList();
				if (isRTDAvailable()) {
					waittingLots = ByEqpUtil.getRtdLots(currentEqp, false);
				} else {
					waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), currentEqp.getObjectRrn(),
							LotStateMachine.STATE_WAIT, Env.getSessionContext());
				}
				
				waittingLots = ByEqpUtil.filterWaitingLots(waittingLots, currentEqp);
				propValues.put("leftFrom-lotList", waittingLots);
				dialog.setPropValues(propValues);
				dialog.setCloseAdaptor(new Consumer<ByEqpPrepareDialog>() {
					
					@Override
					public void accept(ByEqpPrepareDialog t) {
						refresh(2);
					}
				});
				dialog.open();
			} catch (Exception e) {
				logger.error("Error at ByEqpManagerDefault : prepareAdapter() ", e);
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
		
	}

	/**
	 * �豸�ؼ��س��¼�
	 * @param obj
	 */
	@Override
	protected void equipmentEnterPressed(Object obj) {
		try {
			Event event = (Event) obj;
			String equipmentId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			RASManager rasManager = Framework.getService(RASManager.class);
			Equipment equipment = rasManager.getEquipmentByEquipmentId(Env.getOrgRrn(), equipmentId, false);
			if (equipment != null) {
				ByEqpEqpTreeCustomComposite composite = ((ByEqpEqpTreeCustomComposite)fieldEqpTree.getCustomComposite());
				composite.doSelection(equipment);
			} else {
				getContext().setCurrentEqp(null);
				if (console != null && !console.isDisposed()) {
					console.error(Message.getString("ras.equipment_no_found"));
				}
			}
		} catch (Exception e) {
			getContext().setCurrentEqp(null);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}

	/**
	 * �ؾ߿ؼ��س��¼�
	 * @param obj
	 */
	@Override
	protected void carrierEnterPressed(Object obj) {
		try {
			Equipment currentEqp = getContext().getCurrentEqp();
			if (currentEqp == null) {
				if (console != null) {
					console.error(Message.getString("ras.equipment_select_eqp"));
				}
				return;
			}
			
			Event event = (Event) obj;
			String carrierId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);	
			DurableManager durableManager = Framework.getService(DurableManager.class);
			Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId);		
			if (carrier != null) {
				CarrierLotManager carrierLotManager = Framework.getService(CarrierLotManager.class);
				List<Lot> lots = carrierLotManager.getLotsByCarrierId(Env.getOrgRrn(), carrierId);		
				if (lots != null && lots.size() > 0) {	
					Lot lot = lots.get(0);
					searchLot(lot);
				} else {
					if (console != null) {
						console.error(Message.getString("wip.carrier_mainlot_is_not_found"));
					}
				}
			} else {
				if (console != null) {
					console.error(Message.getString("mm.durable_not_exist"));
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpManagerDefault : carrierEnterPressed() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}

	/**
	 * ���οؼ��س��¼�
	 * @param obj
	 */
	@Override
	protected void lotEnterPressed(Object obj) {
		try {
			Equipment currentEqp = getContext().getCurrentEqp();
			if (currentEqp == null) {
				if (console != null) {
					console.error(Message.getString("ras.equipment_select_eqp"));
				}
				return;
			}
			
			Event event = (Event) obj;
			String lotId = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
			Lot lot = LotProviderEntry.getLotByLine(lotId);
			if (lot != null) {
				searchLot(lot);
			} else {
				if (console != null) {
					console.error(Message.getString("wip.lot_is_not_exist"));
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpManagerDefault : lotEnterPressed() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}	
		
	}

	@Override
	protected void searchLot(Lot lot) throws Exception {
		ListTableManager tableManager = 
				((LotListComposite)fieldWaitting.getCustomComposite()).getTableManager();
		// �������б����ҵ�������
		List<? extends Object> inputLots = tableManager.getInput();
		Optional<? extends Object> f = inputLots.stream().filter(
				o -> ((Lot)o).getLotId().equals(lot.getLotId())).findFirst();
		if (!f.isPresent()) {
			if (console != null) {
				console.error(Message.getString("wip.byeqp_scan_lot_not_exist"));
			}
		} else {
			// �ҵ�����
			Lot findLot = (Lot) f.get();
			List<Lot> lots = null;
			if (LotStateMachine.STATE_DISP.equals(lot.getState())) {
				Equipment currentEqp = getContext().getCurrentEqp();
				// ����Prepare����
				LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
				lots = prepareManager.getPrepareGroupLots(
						currentEqp.getEquipmentId(), findLot.getLotId(), null, Env.getSessionContext());
			} else if (!StringUtil.isEmpty(lot.getBatchId())) {
				// ����batch����
				lots = inputLots.stream().filter(o -> lot.getBatchId().equals(((Lot)o).getBatchId()))
						.map(o -> ((Lot)o)).collect(Collectors.toList());
			} else {
				// ��������
				lots = Lists.newArrayList(findLot);
			}
			// ѡ��
			tableManager.setSelection(new StructuredSelection(lots.toArray()));
			// Check
			fieldWaitting.postEvent(fieldWaitting.getFullTopic(LotListComposite.EVENT_SETCHECK), lots);
			//((LotListTableManagerCustomComposite)fieldWaitting.getCustomComposite()).setCheckedLots(lots);
		}
	}

	@Override
	protected void runningSelectionAdaptor(Object obj) {
		Event event = (Event) obj;
		Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
		if (lot != null) {
			lotStatusChanged(lot.getState(), lot.getHoldState());
		} else {
			itemDcop.setEnabled(false);
			itemTrackOut.setEnabled(false);
			itemAbort.setEnabled(false);
		}
	}

	@Override
	protected void waitingSelectionAdaptor(Object obj) {
		try {
			Event event = (Event) obj;
			Lot lot = (Lot) event.getProperty(GlcEvent.PROPERTY_DATA);
			if (lot != null) {
				lotStatusChanged(lot.getState(), lot.getHoldState());				
				Equipment currentEqp = getContext().getCurrentEqp();
				if (console != null && currentEqp != null) {
					/*//�����豸recipe
					LotManager lotManager = Framework.getService(LotManager.class);
					List<Lot> lots = lotManager.calculatePPID(Lists.newArrayList(lot), currentEqp, Env.getSessionContext());
					if (CollectionUtils.isNotEmpty(lots)) {
						if (StringUtil.isEmpty(lots.get(0).getEquipmentRecipe())) {
							console.info("PPID: Not Found !");
						} else {
							console.info("PPID: " + lot.getEquipmentRecipe());
						}
					} else {
						console.info("PPID: Not Found !");
					}
					
					//����̨��ʾ�Ƽ�recitle
					if (!StringUtil.isEmpty(lot.getMask())) {
						try {			
							String maskStr[] = lotManager.getLotEquipmentReticle(currentEqp.getEquipmentId(), lot, true, true);
							if (maskStr != null && maskStr.length > 0) {	
								console.info(Message.getString("ras.recommend.reticle_id")+": "+maskStr[0]);
							}										
						} catch (Exception e) {
							console.error(Message.getString(e.toString()));
						}
					}*/
					ByEqpUtil.noticeEquipmentRecipeAndReticle(lot, currentEqp, console);
				}
			} else {
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(false);
				}
			}
			
			ListTableManager tableManager = 
					((LotListComposite)fieldWaitting.getCustomComposite()).getTableManager();
			List<Object> objects = tableManager.getCheckedObject();
			List<Lot> lots = objects.stream().map(o -> (Lot)o).collect(Collectors.toList());
			Optional<Lot> f = lots.stream().filter(r -> Lot.HOLDSTATE_ON.equals(r.getHoldState())).findFirst();
			if (f.isPresent()) {
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(false);
				}
			} else {
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(true);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	
	@Override
	protected void lotStatusChanged(String state, String holdState) {
		if (!getContext().isEqpAvailable()) {
			if (itemTrackIn != null) {
				itemTrackIn.setEnabled(false);
			}
			if (Lot.HOLDSTATE_ON.equals(holdState)) {
				itemDcop.setEnabled(false);
				itemTrackOut.setEnabled(false);
				itemAbort.setEnabled(false);
				
				if (LotStateMachine.STATE_RUN.equals(state)) {
					itemAbort.setEnabled(true);
				}
			} else if (LotStateMachine.STATE_WAIT.equals(state) || LotStateMachine.STATE_DISP.equals(state)) {
				itemDcop.setEnabled(false);
				itemTrackOut.setEnabled(false);
				itemAbort.setEnabled(false);
			} else if (LotStateMachine.STATE_RUN.equals(state)) {
				LotListComposite composite = (LotListComposite) fieldRunning
						.getCustomComposite();
				Lot lot = (Lot) composite.getSelectedObject();
				boolean docpFlag = false;
				if (lot != null) {
					List<EdcSetCurrent> currents = getEdcSetCurrent(lot);
					if (currents != null && currents.size() > 0) {	
						docpFlag = true;
					}
				}
				itemDcop.setEnabled(docpFlag);					
				itemTrackOut.setEnabled(true);
				itemAbort.setEnabled(true);
			} else {
				itemDcop.setEnabled(false);
				itemTrackOut.setEnabled(false);
				itemAbort.setEnabled(false);
			}		
		} else {
			if (Lot.HOLDSTATE_ON.equals(holdState)) {
				itemDcop.setEnabled(false);
				itemTrackOut.setEnabled(false);
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(false);
				}
				itemAbort.setEnabled(false);
				
				if (LotStateMachine.STATE_RUN.equals(state)) {
					itemAbort.setEnabled(true);
				}
			} else if (LotStateMachine.STATE_WAIT.equals(state) || LotStateMachine.STATE_DISP.equals(state)) {
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(true);
				}
				itemDcop.setEnabled(false);
				itemTrackOut.setEnabled(false);
				itemAbort.setEnabled(false);
			} else if (LotStateMachine.STATE_RUN.equals(state)) {
				LotListComposite composite = (LotListComposite) fieldRunning
						.getCustomComposite();
				Lot lot = (Lot) composite.getSelectedObject();
				boolean docpFlag = false;
				if (lot != null) {
					List<EdcSetCurrent> currents = getEdcSetCurrent(lot);
					if (currents != null && currents.size() > 0) {	
						docpFlag = true;
					}
				}
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(false);
				}
				itemDcop.setEnabled(docpFlag);					
				itemTrackOut.setEnabled(true);
				itemAbort.setEnabled(true);
			} else {
				itemDcop.setEnabled(false);
				itemTrackOut.setEnabled(false);
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(false);
				}
				itemAbort.setEnabled(false);
			}
		}
	}

	@Override
	protected void eqpSelectionChangeAdapter(Object obj) {
		Event event = (Event) obj;
		Equipment equipment = (Equipment) event.getProperty(GlcEvent.PROPERTY_DATA);
		getContext().setCurrentEqp(equipment);
		
		fieldEqp.setText(equipment.getEquipmentId());
		fieldLot.setText(null);
		fieldCarrier.setText(null);
		eqpSelectionChanged();
		
	}

	@Override
	protected void eqpSelectionChanged() {
		try {
			Equipment currentEqp = getContext().getCurrentEqp();
			// �豸�л�ʱ�������豸�����л�����Ӧ�Ķ�̬ҳ��
			boolean swiched = switchView();
			if (swiched) {
				getContext().getEqpEditor().registerByEqpManager(getContext().getCurrentAction().getManager(), false);
				getContext().getEqpEditor().eqpSelectionChanged();
				return;
			}
			
			if (currentEqp == null) {
				itemEqpInfo.setEnabled(false);
				
				if (itemPrepare != null && !itemPrepare.isDisposed()) {
					itemPrepare.setEnabled(false);
				}
				
				return;
			}
			
			itemEqpInfo.setEnabled(true);
			if (itemPrepare != null && !itemPrepare.isDisposed()) {
				itemPrepare.setEnabled(true);
			}
			
			refresh(1);
			
			RASManager ras = Framework.getService(RASManager.class);
			RasState state = ras.getState(Env.getOrgRrn(), currentEqp.getState());
			getContext().setEqpAvailable(state.getIsAvailable());
			if (!state.getIsAvailable()) {
				if (console != null && !console.isDisposed()) {
					String message = "";
					message += "[" + currentEqp.getEquipmentId() + "];";
					message += Message.getString("byeqp.runninglot_eqpisnotavailable") + currentEqp.getState();
					console.error(message);
				}
			}
			
			if (console != null && !console.isDisposed() && getContext().getHoldLots() != null && getContext().getHoldLots().size() > 0) {
				String message = "";			
				message += Message.getString("wip.lot_current_state_is_hold");
				for (Lot slot : getContext().getHoldLots() ) {
					message += "[" + slot.getLotId() + "];";
				}
				console.info(message);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	protected void eqpInfoAdapter(Object obj) {
		Equipment currentEqp = getContext().getCurrentEqp();
		if (currentEqp != null) {
			try {
				// �豸��ϸ��Dialog
				GlcBaseDialog dialog = new GlcBaseDialog("RASEquipmentInfo", null, eventBroker);
				Map<String, Object> propValues = Maps.newHashMap();
				ADManager adManager = Framework.getService(ADManager.class);
				currentEqp = (Equipment)adManager.getEntity(currentEqp);
				propValues.put("equipmentInfo", currentEqp);
				
				// ���豸��Ϣ
				RASManager rasManager = Framework.getService(RASManager.class);
				List<Equipment> subEqps = rasManager.getSubEquipments(Env.getOrgRrn(), currentEqp.getEquipmentId());
				propValues.put("otherInfo-subEquipments", subEqps);
				
				// �˿���Ϣ
				List<Port> ports = rasManager.getPortsByEquipment(Env.getOrgRrn(), currentEqp.getEquipmentId(), false);
				propValues.put("otherInfo-ports", ports);
				dialog.setPropValues(propValues);
				dialog.open();
			} catch (Exception e) {
				logger.error("Error at ByEqpEditor : eqpInfoAdapter() ", e);
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}

	@Override
	protected void eqpRefreshAdapter(Object obj) {
		fieldEqpTree.refresh();
		getContext().setCurrentEqp(null);
		refresh(1);
		eqpSelectionChanged();
	}

	@Override
	protected void trackInAdapter(Object obj) {
		try {
			Event event = (Event) obj;
			String operator1 = Env.getUserName();
			if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}
			
			Equipment currentEqp = getContext().getCurrentEqp();
			boolean eqpAvailable = getContext().isEqpAvailable();
			if (currentEqp == null || !eqpAvailable) {
				return;
			}

			LotListComposite composite = (LotListComposite) fieldWaitting
					.getCustomComposite();

			List<Lot> lots = (List)composite.getCheckedObjects();
			if (CollectionUtils.isEmpty(lots)) {
				return;
			}
			for (Lot lot : lots) {
				lot.setEquipmentId(currentEqp.getEquipmentId());
			}

			if (lots == null || lots.size() == 0) {
				UI.showError(Message.getString("common.byEquipment_check_trackIn_lot"));
				return;
			}

			if (!currentEqp.getIsBatch() && lots.size() > 1) {
				UI.showError(Message.getString("wip.trackin_cannot_batch"));
				return;
			}
			
			// ���Prepare
			LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
			List<LotPrepare> prepares = prepareManager.getPrepareJobs(Env.getOrgRrn(), currentEqp.getEquipmentId(), null, false);
			if (CollectionUtils.isNotEmpty(prepares)) {
				// ���ȼ���Ƿ�����С����ҵ������
				Map<String, List<LotPrepare>> prepareMap = prepares.stream().collect(Collectors.groupingBy(LotPrepare::getJobId));
				
				Optional<String> f = prepareMap.keySet().stream().min(Comparator.comparingLong(Long::valueOf));
				if (f.isPresent()) {
					List<LotPrepare> nextPrepareBatch = prepareMap.get(f.get());
					boolean matchFlag = false;
					// ����Ƿ�ƥ�����Σ�ֻҪƥ����䵽һ�����κž�ͨ��
					for (LotPrepare lotPrepare : nextPrepareBatch) {
						boolean thisMatch = false;
						for (Lot lot : lots) {
							if (lotPrepare.getLotId().equals(lot.getLotId())) {
								thisMatch = true;
								break;
							}
						}
						
						matchFlag = matchFlag || thisMatch;
					}
					
					if (!matchFlag) {
						if (!UI.showConfirm(Message.getString("wip.byeqp_prepare_rule_check"))) {
							return;
						}
					}
				}
				
				// ����Prepare��վ����
				Set<String> jobIds = Sets.newHashSet();
				int i = 0;
				for (LotPrepare lotPrepare : prepares) {
					for (Lot lot : lots) {
						if (lotPrepare.getLotId().equals(lot.getLotId())) {
							jobIds.add(lotPrepare.getJobId());
							i++;
							break;
						}
					}
				}
				
				if (jobIds.size() > 1) {
					// ����������ҵ׼������ͬʱ��վ
					UI.showError(Message.getString("wip.byeqp_prepare_job_multi_error"));
					return;
				} else if (jobIds.size() == 1 && i != lots.size()) {
					// ��������ҵ׼����������������������ͬʱ��վ
					UI.showError(Message.getString("wip.byeqp_prepare_mix"));
					return;
				} else {
					// û��Job��������
				}
			}

			TrackInContext context = new TrackInContext();
			context.setTrackInType(TrackInContext.TRACK_IN_BYEQP);
			List<Equipment> equipments = new ArrayList<Equipment>();
			ADManager adManager = Framework.getService(ADManager.class);
			currentEqp = (Equipment) adManager.getEntity(currentEqp);
			equipments.add(currentEqp);
			context.setSelectEquipments(equipments);
			context.setLots(lots);
			context.setOperator1(operator1);

			LotManager lotManager = Framework.getService(LotManager.class);
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lots.get(0).getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);

			//ѡ�豸���վʱ�����йؼ���
			context = TrackInCheckExtensionPoint.executeTrackInCheck(context, TrackInCheckExtensionPoint.CHECK_POINT_SELECTEQP);
			if (TrackInContext.FAILED_ID == context.getCheckCode()) {
				return;
			}
			
			InContext inContext = new InContext();
			if (!currentEqp.getIsBatch()) {
				String batchId = lots.get(0).getBatchId();
				if (!StringUtil.isEmpty(batchId)) {
					List<LotBatchJob> batchJobs = lotManager.getBatchJobsByBatchId(Env.getOrgRrn(), batchId);
					if (CollectionUtils.isNotEmpty(batchJobs)) {
						List<Lot> bathcLots = lotManager.getLotsByBatch(Env.getOrgRrn(), batchId);
						
						inContext.setMeasureLot(lots.get(0));
						inContext.setMeasure(true);
						inContext.setLots(bathcLots);
						
						context.setMeasureLot(lots.get(0));
						context.setMeasure(true);
						context.setLots(bathcLots);
					} else {
						UI.showInfo(Message.getString("wip.trackin_cannot_batch"));
						return;
					}
				}
			}
			
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);

			ChainContext wipContext = lotManager.checkTrackInConstraint(inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null && wipContext.getReturnMessage().trim().length() > 0) {
				if (console != null) {
					if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
						console.error(Message.formatString(wipContext.getReturnMessage()));
					} else {
						console.info(Message.formatString(wipContext.getReturnMessage()));
					}
				}
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				refresh(1);
				return;
			}
			
			ChainContext checkAutoFutureMergeContext = lotManager.checkAutoFutureMergeConstraint(inContext
					, Env.getSessionContext());
			if (checkAutoFutureMergeContext.getReturnMessage() != null
					&& checkAutoFutureMergeContext.getReturnMessage().trim().length() > 0) {
				UI.showError(checkAutoFutureMergeContext.getReturnMessage());
			}
			if (checkAutoFutureMergeContext.getReturnCode() == ChainContext.FAILED_ID) {
				refresh(1);
				return;
			}

			FlowWizard wizard = WizardPageExtensionPoint.getWizardRegistry().get(step.getTrackInFlow());
			if (wizard instanceof TrackInWizard) {
				((TrackInWizard) wizard).setContext(context);
			}
			TrackInDialog dialog = new TrackInDialog(Display.getCurrent().getActiveShell(), wizard);
			int result = dialog.open();
			if ((result == Dialog.OK || result == TrackInDialog.FIN)
					&& context.getReturnCode() == TrackInContext.OK_ID) {
				if (console != null) {
					String message = "";
					for (Lot slot : context.getLots()) {
						if (message.isEmpty()) {
							message += "[" + slot.getLotId() + "]";
						} else {
							message += ";[" + slot.getLotId() + "]";
						}
					}
					message += " TrackIn Successful! ";
					console.info(message);
				}
				refresh(1);
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : trackInAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	
	@Override
	protected void abortAdapter(Object obj) {
		try {
			Event event = (Event) obj;
			String operator1 = Env.getUserName();
			if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}
			form.getMessageManager().removeAllMessages();
			InContext context = new InContext();
			
	
			LotListComposite composite = (LotListComposite) fieldRunning
					.getCustomComposite();
			Lot lot = (Lot) composite.getSelectedObject();
			if (lot == null) {
				return;
			}
			
			List<Lot> lots = getRunningLotList(lot);
			context.setLots(lots);
	
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lot.getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
	
			context.setCurrentStep(step);
			context.setOperator1(operator1);
			
			FlowWizard wizard = WizardPageExtensionPoint.getWizardRegistry().get(step.getAbortFlow());
			if (wizard instanceof AbortWizard) {
				((AbortWizard) wizard).setContext(context);
			}
	
			AbortDialog dialog = new AbortDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == AbortDialog.FIN) {
				refresh(1);
				if (console != null) {
					String message = "";
					for (Lot slot : context.getLots()) {
						if (message.isEmpty()) {
							message += "[" + slot.getLotId() + "]";
						} else {
							message += ";[" + slot.getLotId() + "]";
						}
					}
					message += " Abort Successful! ";
					console.info(message);
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : abortAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void docpAdapter(Object obj) {
		try {
			Equipment currentEqp = getContext().getCurrentEqp();
			if (currentEqp == null) {
				return;
			}

			LotListComposite composite = (LotListComposite) fieldRunning
					.getCustomComposite();
			Lot lot = (Lot) composite.getSelectedObject();
			
			List<EdcSetCurrent> currents = getEdcSetCurrent(lot);
			if (currents != null && currents.size() > 0) {				
				if (currents.size() == 1 
						&& !EdcSetCurrent.FLAG_DONE.equals(currents.get(0).getEdcFlag())
						&& !EdcSetCurrent.FLAG_PASS.equals(currents.get(0).getEdcFlag())){
					//�����жϣ����Batch�ɼ�ʱ�����ͬBatch��EDCTECN������Ƿ�ѡ��������
					if(!checkBatchEdcTecn(lot)) {
						return;
					}
					
					//��ֻ��һ��EDCʱ����δ���ʱ
					int result = EdcEntry.open(EdcData.EDCFROM_LOT, currents.get(0), null, lot);
					if (result == Dialog.OK) {
						refresh(1);
					}
				} else {
					boolean flag = false;
					for (EdcSetCurrent current : currents) {
						if (!StringUtil.isEmpty(current.getComponentUnitId())) {
							flag = true;
							break;
						}
					}
					EdcSetCurrentDialog edcSetCurrentDialog = null;
					if (flag) {
						edcSetCurrentDialog = new EdcSetCurrentComponentDialog(UI.getActiveShell(), lot, currents);
						
					} else {
						edcSetCurrentDialog = new EdcSetCurrentDialog(UI.getActiveShell(), lot, currents);	
					}
					edcSetCurrentDialog.open();
					if (edcSetCurrentDialog.getReturnCode() == Dialog.OK) {
						refresh(1);
						if (console != null) {
							String message = "";
							message += "[" + lot.getLotId() + "];";
							message += " Data Collection Successful! ";
							console.info(message);
						}
					}
				}
			} else {
				UI.showWarning(Message.getString("edc.alert_message"), Message
						.getString("edc.alert_message_title"));
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : docpAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	
	@Override
	protected void trackOutAdapter(Object obj) {
		try {
			Event event = (Event) obj;
			String operator1 = Env.getUserName();
			if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				operator1 = (String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1);
			}
			form.getMessageManager().removeAllMessages();
			TrackOutContext context = new TrackOutContext();
			context.setTrackOutType(TrackOutContext.TRACK_OUT_BYLOT);
	
			LotListComposite composite = (LotListComposite) fieldRunning
					.getCustomComposite();
			Lot lot = (Lot) composite.getSelectedObject();
			if (lot == null) {
				return;
			}
			
			List<Lot> lots = getRunningLotList(lot);
			context.setLots(lots);
			context.setOperator1(operator1);

			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lot.getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			context.setStep(step);
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkTrackOutConstraint(inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
	
			FlowWizard wizard = WizardPageExtensionPoint.getWizardRegistry()
					.get(step.getTrackOutFlow());
			if (wizard instanceof TrackOutWizard) {
				((TrackOutWizard) wizard).setContext(context);
			}
			TrackOutDialog dialog = new TrackOutDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == TrackOutDialog.FIN) {
				refresh(1);
				UI.showInfo(Message.getString("wip.trackout_success"));
				if (console != null) {
					String message = "";
					for (Lot slot : context.getOutLots()) {
						if (message.isEmpty()) {
							message += "[" + slot.getLotId() + "]";
						} else {
							message += ";[" + slot.getLotId() + "]";
						}
					}
					message += " TrackOut Successful! ";
					console.info(message);
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : trackOutAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	protected void refresh(int index) {
		try {
			Equipment currentEqp = getContext().getCurrentEqp();
			if (currentEqp != null && currentEqp.getObjectRrn() != null) {
				LotManager lotManager = Framework.getService(LotManager.class);
				LotPrepareManager prepareManager = Framework.getService(LotPrepareManager.class);
				
				if (1 == index || 2 == index) {
					List<Lot> dispLots = prepareManager.getSortedLotsByEquipmentId(Env.getOrgRrn(), currentEqp.getEquipmentId());
					
					List<Lot> waittingLots = Lists.newArrayList();
					if (isRTDAvailable()) {
						waittingLots = ByEqpUtil.getRtdLots(currentEqp);
					} else {
						waittingLots = lotManager.getLotsByEqp(Env.getOrgRrn(), currentEqp.getObjectRrn(),
								LotStateMachine.STATE_WAIT, Env.getSessionContext());
					}
					waittingLots.removeAll(dispLots);
					dispLots.addAll(waittingLots);
					getContext().setHoldLots(filterHoldLots(dispLots));
					
					List<Lot> allLots = ByEqpUtil.filterWaitingLots(dispLots, currentEqp);
					allLots.addAll(getContext().getHoldLots());
					fieldWaitting.setValue(allLots);
					fieldWaitting.refresh();
				} 
				
				if (1 == index || 3 == index) {
					List<Lot> runningLots = lotManager.getRunningLotsByEqp(Env.getOrgRrn(), currentEqp.getEquipmentId(), false, false);
					fieldRunning.setValue(runningLots);
					fieldRunning.refresh();
				}
			} else {
				clear();
			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : refresh() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void clear() {
		fieldWaitting.setValue(Lists.newArrayList());
		fieldWaitting.refresh();
		getContext().setHoldLots(Lists.newArrayList());
		fieldRunning.setValue(Lists.newArrayList());
		fieldRunning.refresh();
		
		if (itemTrackIn != null) {
			itemTrackIn.setEnabled(false);
		}
		itemDcop.setEnabled(false);
		itemTrackOut.setEnabled(false);
		itemAbort.setEnabled(false);
		
		ListTableManager runningTableManager = 
				((LotListComposite)fieldRunning.getCustomComposite()).getTableManager();
		ListTableManager waittingTableManager = 
				((LotListComposite)fieldWaitting.getCustomComposite()).getTableManager();
		runningTableManager.getTableManager().setSelectedObject(null);
		waittingTableManager.getTableManager().setSelectedObject(null);
		
		fieldEqp.setText(null);
		fieldLot.setText(null);
		fieldCarrier.setText(null);
	}

	@Override
	protected void runRefreshAdapter(Object obj) {
		refresh(3);
	}
	
	@Override
	protected void waitRefreshAdapter(Object obj) {
		refresh(2);
	}

	@Override
	protected void addKeyListener() {
		LotListComposite wComposite = (LotListComposite) fieldWaitting
				.getCustomComposite();
		wComposite.getTableManager().getNatTable().addListener(SWT.KeyDown, new Listener() {
			@Override
			public void handleEvent(org.eclipse.swt.widgets.Event event) {
				switch (event.keyCode) {
				case SWT.F3:
					if (itemTrackIn != null && itemTrackIn.isEnabled()) {
						trackInAdapter(null);
					}
					break;
				case SWT.F8:
					refresh(2);
					break;
				}
			}
			
		});
		
		LotListComposite rComposite = (LotListComposite) fieldRunning
				.getCustomComposite();
		rComposite.getTableManager().getNatTable().addListener(SWT.KeyDown, new Listener() {
			public void handleEvent(org.eclipse.swt.widgets.Event e) {
				switch (e.keyCode) {
				case SWT.F4:
					if (itemDcop != null && itemDcop.isEnabled()) {
						docpAdapter(null);
					}
					break;
				case SWT.F5:
					if (itemTrackOut != null && itemTrackOut.isEnabled()) {
						trackOutAdapter(null);
					}
					break;
				case SWT.F6:
					if (itemAbort != null && itemAbort.isEnabled()) {
						abortAdapter(null);
					}
					break;
				case SWT.F8:
					if (itemRunRefresh != null && itemRunRefresh.isEnabled()) {
						refresh(3);
					}
					break;
				}
			}
		});
		
		// ͨ��ˢ�½���ȼ�ʧЧ����
		wComposite.getTableManager().getNatTable().refresh();
		rComposite.getTableManager().getNatTable().refresh();
	}

	@Override
	protected boolean switchView() {
		Equipment currentEqp = getContext().getCurrentEqp();
		IByEqpAction currentAction = getContext().getCurrentAction();
		
		if (currentEqp == null) {
			// �л���Ĭ�Ͻ���
			if (DefaultAction.ADFORM_NAME.equals(currentAction.getADFormName())) {
				return false;
			}
			
			ADForm adForm = getContext().getAdManager().getADForm(Env.getOrgRrn(), DefaultAction.ADFORM_NAME);
			fieldLotForm.reflow(adForm);
			// ����init
			getContext().setCurrentAction(LotRunByEqpExtensionPoint.getMainAction());
			
			return true;
		} else {
			String eqpType = currentEqp.getEqpType();
			IByEqpAction byEqpAction = getByEqpAction(eqpType);
			if (byEqpAction == null) {
				if (DefaultAction.ADFORM_NAME.equals(currentAction.getADFormName())) {
					return false;
				}
				
				ADForm adForm = getContext().getAdManager().getADForm(Env.getOrgRrn(), DefaultAction.ADFORM_NAME);
				fieldLotForm.reflow(adForm);
				// ����init
				getContext().setCurrentAction(LotRunByEqpExtensionPoint.getMainAction());
				return true;
			} else {
				ADForm adForm = byEqpAction.getADForm();
				if (adForm.getName().equals(currentAction.getADFormName())) {
					return false;
				}
				
				fieldLotForm.reflow(adForm);
				// ����init
				getContext().setCurrentAction(byEqpAction);
				return true;
			}
		}
		
	}
	
	public IByEqpAction getByEqpAction(String eqpType) {
		Map<String, String> eqpTypeActionMap = getContext().getEqpTypeActionMap();
		if (MapUtils.isEmpty(eqpTypeActionMap)) {
			eqpTypeActionMap = Maps.newHashMap();
			List<ADRefList> refLists = getContext().getAdManager().getADRefList(Env.getOrgRrn(), "ByEqpAction");
			for (ADRefList refList : refLists) {
				eqpTypeActionMap.put(refList.getKey(), refList.getText());
			}
		}
		
		getContext().setEqpTypeActionMap(eqpTypeActionMap);

		if (eqpTypeActionMap.containsKey(eqpType)) {
			String actionName = eqpTypeActionMap.get(eqpType);
			IByEqpAction action = LotRunByEqpExtensionPoint.getByEqpActionRegistry().get(actionName);
			return action;
		}
		
		return LotRunByEqpExtensionPoint.getMainAction();
	}
	
	@Override
	protected boolean isRTDAvailable() {
		if (dispatchField != null) {
			if (!dispatchField.getCheckboxControl().isDisposed()) {
				if (dispatchField.isChecked()) {
					return Activator.isRTDAvailable();
				}
			}
		}
		return false;
	}
	
	protected List<EdcSetCurrent> getEdcSetCurrent(Lot lot) {
		try {
			EDCManager edcManager = Framework.getService(EDCManager.class);
			return edcManager.getItemSetCurrents(Env.getOrgRrn(), lot.getBatchId(), lot.getObjectRrn(), null);
		} catch (ClientException e) {
			return null;
		} catch (Exception e) {
			return null;
		}
	}
	
	public boolean checkBatchEdcTecn(Lot lot) {
		try {
			//�����жϣ����Batch�ɼ�ʱ�����ͬBatch��EDCTECN������Ƿ�ѡ��������
			if(!StringUtil.isEmpty(lot.getBatchId())) {
				LotManager lotManager = Framework.getService(LotManager.class);
				List<Lot> lots = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
				ADManager adManager = Framework.getService(ADManager.class);
				StringBuffer sqlBuffer = new StringBuffer(" status = 'Active' and lotId in (");
				for(Lot batchLot : lots) {
					sqlBuffer.append("'" + batchLot.getLotId() + "',");
				}
				String sql = sqlBuffer.substring(0, sqlBuffer.length() - 1) + ") and stepName = '" + lot.getStepName() + "' ";
				List<EdcTecn> edcTecns = adManager.getEntityList(Env.getOrgRrn(), EdcTecn.class, Integer.MAX_VALUE, sql, null);
				if(edcTecns != null && !edcTecns.isEmpty()) {
					List<EdcTecn> carrLotEdcs = edcTecns.stream().filter(p -> p.getLotId().equals(lot.getLotId())).collect(Collectors.toList());
					if(carrLotEdcs.isEmpty()) {
						UI.showWarning(Message.getString("edc.please_select_other_lot_with_the_same_batch"));
						return false;
					}
				}
			}
		} catch (Exception e) {
			logger.error("Error at ByEqpEditor : checkBatchEdcTecn() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return true;
	}
	
	protected List<Lot> filterHoldLots(List<Lot> lots) {
		Equipment currentEqp = getContext().getCurrentEqp();
		List<Lot> holdLots = new ArrayList<Lot>();
		if (currentEqp != null && currentEqp.getObjectRrn() != null) {
			for (Lot lot : lots) {
				if (Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
				    holdLots.add(lot);
				}
			}
		}
		return holdLots;
	}
	
	public List<Lot> getRunningLotList(Lot lot) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		List<Lot> lotList = new ArrayList<Lot>();
		if (lot.getBatchId() != null) {
			List<Lot> clotList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
			for (Lot clot : clotList) {
				clot = lotManager.getRunningLot(clot.getObjectRrn());
				
				if(LotStateMachine.STATE_RUN.equals(clot.getState())){
					lotList.add(clot);
				}
				
			}
		} else {
			lot = lotManager.getRunningLot(lot.getObjectRrn());
			lotList.add(lot);
		}
		return lotList;
	}

}
