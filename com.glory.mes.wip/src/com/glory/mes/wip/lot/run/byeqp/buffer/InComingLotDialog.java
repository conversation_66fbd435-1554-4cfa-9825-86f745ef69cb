package com.glory.mes.wip.lot.run.byeqp.buffer;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.client.FutureQueryManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class InComingLotDialog extends GlcBaseDialog {

	private static final String FIELD_LOTLIST = "lots";
	private ListTableManagerField lotListField;
	
	public InComingLotDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
		this.setBlockOnOpen(false);
	}

	protected void createFormAction(GlcForm form) {
		lotListField = form.getFieldByControlId(FIELD_LOTLIST, ListTableManagerField.class);
	
		init();
	}
	
	private void init() {
		try {
			Equipment mianEquipment = (Equipment) propValues.get("mainEqp");
			FutureQueryManager futureQueryManager = Framework.getService(FutureQueryManager.class);
			List<Step> steps = futureQueryManager.getStepsByEquipment(mianEquipment.getObjectRrn());
			if (CollectionUtils.isNotEmpty(steps)) {
				List<Lot> lots = futureQueryManager.getForwardLotsByStep(Env.getOrgRrn(), steps, 3);
				lotListField.setValue(lots);
				lotListField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createButtonsForButtonBar(Composite parent) {	
		SquareButton cancel = createSquareButton(parent, IDialogConstants.CANCEL_ID,
				Message.getString(ExceptionBundle.bundle.CommonCancel()), false, UIControlsFactory.BUTTON_GRAY);
		
		FormData fd = new FormData();
		fd.width = 90;
		fd.height = 35;
		fd.top = new FormAttachment(0, 15);
		fd.right = new FormAttachment(100, -12);
		fd.bottom = new FormAttachment(100, -15);
		cancel.setLayoutData(fd);
	}
	
}
