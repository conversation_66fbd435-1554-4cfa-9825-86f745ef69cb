package com.glory.mes.wip.lot.run.abort;

import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.CheckboxTableViewer;
import org.eclipse.jface.viewers.ColumnViewerEditor;
import org.eclipse.jface.viewers.ColumnViewerEditorActivationEvent;
import org.eclipse.jface.viewers.ColumnViewerEditorActivationStrategy;
import org.eclipse.jface.viewers.ICellModifier;
import org.eclipse.jface.viewers.StructuredViewer;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TableViewerEditor;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableItem;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.viewers.ListTableManager;
import com.glory.mes.wip.model.Lot;

public class BatchLotTableManager extends ListTableManager {
	private static final String EDITOR_COLUMN_DES = "description";
	private CellEditor[] cellEditor;

	public BatchLotTableManager(ADTable adTable) {
		super(adTable);
	}
    
	public BatchLotTableManager(ADTable adTable, int style) {
		super(adTable, style);
	}
	
	protected StructuredViewer newViewer(Composite parent, FormToolkit toolkit,
			String[] columns, String[] columnsHeaders, int[] columnsSize, int heightHint){
		Table table = toolkit.createTable(parent, style); 
		table.setHeaderVisible(true);
		table.setLinesVisible(true);
		GridData gd = new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1);
		gd.heightHint = heightHint;
		gd.widthHint = 100;
		table.setLayoutData(gd);
	    toolkit.paintBordersFor(parent);   
	    TableViewer tv;
	    if ((style & SWT.CHECK) != 0) {
	    	tv = new CheckboxTableViewer(table);
	    } else {
	    	tv = new TableViewer(table);
	    }       
	    
		if(columns != null && columnsHeaders!= null && columnsSize != null && 
				columns.length == columnsHeaders.length && columnsHeaders.length== columnsSize.length){
			fillColumns(tv ,columns, columnsHeaders, columnsSize);
		} else {
			fillColumns(tv);
		}
		
		table.addListener(SWT.MeasureItem, new Listener() {
    		public void handleEvent (Event event) {
    			event.height = event.gc.getFontMetrics().getHeight() * 3/2;
    		}
    	});
		setCellEditor(tv);
		ColumnViewerEditorActivationStrategy actSupport = new ColumnViewerEditorActivationStrategy(tv) {
			protected boolean isEditorActivationEvent(ColumnViewerEditorActivationEvent event) {
				return event.eventType == ColumnViewerEditorActivationEvent.TRAVERSAL
						|| event.eventType == ColumnViewerEditorActivationEvent.MOUSE_CLICK_SELECTION
						|| event.eventType == ColumnViewerEditorActivationEvent.PROGRAMMATIC;
			}
		};
		TableViewerEditor.create(tv, actSupport,
				ColumnViewerEditor.TABBING_HORIZONTAL
						| ColumnViewerEditor.TABBING_MOVE_TO_ROW_NEIGHBOR
						| ColumnViewerEditor.TABBING_VERTICAL
						| ColumnViewerEditor.KEYBOARD_ACTIVATION);
		return tv;
	}
	
	protected void setCellEditor(TableViewer tableViewer) {
		int size = this.getColumns().length;
		cellEditor = new CellEditor[size];
		String[] properties = new String[size];
		Table table = tableViewer.getTable();
		for(int i = 0; i < size; i++) {
			String column = (String)table.getColumn(i).getData(COLUMN_ID);
			if(EDITOR_COLUMN_DES.equals(column)) {
				cellEditor[i] = new ModifyTextCellEditor(tableViewer);
			} else {
				cellEditor[i] = null;
			}
			properties[i] = column;
		}

		tableViewer.setColumnProperties(properties);
		tableViewer.setCellEditors(cellEditor);
		TextCellModifier tm = new TextCellModifier(tableViewer);
		tableViewer.setCellModifier(tm);
	}
	
	class TextCellModifier implements ICellModifier {
		private TableViewer tableViewer;

		public TextCellModifier(TableViewer tableViewer) {
			this.tableViewer = tableViewer;
		}

		@Override
		public boolean canModify(Object element, String property) {
			return true;
		}

		@Override
		public Object getValue(Object element, String property) {
			if (element instanceof Lot) {
//				if(((Lot)element).getDescription() != null) 
//					return ((Lot)element).getDescription();
			}
			return "";
		}

		@Override
		public void modify(Object element, String property, Object value) {
			tableViewer.refresh();
		}
	}
	
	class ModifyTextCellEditor extends TextCellEditor {
		private TableViewer tableViewer;
		
		public ModifyTextCellEditor(TableViewer tableViewer) {
	        super(tableViewer.getTable());
	        this.tableViewer = tableViewer;
	    }
		
		@Override
		protected Control createControl(Composite parent) {
			super.createControl(parent);
			text.addModifyListener(new ModifyListener(){
	            public void modifyText(ModifyEvent e) {
	            	TableItem[] items = tableViewer.getTable().getSelection();
	            	if (items != null && items.length > 0){
	            		TableItem item = items[0];
	            		Object obj = item.getData();
	            		String value = text.getText();
	            		if (value != null) {
	            			if(obj instanceof Lot) {
	        				//	((Lot)obj).setDescription(value);
	        				} 
						}
	            	}
	            }
	        });
			return text;
		}
	}
}
