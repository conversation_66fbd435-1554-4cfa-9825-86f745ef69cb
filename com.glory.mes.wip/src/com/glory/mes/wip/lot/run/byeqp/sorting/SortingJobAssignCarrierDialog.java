package com.glory.mes.wip.lot.run.byeqp.sorting;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.widgets.Text;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.mm.client.DurableManager;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.client.CarrierLotManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.client.SortingManager;
import com.glory.mes.wip.custom.ComponentAssignCustomComposite;
import com.glory.mes.wip.custom.ComponentCustomComposite;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.google.common.base.Objects;

public class SortingJobAssignCarrierDialog extends GlcBaseDialog {

	private static final String FIELD_ASSIGNDETAILS = "assignDetails";
	private static final String FIELD_ASSIGNCUSTOM = "assignCustom";
	private static final String FIELD_STEP = "step";
	private static final String FIELD_INMAINMATTYPE = "inMainMatType";
	private static final String FIELD_OUTMAINMATTYPE = "outMainMatType";
	private static final String FIELD_SORUCECARRIERLIST = "soruceCarrierList";
	private static final String FIELD_TARGETCARRIER = "targetCarrier";
	private static final String FIELD_TARGETCARRIERLIST = "targetCarrierList";
	
	private ListTableManagerField soruceCarrierTableManager, targetCarrierTableManager;
	private TextField stepTextField, soruceTextField, targetTypeTextField, targetCarrierTextField;
	private ComponentAssignCustomComposite customCompsite;
	private ComponentCustomComposite sourceComponent, targetComponent;
	private GlcFormField glcFormField;
	
	private DurableManager durableManager;
	private CarrierLotManager carrierLotManager;
	private PrdManager prdManager;
	private LotManager lotManager;
	
	private LotSortingJob sortingJob;
	private Lot sourceLot = new Lot();
	private Step currentStep;
	
	public SortingJobAssignCarrierDialog(String adFormName, String authority, IEventBroker eventBroker, LotSortingJob sortingJob) {
		super(adFormName, authority, eventBroker);
		this.sortingJob = sortingJob;
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		glcFormField = form.getFieldByControlId(FIELD_ASSIGNDETAILS, GlcFormField.class);
		customCompsite = form.getCustomCompositeByControl(FIELD_ASSIGNCUSTOM, ComponentAssignCustomComposite.class);
		stepTextField = glcFormField.getFieldByControlId(FIELD_STEP, TextField.class);
		soruceTextField = glcFormField.getFieldByControlId(FIELD_INMAINMATTYPE, TextField.class);
		targetTypeTextField = glcFormField.getFieldByControlId(FIELD_OUTMAINMATTYPE, TextField.class);
		soruceCarrierTableManager = glcFormField.getFieldByControlId(FIELD_SORUCECARRIERLIST, ListTableManagerField.class);
		targetCarrierTextField = glcFormField.getFieldByControlId(FIELD_TARGETCARRIER, TextField.class);
		targetCarrierTableManager = glcFormField.getFieldByControlId(FIELD_TARGETCARRIERLIST, ListTableManagerField.class);
		sourceComponent = customCompsite.getSourceComponentComposite();
		targetComponent = customCompsite.getTargetComponentComposite();
		
		sourceComponent.getTableManager().addICheckChangedListener(new ICheckChangedListener() {
			@Override
			public void checkChanged(List<Object> eventObjects, boolean checked) {
				if(checked) {
					ComponentUnit unit = (ComponentUnit) eventObjects.get(0);
					List<ComponentUnit> componentUnits = (List<ComponentUnit>) sourceComponent.getTableManager().getInput();
					List<ComponentUnit> units = componentUnits.stream()
							.filter(c -> Objects.equal(unit.getLotId(), c.getLotId())).collect(Collectors.toList());
					if(units != null) {
						units.stream().forEach(u -> {
							sourceComponent.getTableManager().setCheckedObject(u);
						});
					}
				}
			}
		});
		
		targetCarrierTextField.getTextControl().addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text carrierText = ((Text) event.widget);
				carrierText.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					String carrierId = carrierText.getText();
					Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), carrierId, false);
					if(carrier == null || !Carrier.STATE_AVAILABLE.equals(carrier.getState())) {
						targetCarrierTextField.getTextControl().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
						targetCarrierTableManager.getListTableManager().setInput(new ArrayList<Object>());
						UI.showError(Message.getString("wip.carrier_does_not_exist_or_is_already_in_use"));
						return;
					} else {
						targetCarrierTextField.getTextControl().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
					}
					targetCarrierTableManager.getListTableManager().setInput(Arrays.asList(carrier));
				}
			}

		});
		init();
	}

	private void init() {
		try {
			if(sortingJob != null) {
				durableManager = Framework.getService(DurableManager.class);
				carrierLotManager = Framework.getService(CarrierLotManager.class);
				prdManager = Framework.getService(PrdManager.class);
				lotManager = Framework.getService(LotManager.class);
				
				Carrier carrier = durableManager.getCarrierById(Env.getOrgRrn(), sortingJob.getDurableId(), true);
 				List<ComponentUnit> componentUnits = carrierLotManager.getComponentByCarrierId(Env.getOrgRrn(),
 						sortingJob.getDurableId());
 				if(CollectionUtils.isNotEmpty(componentUnits)) {
 					Map<Long, Lot> map = new HashMap<Long, Lot>();
 					componentUnits.stream().forEach(c -> {
 						if(!map.containsKey(c.getParentUnitRrn())){
 							map.put(c.getParentUnitRrn(), new Lot());
 						}
 					});
 					
 					map.keySet().stream().forEach(m -> {
 						Lot lot = lotManager.getLot(m);
 						if(lot != null) {
 							map.put(m, lot);
 							if(Objects.equal(lot.getLotId(), sortingJob.getLotId())) {
 								sourceLot = lot;
 							}
 						}
 					});
 					List<Object> disableUnits = new ArrayList<>();
 					componentUnits.stream().forEach(c -> {
 						c.setLotId(map.get(c.getParentUnitRrn()).getLotId());
 						if(!(map.get(c.getParentUnitRrn()).getLotId() != null && 
 								Objects.equal(map.get(c.getParentUnitRrn()).getLotId(), sortingJob.getLotId()))){
 							disableUnits.add(c);
 							c.setAttribute1("Y");
 						}
 					});
 					
 					sourceComponent.getComponentComposite().initComponents(componentUnits);
 					soruceCarrierTableManager.getListTableManager().setInput(Arrays.asList(carrier));
 					ListTableManager tableManager = sourceComponent.getTableManager();
					((CheckBoxTableViewerManager)tableManager.getTableManager()).setDisableObject(disableUnits);

					Step step = new Step();
 					step.setOrgRrn(Env.getOrgRrn());
 					step.setName(sourceLot.getStepName());
 					currentStep = (Step) prdManager.getActiveProcessDefinition(step);
 					if(currentStep != null) {
 						stepTextField.setText(currentStep.getName());
 						soruceTextField.setText(currentStep.getInMainMatType());
 						targetTypeTextField.setText(currentStep.getOutMainMatType());
 					}
 				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
	}

	@Override
	protected void okPressed() {
		try {
			List<? extends Object> carriers = targetCarrierTableManager.getListTableManager().getInput();
			if(CollectionUtils.isEmpty(carriers)) {
				UI.showInfo(Message.getString("mm.please_input_target_carrier"));
				return;
			}
        	Carrier toCarrier = (Carrier) carriers.get(0);
        	
        	List<ComponentUnit> componentUnits = (List<ComponentUnit>) targetComponent.getTableManager().getInput();
        	componentUnits = componentUnits.stream().filter(c -> c.getObjectRrn() != null).collect(Collectors.toList());
        	BigDecimal size = new BigDecimal(componentUnits.size());
        	if(size.compareTo(sourceLot.getMainQty()) != 0) {
        		UI.showInfo(Message.getString("wip.please_move_all_wafers_to_the_target_carrier"));
        		return;
        	}
        	
//        	if (LotSortingJob.ACTION_TYPE_STARTASSIGN.equals(sortingJob.getActionType()) ||
//        			LotSortingJob.ACTION_TYPE_EXCHANGE.equals(sortingJob.getActionType())) {
//	        	if (!UI.showConfirm(Message.getString("wip.sorting_job_batch_assign_durable"))) {
//	        		return;
//	        	}
//        	}
        	
        	if(currentStep != null) {
        		if(!Objects.equal(currentStep.getOutMainMatType(), toCarrier.getMainMatType())) {
        			UI.showInfo(String.format(Message.getString("wip.step_outmaterial_and_carrier_material_no_match"),
        					currentStep.getOutMainMatType(), toCarrier.getMainMatType()));
        			return;
        		}
        	} else {
        		UI.showInfo(Message.getString("wip.step_not_found"));
        		return;
        	}
        	
        	SortingManager sortingManager = Framework.getService(SortingManager.class);
    		sortingManager.assignSortingJobTargetDurable(sortingJob, toCarrier.getDurableId(), true, Env.getSessionContext());
    		UI.showInfo(Message.getString("common.modify_success"));
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
		super.okPressed();
	}
}
