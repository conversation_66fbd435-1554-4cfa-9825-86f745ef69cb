package com.glory.mes.wip.lot.run.byeqp.glc;

import org.eclipse.e4.core.services.events.IEventBroker;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.core.util.DBUtil;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.port.Port;

public class CarrierOutPortDialog extends GlcBaseDialog {
	
	public static final String FIELD_EQUIPMENTID = "equipmentId";
	public static final String FIELD_PORTID = "portId";
	
	private Equipment parentEqp;
	
	public CarrierOutPortDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		init();
	}

	private void init() {
		TextField textField = form.getFieldByControlId(FIELD_EQUIPMENTID, TextField.class);
		textField.setText(parentEqp.getEquipmentId());
	}
	
	public Port getPort() {
		RefTableField refTableField = form.getFieldByControlId(FIELD_PORTID, RefTableField.class);
		return (Port) refTableField.getData();
	}

	public Equipment getParentEqp() {
		return parentEqp;
	}

	public void setParentEqp(Equipment parentEqp) {
		this.parentEqp = parentEqp;
	}

}
