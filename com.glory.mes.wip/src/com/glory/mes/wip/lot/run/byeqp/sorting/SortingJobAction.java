package com.glory.mes.wip.lot.run.byeqp.sorting;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.inject.Inject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.e4.ui.workbench.modeling.EModelService;
import org.eclipse.e4.ui.workbench.modeling.EPartService;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.ui.action.IMouseAction;
import org.eclipse.swt.events.DisposeEvent;
import org.eclipse.swt.events.DisposeListener;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.widgets.ToolItem;
import org.osgi.service.event.Event;
import org.osgi.service.event.EventHandler;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADForm;
import com.glory.framework.base.application.command.OpenEditorCommand;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.security.model.ADAuthority;
import com.glory.mes.wip.client.SortingManager;
import com.glory.mes.wip.lot.run.byeqp.extensionpoint.IByEqpAction;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpEditor;
import com.glory.mes.wip.lot.run.byeqp.glc.ByEqpManager;
import com.glory.mes.wip.sorting.LotSortingJob;
import com.glory.framework.core.exception.ExceptionBundle;

/*
�û���λ�ο�ֵ��ByEqpAction����Sort�豸��ʾSort job����
����Show Name��Sorter�豸����  | Reference Value:ByEqpSortingJobAction(�̶���) 
����Lot Tracking From EQPѡ��Sorter���͵��豸����Sorter Job����
*/
public class SortingJobAction implements IByEqpAction {

	protected Set<EventHandler> subscribleHandlers = new HashSet<EventHandler>();
	
	private static final Logger logger = Logger.getLogger(SortingJobAction.class);
	
	public static final String AUTHORITY_NAME = "Wip.AbnormalSortingJob";
	
	public static final String ACTION_NAME = "ByEqpSortingJobAction";
	public static final String ADFORM_NAME = "WIPLotByEqpSortingJob";
	
	private static final String ACTION_TYPE_CHANGE = "change";
	private static final String ACTION_TYPE_RESERVE = "reserve";
	private static final String ACTION_TYPE_MANUALCOMPLETE = "manualComplete";
	private static final String ACTION_TYPE_UNRESERVE = "unReserve";
	
	private static final String FIELD_LOTFORM = "lotForm";
	private static final String FIELD_RESERVEDJOBS = "reservedJobs";
	private static final String FIELD_WAITTINGJOBS = "waittingJobs";
	private static final String FIELD_EQUIPMENTID = "equipmentId";
	private static final String FIELD_SOURCECARRIER = "sourceCarrier";
	private static final String FIELD_TARGETCARRIER = "targetCarrier";

	private static final String BUTTON_RESERVE = "reserve";
	private static final String BUTTON_ASSIGNCARRIER = "assignCarrier";
	private static final String BUTTON_CANCEL = "cancel";
	private static final String BUTTON_WAITREFRESH = "waitRefresh";
	private static final String BUTTON_CLEANEQPINFO = "cleanEqpInfo";
	private static final String BUTTON_UNRESERVER = "unreserver";
	private static final String BUTTON_CHANGEPORT = "changeport";
	private static final String BUTTON_MANUALCOMPLETE = "manualcomplete";
	private static final String BUTTON_ABNORMALHANDLE = "abnormalhandle";
	private static final String BUTTON_RESERVEDREFRESH = "reservedRefresh";
	
	private static final String DIALOG_WIPLOTSORTINGJOBDETAIL = "WIPLotSortingJobDetailDialog";
	private static final String DIALOG_WIPASSIGNCARRIER = "WIPAssignCarrierDialog";
	private static final String DIALOG_SORTINGJOBMERGERESERVE = "SortingJobMergeReserveDialog";
	private static final String DIALOG_SORTINGJOBOTHERRESERVE = "SortingJobOtherReserveDialog";
	private static final String DIALOG_SORTINGJOBUNRESERVE = "SortingJobUnReserveDialog";

	private ToolItem itemReserve;
	private ToolItem itemAssignCarrier;
	private ToolItem itemCancel;
	private ToolItem itemCleanEqpInfo;
	private ToolItem itemUnReserver;
	private ToolItem itemChangePort;
	private ToolItem itemManualComplete;
	private ToolItem itemAbnormalHandle;

	private ListTableManagerField waitListTable;
	private ListTableManagerField reservedListTable;
	private TextField fieldEqp;
	private TextField fieldSourceCarrier;
	private TextField fieldTargetCarrier;
	
	private ByEqpEditor byEqpEditor;
	private IEventBroker eventBroker;
	
	public SortingManager sortingManager;
	public ADManager adManager;
	
	@Inject
	EPartService partService;
	
	@Inject
	EModelService modelService;

	private ByEqpManager manager;
	
	@Override
	public String getADFormName() {
		return ADFORM_NAME;
	}

	@Override
	public String getActionName() {
		return ACTION_NAME;
	}

	@Override
	public ADForm getADForm() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			return adManager.getADForm(Env.getOrgRrn(), ADFORM_NAME);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return null;
		}
	}

	@Override
	public void initExtend(ByEqpEditor byEqpEditor, GlcForm form) {
		this.byEqpEditor = byEqpEditor;
		
		try {
			adManager = Framework.getService(ADManager.class);
	    	sortingManager = Framework.getService(SortingManager.class);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
		
		itemReserve = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_RESERVE);
		itemAssignCarrier = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_ASSIGNCARRIER);
		itemCancel = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_CANCEL);
		itemCleanEqpInfo = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_CLEANEQPINFO);
		itemUnReserver = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_UNRESERVER);
		itemChangePort = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_CHANGEPORT);
		itemManualComplete = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_MANUALCOMPLETE);
		itemAbnormalHandle = (ToolItem) form.getButtonByControl(FIELD_LOTFORM, BUTTON_ABNORMALHANDLE);
		
		waitListTable = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_WAITTINGJOBS , ListTableManagerField.class);
		reservedListTable = form.getFieldByControlId(FIELD_LOTFORM+ GlcEvent.NAMESPACE_SEPERATOR + FIELD_RESERVEDJOBS , ListTableManagerField.class);
		fieldEqp = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_EQUIPMENTID , TextField.class);
		fieldSourceCarrier = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_SOURCECARRIER , TextField.class);
		fieldTargetCarrier = form.getFieldByControlId(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_TARGETCARRIER , TextField.class);
		unSubscribeExtend();
		init();
	}

	@Override
	public void subscribeExtend(IEventBroker eventBroker, GlcForm form) {
		this.eventBroker = eventBroker;
		waitListTable.getListTableManager().addDoubleClickListener(new IMouseAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				LotSortingJob sortingJob = (LotSortingJob) waitListTable.getListTableManager().getSelectedObject();
				doubleClickAdaptor(sortingJob);
			}
		});
		
		reservedListTable.getListTableManager().addDoubleClickListener(new IMouseAction() {
			@Override
			public void run(NatTable natTable, MouseEvent event) {
				LotSortingJob sortingJob = (LotSortingJob) reservedListTable.getListTableManager().getSelectedObject();
				doubleClickAdaptor(sortingJob);
			}
		});
		
		EventHandler handler1 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_WAITTINGJOBS, GlcEvent.EVENT_SELECTION_CHANGED), this::waitingSelectionChangedAdaptor);
		subscribleHandlers.add(handler1);
		EventHandler handler2 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_RESERVEDJOBS, GlcEvent.EVENT_SELECTION_CHANGED), this::reservedSelectionChangeAdaptor);
		subscribleHandlers.add(handler2);
		EventHandler handler3 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_ASSIGNCARRIER), this::assignCarrierAdaptor);
		subscribleHandlers.add(handler3);
		EventHandler handler4 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_RESERVE), this::reserveAdaptor);
		subscribleHandlers.add(handler4);
		EventHandler handler5 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_CHANGEPORT), this::changeAdaptor);
		subscribleHandlers.add(handler5);
		EventHandler handler6 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_CANCEL), this::cancelJobAdapter);
		subscribleHandlers.add(handler6);
		EventHandler handler7 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_MANUALCOMPLETE), this::manualCompleteAdaptor);
		subscribleHandlers.add(handler7);
		EventHandler handler8 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_UNRESERVER), this::unReserveAdapter);
		subscribleHandlers.add(handler8);
		EventHandler handler9 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_WAITREFRESH), this::waitReRresh);
		subscribleHandlers.add(handler9);
		EventHandler handler10 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_RESERVEDREFRESH), this::reservedReRresh);
		subscribleHandlers.add(handler10);
		EventHandler handler11 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_ABNORMALHANDLE), this::abnormalhandleAdaptor);
		subscribleHandlers.add(handler11);
		EventHandler handler12 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_SOURCECARRIER, GlcEvent.EVENT_ENTERPRESSED), this::sourceCarrierEnterPressed);
		subscribleHandlers.add(handler12);
		EventHandler handler13 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + FIELD_TARGETCARRIER, GlcEvent.EVENT_ENTERPRESSED), this::targetCarrierEnterPressed);
		subscribleHandlers.add(handler13);
		EventHandler handler14 = GlcEvent.subscribeAndExecute(eventBroker, form.getFullTopic(FIELD_LOTFORM + GlcEvent.NAMESPACE_SEPERATOR + BUTTON_CLEANEQPINFO), this::cleanEqpInfoAdapter);
		subscribleHandlers.add(handler14);
	}

	@Override
	public void unSubscribeExtend() {
		itemReserve.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
		itemAssignCarrier.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
		itemCancel.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
		itemUnReserver.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
		itemChangePort.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
		itemManualComplete.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
		itemAbnormalHandle.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
		itemCleanEqpInfo.addDisposeListener(new DisposeListener() {
			@Override
			public void widgetDisposed(DisposeEvent e) {
				if (!subscribleHandlers.isEmpty()) {
					for (EventHandler handler : subscribleHandlers) {
						eventBroker.unsubscribe(handler);
					}
				}
			}
		});
	}

	public void init() {
		itemReserve.setEnabled(false);
		itemAssignCarrier.setEnabled(false);
		itemCancel.setEnabled(false);
		itemUnReserver.setEnabled(false);
		itemChangePort.setEnabled(false);
		itemManualComplete.setEnabled(false);
		itemAbnormalHandle.setEnabled(false);
		itemCleanEqpInfo.setEnabled(false);
		
		
		loadWaitSortingJob(byEqpEditor.getCurrentEqp() != null ? byEqpEditor.getCurrentEqp().getEquipmentId() : null);
		loadReservedSortingJob(byEqpEditor.getCurrentEqp() != null ? byEqpEditor.getCurrentEqp().getEquipmentId() : null);
		
		if(byEqpEditor.getCurrentEqp() != null) {
			fieldEqp.setText(byEqpEditor.getCurrentEqp().getEquipmentId());
		}
	}
	
	private void loadWaitSortingJob(String equipmentId) {
    	if (!StringUtil.isEmpty(equipmentId)) {
    		List<LotSortingJob> allLotSortingJobs = new ArrayList<>();
    		List<LotSortingJob> waitLotSortingJobs = adManager.getEntityList(Env.getOrgRrn(), LotSortingJob.class, Integer.MAX_VALUE, 
    				" (jobState = '" + LotSortingJob.STATE_PREPARE + "' OR jobState = '" + LotSortingJob.STATE_READY + "') AND (equipmentId is null or equipmentId = '')",
    				"priority, created");
    		List<String> jobStates = new ArrayList<String>();
    		jobStates.add(LotSortingJob.STATE_PREPARE); 
    		List<LotSortingJob> currentLotSortingJobs = sortingManager.getSortingJobs(Env.getOrgRrn(), 
    				equipmentId, null, jobStates, " priority, created ");
    		
    		allLotSortingJobs.addAll(currentLotSortingJobs);
    		allLotSortingJobs.addAll(waitLotSortingJobs);
    		waitListTable.getListTableManager().setInput(allLotSortingJobs);
    	} else {
    		waitListTable.getListTableManager().setInput(new ArrayList<>());
    	}
    	waitListTable.getListTableManager().refresh();
	}
	
	private void loadReservedSortingJob(String equipmentId) {
    	if (!StringUtil.isEmpty(equipmentId)) {
    		List<String> jobStates = new ArrayList<String>();
    		jobStates.add(LotSortingJob.STATE_START); 
    		jobStates.add(LotSortingJob.STATE_READY);
    		jobStates.add(LotSortingJob.STATE_DOWNLOAD);
    		jobStates.add(LotSortingJob.STATE_RESERVED); 
    		List<LotSortingJob> currentLotSortingJobs = sortingManager.getSortingJobs(Env.getOrgRrn(), 
    				equipmentId, null, jobStates, " priority, created ");
    		reservedListTable.getListTableManager().setInput(currentLotSortingJobs);
    	} else {
    		reservedListTable.getListTableManager().setInput(new ArrayList<>());
    	}
    	reservedListTable.getListTableManager().refresh();
	}
	
	private void doubleClickAdaptor(LotSortingJob sortingJob) {
		if(sortingJob != null && sortingJob.getObjectRrn() != null) {
			SortingJobDetailDialog detailDialog = new SortingJobDetailDialog(DIALOG_WIPLOTSORTINGJOBDETAIL, null, eventBroker, sortingJob);
			detailDialog.open();
		}
	}
	
	private void waitingSelectionChangedAdaptor(Object object) {
		Event event = (Event) object;
		LotSortingJob sortingJob = (LotSortingJob) event.getProperty(GlcEvent.PROPERTY_DATA);
		if(sortingJob != null) {
			if(LotSortingJob.ACTION_TYPE_EXCHANGE.equals(sortingJob.getActionType()) && LotSortingJob.JOB_TYPE_AUTO.equals(sortingJob.getJobType())) {
				itemAssignCarrier.setEnabled(true);
			} else {
				itemAssignCarrier.setEnabled(false);
			}
			if(StringUtils.isNotEmpty(sortingJob.getEquipmentId())) {
				itemCleanEqpInfo.setEnabled(true);
			} else {
				itemCleanEqpInfo.setEnabled(false);
			}
			itemReserve.setEnabled(true);
			itemCancel.setEnabled(true);
		}
	}
	
	private void reservedSelectionChangeAdaptor(Object object) {
		Event event = (Event) object;
		LotSortingJob sortingJob = (LotSortingJob) event.getProperty(GlcEvent.PROPERTY_DATA);
		if(sortingJob != null) {
			if(LotSortingJob.HOLDSTATE_ON.equals(sortingJob.getHoldState())) {
				itemUnReserver.setEnabled(false);
				itemManualComplete.setEnabled(false);
				itemChangePort.setEnabled(false);
				itemAbnormalHandle.setEnabled(true);
			} else if(LotSortingJob.STATE_READY.equals(sortingJob.getJobState())) {
				itemUnReserver.setEnabled(true);
				itemManualComplete.setEnabled(true);
				itemChangePort.setEnabled(true);
				itemAbnormalHandle.setEnabled(false);
			}  else if(LotSortingJob.STATE_RESERVED.equals(sortingJob.getJobState())) {
				itemReserve.setEnabled(false);
				itemAssignCarrier.setEnabled(false);
				itemCancel.setEnabled(false);
				itemUnReserver.setEnabled(false);
				itemChangePort.setEnabled(false);
				itemManualComplete.setEnabled(false);
				itemAbnormalHandle.setEnabled(false);
			} else if (LotSortingJob.STATE_DOWNLOAD.equals(sortingJob.getJobState())) {
				itemUnReserver.setEnabled(true);
				itemManualComplete.setEnabled(false);
				itemChangePort.setEnabled(false);
				itemAbnormalHandle.setEnabled(false);
			} else {
				itemUnReserver.setEnabled(false);
				itemManualComplete.setEnabled(false);
				itemChangePort.setEnabled(false);
				itemAbnormalHandle.setEnabled(false);
			}
		}
	}
	
	/**
	 * ����Ŀ���ؾ�-ֻ֧��
	 */
	private void assignCarrierAdaptor(Object object) {
		LotSortingJob sortingJob = (LotSortingJob) waitListTable.getListTableManager().getSelectedObject();
		if (sortingJob == null) {
    		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
            return;
    	}
		if(sortingJob != null && sortingJob.getObjectRrn() != null) {
			SortingJobAssignCarrierDialog detailDialog = new SortingJobAssignCarrierDialog(DIALOG_WIPASSIGNCARRIER, null, eventBroker, sortingJob);
			detailDialog.open();
			waitReRresh(new Object());
		}
	}
	
	/**
	 * Ԥ��SortingJob
	 */
	private void reserveAdaptor(Object object) {
		LotSortingJob sortingJob = (LotSortingJob) waitListTable.getListTableManager().getSelectedObject();
		if (sortingJob == null) {
    		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
            return;
    	}
		if(sortingJob != null && sortingJob.getObjectRrn() != null) {
			if(LotSortingJob.ACTION_TYPE_MERGE.equals(sortingJob.getActionType())) {
				SortingJobMergeReserveDialog detailDialog = new SortingJobMergeReserveDialog(DIALOG_SORTINGJOBMERGERESERVE, null, eventBroker, sortingJob, byEqpEditor.getCurrentEqp(), ACTION_TYPE_RESERVE);
				detailDialog.open();
				waitReRresh(new Object());
				reservedReRresh(new Object());
			} else {
				SortingJobOtherReserveDialog detailDialog = new SortingJobOtherReserveDialog(DIALOG_SORTINGJOBOTHERRESERVE, null, eventBroker, sortingJob, byEqpEditor.getCurrentEqp(), ACTION_TYPE_RESERVE);
				detailDialog.open();
				waitReRresh(new Object());
				reservedReRresh(new Object());
			}
		}
	}
	
	/**
	 * ���Port��
	 */
	private void changeAdaptor(Object object) {
		LotSortingJob sortingJob = (LotSortingJob) reservedListTable.getListTableManager().getSelectedObject();
		if (sortingJob == null) {
    		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
            return;
    	}
		if(sortingJob != null && sortingJob.getObjectRrn() != null) {
			if(LotSortingJob.ACTION_TYPE_MERGE.equals(sortingJob.getActionType())) {
				SortingJobMergeReserveDialog detailDialog = new SortingJobMergeReserveDialog(DIALOG_SORTINGJOBMERGERESERVE, null, eventBroker, sortingJob, byEqpEditor.getCurrentEqp(), ACTION_TYPE_CHANGE);
				detailDialog.open();
			} else {
				SortingJobOtherReserveDialog detailDialog = new SortingJobOtherReserveDialog(DIALOG_SORTINGJOBOTHERRESERVE, null, eventBroker, sortingJob, byEqpEditor.getCurrentEqp(), ACTION_TYPE_CHANGE);
				detailDialog.open();
			}
			reservedReRresh(new Object());
		}
	}
	/**
	 * ȡ������
	 */
	private void cancelJobAdapter(Object object) {
        try {
        	Object o = waitListTable.getListTableManager().getSelectedObject();
        	if (o == null) {
        		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
        	}
        	LotSortingJob lotSortingJob = (LotSortingJob) o;
        	if (lotSortingJob.getObjectRrn() != null) {
                if (StringUtils.equals(LotSortingJob.SORTING_MODE_EXCHNAGE_DOWNGRADE, lotSortingJob.getSortingMode())) {
                    UI.showInfo(Message.getString("wip.downgrade_lot_sorting_job_cannot_cancel"));
                    return;
                }

	        	if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmCancel()))) {
	        		sortingManager.cancelSortingJob(lotSortingJob, Env.getSessionContext());      		
	        		UI.showInfo(Message.getString("common.cancel_success"));
	        		waitReRresh(new Object());
	        	} 	
        	} else {
        		UI.showInfo(Message.getString("wip.lot_sorting_job_not_create"));
        	}
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	/**
	 * �ֶ����
	 */
	private void manualCompleteAdaptor(Object object) {
		LotSortingJob sortingJob = (LotSortingJob) reservedListTable.getListTableManager().getSelectedObject();
		if (sortingJob == null) {
    		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
            return;
    	}
		if(sortingJob != null && sortingJob.getObjectRrn() != null) {
			SortingJobUnReserveDialog detailDialog = new SortingJobUnReserveDialog(DIALOG_SORTINGJOBUNRESERVE, null, eventBroker, sortingJob, ACTION_TYPE_MANUALCOMPLETE);
			detailDialog.open();
			reservedReRresh(new Object());
		}
	}
	
	/**
	 * ���
	 */
	private void cleanEqpInfoAdapter(Object object) {
		LotSortingJob sortingJob = (LotSortingJob) waitListTable.getListTableManager().getSelectedObject();
    	if (sortingJob == null) {
    		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
            return;
    	}
		if(sortingJob != null && sortingJob.getObjectRrn() != null) {
			SortingJobUnReserveDialog detailDialog = new SortingJobUnReserveDialog(DIALOG_SORTINGJOBUNRESERVE, null, eventBroker, sortingJob, ACTION_TYPE_UNRESERVE);
			detailDialog.open();
			waitReRresh(new Object());
			reservedReRresh(new Object());
		}
	}
	
	/**
	 * ȡ��Ԥ��
	 */
	private void unReserveAdapter(Object object) {
		LotSortingJob sortingJob = (LotSortingJob) reservedListTable.getListTableManager().getSelectedObject();
    	if (sortingJob == null) {
    		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
            return;
    	}
		if(sortingJob != null && sortingJob.getObjectRrn() != null) {
			SortingJobUnReserveDialog detailDialog = new SortingJobUnReserveDialog(DIALOG_SORTINGJOBUNRESERVE, null, eventBroker, sortingJob, ACTION_TYPE_UNRESERVE);
			detailDialog.open();
			waitReRresh(new Object());
			reservedReRresh(new Object());
		}
	}
	
	/**
	 * �쳣����
	 */
	private void abnormalhandleAdaptor(Object object) {
		try {
			LotSortingJob sortingJob = (LotSortingJob) reservedListTable.getListTableManager().getSelectedObject();
			if (sortingJob == null) {
	    		UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
	            return;
	    	}
			List<ADAuthority> authority = adManager.getEntityList(Env.getOrgRrn(), ADAuthority.class, Env.getMaxResult(), "name = '" + AUTHORITY_NAME + "'", "");
			if (authority.size() != 1) {
				return;
			}
			authority.get(0).setAttribute1(sortingJob.getBatchId().toString());
			OpenEditorCommand.open(authority.get(0), partService, modelService);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
            return;
		}
	}
	
	/**
	 * Waiting jobs�б�ˢ�·���
	 */
	private void waitReRresh(Object object) {
		loadWaitSortingJob(byEqpEditor.getCurrentEqp() != null ? byEqpEditor.getCurrentEqp().getEquipmentId() : null);
	}
	
	/**
	 * Reserved jobs�б�ˢ�·���
	 */
	private void reservedReRresh(Object object) {
		loadReservedSortingJob(byEqpEditor.getCurrentEqp() != null ? byEqpEditor.getCurrentEqp().getEquipmentId() : null);
	}
	
	/**
	 * Source Carrier�س��¼�
	 */
	private void sourceCarrierEnterPressed(Object obj) {
		Event event = (Event) obj;
		String sourceCarrier = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
		List<LotSortingJob> sortingJobs = (List<LotSortingJob>) waitListTable.getListTableManager().getInput();
		Optional<LotSortingJob> optional = sortingJobs.stream()
				.filter(s -> (s.getDurableId() != null && s.getDurableId().equals(sourceCarrier))).findFirst();
		if(optional.isPresent()) {
			fieldSourceCarrier.getTextControl().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
			waitListTable.getListTableManager().setSelection(new StructuredSelection(optional.get()));
		} else {
			fieldSourceCarrier.getTextControl().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
		}
	}
	
	/**
	 * Target Carrier�س��¼�
	 */
	private void targetCarrierEnterPressed(Object obj) {
		Event event = (Event) obj;
		String targetCarrier = (String) event.getProperty(GlcEvent.PROPERTY_DATA);
		List<LotSortingJob> sortingJobs = (List<LotSortingJob>) waitListTable.getListTableManager().getInput();
		Optional<LotSortingJob> optional = sortingJobs.stream()
				.filter(s -> (s.getToDurableId() != null && s.getToDurableId().equals(targetCarrier))).findFirst();
		if(optional.isPresent()) {
			fieldTargetCarrier.getTextControl().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
			waitListTable.getListTableManager().setSelection(new StructuredSelection(optional.get()));
		} else {
			fieldTargetCarrier.getTextControl().setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
		}		
	}
	
	public void setPartService(EPartService partService) {
		this.partService = partService;
	}

	public void setModelService(EModelService modelService) {
		this.modelService = modelService;
	}
	
	@Override
	public ByEqpManager getManager() {
		return manager;
	}

	@Override
	public void setManager(ByEqpManager manager) {
		this.manager = manager;

		if (manager != null) {
			((SortingJobByEqpManagerDefault) manager).setAction(this);
		}
	}

}
