package com.glory.mes.wip.lot.run.bylot.glc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.osgi.service.event.Event;

import com.glory.common.fel.common.StringUtils;
import com.glory.edc.EdcEntry;
import com.glory.edc.client.EDCManager;
import com.glory.edc.collection.EdcSetCurrentComponentDialog;
import com.glory.edc.collection.EdcSetCurrentDialog;
import com.glory.edc.model.AbstractEdcSet;
import com.glory.edc.model.EdcAQLSet;
import com.glory.edc.model.EdcData;
import com.glory.edc.model.EdcSetCurrent;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.extensionpoints.WizardPageExtensionPoint;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.wizard.FlowWizard;
import com.glory.framework.core.chain.ChainContext;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.lot.ILotChangeListener;
import com.glory.mes.wip.lot.action.dialog.WIPLotHoldDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotMergeDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotParameterDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotReleaseDialog;
import com.glory.mes.wip.lot.action.dialog.WIPLotSplitDialog;
import com.glory.mes.wip.lot.glc.bylot.LotGlcEditor;
import com.glory.mes.wip.lot.provider.LotProviderEntry;
import com.glory.mes.wip.lot.run.abort.AbortDialog;
import com.glory.mes.wip.lot.run.abort.AbortWizard;
import com.glory.mes.wip.lot.run.trackin.TrackInContext;
import com.glory.mes.wip.lot.run.trackin.TrackInDialog;
import com.glory.mes.wip.lot.run.trackin.TrackInWizard;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveContext;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveDialog;
import com.glory.mes.wip.lot.run.trackmove.TrackMoveWizard;
import com.glory.mes.wip.lot.run.trackmove.modal.TrackMoveModalDialog;
import com.glory.mes.wip.lot.run.trackmove.modal.TrackMoveModalWizard;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutDialog;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.QtyUnit;
import com.glory.mes.wip.track.model.InContext;
import com.glory.framework.core.exception.ExceptionBundle;

public class ByLotGlcEditor extends LotGlcEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip/com.glory.mes.wip.lot.run.bylot.glc.ByLotGlcEditor";
	
	private static final Logger logger = Logger.getLogger(ByLotGlcEditor.class);
	
	protected List<ILotChangeListener> lotChangeListeners = new LinkedList<ILotChangeListener>();
	protected Step currentStep; 
	protected IManagedForm iManagedForm;
	
	private static final String BUTTON_TRACK_IN = "trackIn";
	private static final String BUTTON_TRACK_OUT = "trackOut";
	private static final String BUTTON_TRACK_MOVE = "trackMove";
	private static final String BUTTON_DOCP = "dcop";
	private static final String BUTTON_ABORT = "abort";
	private static final String BUTTON_HIS_SEARCH = "hisSearch";
	private static final String BUTTON_FUTURE_STEP = "futureStep";
	private static final String BUTTON_CHANGE_COMMENT = "changeComment";
	private static final String BUTTON_REFRESH = "refresh";
	
	public static final String BUTTON_NAME_HOLD = "hold"; 
	public static final String BUTTON_NAME_RELEASE = "release"; 
	public static final String BUTTON_NAME_SPILT = "spilt"; 
	public static final String BUTTON_NAME_MERGE = "merge"; 
	public static final String BUTTON_NAME_CHANGE_PARAMETER = "changeParameter"; 
	
	private static final String FORM_HOLDFORM = "WIPLotActionHoldDialog";
	private static final String FORM_RELEASEFORM = "WIPLotActionReleaseDialog";
	private static final String FORM_SPILTFORM = "WIPLotActionSplitCompDialog";
	private static final String FORM_QTY_SPILTFORM = "WIPLotActionSplitQtyDialog";
	private static final String FORM_MERGEFORM = "WIPLotActionMergeDialog";
	private static final String FORM_CHANGE_PARAMTER_FORM = "WIPLotActionParameterDialog";
	private static final String FORM_CHANGE_COMMENT_FORM = "WIPLotTrackCommentDialog";
	private static final String FORM_HIS_SEARCH_FORM = "WIPLotTrackHisDialog";
	private static final String FORM_FUTURE_STEP_FORM = "WIPLotTrackFutureStepDialog";
	
	protected boolean trackMoveFlag = false;
	protected ToolItem itemTrackIn;
	protected ToolItem itemTrackOut;
	protected ToolItem itemTrackMove;
	protected ToolItem itemDcop;
	protected AuthorityToolItem itemAbort;	
	protected ToolItem itemHisSearch;
	protected ToolItem itemFutureStep;
	protected ToolItem itemChangeComment;
	protected ToolItem itemRefresh;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_TRACK_IN), this::trackInAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_TRACK_OUT), this::trackOutAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_TRACK_MOVE), this::trackMoveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DOCP), this::docpAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ABORT), this::abortAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_HIS_SEARCH), this::hisSearchAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_FUTURE_STEP), this::futureStepAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CHANGE_COMMENT), this::changeCommentAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		
		subscribeAndExecute(eventBroker, lotParamterAndChildAndHoldForm.getFullTopic(BUTTON_NAME_HOLD), this::holdAdapter);
		subscribeAndExecute(eventBroker, lotParamterAndChildAndHoldForm.getFullTopic(BUTTON_NAME_RELEASE), this::releaseAdapter);
        subscribeAndExecute(eventBroker, lotParamterAndChildAndHoldForm.getFullTopic(BUTTON_NAME_SPILT), this::spiltAdapter);
		subscribeAndExecute(eventBroker, lotParamterAndChildAndHoldForm.getFullTopic(BUTTON_NAME_MERGE), this::mergeAdapter);
		subscribeAndExecute(eventBroker, lotParamterAndChildAndHoldForm.getFullTopic(BUTTON_NAME_CHANGE_PARAMETER), this::changeParameterAdapter);
		
		itemInit();
	}
	
	private void itemInit() {
		itemTrackIn = (ToolItem) form.getButtonByControl(null, BUTTON_TRACK_IN);
		itemTrackOut = (ToolItem) form.getButtonByControl(null, BUTTON_TRACK_OUT);
		itemTrackMove = (ToolItem) form.getButtonByControl(null, BUTTON_TRACK_MOVE);
		itemDcop = (ToolItem) form.getButtonByControl(null, BUTTON_DOCP);
		itemAbort = (AuthorityToolItem) form.getButtonByControl(null, BUTTON_ABORT);
		itemHisSearch = (ToolItem) form.getButtonByControl(null, BUTTON_HIS_SEARCH);
		itemFutureStep = (ToolItem) form.getButtonByControl(null, BUTTON_FUTURE_STEP);
		itemChangeComment = (ToolItem) form.getButtonByControl(null, BUTTON_CHANGE_COMMENT);
		itemRefresh = (ToolItem) form.getButtonByControl(null, BUTTON_REFRESH);
		
		itemTrackIn.setEnabled(false);
		itemTrackOut.setEnabled(false);
		itemTrackMove.setEnabled(false);
		itemDcop.setEnabled(false);
		itemAbort.setEnabled(false);
		
		registerAccelerator();
	}
	
	protected void trackInAdapter(Object obj) {
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			if ((this.txtOperator != null && this.txtOperator.getVisible()) || MesCfMod.isTrackOperator(Env.getOrgRrn(), sysParamManager)) {
				if(!CheckOperatorOrNoCheck()) {
					return;
				}
			}
			form.getMessageManager().removeAllMessages();
			TrackInContext context = new TrackInContext();
			context.setTrackInType(TrackInContext.TRACK_IN_BYLOT);
			List<Lot> lots = new ArrayList<Lot>();
	        if (MesCfMod.isUseDurable(Env.getOrgRrn(), sysParamManager)) {
	        	List<Object> objects = carrierLotCustomComposite.getLotTableManager().getCheckedObject();
				if (objects == null || objects.size() == 0) {
					UI.showError(Message.getString("wip.please_select_lot_first"));
					return;
				}
				List<Lot> checkLots = objects.stream().map(o -> ((Lot)o)).collect(Collectors.toList());
				lots.addAll(checkLots);
	        } else {
	        	Lot lot = (Lot) basicInfoForm.getValue();
	        	lots.add(lot);
	        }

			context.setLots(lots);
			Step step = getCurrentStep();
			context.setStep(step);
			
			FlowWizard wizard = getTrackInWizard(context, step.getTrackInFlow());
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			inContext.setMultiEqp(step.getIsMultiEqp());
			
			LotManager lotManager = Framework.getService(LotManager.class);			
			ChainContext wipContext = lotManager.checkTrackInConstraint(inContext
					, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}

			ChainContext checkAutoFutureMergeContext = lotManager.checkAutoFutureMergeConstraint(inContext
					, Env.getSessionContext());
			if (checkAutoFutureMergeContext.getReturnMessage() != null
					&& checkAutoFutureMergeContext.getReturnMessage().trim().length() > 0) {
				UI.showError(checkAutoFutureMergeContext.getReturnMessage());
			}
			if (checkAutoFutureMergeContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}

			TrackInDialog dialog = new TrackInDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if ((result == Dialog.OK || result == TrackInDialog.FIN) 
					&& context.getReturnCode() == TrackInContext.OK_ID) {
				Lot trackInLot = ((TrackInWizard) wizard).getContext().getTrackInLot();
				if (trackInLot != null) {
					if (txtLotId != null) {
						txtLotId.setText(trackInLot.getLotId());
					}
				}
				refreshAdapter(null);
			}
			if (txtLotId != null) {
				txtLotId.selectAll();
			}
		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void trackOutAdapter(Object obj) {
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			if ((this.txtOperator != null && this.txtOperator.getVisible()) || MesCfMod.isTrackOperator(Env.getOrgRrn(), sysParamManager)) {
				if(!CheckOperatorOrNoCheck()) {
					return;
				}
			}
			form.getMessageManager().removeAllMessages();
			TrackOutContext context = new TrackOutContext();
			context.setTrackOutType(TrackOutContext.TRACK_OUT_BYLOT);

			Lot lot = (Lot)basicInfoForm.getValue();
			List<Lot> lots = getRunningLotList(lot);
			context.setLots(lots);

			Step step = getCurrentStep();
			context.setStep(step);
			
			FlowWizard wizard = getTrackOutWizard(context, step.getTrackOutFlow());
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			inContext.setCurrentStep(step);
			inContext.setMultiEqp(step.getIsMultiEqp());
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkTrackOutConstraint(inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}

			TrackOutDialog dialog = new TrackOutDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == TrackOutDialog.FIN) {
				if (((TrackOutWizard) wizard).getContext().getOutLots().size() > 0) {
					Lot trackOutLot = ((TrackOutWizard) wizard).getContext().getOutLots().get(0);
					if (trackOutLot != null) {
						if (txtLotId != null) {
							txtLotId.setText(trackOutLot.getLotId());
						}
					}

					if (!LotStateMachine.STATE_RUN.equals(trackOutLot.getState())) {
						UI.showInfo(Message.getString("wip.trackout_success"));
					}
				}
				refreshAdapter(null);
				//ֻ����TrackOut��TrackMoveʱ����Ҫ�������Step�仯
				lotChanged((Lot)basicInfoForm.getValue());
			}
			if (txtLotId != null) {
				txtLotId.selectAll();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void trackMoveAdapter(Object obj) {
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
			if ((this.txtOperator != null && this.txtOperator.getVisible()) || MesCfMod.isTrackOperator(Env.getOrgRrn(), sysParamManager)) {
				if(!CheckOperatorOrNoCheck()) {
					return;
				}
			}
			form.getMessageManager().removeAllMessages();
			TrackMoveContext context = new TrackMoveContext();
			context.setTrackMoveType(TrackMoveContext.TRACK_MOVE_DEFAULT);
			List<Lot> lots = new ArrayList<Lot>();
			Lot lot = (Lot) basicInfoForm.getValue();
			lots.add(lot);
			context.setLots(lots);

			Step step = getCurrentStep();
			context.setStep(step);
			
			FlowWizard wizard = getTrackMoveWizard(context, step.getMoveNextFlow());
			
			InContext inContext = new InContext();
			inContext.setLots(context.getLots());
			inContext.setOperator1(context.getOperator1());
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkTrackInConstraint(
					inContext, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}

			if (wizard instanceof TrackMoveWizard) {
				TrackMoveDialog dialog = new TrackMoveDialog(UI.getActiveShell(), wizard);
				int result = dialog.open();
				if (result == Dialog.OK || result == TrackMoveDialog.FIN) {
					refreshAdapter(null);
					//ֻ����TrackOut��TrackMoveʱ����Ҫ�������Step�仯
					lotChanged((Lot)basicInfoForm.getValue());
					UI.showInfo(Message.getString("wip.trackmove_success"));
				}
			} else if (wizard instanceof TrackMoveModalWizard) {
				//�Թ̶����ڵ���ʽ��վ
				((TrackMoveContext)((TrackMoveModalWizard)wizard).getContext()).setTrackMoveType(TrackMoveContext.TRACK_MOVE_MODAL);
				TrackMoveModalDialog dialog = new TrackMoveModalDialog(UI.getActiveShell(), wizard);
				dialog.open();
				refreshAdapter(null);
			}
			
			if (txtLotId != null) {
				txtLotId.selectAll();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	
	}
	
	protected void docpAdapter(Object obj) {
		try {
			List<EdcSetCurrent> currents = getEdcSetCurrent();
			if (currents != null && currents.size() > 0) {
			    for (EdcSetCurrent current : currents) {
	                AbstractEdcSet itemEdcSet = new AbstractEdcSet();
	                itemEdcSet.setObjectRrn(current.getItemSetRrn());
	                ADManager adManager = Framework.getService(ADManager.class);
	                itemEdcSet = (AbstractEdcSet) adManager.getEntity(itemEdcSet);
	                if (itemEdcSet instanceof EdcAQLSet) {
	                    EdcAQLSet edcAQLSet = (EdcAQLSet)itemEdcSet;
	                    EDCManager edcManager = Framework.getService(EDCManager.class);
	                    edcManager.createAqlSamplingPlanInstance(edcAQLSet, (Lot)basicInfoForm.getValue());
	                }
	            }
				if (currents.size() == 1 
						&& !EdcSetCurrent.FLAG_DONE.equals(currents.get(0).getEdcFlag())
						&& !EdcSetCurrent.FLAG_PASS.equals(currents.get(0).getEdcFlag())){
					//��ֻ��һ��EDCʱ����δ���ʱ
					int result = EdcEntry.open(EdcData.EDCFROM_LOT, currents.get(0), null, (Lot)basicInfoForm.getValue());
					if (result == Dialog.OK) {
						refreshAdapter(null);
					}
				} else {
					boolean flag = false;
					for (EdcSetCurrent current : currents) {
						if (!StringUtil.isEmpty(current.getComponentUnitId())) {
							flag = true;
							break;
						}
					}
					EdcSetCurrentDialog edcSetCurrentDialog = null;
					if (flag) {
						edcSetCurrentDialog = new EdcSetCurrentComponentDialog(UI.getActiveShell(), iManagedForm, (Lot)basicInfoForm.getValue(), currents);
						
					} else {
						edcSetCurrentDialog = new EdcSetCurrentDialog(UI.getActiveShell(), iManagedForm, (Lot)basicInfoForm.getValue(), currents);	
					}
					edcSetCurrentDialog.open();
					if (edcSetCurrentDialog.getReturnCode() == Dialog.OK) {
						refreshAdapter(null);
					}		
				}
			} else {
				UI.showWarning(Message.getString("edc.alert_message"), Message
						.getString("edc.alert_message_title"));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void abortAdapter(Object obj) {
		try {
			form.getMessageManager().removeAllMessages();
			InContext context = new InContext();

			Lot lot = (Lot)basicInfoForm.getValue();
			List<Lot> lots = getRunningLotList(lot);
			context.setLots(lots);

			Step step = getCurrentStep();
			context.setCurrentStep(step);
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ChainContext wipContext = lotManager.checkAbortConstraint(
					context, Env.getSessionContext());
			if (wipContext.getReturnMessage() != null
					&& wipContext.getReturnMessage().trim().length() > 0) {
				UI.showError(wipContext.getReturnMessage());
			}
			if (wipContext.getReturnCode() == ChainContext.FAILED_ID) {
				return;
			}
			
			FlowWizard wizard = getAbortWizard(context, step.getAbortFlow());

			AbortDialog dialog = new AbortDialog(UI.getActiveShell(), wizard);
			int result = dialog.open();
			if (result == Dialog.OK || result == AbortDialog.FIN) {
				refreshAdapter(null);
			}
			if (txtLotId != null) {
				txtLotId.selectAll();
			}
		} catch (Exception e) {
			logger.error("Error at RunningLotSection : abortAdapter() ", e);
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	
	}
	
	protected void hisSearchAdapter(Object obj) {
		Lot lot = (Lot) basicInfoForm.getValue();
		if (lot == null || lot.getObjectRrn() == null) {
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			return;
		}
		LotHisQueryDialog hisQueryDialog = new LotHisQueryDialog(lot, FORM_HIS_SEARCH_FORM, null, eventBroker);
		if (Dialog.OK == hisQueryDialog.open()) {
			refreshAdapter(obj);
		}
	}
	
	protected void futureStepAdapter(Object obj) {
		Lot lot = (Lot) basicInfoForm.getValue();
		if (lot == null || lot.getObjectRrn() == null) {
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			return;
		}
		LotTrackFutureStepDialog lotTrackFutureStepDialog = new LotTrackFutureStepDialog(FORM_FUTURE_STEP_FORM,
				null, eventBroker, lot);
		if (Dialog.OK == lotTrackFutureStepDialog.open()) {
			refreshAdapter(obj);
		}
	}
	
	protected TrackInWizard getTrackInWizard(TrackInContext context, String wizardName) {
		if (this.txtOperator != null && this.txtOperator.getVisible()) {
			context.setOperator1(txtOperator.getText().trim());
		} else {
			context.setOperator1(Env.getUserName());
		}
		TrackInWizard wizard = (TrackInWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setAuthority(form.getAuthority());
		wizard.setEventBroker(eventBroker);
		wizard.setContext(context);
		
		return wizard;
	}
	
	protected TrackOutWizard getTrackMoveWizard(TrackMoveContext context, String wizardName) {
		if (this.txtOperator != null && this.txtOperator.getVisible()) {
			context.setOperator1(txtOperator.getText().trim());
		} else {
			context.setOperator1(Env.getUserName());
		}
		TrackOutWizard wizard = (TrackOutWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setContext(context);
		return wizard;
	}
	
	protected TrackOutWizard getTrackOutWizard(TrackOutContext context, String wizardName) {
		if (this.txtOperator != null && this.txtOperator.getVisible()) {
			context.setOperator1(txtOperator.getText().trim());
		} else {
			context.setOperator1(Env.getUserName());
		}
		TrackOutWizard wizard = (TrackOutWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setContext(context);
		return wizard;
	}
	
	protected AbortWizard getAbortWizard(InContext context, String wizardName) {
		if (this.txtOperator != null && this.txtOperator.getVisible()) {
			context.setOperator1(txtOperator.getText().trim());
		} else {
			context.setOperator1(Env.getUserName());
		}
		AbortWizard wizard = (AbortWizard)WizardPageExtensionPoint.getWizardRegistry().get(wizardName);
		wizard.setContext(context);
		return wizard;
	}
	
	protected List<Lot> getRunningLotList(Lot lot) throws Exception {
		LotManager lotManager = Framework.getService(LotManager.class);
		List<Lot> lotList = new ArrayList<Lot>();
		if (lot.getBatchId() != null) {
			List<Lot> clotList = lotManager.getLotsByBatch(Env.getOrgRrn(), lot.getBatchId());
			for (Lot clot : clotList) {
				clot = lotManager.getRunningLot(clot.getObjectRrn());
				
				if(LotStateMachine.STATE_RUN.equals(clot.getState())){
					lotList.add(clot);
				}
				
			}
		} else {
			lot = lotManager.getRunningLot(lot.getObjectRrn());
			lotList.add(lot);
		}
		return lotList;
	}
	
	@Override
	public void setAdObject(Lot lot) {
		try {
			if (lot != null) {	
				try {
					if (lot.getObjectRrn() != null) {
						//������Ϣ
						basicInfoForm.setValue(lot);
						prdProcedureForm.setValue(lot);
						basicInfoForm.refresh();
						prdProcedureForm.refresh();
						lotChanged(lot);
						statusChanged(lot.getState());
						//�����Qtime��Ϣ
						SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
						if (MesCfMod.isUseDurable(Env.getOrgRrn(), sysParamManager)) {
							if (carrierLotCustomComposite != null && carrierLotCustomComposite.getLotTableManager() != null) {
								carrierLotCustomComposite.getLotTableManager().update(lot);
							}
						}
						//��ͣ��Ϣ
						LotManager lotManager = Framework.getService(LotManager.class);
						List<LotHold> lotHoldsList = lotManager.getLotHolds(lot.getObjectRrn());
						holdTableManagerField.getListTableManager().setInput(lotHoldsList);
						//������Ϣ
						List<Lot> lotChilds = lotManager.getLotWithChildren(lot.getObjectRrn()).getChildrenLots();
						childLoTableManagerField.getListTableManager().setInput(lotChilds);
						//������Ϣ
						PrdManager prdManager = Framework.getService(PrdManager.class);
						Map<String, Object> paramMap = new HashMap<String, Object>();
						if(lot.getProcessInstanceRrn() != null) {
							paramMap = prdManager.getCurrentParameter(lot.getProcessInstanceRrn());
							List<Lot> lotParameters = new ArrayList<Lot>();
							if(paramMap !=null && paramMap.size() > 0) {
								for (String key : paramMap.keySet()) {
									Lot lotParameter = (Lot)lot.clone();
									lotParameter.setAttribute1(key);
									lotParameter.setAttribute3(String.valueOf(paramMap.get(key)));
									lotParameters.add(lotParameter);
								}
							}
							parameterTableManagerField.getListTableManager().setInput(lotParameters);
						}
				 }
				} catch (Exception e) {
					e.printStackTrace();
				}
		        
			} else {		
				lotChanged(null);
				statusChanged("");
			}
		
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	
	}
	
	@Override
	public Lot searchLot(String lotId) {
		try {
			//���߱�������
			Lot lot = LotProviderEntry.getLotByLine(lotId);
			if (lot != null) {
				loadCurrentStep(lot);
				lotChanged(lot);
			}
			return lot;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	@Override
	public List<Lot> searchLotByDurableId(String durableId) {
		try {
			//���ؾ߼������
			LotManager lotManager = Framework.getService(LotManager.class);
			List<Lot> lots = lotManager.getLotsByDurableId(Env.getOrgRrn(), durableId);
			if (lots.get(0) != null) {
				loadCurrentStep(lots.get(0));
				lotChanged(lots.get(0));
			}
			return lots;
		} catch (Exception e) {
			logger.warn("LotSection searchLotEntity(): Lot isn' t exsited!");
		}
		return null;
	}
	
	public void lotChanged(Lot lot) {
		try {
			if (lot != null && LotStateMachine.STATE_WAIT.equals(lot.getState())) {
				//ֻ����Waitʱ�ż��������TrackIn����TrackMove
				Step step = getCurrentStep();
				if (step.getIsMoveNext()) {
					trackMoveFlag = true;
				} else {
					trackMoveFlag = false;
				}
				if (trackMoveFlag) {
					if (itemTrackMove != null) {
						itemTrackMove.setEnabled(true);
					}
					if (itemTrackIn != null) {
						itemTrackIn.setEnabled(false);
					}
				} else {
					if (itemTrackMove != null) {
						itemTrackMove.setEnabled(false);
					}
					if (itemTrackIn != null) {
						itemTrackIn.setEnabled(true);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public void statusChanged(String newStatus) {
		if (LotStateMachine.STATE_RUN.equals(newStatus)) {
			if (itemTrackIn != null) {
				itemTrackIn.setEnabled(false);
				if (getCurrentStep().getIsMultiEqp()) {
					itemTrackIn.setEnabled(true);
				}
			}
			
			//������ݲɼ��Ƿ���ڡ��Ƿ��Ѿ����
			boolean edcCompFlag = true;
			List<EdcSetCurrent> currents = getEdcSetCurrent();
			if (currents != null && currents.size() > 0){
				if (itemDcop != null) {
					itemDcop.setEnabled(true);
					//itemAutoDcop.setEnabled(true);
				}
				for (EdcSetCurrent current : currents) {
					if (!EdcSetCurrent.FLAG_DONE.equals(current.getEdcFlag())
							&& !EdcSetCurrent.FLAG_PASS.equals(current.getEdcFlag())) {
						//ֻҪ��һ�����ݲɼ�δ���
						edcCompFlag = false;
						break;
					}
				}
			} else {
				if (itemDcop != null) {
					itemDcop.setEnabled(false);
					//itemAutoDcop.setEnabled(false);
				}
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(edcCompFlag);
			}
			if (itemAbort != null) {
				itemAbort.setEnabled(true);
			}
			if (itemTrackMove != null) {
				itemTrackMove.setEnabled(false);
			}
		} else if (LotStateMachine.STATE_WAIT.equals(newStatus)) {
			if (trackMoveFlag) {
				if (itemTrackMove != null) {
					itemTrackMove.setEnabled(true);
				}
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(false);
				}
			} else {
				if (itemTrackMove != null) {
					itemTrackMove.setEnabled(false);
				}
				if (itemTrackIn != null) {
					itemTrackIn.setEnabled(true);
				}
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(false);
			}
			if (itemDcop != null) {
				itemDcop.setEnabled(false);
				//itemAutoDcop.setEnabled(false);
			}
			if (itemAbort != null) {
				itemAbort.setEnabled(false);
			}
		} else {
			if (itemTrackIn != null) {
				itemTrackIn.setEnabled(false);
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(false);
			}
			if (itemDcop != null) {
				itemDcop.setEnabled(false);
			}
			if (itemTrackOut != null) {
				itemTrackOut.setEnabled(false);
			}
			if (itemTrackMove != null) {
				itemTrackMove.setEnabled(false);
			}
			if (itemAbort != null) {
				itemAbort.setEnabled(false);
			}
		}
		Lot lot = (Lot) prdProcedureForm.getValue();
		if (lot != null && Lot.HOLDSTATE_ON.equals(lot.getHoldState())) {
			//run Hold״̬���Գ�վ
			if(itemTrackIn != null) {
				itemTrackIn.setEnabled(false);
			}
		}
	}
	
	protected void loadCurrentStep(Lot lot) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);
			Step step = new Step();
			step.setObjectRrn(lot.getStepRrn());
			step = (Step) prdManager.getSimpleProcessDefinition(step);
			setCurrentStep(step);
		} catch (Exception e) {
			logger.warn("LotSection loadCurrentStep(): Step isn' t exsited!");
		}
	}
	
	public void notifyLotChangeListeners(Object sender, Lot newLot) {
		synchronized (lotChangeListeners) {
			for (ILotChangeListener listener : lotChangeListeners) {
				try {
					listener.lotChanged(sender, newLot);
				} catch (Throwable t) {
					t.printStackTrace();
				}
			}
		}
	}
	
	protected void refreshAdapter(Object obj) {
		Lot lot = (Lot) basicInfoForm.getValue();
		if (lot == null || lot.getObjectRrn() == null) {
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			return;
		}
		lot = searchLot(lot.getLotId());
		setAdObject(lot);
		setLotById(lot.getLotId());
		if (lot != null && lot.getObjectRrn() != null) {
			loadCurrentStep(lot);
		}
	}
	
	protected List<EdcSetCurrent> getEdcSetCurrent() {
		try {
			Lot lot = (Lot) prdProcedureForm.getValue();
			EDCManager edcManager = Framework.getService(EDCManager.class);
			return edcManager.getItemSetCurrents(Env.getOrgRrn(), lot.getBatchId(), lot.getObjectRrn(), null);
		} catch (ClientException e) {
			return null;
		} catch (Exception e) {
			return null;
		}
	}
	
	public Step getCurrentStep() {
		if (currentStep == null) {
			Lot lot = (Lot) prdProcedureForm.getValue();
			if (lot != null && lot.getObjectRrn() != null) {
				loadCurrentStep(lot);
			}
		}
		return currentStep;
	}
	
	public void setCurrentStep(Step currentStep) {
		this.currentStep = currentStep;
	}
	
	
	// ��ͣ��ť
	protected void holdAdapter(Object object) {
		try {
			Object selectedObject = basicInfoForm.getValue();

			if (selectedObject == null || ((Lot)selectedObject).getObjectRrn() == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}

			Event event = (Event) object;
			WIPLotHoldDialog baseDialog = new WIPLotHoldDialog(FORM_HOLDFORM, null, eventBroker, (Lot) selectedObject, event);
			if (!baseDialog.validate()) {
				return;
			}
			if (Dialog.OK == baseDialog.open()) {
				getLotByLotId(((Lot) selectedObject).getLotId());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	// ���а�ť
	protected void releaseAdapter(Object object) {
		try {
			Object selectedObject = basicInfoForm.getValue();

			if (selectedObject == null || ((Lot)selectedObject).getObjectRrn() == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}

			Lot lot = (Lot) selectedObject;

			if (!StringUtils.equals(lot.getHoldState(), Lot.HOLDSTATE_ON)) {
				UI.showInfo(Message.getString("wip.release_lothold_not_found"));
				return;
			}

			Event event = (Event) object;
			WIPLotReleaseDialog baseDialog = new WIPLotReleaseDialog(FORM_RELEASEFORM, null, eventBroker, (Lot) selectedObject, event);
			if (!baseDialog.validate()) {
				return;
			}
			if (Dialog.OK == baseDialog.open()) {
				getLotByLotId(((Lot) selectedObject).getLotId());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	// ������ť
	protected void spiltAdapter(Object object) {
		try {
			Event event = (Event) object;
			Object selectedObject = basicInfoForm.getValue();

			if (selectedObject == null || ((Lot)selectedObject).getObjectRrn() == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			Lot lot = (Lot) selectedObject;
			if (QtyUnit.class.getSimpleName().equals(lot.getSubUnitType())) {
				WIPLotSplitDialog baseDialog = new WIPLotSplitDialog(FORM_QTY_SPILTFORM, null, eventBroker, (Lot) selectedObject, event);
				if (!baseDialog.validate()) {
					return;
				}
				// ������巵�� �������岻ˢ������
				baseDialog.setCloseAdaptor(new Consumer<WIPLotSplitDialog>() {
	    			
	    			@Override
	    			public void accept(WIPLotSplitDialog t) {
	    				getLotByLotId(((Lot) selectedObject).getLotId());
	    				
	    			}
	    		});
	    		if (Dialog.OK == baseDialog.open()) {
	    			getLotByLotId(((Lot) selectedObject).getLotId());
	    		}
			} else if (ComponentUnit.class.getSimpleName().equals(lot.getSubUnitType())) {
				WIPLotSplitDialog baseDialog = new WIPLotSplitDialog(FORM_SPILTFORM, null, eventBroker, (Lot) selectedObject, event);
				if (!baseDialog.validate()) {
					return;
				}
				// ������巵�� �������岻ˢ������
				baseDialog.setCloseAdaptor(new Consumer<WIPLotSplitDialog>() {
	    			
	    			@Override
	    			public void accept(WIPLotSplitDialog t) {
	    				getLotByLotId(((Lot) selectedObject).getLotId());
	    				
	    			}
	    		});
				if (Dialog.OK == baseDialog.open()) {
					getLotByLotId(((Lot) selectedObject).getLotId());
				}
			}

		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	// ������ť
	protected void mergeAdapter(Object object) {
		try {
			Object selectedObject = basicInfoForm.getValue();

			if (selectedObject == null || ((Lot)selectedObject).getObjectRrn() == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}

			Event event = (Event) object;
			WIPLotMergeDialog baseDialog = new WIPLotMergeDialog(FORM_MERGEFORM, null, eventBroker, (Lot) selectedObject, event);
			if (!baseDialog.validate()) {
				return;
			}
			// ������巵�� �������岻ˢ������
			baseDialog.setCloseAdaptor(new Consumer<WIPLotMergeDialog>() {

				@Override
				public void accept(WIPLotMergeDialog t) {
					getLotByLotId(((Lot) selectedObject).getLotId());

				}
			});
			if (Dialog.OK == baseDialog.open()) {
				getLotByLotId(((Lot) selectedObject).getLotId());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	// �����޸İ�ť
	protected void changeParameterAdapter(Object object) {
		try {
			Object selectedObject = basicInfoForm.getValue();

			if (selectedObject == null || ((Lot)selectedObject).getObjectRrn() == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}

			Event event = (Event) object;
			WIPLotParameterDialog baseDialog = new WIPLotParameterDialog(FORM_CHANGE_PARAMTER_FORM, null, eventBroker, (Lot) selectedObject, event);
			if (!baseDialog.validate()) {
				return;
			}
			// ������巵�� �������岻ˢ������
			baseDialog.setCloseAdaptor(new Consumer<WIPLotParameterDialog>() {
    			
    			@Override
    			public void accept(WIPLotParameterDialog t) {
    				getLotByLotId(((Lot) selectedObject).getLotId());
    				
    			}
    		});
    		if (Dialog.OK == baseDialog.open()) {
    			getLotByLotId(((Lot) selectedObject).getLotId());
    		}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	// �����޸İ�ť
	protected void changeCommentAdapter(Object object) {
		try {
			Object selectedObject = basicInfoForm.getValue();

			if (selectedObject == null || ((Lot)selectedObject).getObjectRrn() == null) {
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}

			Event event = (Event) object;
			LotChangeCommentDialog baseDialog = new LotChangeCommentDialog(FORM_CHANGE_COMMENT_FORM, null, eventBroker, (Lot) selectedObject, event);
			if (Dialog.OK == baseDialog.open()) {
				getLotByLotId(((Lot) selectedObject).getLotId());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	public void registerAccelerator() {
		acceleratorListener = acceleratorListener();
		this.txtLotId.addListener(SWT.KeyDown, acceleratorListener);
		if (this.txtOperator != null) {
			this.txtOperator.addListener(SWT.KeyDown, acceleratorListener);
		}
	}
	
	protected Listener acceleratorListener() {
		return new Listener() {
			public void handleEvent(org.eclipse.swt.widgets.Event e) {
				if ((Lot)basicInfoForm.getValue() != null) {
					switch (e.keyCode) {
					case SWT.F2:
						if (itemTrackMove != null && itemTrackMove.isEnabled()) {
							trackMoveAdapter(null);
						}
						break;
					case SWT.F3:
						if (itemTrackIn != null && itemTrackIn.isEnabled()) {
							trackInAdapter(null);
						}
						break;
					case SWT.F4:
						if (itemDcop != null && itemDcop.isEnabled()) {
							docpAdapter(null);
						}
						break;
					case SWT.F5:
						if (itemTrackOut != null && itemTrackOut.isEnabled()) {
							trackOutAdapter(null);
						}
						break;
					case SWT.F6:
						if (itemAbort != null && itemAbort.isEnabled()) {
							abortAdapter(null);
						}
						break;
					case SWT.F7:
						if (itemHisSearch != null && itemHisSearch.isEnabled()) {
							hisSearchAdapter(null);
						}
						break;
					case SWT.F8:
						if (itemFutureStep != null && itemFutureStep.isEnabled()) {
							futureStepAdapter(null);
						}
						break;
					case SWT.F9:
						if (itemChangeComment != null && itemChangeComment.isEnabled()) {
							changeCommentAdapter(null);
						}
						break;
					case SWT.F10:
						if (itemRefresh != null && itemRefresh.isEnabled()) {
							refreshAdapter(null);
						}
						break;
					}
				}
			}
		};
	}
	
}
