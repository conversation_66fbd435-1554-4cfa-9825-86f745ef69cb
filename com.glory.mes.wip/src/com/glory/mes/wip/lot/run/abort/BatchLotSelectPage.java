package com.glory.mes.wip.lot.run.abort;

import org.apache.log4j.Logger;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.wizard.FlowWizardPage;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.lot.run.trackout.TrackOutContext;
import com.glory.mes.wip.lot.run.trackout.TrackOutWizard;

public class BatchLotSelectPage extends FlowWizardPage {
	private static final Logger logger = Logger.getLogger(BatchLotSelectPage.class);
	private static final String PREVIOUS_PAGE = "showKeyMaterial";
	private static String TABLE_ANME = "ByEqpSelectedLot";
	
	private BatchLotSelectSection section;
	protected IManagedForm form;
	
	public BatchLotSelectPage() {
		super();
	}
	
	public BatchLotSelectPage(String pageName, Wizard wizard, String defaultDirect) {
		super(pageName, wizard, defaultDirect);
	}

	@Override
	public void createControl(Composite parent) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable  = adManager.getADTable(Env.getOrgRrn(), TABLE_ANME);
			setTitle(Message.getString("wip.batch_lot_list"));
			
			Composite composite = new Composite(parent, SWT.NULL);
			GridLayout layout = new GridLayout();
			layout.marginHeight = 0;
			layout.marginWidth = 0;
			composite.setLayout(layout);
			GridData gd = new GridData(GridData.FILL_BOTH);
			composite.setLayoutData(gd);
			
			FormToolkit toolkit = new FormToolkit(composite.getDisplay());
			ScrolledForm sForm = toolkit.createScrolledForm(composite);
			sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
			Composite body = sForm.getForm().getBody();
			form = new ManagedForm(toolkit, sForm);
			configureBody(body);

			section = new BatchLotSelectSection(adTable, this);
			section.createContents(form, body);
			
			setControl(composite);
			setPageComplete(true);
		} catch (Exception e) {
			logger.error("ShowStepOperationForm : addFields()",e);
		}		
	}
	
	@Override
	public String doNext() {
		TrackOutContext context = ((TrackOutWizard)getWizard()).getContext();
		context.setLots(section.getSelectedLots());
		return "finish";
	}

	@Override
	public String doPrevious() {
		this.setErrorMessage(null);
		return PREVIOUS_PAGE;
	}
	
	public IWizardPage getPreviousPage() {
		return this.getWizard().getPage(PREVIOUS_PAGE);
	}
	
	@Override
	public boolean canFlipToNextPage() {
		return isPageComplete();
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, true);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
}
