package com.glory.mes.pvc.ejb;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.ejb.EJB;
import javax.ejb.Remote;
import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.log4j.Logger;
import org.infinispan.manager.CacheContainer;

import com.glory.common.rti.RTIExecutorService;
import com.glory.framework.activeentity.ejb.SysParameterManagerLocal;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.model.Documentation;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ClientParameterException;
import com.glory.framework.core.exception.ExceptionManager;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.base.ejb.MBASManagerLocal;
import com.glory.mes.base.model.Line;
import com.glory.mes.base.model.Vendor;
import com.glory.mes.base.model.idgenerator.DateRuleLine;
import com.glory.mes.base.model.idgenerator.FixedStringRuleLine;
import com.glory.mes.base.model.idgenerator.GeneratorContext;
import com.glory.mes.base.model.idgenerator.GeneratorRule;
import com.glory.mes.base.model.idgenerator.GeneratorRuleLine;
import com.glory.mes.base.model.idgenerator.SequenceRuleLine;
import com.glory.mes.mm.bom.model.AbstractBomLine;
import com.glory.mes.mm.bom.model.Bom;
import com.glory.mes.mm.bom.model.BomLine;
import com.glory.mes.mm.durable.model.Carrier;
import com.glory.mes.mm.durable.model.DurableSpec;
import com.glory.mes.mm.ejb.DurableManagerLocal;
import com.glory.mes.mm.ejb.MMManagerLocal;
import com.glory.mes.mm.his.model.MLotHis;
import com.glory.mes.mm.inv.model.Warehouse;
import com.glory.mes.mm.lot.model.EquipmentMaterial;
import com.glory.mes.mm.lot.model.MLot;
import com.glory.mes.mm.lot.model.MLotAction;
import com.glory.mes.mm.lot.model.MLotStorage;
import com.glory.mes.mm.model.Exceptions;
import com.glory.mes.mm.model.Material;
import com.glory.mes.mm.state.model.MaterialEvent;
import com.glory.mes.pp.ejb.PpManagerLocal;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pp.model.WorkOrderBomLine;
import com.glory.mes.pp.model.WorkOrderLot;
import com.glory.mes.prd.ejb.PrdManagerLocal;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.model.Step;
import com.glory.mes.pvc.cdi.PvcAlarmAction;
import com.glory.mes.pvc.cdi.PvcGenerateComponentsByLotAction;
import com.glory.mes.pvc.cdi.PvcGenerateDefectMLotIdAction;
import com.glory.mes.pvc.cdi.PvcGenerateLotByWorkOrderAction;
import com.glory.mes.pvc.cdi.PvcGenerateLotIdAction;
import com.glory.mes.pvc.cdi.PvcMLotProcessAction;
import com.glory.mes.pvc.cdi.service.alarm.AlarmCdiContext;
import com.glory.mes.pvc.cdi.service.alarm.WorkOrderAlarmProcessAction;
import com.glory.mes.pvc.client.PvcCdiManager;
import com.glory.mes.pvc.client.PvcManager;
import com.glory.mes.pvc.context.PvcInContext;
import com.glory.mes.pvc.context.PvcNewLotCdiContext;
import com.glory.mes.pvc.exception.PvcExceptionBundle;
import com.glory.mes.pvc.model.BoatPosition;
import com.glory.mes.pvc.model.BoxInfo;
import com.glory.mes.pvc.model.BoxInfoHis;
import com.glory.mes.pvc.model.CarrierGenerateRule;
import com.glory.mes.pvc.model.DefectHis;
import com.glory.mes.pvc.model.EdcDataMappingRule;
import com.glory.mes.pvc.model.LineChildren;
import com.glory.mes.pvc.model.LineWorkOrder;
import com.glory.mes.pvc.model.LineWorkOrderHis;
import com.glory.mes.pvc.model.PvcCode;
import com.glory.mes.pvc.model.StockTakingResult;
import com.glory.mes.pvc.model.wip.ComponentUnitProcessHis;
import com.glory.mes.pvc.model.wip.WorkOrderMLotHis;
import com.glory.mes.ras.ejb.RASManagerLocal;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.ras.model.EquipmentLine;
import com.glory.mes.wip.ejb.CarrierLotManagerLocal;
import com.glory.mes.wip.ejb.ComponentManagerLocal;
import com.glory.mes.wip.ejb.ConstraintManagerLocal;
import com.glory.mes.wip.ejb.LotManagerLocal;
import com.glory.mes.wip.ejb.MLotManagerLocal;
import com.glory.mes.wip.his.LotHis;
import com.glory.mes.wip.mm.MaterialRequisition;
import com.glory.mes.wip.mm.MaterialRequisitionDetail;
import com.glory.mes.wip.mm.MaterialRequisitionLine;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotStateMachine;
import com.glory.mes.wip.model.ProcessUnit;
import com.google.common.collect.Lists;

@Deprecated
@Stateless
@Remote(PvcManager.class)
public class PvcManagerBean implements PvcManager {

	private static final Logger logger = Logger.getLogger(PvcManagerBean.class);

	@PersistenceContext
	private EntityManager em;

	@EJB
	private PvcADManagerLocal adManager;
	
	@EJB(lookup="java:global/MESwell/mesbase/MBASManagerBean!com.glory.mes.base.ejb.MBASManagerLocal")
	private MBASManagerLocal mBasManager;
	
	@EJB(lookup="java:global/MESwell/ras/RASManagerBean!com.glory.mes.ras.ejb.RASManagerLocal")
	private RASManagerLocal rasManager;
	
	@EJB(lookup="java:global/MESwell/mm/DurableManagerBean!com.glory.mes.mm.ejb.DurableManagerLocal")
	private DurableManagerLocal durableManager;
	
	@EJB(lookup="java:global/MESwell/wip/PpManagerBean!com.glory.mes.pp.ejb.PpManagerLocal")
	private PpManagerLocal ppManager;

	@EJB(lookup="java:global/MESwell/wip/LotManagerBean!com.glory.mes.wip.ejb.LotManagerLocal")
	private LotManagerLocal lotManager;

	@EJB(lookup="java:global/MESwell/wip/PrdManagerBean!com.glory.mes.prd.ejb.PrdManagerLocal")
	private PrdManagerLocal prdManager;

	@EJB(lookup="java:global/MESwell/wip/CarrierLotManagerBean!com.glory.mes.wip.ejb.CarrierLotManagerLocal")
	private CarrierLotManagerLocal carrierLotManager;
	
	@EJB(lookup="java:global/MESwell/wip/ComponentManagerBean!com.glory.mes.wip.ejb.ComponentManagerLocal")
	private ComponentManagerLocal componentManager;
	
	@EJB(lookup="java:global/MESwell/mm/MMManagerBean!com.glory.mes.mm.ejb.MMManagerLocal")
	private MMManagerLocal mmManager;
	
	@EJB(lookup="java:global/MESwell/wip/MLotManagerBean!com.glory.mes.wip.ejb.MLotManagerLocal")
	private MLotManagerLocal mLotManager;
	
	@EJB(lookup="java:global/MESwell/wip/ConstraintManagerBean!com.glory.mes.wip.ejb.ConstraintManagerLocal")
	private ConstraintManagerLocal constraintManager;
	
	@EJB(lookup="java:global/MESwell/system/SysParameterManagerBean!com.glory.framework.activeentity.ejb.SysParameterManagerLocal")
	private SysParameterManagerLocal sysParamManager;
	
	@EJB
	private PvcCdiManager pvcCdiManager;
	
	@EJB(lookup="java:global/rti/rti/RTIExecutorService")
    private RTIExecutorService rtiService;
	
	@Resource(lookup = "java:jboss/infinispan/container/pvc")
	private CacheContainer container;

	public EntityManager getEntityManager() {
		return em;
	}
	
	//碎片
	private static final String FRAGMENT_TYPE = "碎片";
	
	//不良
	private static final String REWORK_TYPE = "不良";

	


	/**
	 * 根据ID获取ComponentUnit列表
	 * @param orgRrn
	 * @param componentIds
	 * @return
	 * @throws ClientException
	 */
	@Override
	public List<ComponentUnit> getComponentsByIds(long orgRrn, List<String> componentIds) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer(" SELECT ComponentUnit FROM ComponentUnit ComponentUnit ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND componentId in (:componentIds) ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("componentIds", componentIds);
			List<ComponentUnit> componentList = query.getResultList();
			return componentList;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 太阳能电池片一个设备只会对应一个工序
	 * @param orgRrn
	 * @param equipmentId
	 * @return
	 * @throws ClientException
	 */
	public Step getStepByEquipmentId(long orgRrn, String equipmentId) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer("SELECT Step FROM Equipment Equipment, EquipmentCapa EquipmentCapa, Capa Capa, Step Step ");
			sql.append("WHERE Equipment.objectRrn = EquipmentCapa.equipmentRrn ");
			sql.append("AND EquipmentCapa.capaRrn = Capa.objectRrn ");
			sql.append("AND Capa.objectRrn = Step.capability ");
			sql.append("AND Equipment.orgRrn = :orgRrn ");
			sql.append("AND Equipment.equipmentId = :equipmentId");

			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("equipmentId", equipmentId);

			List<Step> steps = query.getResultList();
			if (steps == null || steps.isEmpty()) {
				return null;
			}
			return steps.get(0);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 从Cache中获取设备对应的工序
	 * @param orgRrn
	 * @param equipmentId
	 * @return
	 * @throws ClientException
	 */
	@Deprecated
	public Step getStepByEquipmentIdCache(long orgRrn, String equipmentId) throws ClientException {
		try {
			Step step = (Step) container.getCache(StartupPvc.CACHE_NAME_EQP_STEP).get(orgRrn + equipmentId);
			return step;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 从Cache中获取当前工序的下一个工序
	 * @param step
	 * @return
	 * @throws ClientException
	 */
	@Deprecated
	public Step getNextStepCache(Step step, SessionContext sc) throws ClientException {
		try {
			Step nextStep = (Step) container.getCache(StartupPvc.CACHE_NAME_NEXT_STEP).get(sc.getOrgRrn() + step.getName());
			return nextStep;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 从Cache中获取第一个工序
	 * @param step
	 * @return
	 * @throws ClientException
	 */
	@Deprecated
	public Step getFirstStepCache(long orgRrn) throws ClientException {
		try {
			Step firstStep = (Step) container.getCache(StartupPvc.CACHE_NAME_FIRST_STEP).get(orgRrn);
			return firstStep;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据设备ID获取线体
	 * 
	 * @param orgRrn
	 * @param equipmentId
	 * @return
	 * @throws ClientException
	 */
	public EquipmentLine getEquipmentLine(long orgRrn, String equipmentId) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer("SELECT EquipmentLine FROM EquipmentLine EquipmentLine WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND equipmentId = :equipmentId");

			Query query = rasManager.getEntityManager().createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("equipmentId", equipmentId);
			List<EquipmentLine> lines = query.getResultList();
			if (CollectionUtils.isEmpty(lines)) {
				return null;
			}
			return lines.get(0);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据设备ID获取线体
	 * 
	 * @param orgRrn
	 * @param equipmentId
	 * @return
	 * @throws ClientException
	 */
	public EquipmentLine getEquipmentLineByEquipmentId(long orgRrn, String equipmentId) throws ClientException {
		try {
			EquipmentLine line = null;
			Equipment parentEquipment = null;
			
			Equipment equipment = rasManager.getEquipmentByEquipmentId(orgRrn, equipmentId);
			if (equipment.getParentEqpRrn() != null) {
				parentEquipment = rasManager.getEntityManager().find(Equipment.class, equipment.getParentEqpRrn());
			} else {
				parentEquipment = equipment;
			}
			if (parentEquipment != null) {
				line = getEquipmentLine(orgRrn, parentEquipment.getEquipmentId());
			}
			return line;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 工单分多次投料
	 * 
	 * @param workOrder
	 *            待投料工单
	 * @param workOrderLots
	 *            工单做对应的工单批次
	 * @param sourceLots
	 *            工单做对应的原材料批次
	 * @param isAssignDurable
	 *            是否Assign到载具
	 * @param sc
	 */
	public List<Lot> startWorkOrder(WorkOrder workOrder, List<WorkOrderLot> workOrderLots, List<MLot> sourceLots, Map<String, Object> generateParaMap, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			// 重新获取wo
			workOrder = ppManager.getWorkOrder(workOrder, sc);
			List<WorkOrderBomLine> bomLines = ppManager.getWorkOrderBomLines(workOrder, sc);
			List<Lot> lots = Lists.newArrayList();
			
			// 按orderLot单独投料
			for (WorkOrderLot orderLot : workOrderLots) {
				// 重新获取sourceLots
				List<MLot> nowSources = sourceLots.stream()
						.map(s -> mmManager.getMLotByMLotId(sc.getOrgRrn(), s.getmLotId()))
						.collect(Collectors.toList());
				
				// 计算出每一种物料需要多少
				List<MLot> sourceMLotsByLot = Lists.newArrayList();
				for (WorkOrderBomLine bomLine : bomLines) {
					// 首先检查原材料中有没有该种物料
					List<MLot> lineSources = nowSources.stream().filter(n -> n.getMaterialName().equals(bomLine.getMaterialName()) &&
							n.getMaterialVersion().compareTo(bomLine.getMaterialVersion()) == 0).collect(Collectors.toList());
					if (CollectionUtils.isNotEmpty(lineSources)) {
						// 计算出批次需要的总数
						BigDecimal needQty = bomLine.getUnitQty().multiply(orderLot.getMainQty());
						
						Iterator<MLot> it = lineSources.iterator();
						while (needQty.compareTo(BigDecimal.ZERO) > 0 && it.hasNext()) {
							MLot lineSource = it.next();
							BigDecimal avaliableQty = lineSource.getAvaliableMainQty();
							if (avaliableQty.compareTo(BigDecimal.ZERO) >= 0) {
								if (avaliableQty.compareTo(needQty) > 0) {
									lineSource.setTransMainQty(needQty);
								} else {
									lineSource.setTransMainQty(lineSource.getMainQty());
								}
								needQty = needQty.subtract(lineSource.getTransMainQty());
								
								sourceMLotsByLot.add(lineSource);
							}
						}
					}
				}
				
				// 重新获取wo
				workOrder = ppManager.getWorkOrder(workOrder, sc);
				
				PvcNewLotCdiContext pncContext = new PvcNewLotCdiContext();
				pncContext.setWorkOrder(workOrder);
				pncContext.setWorkOrderLot(orderLot);
				pncContext.setIdentityComponent(true);
				pncContext.setGenerateParaMap(generateParaMap);
				
				PvcGenerateLotByWorkOrderAction action = pvcCdiManager.getGenerateLotActions().get(0);
				Lot newLot = action.newLotStartByWorkOrder(pncContext, sc);
				
				lots.add(newLot);
			}
			return lots;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw ExceptionManager.handleException(e);
		}
	}
	
	@Override
	public LineWorkOrder savaLineWorkOrder(LineWorkOrder lineWorkOrder, SessionContext sc) throws ClientException {
		sc.buildTransInfo();
		try {

			if (lineWorkOrder.getObjectRrn() == null) {
				lineWorkOrder.setCreatedBy(sc.getUserName());
				em.persist(lineWorkOrder);
				LineWorkOrderHis lineWorkOrderHis = new LineWorkOrderHis(lineWorkOrder, sc);
				lineWorkOrderHis.setTransType(LineWorkOrderHis.TRANS_TYPE_CREATE);
				em.persist(lineWorkOrderHis);
			} else {
				lineWorkOrder.setUpdatedBy(sc.getUserName());
				em.merge(lineWorkOrder);
				LineWorkOrderHis lineWorkOrderHis = new LineWorkOrderHis(lineWorkOrder, sc);
				lineWorkOrderHis.setTransType(LineWorkOrderHis.TRANS_TYPE_UPDATE);
				em.persist(lineWorkOrderHis);
			}

			return lineWorkOrder;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	public Line saveLine(Line line, List<LineChildren> lineChildrens, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			line.setIsActive(true);
			line.setOrgRrn(sc.getOrgRrn());
			if (line.getObjectRrn() == null) {
				line.setCreated(new Date());
				line.setCreatedBy(sc.getUserName());
				em.persist(line);
				for (LineChildren lineChildren : lineChildrens) {
					lineChildren.setCreated(new Date());
					lineChildren.setCreatedBy(sc.getUserName());
					lineChildren.setLineRrn(line.getObjectRrn());
					em.persist(lineChildren);
				}

			} else {
				line.setUpdated(new Date());
				line.setUpdatedBy(sc.getUserName());
				em.merge(line);
				String del = "DELETE FROM LineChildren LineChildren WHERE lineRrn = :lineRrn";
				Query query2 = em.createQuery(del);
				query2.setParameter("lineRrn", line.getObjectRrn());
				query2.executeUpdate();
				for (LineChildren lineChildren : lineChildrens) {
					lineChildren.setUpdated(new Date());
					lineChildren.setUpdatedBy(sc.getUserName());
					lineChildren.setLineRrn(line.getObjectRrn());
					em.persist(lineChildren);
				}
			}
			return line;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}

	@Override
	public void deleteLineChildren(Line line, SessionContext sc) throws ClientException {
		String del = "DELETE FROM LineChildren LineChildren WHERE lineRrn = :lineRrn";
		Query query2 = em.createQuery(del);
		query2.setParameter("lineRrn", line.getObjectRrn());
		query2.executeUpdate();		
	}
	
	
	/**
	 * 根据WorkOrderLot生成返工批次
	 * 
	 * @param workOrder 返工工单
	 * @param workOrderLots 返工计划批次信息
	 * @param sourceLots 返工批次源物料
	 * 
	 * @param sc
	 * @throws ClientException
	 */
	public List<Lot> startReworkLot(WorkOrder workOrder, List<WorkOrderLot> workOrderLots, List<MLot> sourceLots, Map<String, Object> generateParaMap, SessionContext sc) throws ClientException {
		return startReworkWorkOrder(workOrder, workOrderLots, sourceLots, generateParaMap, sc);
	}
	
	/**
	 * 返工工单分多次投料，返工工单没有BOM，只支持消耗一个物料批，按照工单批次数量消耗
	 * @param workOrder 待投料工单
	 * @param workOrderLots 工单做对应的工单批次
	 * @param sourceLots 工单做对应的原材料批次
	 * @param sc
	 */
	public List<Lot> startReworkWorkOrder(WorkOrder workOrder, List<WorkOrderLot> workOrderLots, List<MLot> sourceLots,
			Map<String, Object> generateParaMap, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			// 重新获取wo
			workOrder = ppManager.getWorkOrder(workOrder, sc);
			List<Lot> lots = Lists.newArrayList();

			// 按orderLot单独投料
			for (WorkOrderLot orderLot : workOrderLots) {
				MLot mlot = mmManager.getMLotByMLotId(sc.getOrgRrn(), sourceLots.get(0).getmLotId());
				if (orderLot.getMainQty().compareTo(mlot.getAvaliableMainQty()) > 0) {
					throw new ClientException("pvc.mlot_qty_less");
				}
				mlot.setTransMainQty(orderLot.getMainQty());

				PvcGenerateLotByWorkOrderAction action = pvcCdiManager.getGenerateLotActions().get(0);
				// 重新获取wo
				workOrder = ppManager.getWorkOrder(workOrder, sc);
				
				/*Lot newLot = action.newLotStartByWorkOrder(workOrder, orderLot, Arrays.asList(mlot), null,
						PvcCode.isTrackByComponent(sc.getOrgRrn(), sysParamManager), true, sc);*/
				
				PvcNewLotCdiContext pncContext = new PvcNewLotCdiContext();
				pncContext.setWorkOrder(workOrder);
				pncContext.setWorkOrderLot(orderLot);
				pncContext.setIdentityComponent(true);
				pncContext.setGenerateParaMap(generateParaMap);
				Lot newLot = action.newLotStartByWorkOrder(pncContext, sc);
				lots.add(newLot);
			}
			return lots;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw ExceptionManager.handleException(e);
		}
	}
	
	/**
	 * 工单物料退回到第三方
	 * 
	 * @param workOrder 工单
	 * @param mLots 待退库物料
	 * @param sc
	 * @throws ClientException
	 */
	public List<MLot> returnMLotsToThird(WorkOrder workOrder, List<MLot> mLots, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			List<MLotAction> actions = Lists.newArrayList();
			List<MLot> retLots = Lists.newArrayList();
			for (MLot mLot : mLots) {
				List<MLotStorage> storages = mmManager.getLotStorages(mLot.getObjectRrn());
				if (CollectionUtils.isEmpty(storages)) {
					throw new ClientException("error.mlot_not_in_storage");
				}
				
				if (storages.size() > 1) {
					throw new ClientException(com.glory.mes.mm.model.Exceptions.MLOT_IN_MULIT_LOTSTORAGE);
				}
				
				MLotStorage storage = storages.get(0);
				
				MLotAction lotAction = new MLotAction();
				lotAction.setmLotRrn(mLot.getObjectRrn());
				lotAction.setMainQty(mLot.getTransMainQty());
				lotAction.setActionCode(mLot.getAttribute1());
				lotAction.setActionReason(mLot.getAttribute2());
				lotAction.setActionComment(mLot.getAttribute3());
				actions.add(lotAction);
				
				//物料消耗
				MLot retLot = mmManager.consumeAction(
						mLot, Lists.newArrayList(lotAction), PvcCode.MATERIAL_EVENT_THIRDRETURN, 
						storage.getWarehouseRrn(), null, storage.getStorageType(), storage.getStorageId(), false, sc);
				
				retLots.add(retLot);
				
				//工单物料退回历史
         		WorkOrderMLotHis woMLotHis = new WorkOrderMLotHis();
         		woMLotHis.setOrgRrn(sc.getOrgRrn());
         		woMLotHis.setTransType(WorkOrderMLotHis.TRANS_TYPE_RETURN);
         		woMLotHis.setTransTime(sc.getTransTime());
         		woMLotHis.setHistorySeq(sc.getTransRrn());
         		woMLotHis.setmLotId(mLot.getmLotId());
         		woMLotHis.setmLotRrn(mLot.getObjectRrn());
         		woMLotHis.setMaterialName(mLot.getMaterialName());
         		woMLotHis.setMaterialRrn(mLot.getMaterialRrn());
         		woMLotHis.setMaterialDesc(mLot.getMaterialDesc());
         		woMLotHis.setWoId(workOrder.getDocId());
         		woMLotHis.setPartName(workOrder.getPartName());
         		woMLotHis.setMainQty(lotAction.getMainQty());
         		woMLotHis.setUdf(mLot.getUdf());
         		
         		em.persist(woMLotHis);
			}
			return retLots;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 申请退料，通知第三方
	 * @param workOrder
	 * @param mLots
	 * @param sc
	 * @throws ClientException
	 */
	public void returnRequestMLot2Third(WorkOrder workOrder, List<MLot> mLots, SessionContext sc)
			throws ClientException {
		try {
			// 通知到第三方
			PvcMLotProcessAction action = pvcCdiManager.getMLotProcessActions().get(0);
			action.returnMLot2Third(workOrder, mLots, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 物料不良入库
	 * @param sourceMLot			物料批
	 * @param mLotAction	不良代码
	 * @param sc
	 * @throws ClientException
	 */
	public void defectMLots2Warehouse(MLot sourceMLot, MLotAction mLotAction, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			Long toWarehouseRrn = sourceMLot.getTransWarehouseRrn(); //目标仓库RRN
			String toWarehouseId = sourceMLot.getTransWarehouseId(); //目标仓库ID
			String toStorageType = sourceMLot.getTransStorageType(); //目标库位类型
			String toStorageId = sourceMLot.getTransStorageId(); //目标库位ID
			BigDecimal defectQty = sourceMLot.getTransMainQty(); //不良数量
			
			//消耗源物料的数量(Defect事件与Consume事件相同,只有记录历史的区别)
			MLotAction receiveAction = new MLotAction();
			receiveAction.setMainQty(defectQty);
			sourceMLot = mmManager.consumeAction(sourceMLot, Lists.newArrayList(receiveAction),
					PvcCode.MATERIAL_EVENT_DEFECT, null, null, null, null, false, sc);
			
			if (toWarehouseRrn != null) {
				PvcGenerateDefectMLotIdAction action = pvcCdiManager.getGenerateDefectMLotIdAction().get(0);
				String newMLotId = action.generateMLotIdBySourceMLotDefect(sourceMLot, mLotAction, sc);
				MLot defectMLot = mmManager.getMLotByMLotId(sc.getOrgRrn(), newMLotId);
				if (defectMLot == null) {
					defectMLot = new MLot();
					PropertyUtil.copyProperties(defectMLot, sourceMLot);
					defectMLot.setObjectRrn(null);
					defectMLot.setLockVersion(null);
					defectMLot.setmLotId(newMLotId);
					defectMLot.setMaterialName(sourceMLot.getMaterialName());
					defectMLot.setMainQty(sourceMLot.getTransMainQty());
					defectMLot.setTransMainQty(sourceMLot.getTransMainQty());
					
					//不良物料接收+入库到不良仓库
					mmManager.receiveMLot2Warehouse(defectMLot, receiveAction, toWarehouseRrn, toWarehouseId, toStorageType, toStorageId, sc);
				} else {
					MLotStorage storage = mmManager.getLotStorage(defectMLot.getObjectRrn(), toWarehouseRrn, toStorageType, toStorageId, false);
					if (storage == null) {
						throw new ClientException(Exceptions.STORAGE_IS_NOT_EXIST);
					}
					//变更批次库存
					mmManager.moveMLot(defectMLot.getObjectRrn(), receiveAction.getMainQty(), null, null, null, toWarehouseRrn, toStorageType, toStorageId);
					
					defectMLot.setMainQty(defectMLot.getMainQty().add(receiveAction.getMainQty()));
					defectMLot.setUpdated(sc.getTransTime());
					defectMLot.setUpdatedBy(sc.getUserName());
					em.merge(defectMLot);
					
					MLotHis lotHis = new MLotHis(defectMLot, sc);
					lotHis.setOrgRrn(sc.getOrgRrn());
					lotHis.setCreated(new Date());
					lotHis.setCreatedBy(sc.getUserName());
					lotHis.setTransType(MLotHis.TRANS_TYPE_IN);
					lotHis.setTransMainQty(receiveAction.getMainQty());
					lotHis.setTransWarehouseId(toWarehouseId);
					lotHis.setTransStorageId(toStorageId);
					lotHis.setTransStorageType(toStorageType);
					lotHis.setActionCode(mLotAction.getActionCode());
					lotHis.setActionComment(mLotAction.getActionComment());
					em.persist(lotHis);
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	
	/** 
	 * 在制品不良录入
	 * @param defectHis 不良记录
	 * @param sc
	 * @throws ClientException
	 */
	@Deprecated
	public void defectWipLot2Warehouse(DefectHis defectHis, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			Long toWarehouseRrn = defectHis.getToWarehouseRrn();
			String toWarehouseId = defectHis.getToWarehouseId();
			BigDecimal defectQty = defectHis.getDefectQty();
			
			Part part = prdManager.getPart(sc.getOrgRrn(), defectHis.getPartName());
			
			PvcGenerateDefectMLotIdAction action = pvcCdiManager.getGenerateDefectMLotIdAction().get(0);
			String newMLotId = action.generateMLotIdByWipDefect(part, sc);
			MLot defectMLot = mmManager.getMLotByMLotId(sc.getOrgRrn(), newMLotId);
			if (defectMLot == null) {
				defectMLot = new MLot();
				defectMLot.setObjectRrn(null);
				defectMLot.setLockVersion(null);
				defectMLot.setmLotId(newMLotId);
				defectMLot.setMaterialName(DefectHis.DEFAULT_REWORK_MATERIAL_NAME);
				defectMLot.setTransMainQty(defectQty);

				MLotAction receiveAction = new MLotAction();
				receiveAction.setActionCode(defectHis.getDefectCode());
				receiveAction.setActionComment(defectHis.getDefectDesc());
				receiveAction.setMainQty(defectQty);
				if (toWarehouseRrn != null) {
					// 不良物料接收+入库到不良仓库
					mmManager.receiveMLot2Warehouse(defectMLot, receiveAction, toWarehouseRrn, toWarehouseId, null, null, sc);
				} else {
					mmManager.receiveMLot(defectMLot, receiveAction, sc);
				}
			} else {
				if (toWarehouseRrn != null) {
					MLotStorage storage = mmManager.getLotStorage(defectMLot.getObjectRrn(), toWarehouseRrn, null, null, false);
					if (storage == null) {
						throw new ClientException(Exceptions.STORAGE_IS_NOT_EXIST);
					}
					// 变更批次库存
					mmManager.moveMLot(defectMLot.getObjectRrn(), defectHis.getDefectQty(), null, null, null,
							toWarehouseRrn, null, null);
				}
				defectMLot.setMainQty(defectMLot.getMainQty().add(defectHis.getDefectQty()));
				defectMLot.setUpdated(sc.getTransTime());
				defectMLot.setUpdatedBy(sc.getUserName());
				em.merge(defectMLot);

				MLotHis lotHis = new MLotHis(defectMLot, sc);
				lotHis.setOrgRrn(sc.getOrgRrn());
				lotHis.setCreated(new Date());
				lotHis.setCreatedBy(sc.getUserName());
				lotHis.setTransType(MLotHis.TRANS_TYPE_IN);
				lotHis.setTransMainQty(defectHis.getDefectQty());
				lotHis.setTransWarehouseId(toWarehouseId);
				em.persist(lotHis);
			}
			
			//保存在制品不良录入历史
			defectHis.setCreated(sc.getTransTime());
			defectHis.setCreatedBy(sc.getUserName());
			defectHis.setTransTime(sc.getTransTime());
			defectHis.setTransType(DefectHis.TRANS_TYPE_WIP_DEFECT);
			em.persist(defectHis);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据在制品不良录入信息生成返工用物料批次
	 * @param defectHisList
	 * @param receiveQty 生成数量
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public MLot generateReworkMLotsByWipDefectHis(List<DefectHis> defectHisList, BigDecimal receiveQty, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			MLot reworkMLot = null;
			BigDecimal useQty = new BigDecimal(0);//已经消耗数量
			for (DefectHis defectHis : defectHisList) {
				BigDecimal defectQty = new BigDecimal(0);
				BigDecimal needQty=receiveQty.subtract(useQty);//还需要数量
				if (needQty.compareTo(defectHis.getDefectQty()) >= 0) {
					defectQty = defectHis.getDefectQty();
				} else {
					defectQty = needQty;
				}
				useQty = useQty.add(defectQty);
				
				Long toWarehouseRrn = defectHis.getToWarehouseRrn();
				String toWarehouseId = defectHis.getToWarehouseId();
				
				Part part = prdManager.getPart(sc.getOrgRrn(), defectHis.getPartName());
				
				PvcGenerateDefectMLotIdAction action = pvcCdiManager.getGenerateDefectMLotIdAction().get(0);
				String newMLotId = action.generateMLotIdByWipDefect(part, sc);
				reworkMLot = mmManager.getMLotByMLotId(sc.getOrgRrn(), newMLotId);
				if (reworkMLot == null) {
					reworkMLot = new MLot();
					reworkMLot.setObjectRrn(null);
					reworkMLot.setLockVersion(null);
					reworkMLot.setmLotId(newMLotId);
					reworkMLot.setMaterialName(DefectHis.DEFAULT_REWORK_MATERIAL_NAME);
					reworkMLot.setTransMainQty(defectQty);
					reworkMLot.setmLotType("R");

					MLotAction receiveAction = new MLotAction();
					receiveAction.setActionCode(defectHis.getDefectCode());
					receiveAction.setActionComment(defectHis.getDefectDesc());
					receiveAction.setMainQty(defectQty);
					if (toWarehouseRrn != null) {
						// 不良物料接收+入库到不良仓库
						reworkMLot = mmManager.receiveMLot2Warehouse(reworkMLot, receiveAction, toWarehouseRrn, toWarehouseId, null, null, sc);
					} else {
						reworkMLot = mmManager.receiveMLot(reworkMLot, receiveAction, sc);
					}
				} else {
					if (toWarehouseRrn != null) {
						MLotStorage storage = mmManager.getLotStorage(reworkMLot.getObjectRrn(), toWarehouseRrn, null, null, false);
						if (storage == null) {
							throw new ClientException(Exceptions.STORAGE_IS_NOT_EXIST);
						}
						// 变更批次库存
						mmManager.moveMLot(reworkMLot.getObjectRrn(), defectQty, null, null, null,
								toWarehouseRrn, null, null);
					}
					reworkMLot.setMainQty(reworkMLot.getMainQty().add(defectQty));
					reworkMLot.setUpdated(sc.getTransTime());
					reworkMLot.setUpdatedBy(sc.getUserName());
					em.merge(reworkMLot);

					MLotHis lotHis = new MLotHis(reworkMLot, sc);
					lotHis.setOrgRrn(sc.getOrgRrn());
					lotHis.setCreated(new Date());
					lotHis.setCreatedBy(sc.getUserName());
					lotHis.setTransType(MLotHis.TRANS_TYPE_IN);
					lotHis.setTransMainQty(defectHis.getDefectQty());
					lotHis.setTransWarehouseId(toWarehouseId);
					em.persist(lotHis);
				}
				
				defectHis.setDefectQty(defectHis.getDefectQty().subtract(defectQty));
				if (defectHis.getDefectQty().compareTo(BigDecimal.ZERO) == 0) {
					defectHis.setIsRework("Y");
				}
				em.merge(defectHis);
				
				if (useQty.compareTo(receiveQty) == 0) {
					break;
				}
			}
			return reworkMLot;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获取已录入的未返工的不良品录入历史
	 * @param orgRrn
	 * @param partName
	 * @return
	 * @throws ClientException
	 */
	public List<DefectHis> getNoReworkedWipDefectHis(long orgRrn, String partName) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer("SELECT DefectHis FROM DefectHis DefectHis WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND partName = :partName");
			sql.append(" AND transType = :transType");
			sql.append(" AND isRework = 'N' ");
			sql.append(" ORDER BY transTime");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("partName", partName);
			query.setParameter("transType", DefectHis.TRANS_TYPE_WIP_DEFECT);
			
			List<DefectHis> results = query.getResultList();
			return results;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 获取设备EDC定义
	 * @param orgRrn
	 * @param equipmentId
	 * @param dataType
	 * @param edcFrom
	 * @return
	 * @throws ClientException
	 */
	public List<EdcDataMappingRule> getEdcDataMappingRule(long orgRrn, String equipmentId, String dataType,
			String edcFrom) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer("SELECT EdcDataMappingRule FROM EdcDataMappingRule EdcDataMappingRule WHERE");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append("AND equipmentId = :equipmentId ");
			if (!StringUtil.isEmpty(dataType)) {
				sql.append("AND dataType = :dataType ");
			}
			if (!StringUtil.isEmpty(edcFrom)) {
				sql.append("AND edcFrom = :edcFrom");
			}
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("equipmentId", equipmentId);
			if (!StringUtil.isEmpty(dataType)) {
				query.setParameter("dataType", dataType);
			}
			if (!StringUtil.isEmpty(edcFrom)) {
				query.setParameter("edcFrom", edcFrom);
			}
			List<EdcDataMappingRule> definitions = query.getResultList();
			return definitions;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存仓库物料批次盘点结果
	 * @param warehouse 盘点仓库
	 * @param material 盘点物料
	 * @param mLots 盘点物料批次
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
//	public StockTakingResult saveStockTakingResult(Warehouse warehouse, Material material, List<MLot> mLots,
//			SessionContext sc) throws ClientException {
//		try {
//			StockTakingResult result = new StockTakingResult();
//			result.setOrgRrn(sc.getOrgRrn());
//			result.setCreated(sc.getTransTime());
//			result.setCreatedBy(sc.getUserName());
//			result.setStockTakingTime(sc.getTransTime());
//			result.setWarehouseId(warehouse.getWarehouseId());
//			result.setWarehouseRrn(warehouse.getObjectRrn());
//			result.setWarehouseType(warehouse.getWarehouseType());
//			
//			List<StockTakingResultLine> lines = new ArrayList<StockTakingResultLine>();
//			for (MLot mLot : mLots) {
//				StockTakingResultLine line = new StockTakingResultLine();
//				line.setOrgRrn(sc.getOrgRrn());
//				line.setmLotId(mLot.getmLotId());
//				line.setmLotRrn(mLot.getObjectRrn());
//				line.setMaterialName(material.getName());
//				line.setMaterialRrn(material.getObjectRrn());
//				line.setCurrentQty(mLot.getMainQty());
//				line.setStockTakingQty(mLot.getTransMainQty());
//				line.setComment(mLot.getLotComment());
//				
//				if (line.getCurrentQty().intValue() > line.getStockTakingQty().intValue()) {
//					line.setStockTakingResult(StockTakingResultLine.RESULT_LOSS);
//				} else {
//					line.setStockTakingResult(StockTakingResultLine.RESULT_PROFIT);
//				}
//				lines.add(line);
//			}
//			result.setLines(lines);
//			em.persist(result);
//
//			return result;
//		} catch (Exception e) {
//			throw ExceptionManager.handleException(logger, e);
//		}
//	}
	
	/**
	 * 修改仓库物料批次盘点结果
	 * @param warehouse 盘点仓库
	 * @param material 盘点物料
	 * @param mLots 盘点物料批次
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
//	public StockTakingResult updateStockTakingResult(StockTakingResult result, List<MLot> mLots,
//			SessionContext sc) throws ClientException {
//		try {
//			result.setUpdated(sc.getTransTime());
//			result.setUpdatedBy(sc.getUserName());
//			result.setStockTakingTime(sc.getTransTime());
//			
//			List<StockTakingResultLine> oldLines = result.getLines();
//			for (StockTakingResultLine oldLine : oldLines) {
//				em.remove(oldLine);
//			}
//			List<StockTakingResultLine> lines = new ArrayList<StockTakingResultLine>();
//			for (MLot mLot : mLots) {
//				StockTakingResultLine line = new StockTakingResultLine();
//				line.setOrgRrn(sc.getOrgRrn());
//				line.setmLotId(mLot.getmLotId());
//				line.setmLotRrn(mLot.getObjectRrn());
//				line.setCurrentQty(mLot.getMainQty());
//				line.setStockTakingQty(mLot.getTransMainQty());
//				line.setComment(mLot.getLotComment());
//				
//				if (line.getCurrentQty().intValue() > line.getStockTakingQty().intValue()) {
//					line.setStockTakingResult(StockTakingResultLine.RESULT_LOSS);
//				} else {
//					line.setStockTakingResult(StockTakingResultLine.RESULT_PROFIT);
//				}
//				lines.add(line);
//			}
//			result.setLines(lines);
//			em.merge(result);
//			
//			return result;
//		} catch (Exception e) {
//			throw ExceptionManager.handleException(logger, e);
//		}
//	}
	
	/**
	 * 根据工单查询工单的领料单信息
	 * @param workOrder
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public List<MaterialRequisition> getMaterialRequisitions(WorkOrder workOrder, SessionContext sc) throws ClientException {
		try {
    		StringBuffer hql = new StringBuffer("SELECT MaterialRequisition FROM MaterialRequisition MaterialRequisition WHERE ");
			hql.append(ADBase.BASE_CONDITION_N);
			hql.append(" and woRrn = :woRrn ");
			
			Query query = em.createQuery(hql.toString());
			query.setParameter("orgRrn", sc.getOrgRrn());
			query.setParameter("woRrn", workOrder.getObjectRrn());
			List<MaterialRequisition> requisitions = query.getResultList();
			return requisitions;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	public List<MaterialRequisitionDetail> getMaterialRequisitionDetails(long requisitionRrn) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer("SELECT MaterialRequisitionDetail FROM MaterialRequisitionDetail MaterialRequisitionDetail WHERE ");
			sql.append("requisitionRrn = :requisitionRrn ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("requisitionRrn", requisitionRrn);
			
			List<MaterialRequisitionDetail> details = query.getResultList();
			return details;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据工单获取工单实际领料信息
	 * @param workOrder
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public List<MaterialRequisitionDetail> getMaterialRequisitionDetailsByWorkOrder(WorkOrder workOrder, SessionContext sc) throws ClientException {
		try {
			List<MaterialRequisitionDetail> details = new ArrayList<MaterialRequisitionDetail>();
			
			List<MaterialRequisition> materialRequisitions = getMaterialRequisitions(workOrder, sc);
			for (MaterialRequisition materialRequisition : materialRequisitions) {
				List<MaterialRequisitionDetail> mrDetails = getMaterialRequisitionDetails(materialRequisition.getObjectRrn());
				details.addAll(mrDetails);
			}
			return details;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据工单获取已领取物料批次信息
	 * @param workOrder
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public List<MLot> getReceivedMLotsByWorkOrder(WorkOrder workOrder, SessionContext sc) throws ClientException {
		try {
			List<MaterialRequisitionDetail> details = getMaterialRequisitionDetailsByWorkOrder(workOrder, sc);
			
			List<MLot> mLots = new ArrayList<MLot>();
			for (MaterialRequisitionDetail materialRequisitionDetail : details) {
				if (materialRequisitionDetail.getmLotId() != null) {
					MLot mLot = mmManager.getMLotByMLotId(sc.getOrgRrn(), materialRequisitionDetail.getmLotId(), false);
					if (mLot != null) {
						mLots.add(mLot);
					}
				}
			}
			return mLots;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	
	/**
	 * 根据工单创建新的领料单据
	 * @param workOrder
	 * @param lines
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public MaterialRequisition generateNewWorkOrderMaterialRequisition(WorkOrder workOrder,
			List<MaterialRequisitionLine> lines, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();

			MaterialRequisition materialRequisition = new MaterialRequisition();
			materialRequisition.setOrgRrn(sc.getOrgRrn());
			materialRequisition.setCreated(sc.getTransTime());
			materialRequisition.setCreatedBy(sc.getUserName());
			materialRequisition.setUpdated(sc.getTransTime());
			materialRequisition.setUpdatedBy(sc.getUserName());
			materialRequisition.setDocStatus(Documentation.STATUS_CREATED);
			materialRequisition.setWoId(workOrder.getDocId());
			materialRequisition.setWoRrn(workOrder.getObjectRrn());

			Map<String, Object> parameterMap = new HashMap<String, Object>();
			parameterMap.put(GeneratorContext.CONTEXT_FIELD_OBJECT_TYPE, "Doc");
			parameterMap.put(GeneratorContext.CONTEXT_FIELD_TRANSACTION, "CreateMaterialRequisition");
			GeneratorContext context = new GeneratorContext(materialRequisition, parameterMap, sc, "Doc");
			String docId = mBasManager.generatorId(sc.getOrgRrn(), context);
			if (StringUtil.isEmpty(docId)) {
				throw new ClientException("aps.mr_id_cannot_generate");
			}
			materialRequisition.setDocId(docId);

			em.persist(materialRequisition);

			for (MaterialRequisitionLine materialRequisitionLine : lines) {
				materialRequisitionLine.setRequisitionRrn(materialRequisition.getObjectRrn());
				materialRequisitionLine.setRequisitionId(materialRequisition.getDocId());
				materialRequisitionLine.setOrgRrn(sc.getOrgRrn());
				em.persist(materialRequisitionLine);
			}
			return materialRequisition;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 申请领料
	 * @param materialRequisitions 申请的领料单
	 * @param sc
	 */
	public void receiveMLotsRequestByMaterialRequisition(List<MaterialRequisition> materialRequisitions,
			SessionContext sc) throws ClientException {
		try {
			PvcMLotProcessAction action = pvcCdiManager.getMLotProcessActions().get(0);
			action.receiveMLotsRequestByMaterialRequisition(materialRequisitions, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据领料单收料, 与外部系统(如WMS)打通时需要做申请,等待WMS发料,此处直接接受物料批次,与WMS做接口时需要自行扩展方法
	 * @param materialRequisition 领料单
	 * @param pickingMLots 领料批次
	 * @param sc
	 * 
	 */
	public List<MLot> receiveMaterialRequisition(MaterialRequisition materialRequisition, List<MLot> pickingMLots, SessionContext sc) throws ClientException {
		try {
			PvcMLotProcessAction action = pvcCdiManager.getMLotProcessActions().get(0);
			return action.receiveMLotsByMaterialRequisition(materialRequisition, pickingMLots, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据领料单接受物料批次, 并接受物料到指定仓库
	 * @param materialRequisition
	 * @param pickingMLots
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public List<MLot> receiveMLotsByMaterialRequisition(MaterialRequisition materialRequisition,
			List<MLot> pickingMLots, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			materialRequisition = em.find(MaterialRequisition.class, materialRequisition.getObjectRrn());
			List<MaterialRequisitionLine> mrLines = materialRequisition.getMrLines();
			
			WorkOrder workOrder = new WorkOrder();
			workOrder.setObjectRrn(materialRequisition.getWoRrn());
			workOrder = ppManager.getWorkOrder(workOrder, sc);
			
			List<MLot> receiveMLots = new ArrayList<MLot>();
			for (MLot pickingMLot : pickingMLots) {
				//1,检查物料批是否有效
				MLot.checkMLotIsAvailable(pickingMLot);
				
				//2,检查物料是否在领料单中
				MaterialRequisitionLine curMrLine = null;
				for (MaterialRequisitionLine mrLine : mrLines) {
					if (pickingMLot.getMaterialName().equals(mrLine.getMaterialName())) {
						curMrLine = mrLine;
						break;
					}
				}
				if (curMrLine == null) {
					throw new ClientException("error.no_match_material_in_material_requestion_line");
				}
				//3. 检查物料仓库信息
				if (pickingMLot.getTransTargetWarehouseRrn() != null && pickingMLot.getObjectRrn() != null) {
					List<MLotStorage> storages = mmManager.getLotStorages(pickingMLot.getObjectRrn());
					if (storages.get(0).getWarehouseRrn() != pickingMLot.getTransTargetWarehouseRrn()) {
						//不允许同一个物料批次放到多个仓库中
						throw new ClientException("error.mlot_not_allow_multi_storage");
					}
				}
				
				//更新领料单已领数量
				curMrLine.setRealTotalQty((curMrLine.getRealTotalQty() != null ? curMrLine.getRealTotalQty() : BigDecimal.ZERO).add(pickingMLot.getTransMainQty()));
				curMrLine = em.merge(curMrLine);
				
				MaterialRequisitionDetail detail = new MaterialRequisitionDetail();
				detail.setLineRrn(curMrLine.getObjectRrn());
				detail.setRequisitionRrn(materialRequisition.getObjectRrn());
				detail.setRequisitionId(materialRequisition.getDocId());
				detail.setMaterialRrn(pickingMLot.getMaterialRrn());
				detail.setMaterialName(pickingMLot.getMaterialName());
				detail.setMaterialVersion(pickingMLot.getMaterialVersion());
				detail.setMaterialDesc(pickingMLot.getMaterialDesc());
				detail.setMaterialType(pickingMLot.getMaterialType());
				detail.setMaterialSupplier(pickingMLot.getPartnerCode());
				detail.setmLotId(pickingMLot.getmLotId());
				detail.setMainQty(pickingMLot.getTransMainQty());
				detail.setComments(pickingMLot.getReserved7());
				detail.setReserved1(pickingMLot.getReserved2());
				detail.setReserved2(pickingMLot.getReserved3());
				detail.setReserved3(pickingMLot.getReserved4());
				detail.setReserved4(pickingMLot.getReserved5());
				detail.setReserved5(pickingMLot.getReserved6());
				
				em.persist(detail);
				
				pickingMLot.setReserved2(null);
				pickingMLot.setReserved3(null);
				pickingMLot.setReserved4(null);
				pickingMLot.setReserved5(null);
				pickingMLot.setReserved6(null);
				pickingMLot.setReserved7(null);
				
				receiveMLots.add(pickingMLot);
			}
			
			List<MLot> returnMLots = new ArrayList<MLot>();
			for (MLot receiveMLot : receiveMLots) {
				if (receiveMLot.getObjectRrn() == null) {
					if (receiveMLot.getTransTargetWarehouseRrn() != null) {
						//接收+入库
						mmManager.receiveMLot2Warehouse(receiveMLot, new MLotAction(), receiveMLot.getTransTargetWarehouseRrn(), 
								receiveMLot.getTransTargetWarehouseId(), receiveMLot.getTransTargetStorageType(), receiveMLot.getTransTargetStorageId(), sc);
					} else {
						//接收
						mmManager.receiveMLot(receiveMLot, new MLotAction(), sc);
					}
				} else {
					if (receiveMLot.getTransTargetWarehouseRrn() != null) {
						MLotStorage storage = mmManager.getLotStorage(receiveMLot.getObjectRrn(), receiveMLot.getTransTargetWarehouseRrn());
						if (storage == null) {
							throw new ClientException(Exceptions.STORAGE_IS_NOT_EXIST);
						}
						//变更批次库存
						mmManager.moveMLot(receiveMLot.getObjectRrn(), receiveMLot.getTransMainQty(), null, null, null, receiveMLot.getTransTargetWarehouseRrn(), null, null);
					}
					//增加物料批次数量
					receiveMLot.setMainQty(receiveMLot.getMainQty().add(receiveMLot.getTransMainQty()));
					receiveMLot.setUpdated(sc.getTransTime());
					receiveMLot.setUpdatedBy(sc.getUserName());
					em.merge(receiveMLot);

					MLotHis lotHis = new MLotHis(receiveMLot, sc);
					lotHis.setOrgRrn(sc.getOrgRrn());
					lotHis.setCreated(new Date());
					lotHis.setCreatedBy(sc.getUserName());
					lotHis.setTransType(MLotHis.TRANS_TYPE_IN);
					lotHis.setTransMainQty(receiveMLot.getTransMainQty());
					lotHis.setTransWarehouseId(receiveMLot.getTransTargetWarehouseId());
					em.persist(lotHis);
				}
				
				receiveMLot = mmManager.getMLotByMLotId(sc.getOrgRrn(), receiveMLot.getmLotId());
				Material material = mmManager.getMaterial(sc.getOrgRrn(), receiveMLot.getMaterialName());
				
				//工单领料历史
         		WorkOrderMLotHis woMLotHis = new WorkOrderMLotHis();
         		woMLotHis.setOrgRrn(sc.getOrgRrn());
         		woMLotHis.setTransType(WorkOrderMLotHis.TRANS_TYPE_PICK);
         		woMLotHis.setTransTime(sc.getTransTime());
         		woMLotHis.setHistorySeq(sc.getTransRrn());
         		woMLotHis.setmLotId(receiveMLot.getmLotId());
         		woMLotHis.setmLotRrn(receiveMLot.getObjectRrn());
         		woMLotHis.setMaterialName(material.getName());
         		woMLotHis.setMaterialRrn(material.getObjectRrn());
         		woMLotHis.setMaterialDesc(material.getDescription());
         		woMLotHis.setSourceWarehouseRrn(receiveMLot.getTransTargetWarehouseRrn());
         		woMLotHis.setSourceWarehouseId(receiveMLot.getTransTargetWarehouseId());
         		woMLotHis.setWoId(workOrder.getDocId());
         		woMLotHis.setPartName(workOrder.getPartName());
         		woMLotHis.setMainQty(receiveMLot.getTransMainQty());
         		woMLotHis.setUdf(receiveMLot.getUdf());
         		
         		em.persist(woMLotHis);
         		
         		returnMLots.add(receiveMLot);
			}
			materialRequisition.setUpdated(sc.getTransTime());
			materialRequisition.setUpdatedBy(sc.getUserName());
			materialRequisition.setDocStatus(Documentation.STATUS_CLOSED);
			em.merge(materialRequisition);
			
			return returnMLots;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 客户端手动进站方法
	 * @param inContext
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public Lot trackInLot(PvcInContext inContext, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			Lot lot = inContext.getLots().get(0);
			Equipment equipment = inContext.getEquipments().get(0);
			inContext.setMultiEqp(true);

			//检查批次进站状态
			constraintManager.checkLotTrackInStatus(inContext, true);
			//检查工序用户权限
			constraintManager.checkStepAuthority(sc.getOrgRrn(), sc.getUserName(), inContext.getLots(), true);
			//检查设备能力
			constraintManager.checkEquipmentCapa(inContext, inContext.getLots(), true);
			//检查设备状态
			constraintManager.checkEquipmentStatus(inContext.getEquipments(), lot.getLotType(), true);
			
			lot.setUpdated(sc.getTransTime());
			lot.setUpdatedBy(sc.getUserName());
			lot.setComClass(LotStateMachine.COMCLASS_WIP);
			lot.setState(LotStateMachine.STATE_RUN);
			lot.setEquipmentId(equipment.getEquipmentId());
			lot.setTrackInTime(sc.getTransTime());
			lot = lotManager.getEntityManager().merge(lot);
			
			LotHis lotHis = new LotHis(lot, sc);
			lotHis.setTransType(LotStateMachine.TRANS_TRACKIN);
			lotManager.getEntityManager().persist(lotHis);
			
			return lot;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 客户端手动出站方法
	 * @param inContext
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public Lot trackOutLot(PvcInContext inContext, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
	
			Lot lot = inContext.getLots().get(0);
			String trackOutEquipmentId = lot.getEquipmentId();
			Step currentStep = getStepByEquipmentIdCache(sc.getOrgRrn(), trackOutEquipmentId);
			
			//检查工序用户权限
			constraintManager.checkStepAuthority(sc.getOrgRrn(), sc.getUserName(), inContext.getLots(), true);
			//检查批次出站状态
			constraintManager.checkLotTrackOutStatus(currentStep, inContext.getLots(), true);
			
			lot.setUpdated(sc.getTransTime());
			lot.setUpdatedBy(sc.getUserName());
			lot.setState(LotStateMachine.STATE_WAIT);
			lot.setTrackOutTime(sc.getTransTime());
			
			//当前站点TrackOut
			LotHis lotHis = new LotHis(lot, sc);
			lotHis.setEquipmentId(trackOutEquipmentId);
			lotHis.setTransType(LotStateMachine.TRANS_TRACKOUT);
			lotManager.getEntityManager().persist(lotHis);
			
			//批次Move Next
			Step nextStep = getNextStepCache(currentStep, sc);
			if (nextStep != null) {
				lot.setEquipmentId(null);
				lot.setComClass(LotStateMachine.COMCLASS_WIP);
				lot.setState(LotStateMachine.STATE_WAIT);
				lot.setLastStepName(currentStep.getName());
				lot.setStepRrn(nextStep.getObjectRrn());
				lot.setStepName(nextStep.getName());
				lot.setStepDesc(nextStep.getDescription());
				lot.setStepVersion(nextStep.getVersion());
				lot.setQueueTime(sc.getTransTime());
			} else {
				lot.setEquipmentId(null);
				lot.setComClass(LotStateMachine.COMCLASS_FIN);
				lot.setState(LotStateMachine.STATE_FIN);
				lot.setStepRrn(null);
				lot.setStepName(null);
				lot.setStepDesc(null);
				lot.setStepVersion(null);
				lot.setQueueTime(null);
			}
			lot.setTrackInTime(null);
			lot.setTrackOutTime(null);
			lot = lotManager.getEntityManager().merge(lot);
			
			return lot;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 物料批次盘点, 比对当前值与实际盘点的结果
	 * 
	 * 实际值 > 当前值 盘盈 -> 增加当前物料批次数量
	 * 实际值 < 当前值 盘亏 -> 消耗当前物料批次数量
	 * 实际值 = 当前值 盘盈 -> 不做物料数量修改，仅记录盘点历史
	 * 
	 * @param warehouse 盘点仓库
	 * @param mLots 待盘点物料批次
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public List<MLot> stockTakingMLots(Warehouse warehouse, List<MLot> mLots, SessionContext sc) throws ClientException {
		try {
			List<MLot> results = new ArrayList<MLot>();
			for (MLot sourceMLot : mLots) {
				BigDecimal currentQty = sourceMLot.getMainQty();
				BigDecimal realQty = sourceMLot.getTransMainQty();
				
				MLotAction mLotAction = null;
				if (currentQty.compareTo(realQty) < 0) {
					//实际值 > 当前值 盘盈 -> 增加当前物料批次数量
					mLotAction = new MLotAction();
					mLotAction.setMainQty(realQty.subtract(currentQty));
					sourceMLot = mmManager.unConsumeAction(sourceMLot, Lists.newArrayList(mLotAction), PvcCode.MATERIAL_EVENT_PROFIT,
							warehouse.getObjectRrn(), warehouse.getWarehouseId(), null, null, sc);
					
				} else if (currentQty.compareTo(realQty) > 0) {
					//实际值 < 当前值 盘亏 -> 消耗当前物料批次数量
					mLotAction = new MLotAction();
					mLotAction.setMainQty(currentQty.subtract(realQty));
					sourceMLot = mmManager.consumeAction(sourceMLot, Lists.newArrayList(mLotAction), PvcCode.MATERIAL_EVENT_LOSS,
							warehouse.getObjectRrn(), warehouse.getWarehouseId(), null, null, false, sc);
					
				} else if (currentQty.compareTo(realQty) == 0) {
					//实际值 = 当前值 盘盈 -> 不做物料数量修改，仅记录盘点历史
					MLotHis mLotHis = new MLotHis(sourceMLot, sc);
					mLotHis.setTransMainQty(BigDecimal.ZERO);
					mLotHis.setTransType(PvcCode.MATERIAL_EVENT_PROFIT);
					em.persist(mLotHis);
				}
				results.add(sourceMLot);
			}
			return results;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据工单获取工单的物料盘点结果 
	 * By物料批次的维度返回以下值，总领料数量、总报废量、总不良量、总使用量、剩余数量
	 * @param workOrder
	 * @param sc
	 * @throws ClientException
	 */
	public List<StockTakingResult> getWorkOrderStockTakingResult(WorkOrder workOrder, SessionContext sc) throws ClientException {
		try {
			List<StockTakingResult> results = new ArrayList<StockTakingResult>();
			
			List<MaterialRequisitionDetail> details = getMaterialRequisitionDetailsByWorkOrder(workOrder, sc);
			if (CollectionUtils.isNotEmpty(details)) {
				List<MLot> stockTakingMLots = new ArrayList<MLot>();
				for (MaterialRequisitionDetail materialRequisitionDetail : details) {
					MLot stockTakingMLot = mmManager.getMLotByMLotId(sc.getOrgRrn(), materialRequisitionDetail.getmLotId(), false);
					if (stockTakingMLot != null) {
						stockTakingMLots.add(stockTakingMLot);
					}
				}
				if (CollectionUtils.isNotEmpty(stockTakingMLots)) {
					Map<String, MLot> mLotsMap = stockTakingMLots.stream().collect(Collectors.toMap(MLot :: getmLotId, x ->x));
					List<String> mLotIds = mLotsMap.keySet().stream().collect(Collectors.toList());
					
					StringBuffer sql = new StringBuffer("SELECT mlot_id, ");
					sql.append("sum(decode(trans_type, 'RECEIVE', trans_main_qty, 0)) as totalReceiveQty, ");
					sql.append("sum(decode(trans_type, 'DEFECT', trans_main_qty, 0)) as totalDefectQty, ");
					sql.append("sum(decode(trans_type, 'SCRAP', trans_main_qty, 0)) as totalScrapQty, ");
					sql.append("sum(decode(trans_type, 'CONSUME', trans_main_qty, 0)) as totalConsumeQty ");
					sql.append("FROM mm_lot_his ");
					sql.append("WHERE mlot_id in (:mLotIds) ");
					sql.append("GROUP BY mlot_id");
					
					Query query = em.createNativeQuery(sql.toString());
					query.setParameter("mLotIds", mLotIds);
					
					List resultList = query.getResultList();
					for (Object object : resultList) {
						Object[] result = (Object[])object;
						String mLotId = String.valueOf(result[0]);
						
						StockTakingResult stResult = new StockTakingResult();
						stResult.setmLotId(mLotId);
						stResult.setMaterialName(mLotsMap.get(mLotId).getMaterialName());
						stResult.setMaterialDesc(mLotsMap.get(mLotId).getMaterialDesc());
						stResult.setRemainQty(mLotsMap.get(mLotId).getMainQty());
						stResult.setTotalReceiveQty((BigDecimal)result[1]);
						stResult.setTotalDefectQty((BigDecimal)result[2]);
						stResult.setTotalScrapQty((BigDecimal)result[3]);
						stResult.setTotalConsumeQty((BigDecimal)result[4]);
						
						results.add(stResult);
					}
				}
			}
			return results;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据工单检查已经物料的水位, 低于限制水位时触发报警通知
	 * @param workOrder
	 * @param sc
	 * @throws ClientException
	 */
	public void checkWarehouseSafetyLevelByWorkOrder(WorkOrder workOrder, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			List<MaterialRequisitionDetail> mrDetails = getMaterialRequisitionDetailsByWorkOrder(workOrder, sc);
			
			List<MLot> availableMLots = new ArrayList<MLot>();
			for (MaterialRequisitionDetail detail : mrDetails) {
				MLot mLot = mmManager.getMLotByMLotId(sc.getOrgRrn(), detail.getmLotId(), false);
				if (mLot != null && mLot.getMainQty() != null && mLot.getMainQty().compareTo(BigDecimal.ZERO) > 0) {
					List<MLotStorage> storages = mmManager.getLotStorages(mLot.getObjectRrn());
					if (CollectionUtils.isNotEmpty(storages)) {
						availableMLots.add(mLot);
					}
				}
			}
			//当前可用的物料批次根据物料名称进行分组，再与工单BOM去比对
			Map<String, List<MLot>> mLotMap = availableMLots.stream().collect(Collectors.groupingBy(MLot :: getMaterialName));
			
			Map<String, Object> warningMaterialRemainQtyMap = new HashMap<String, Object>();
			List<WorkOrderBomLine> bomLines = ppManager.getWorkOrderBomLines(workOrder, sc);
			for (WorkOrderBomLine bomLine : bomLines) {
				if (!mLotMap.containsKey(bomLine.getMaterialName())) {
					warningMaterialRemainQtyMap.put(bomLine.getMaterialName(), BigDecimal.ZERO);
				} else {
					BigDecimal needQty = bomLine.getUnitQty().multiply(workOrder.getMainQty());
					BigDecimal safetyLevel = needQty.multiply(
							new BigDecimal(PvcCode.getWorkOrderSafetyLevelRatio(sc.getOrgRrn(), sysParamManager)));
					BigDecimal remainQty = mLotMap.get(bomLine.getMaterialName()).stream()
							.map(x -> x.getMainQty()).reduce(BigDecimal::add).get();
					if (safetyLevel.compareTo(remainQty) >= 0) {
						warningMaterialRemainQtyMap.put(bomLine.getMaterialName(), remainQty);
					}
				}
			}
			
			if (!warningMaterialRemainQtyMap.isEmpty()) {
				//触发报警
				AlarmCdiContext context = new AlarmCdiContext();
				context.setActionType(WorkOrderAlarmProcessAction.ACTION_WAREHOUSE_LEVEL_UNSAFETY);
				context.setWorkOrder(workOrder);
				context.setSessionContext(sc);
				context.setDatas(warningMaterialRemainQtyMap);
				
				List<PvcAlarmAction> actions = pvcCdiManager.getAlarmProcessAction();
				for (PvcAlarmAction action : actions) {
					action.invokeAlarm(context);
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 返工工单的领料, 返工工单也会生成领料单,但用于其他业务处理, 在返工工单的领料中直接接收物料批次
	 * @param workOrder
	 * @param receiveQty 领料数量
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	public MaterialRequisition generateReworkWorkOrderMaterialRequisition(WorkOrder workOrder, BigDecimal receiveQty,
			SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();

			List<DefectHis> defectHisList = getNoReworkedWipDefectHis(sc.getOrgRrn(), workOrder.getPartName());
			BigDecimal totalQty = defectHisList.stream().map(x -> x.getDefectQty()).reduce(BigDecimal.ZERO, BigDecimal :: add);
			if (receiveQty.intValue() > totalQty.intValue()) {
				//请求数量大于当前可返工数量
				throw new ClientException("error.available_qty_is_less_than_required_qty");
			}
			MaterialRequisition materialRequisition = new MaterialRequisition();
			materialRequisition.setOrgRrn(sc.getOrgRrn());
			materialRequisition.setCreated(sc.getTransTime());
			materialRequisition.setCreatedBy(sc.getUserName());
			materialRequisition.setUpdated(sc.getTransTime());
			materialRequisition.setUpdatedBy(sc.getUserName());
			materialRequisition.setDocStatus(Documentation.STATUS_CLOSED);
			materialRequisition.setWoId(workOrder.getDocId());
			materialRequisition.setWoRrn(workOrder.getObjectRrn());

			Map<String, Object> parameterMap = new HashMap<String, Object>();
			parameterMap.put(GeneratorContext.CONTEXT_FIELD_OBJECT_TYPE, "Doc");
			parameterMap.put(GeneratorContext.CONTEXT_FIELD_TRANSACTION, "CreateMaterialRequisition");
			GeneratorContext context = new GeneratorContext(materialRequisition, parameterMap, sc, "Doc");
			String docId = mBasManager.generatorId(sc.getOrgRrn(), context);
			if (StringUtil.isEmpty(docId)) {
				throw new ClientException("aps.mr_id_cannot_generate");
			}
			materialRequisition.setDocId(docId);
			
			//返工物料
			Material reworkMaterial = mmManager.getMaterial(sc.getOrgRrn(), DefectHis.DEFAULT_REWORK_MATERIAL_NAME);
			
			MaterialRequisitionLine mrLine = new MaterialRequisitionLine();
			mrLine.setOrgRrn(sc.getOrgRrn());
			mrLine.setRequisitionRrn(materialRequisition.getObjectRrn());
			mrLine.setRequisitionId(materialRequisition.getDocId());
			mrLine.setMaterialRrn(reworkMaterial.getObjectRrn());
			mrLine.setMaterialName(reworkMaterial.getName());
			mrLine.setMaterialDesc(reworkMaterial.getDescription());
			mrLine.setMaterialType(reworkMaterial.getMaterialType());
			mrLine.setUnitQty(new BigDecimal(1));
			mrLine.setLineQty(receiveQty);
			mrLine.setRealTotalQty(receiveQty);
			
			materialRequisition.setMrLines(Lists.newArrayList(mrLine));
			em.persist(materialRequisition);
			
			MLot reworkMLot = generateReworkMLotsByWipDefectHis(defectHisList, receiveQty, sc);
			
			MaterialRequisitionDetail detail = new MaterialRequisitionDetail();
			detail.setLineRrn(mrLine.getObjectRrn());
			detail.setRequisitionRrn(materialRequisition.getObjectRrn());
			detail.setRequisitionId(materialRequisition.getDocId());
			detail.setMaterialRrn(reworkMaterial.getObjectRrn());
			detail.setMaterialName(reworkMaterial.getName());
			detail.setMaterialVersion(reworkMaterial.getVersion());
			detail.setMaterialDesc(reworkMaterial.getDescription());
			detail.setMaterialType(reworkMaterial.getMaterialType());
			detail.setMaterialSupplier(reworkMLot.getPartnerCode());
			detail.setmLotId(reworkMLot.getmLotId());
			detail.setMainQty(receiveQty);
			em.persist(detail);
			
			return materialRequisition;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 删除设备物料绑定资料
	 * @param equipmentId
	 * @param mLotId
	 * @param sc
	 * @throws ClientException
	 */
	public void equipmentMaterialFinish(String equipmentId, String mLotId, SessionContext sc) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" DELETE FROM EquipmentMaterial EquipmentMaterial");
            sql.append(" WHERE ");
            sql.append(ADBase.BASE_CONDITION_N);
            sql.append(" AND equipmentId = :equipmentId ");
            sql.append(" AND mLotId = :mLotId ");
            
            Query query = em.createQuery(sql.toString());
            query.setParameter("orgRrn", sc.getOrgRrn());
            query.setParameter("equipmentId", equipmentId);
            query.setParameter("mLotId", mLotId);
            query.executeUpdate();
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据物料批次获取该物料批次与设备绑定的信息
	 * @param orgRrn
	 * @param mLotRrn
	 * @return
	 * @throws ClientException
	 */
	public List<EquipmentMaterial> getEquipmentMaterialsByMLot(Long orgRrn, Long mLotRrn) throws ClientException {   	
    	try {  		 
    		 StringBuffer sql = new StringBuffer();
    		 sql.append(" SELECT EquipmentMaterial FROM EquipmentMaterial EquipmentMaterial");
             sql.append(" WHERE ");
             sql.append(ADBase.BASE_CONDITION_N);
             sql.append(" AND mLotRrn = :mLotRrn ");

             Query query = em.createQuery(sql.toString());
             query.setParameter("orgRrn", orgRrn);
             query.setParameter("mLotRrn", mLotRrn);
             
             List<EquipmentMaterial> equipmentMaterials = query.getResultList();
             return equipmentMaterials;
    	} catch (Exception e) { 
 			throw ExceptionManager.handleException(logger, e);
 	    }
    }
	
	
	/**
	 * 物料Kitting到设备上与设备绑定, 物料可支持同时间Kitting到多个设备上
	 * Kitting的时候不消耗物料数量, 在下一个物料Kitting到此设备时消耗物料数量
	 * @param mLot
	 * @param equipments
	 * @param sc
	 * @throws ClientException
	 */
	public void kittingEquipmentMaterial(MLot mLot, List<Equipment> equipments,SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			if (!(Material.BATCH_TYPE_LOT.equals(mLot.getBatchType()))) {
				throw new ClientException(Exceptions.MLOT_UNSUPPORT_BATCH_TYPE);
			}
			
			MLot transMLot = mLot;
			//检查物料批是否可用
			MLot.checkMLotIsAvailable(mLot);
			
			//触发状态变化(包括检查物料状态Action)
			mLot = mmManager.changeMLotState(mLot, MaterialEvent.EVENT_KITTING, null, sc);
			mLot.setTransProperty(transMLot);
			
			List<MLot> checkUnkittingMLots = new ArrayList<MLot>();
			out:for (Equipment equipment : equipments) {
				EquipmentLine line = getEquipmentLineByEquipmentId(sc.getOrgRrn(), equipment.getEquipmentId());
				
				List<EquipmentMaterial> eqpMaterials = mLotManager.getEquipmentMaterials(sc.getOrgRrn(), equipment.getEquipmentId(), null);
				if (!eqpMaterials.isEmpty()) {
					for(EquipmentMaterial equipmentMaterial:eqpMaterials) {
						if(StringUtils.equals(equipmentMaterial.getMaterialType(), mLot.getMaterialType())) {
							MLot oldMLot = mmManager.getEntityManager().find(MLot.class,equipmentMaterial.getMLotRrn());
							if(StringUtils.equals(oldMLot.getmLotId(), mLot.getmLotId())) {
								break out;
							}
							if (!checkUnkittingMLots.contains(oldMLot)) {
								checkUnkittingMLots.add(oldMLot);
							}
							equipmentMaterialFinish(equipment.getEquipmentId(), equipmentMaterial.getmLotId(), sc);
						}
					}
				}
				em.flush();
				em.clear();
				
				EquipmentMaterial equipmentMaterial = new EquipmentMaterial();
				equipmentMaterial.setOrgRrn(sc.getOrgRrn());
				equipmentMaterial.setIsActive(true);
				equipmentMaterial.setCreatedBy(sc.getUserName());
				equipmentMaterial.setUpdatedBy(sc.getUserName());
				equipmentMaterial.setEquipmentId(equipment.getEquipmentId());					
				equipmentMaterial.setPositionName(null);
				equipmentMaterial.setMLotRrn(mLot.getObjectRrn());
				equipmentMaterial.setmLotId(mLot.getmLotId());
				equipmentMaterial.setMaterialName(mLot.getMaterialName());
				equipmentMaterial.setMaterialDesc(mLot.getMaterialDesc());
				equipmentMaterial.setMaterialType(mLot.getMaterialType());
				equipmentMaterial.setSourceWarehouseRrn(mLot.getTransWarehouseRrn());
				equipmentMaterial.setSourceStorageType(mLot.getTransStorageType());
				equipmentMaterial.setSourceStorageId(mLot.getTransStorageId());
				equipmentMaterial.setAttachDate(new Date());
				equipmentMaterial.setAttachMainQty(mLot.getTransMainQty());
				equipmentMaterial.setAttachSubQty(mLot.getTransSubQty());
				mmManager.getEntityManager().persist(equipmentMaterial);

				MLotHis his = new MLotHis(mLot, sc);
				his.setTransType(MaterialEvent.EVENT_KITTING);
				his.setCreatedBy(sc.getUserName());
				his.setTransMainQty(mLot.getTransMainQty());
				his.setEquipmentId(equipment.getEquipmentId());
				his.setPosition(null);
				his.setOperator(sc.getUserName());
				his.putUdfValue(PvcCode.EXTEND_MM_LOT_HIS_LINEID, line != null ? line.getLineId() : null);
				mmManager.getEntityManager().persist(his);
			}
			if (CollectionUtils.isNotEmpty(checkUnkittingMLots)) {
				//一个物料可能绑在两个设备上，绑在两个设备上的物料都被顶替掉之后，这个物料才能结束掉
				for (MLot consumeMLot : checkUnkittingMLots) {
					List<EquipmentMaterial> equipmentMaterials = getEquipmentMaterialsByMLot(sc.getOrgRrn(), consumeMLot.getObjectRrn());
					if (CollectionUtils.isEmpty(equipmentMaterials)) {
						MLotAction consumeAction = new MLotAction();
						consumeAction.setMainQty(consumeMLot.getMainQty());
						
						mmManager.consumeAction(consumeMLot, Lists.newArrayList(consumeAction), MaterialEvent.EVENT_UNKITTING, null, null, null, null, false, sc);
					}
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据舟的载具规格信息获取对应的舟Position信息
	 * @param orgRrn
	 * @param durableSpec
	 * @throws ClientException
	 */
	public List<BoatPosition> getBoatPositionByDurableSpec(long orgRrn, long durableSpec, String side) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer("SELECT BoatPosition FROM BoatPosition BoatPosition WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append("AND durableSpecRrn = :durableSpecRrn ");
			sql.append("AND positionSide = :positionSide");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("durableSpecRrn", durableSpec);
			query.setParameter("positionSide", side);
			
			List<BoatPosition> results = query.getResultList();
			return results;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	@Override
	public void scrapInfoProcessor(DefectHis defectHis, SessionContext sc) throws ClientException {
		try {
			// 增加工序权限卡控
			Lot checkLot = new Lot();
			checkLot.setStepName(defectHis.getStepName());
			constraintManager.checkStepAuthority(sc.getOrgRrn(), sc.getUserName(), Lists.newArrayList(checkLot), true);

			defectHis.setTransTime(new Date());
			defectHis.setCreatedBy(sc.getUserName());
			defectHis.setTransType(DefectHis.TRANS_TYPE_WIP_SCRAP);
			defectHis.setDefectCode(FRAGMENT_TYPE);
			defectHis.setIsRework("N");
			adManager.saveEntity(defectHis, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	
	
	@Override
	public void defectInfoProcessor(DefectHis defectHis, SessionContext sc) throws ClientException {
		try {
			//增加工序权限卡控
			Lot checkLot = new Lot();
			checkLot.setStepName(defectHis.getStepName());
			constraintManager.checkStepAuthority(sc.getOrgRrn(), sc.getUserName(),
					Lists.newArrayList(checkLot), true);
			
			defectHis.setCreated(new Date());
			defectHis.setCreatedBy(sc.getUserName());
			defectHis.setTransTime(defectHis.getCreated());
			defectHis.setTransType(DefectHis.TRANS_TYPE_WIP_DEFECT);
			defectHis.setIsRework("N");
			defectHis.setDefectCode(REWORK_TYPE);
			adManager.saveEntity(defectHis, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 保存BoxInfo，记录Box跟物料批的关系
	 * @param box
	 * @param sc
	 */
	public void saveBoxInfo(BoxInfo box, SessionContext sc) {
		try {
			sc.buildTransInfo();
			BoxInfo oldBox = getBoxInfoByBoxId(box.getBoxId(), sc.getOrgRrn());
			if (oldBox != null) {
				oldBox.setEquipmentId(box.getEquipmentId());
				oldBox.setmLotId(box.getmLotId());
				oldBox.setBindTime(sc.getTransTime());
				oldBox.setAgvSort(box.getAgvSort());
				oldBox.setWaferQty(box.getWaferQty());
			} else {
				oldBox = box;
				oldBox.setCreatedBy(sc.getUserName());
			}
			
			oldBox.setUpdatedBy(sc.getUserName());
			em.persist(oldBox);
			
			BoxInfoHis his = new BoxInfoHis(oldBox, sc);
			em.persist(his);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据BoxID查询BoxInfo信息
	 * @param boxId
	 * @param orgRrn
	 * @return
	 */
	public BoxInfo getBoxInfoByBoxId(String boxId, long orgRrn) {
		try {
			StringBuffer sql = new StringBuffer("SELECT BoxInfo FROM BoxInfo BoxInfo WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append("AND boxId = :boxId ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", orgRrn);
			query.setParameter("boxId", boxId);
			
			List<BoxInfo> results = query.getResultList();
			if (results!=null&&results.size()>0) { 
				return results.get(0);
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
		return null;
	}
	
	/**
	 * 将ComponentUnit从Carrier，Lot里解绑
	 * 
	 * @param carrier
	 * 			要解绑的载具
	 * @param mainLot
	 * 			要解绑的主批
	 * @param components
	 * 			要解绑的ComponentUnit
	 * @param sc
	 * @throws ClientException
	 */
	@Override
	public void deassignCarrierComponentUnits(Carrier carrier, Lot mainLot, List<ComponentUnit> components, SessionContext sc)
			throws ClientException {
		try {
			// 解绑载具
			carrier = durableManager.detachCarrier(carrier, BigDecimal.valueOf(components.size()), false, sc);
			
			// 解绑ComponentUnit
			deassignComponentUnits(components, sc);
			
			// 更新主批
			if (BigDecimal.ZERO.compareTo(carrier.getCurrentQty()) < 0) {
				// 载具数量已为0，解除主批跟载具的关系
				deassignCarrierLot(carrier, mainLot, sc);
			} else {
				mainLot.setUpdated(sc.getTransTime());
				mainLot.setUpdatedBy(sc.getUserName());
				mainLot.setMainQty(mainLot.getMainQty().subtract(BigDecimal.valueOf(components.size())));
				
				mainLot = lotManager.getEntityManager().merge(mainLot);

				LotHis lotHis = new LotHis(mainLot, sc);
				lotHis.setDurable(carrier.getDurableId()); // 记录解绑的原卡匣
				lotHis.setTransType(LotStateMachine.TRANS_DETACHLOT);
				lotHis.setTransMainQty(mainLot.getMainQty());
				lotManager.getEntityManager().persist(lotHis);
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 解绑Component与Carrier,Lot间的关系。这些Component应该属于同一个载具，主批
	 * @param carrier
	 * @param lot
	 * @param sc
	 * @throws ClientException
	 */
	public void deassignComponentUnits(List<ComponentUnit> components, SessionContext sc) throws ClientException {
		try {
			for (ComponentUnit component : components) {
				component.setUpdated(sc.getTransTime());
				component.setUpdatedBy(sc.getUserName());
				component.setDurable(null);
				component.setPosition(null);
				component.setParentUnitRrn(null);
				component.setLastMainLotId(component.getLotId());
				component.setLotId(null);
				componentManager.getEntityManager().merge(component);
			}
			// TODO 暂不记录单片的解绑历史
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 解绑Component与Carrier,Lot间的关系
	 * @param carrier
	 * @param lot
	 * @param sc
	 * @throws ClientException
	 */
	public void deassignComponentUnits(Lot lot, SessionContext sc) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer("UPDATE ComponentUnit ComponentUnit SET ");
			sql.append("ComponentUnit.updated = :updated, ");
			sql.append("ComponentUnit.updatedBy = :updatedBy, ");
			sql.append("ComponentUnit.durable = NULL, ");
			sql.append("ComponentUnit.position = NULL, ");
			sql.append("ComponentUnit.parentUnitRrn = NULL, ");
			sql.append("ComponentUnit.lastMainLotId = :lastMainLotId, ");
			sql.append("ComponentUnit.lastPartName = :lastPartName, ");
			sql.append("ComponentUnit.lastEquipmentId = :lastEquipmentId, ");
			sql.append("ComponentUnit.lastStepName = :lastStepName ");
			sql.append("WHERE ComponentUnit.parentUnitRrn = :parentUnitRrn");
			
			Query query = componentManager.getEntityManager().createQuery(sql.toString());
			query.setParameter("updated", sc.getTransTime());
			query.setParameter("updatedBy", sc.getUserName());
			query.setParameter("lastMainLotId", lot.getLotId());
			query.setParameter("lastPartName", lot.getPartName());
			query.setParameter("lastEquipmentId", lot.getEquipmentId());
			query.setParameter("lastStepName", lot.getStepName());
			query.setParameter("parentUnitRrn", lot.getObjectRrn());
			query.executeUpdate();
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 解除Carrier, Lot, Component的关系
	 * @param carrier 待解除的载具
	 * @param lot 待解除的批次
	 * @param sc
	 * @throws ClientException
	 */
	public Lot deassignCarrierLot(Carrier carrier, Lot lot, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			lot.setUpdated(sc.getTransTime());
			lot.setUpdatedBy(sc.getUserName());
			lot.setMainQty(BigDecimal.ZERO);
			lot.setDurable(null);
			lot = lotManager.getEntityManager().merge(lot);

			LotHis lotHis = new LotHis(lot, sc);
			lotHis.setDurable(carrier.getDurableId()); // 记录解绑的原卡匣
			lotHis.setTransType(LotStateMachine.TRANS_DETACHLOT);
			lotHis.setTransMainQty(lot.getMainQty());
			lotManager.getEntityManager().persist(lotHis);
			
			// 载具清空
			carrier.setUpdated(sc.getTransTime());
			carrier.setUpdatedBy(sc.getUserName());
			carrier.setCurrentQty(BigDecimal.ZERO);
			carrier.setComClass(Carrier.STATE_AVAILABLE);
			carrier.setState(Carrier.STATE_AVAILABLE);
			carrier = durableManager.getEntityManager().merge(carrier);
			
			if (ProcessUnit.UNIT_TYPE_COMPONENT.equals(lot.getSubUnitType())) {
				this.deassignComponentUnits(lot, sc);
			}
			return lot;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 绑定载具与Lot的关系
	 * @param carrier
	 * @param lot
	 * @param sc
	 * @return
	 * @throws Exception
	 */
	@Override
	public Lot assignCarrierLot(Carrier carrier, Lot lot, SessionContext sc) throws Exception {
		try {
			lot.setUpdated(sc.getTransTime());
			lot.setUpdatedBy(sc.getUserName());
			lot.setDurable(carrier.getDurableId());
			lot = lotManager.getEntityManager().merge(lot);
			
			LotHis lotHis = new LotHis(lot, sc);
			lotHis.setTransType(LotStateMachine.TRANS_ATTACHLOT);
			lotManager.getEntityManager().persist(lotHis);
			
			carrier.setUpdated(sc.getTransTime());
			carrier.setUpdatedBy(sc.getUserName());
			carrier.setCurrentQty(carrier.getCurrentQty() == null ? lot.getMainQty() : carrier.getCurrentQty().add(lot.getMainQty()));
			carrier.setComClass(Carrier.STATE_INUSE);
			carrier.setState(Carrier.STATE_INUSE);
			carrier = durableManager.getEntityManager().merge(carrier);
    		
			return lot;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 用于自动化作业时与放片逻辑对应,石英舟，石墨舟，载板等在设备内加工的载具的绑定
	 * 将ComponentUnit绑定到载具,只建立ComponentUnit与载具的对应关系,不与批次产生关联
	 * @param carrier
	 * @param components
	 * @param sc
	 */
	@Override
	public void assignCarrierComponent(Carrier carrier, List<ComponentUnit> components, SessionContext sc)
			throws ClientException {
		try {
			// 不记录Wafer的绑定历史
			carrierLotManager.assignCarrierComp(carrier, components, false, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 用于自动化作业时与取片逻辑对应,石英舟，石墨舟，载板等在设备内加工的载具的解绑
	 * 只解绑ComponentUnit与载具的对应关系,不与批次产生关联
	 * @param carrier
	 * @param components
	 * @param sc
	 */
	@Override
	public void deassignCarrierComponent(Carrier carrier, List<ComponentUnit> components, SessionContext sc)
			throws ClientException {
		try {
			// 不记录Wafer的解绑历史
			carrierLotManager.deassignCarrierComp(carrier, components, false, sc);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 检查花篮是否存在
	 * 特殊设备类型碰到不存在的花篮需要按照规则生成新花篮信息
	 * @param carrierId 花篮号
	 * @param equipmentId 设备号
	 * @return
	 * @throws ClientExceptionok
	 */
	public Carrier checkCarrier(String carrierId, String equipmentId, SessionContext sc) throws ClientException {
		try {
			Carrier carrier = durableManager.getCarrierById(sc.getOrgRrn(), carrierId);
			if (null == carrier) {
				// 系统参数控制是否开启自动生成花篮功能
				if (!PvcCode.isCheckGenerateCarrier(sc.getOrgRrn(), sysParamManager)) {
					throw new ClientException("mm.durable_not_exist");
				}
				
				Equipment equipment = rasManager.getEquipmentByEquipmentId(sc.getOrgRrn(), equipmentId);
				// 根据设备类型获取生成花篮规则
				List<CarrierGenerateRule> rules = adManager.getEntityList(sc.getOrgRrn(), CarrierGenerateRule.class,
						Integer.MAX_VALUE, "eqpType ='" + equipment.getEqpType() + "'", null);
				if (CollectionUtils.isEmpty(rules)) {
					throw new ClientException("mm.durable_not_exist");
				}
				CarrierGenerateRule generateRule = rules.get(0);

				// 根据Id生成规则校验花篮号是否读错
				String idGeneratorRuleName = generateRule.getMatchIdRule();
				if (StringUtils.isNotBlank(idGeneratorRuleName)) {
					//获取指定校验的ID生成规则
					List<GeneratorRule> idRules = adManager.getEntityList(sc.getOrgRrn(), GeneratorRule.class, 1, " name = '" + idGeneratorRuleName + "'", "");
					if (CollectionUtils.isEmpty(idRules)) {
						throw new ClientParameterException("mbas.idgenerator_rule_not_found", idGeneratorRuleName);
					}
					GeneratorRule rule = idRules.get(0);
					
					//校验结果
					boolean isMatch = matchCarrierId(carrierId,rule);
					if (!isMatch) {
						throw PvcExceptionBundle.bundle.CarrierIdIllegality(carrierId, idGeneratorRuleName);
					}
				}

				// 获取花篮规格，花篮信息根据指定的花篮规格生成
				DurableSpec carrierSpec = durableManager.getDurableSpec(sc.getOrgRrn(), null,
						generateRule.getCarrierSpec(), null);
				if (null == carrierSpec) {
					throw PvcExceptionBundle.bundle.GenerateCarrierSpecNotSetting();
				}
				Carrier newCarrier = new Carrier();
				newCarrier.setDurableId(carrierId);
				newCarrier.setDurableType(carrierSpec.getDurableType());
				newCarrier.setDurableSpecName(carrierSpec.getName());
				newCarrier.setDurableSpecVersion(carrierSpec.getVersion());
				newCarrier.setMainMatType(carrierSpec.getMainMatType());
				newCarrier.setSubMatType(carrierSpec.getSubMatType());
				newCarrier.setWarningCount(carrierSpec.getWarningCount());
				newCarrier.setLimitCount(carrierSpec.getLimitCount());
				newCarrier.setWarningTime(carrierSpec.getWarningTime());
				newCarrier.setLimitTime(carrierSpec.getLimitTime());
				newCarrier.setLimitTimeUnit(carrierSpec.getTimeUnit());
				newCarrier.setCurrentCount(0L);
				newCarrier.setCleanDate(new Date());
				newCarrier.setTransferState("Idle");
				carrier = (Carrier) durableManager.saveDurable(newCarrier, sc);
			}
			return carrier;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据指定的ID生成规则去校验花篮ID
	 * 考虑到花篮实际编码规则不会太复杂，暂时只校验一些简单的生成规则
	 * @param carrierId		花篮号
	 * @param rule			ID生成规则
	 * @return
	 */
	public boolean matchCarrierId(String carrierId, GeneratorRule rule) throws ClientException {
		try {
			boolean result = true;

			// 获取ID生成规则明细
			List<GeneratorRuleLine> ruleLines = rule.getRuleLines();
			if (CollectionUtils.isEmpty(ruleLines)) {
				return result;
			}

			// 只校验固定字符（F），时间格式(D)，序列号格式(S)
			List<String> filterRules = Arrays.asList("F", "D", "S");
			List<GeneratorRuleLine> matchRuleLines = ruleLines.stream().filter(r -> filterRules.contains(r.getDsType()))
					.sorted(Comparator.comparing(GeneratorRuleLine::getSeqNo)).collect(Collectors.toList());

			// 先获取编码规则长度，判断花篮ID长度是否和编码规则一样
			int rightIdLength = 0;
			for (GeneratorRuleLine ruleLine : matchRuleLines) {
				if (StringUtils.equals("F", ruleLine.getDsType())) {
					FixedStringRuleLine fixRuleLine = (FixedStringRuleLine) ruleLine;
					rightIdLength += fixRuleLine.getFixedString().length();
				} else if (StringUtils.equals("D", ruleLine.getDsType())) {
					DateRuleLine dateRuleLine = (DateRuleLine) ruleLine;
					rightIdLength += dateRuleLine.getDateFormat().length();
				} else if (StringUtils.equals("S", ruleLine.getDsType())) {
					SequenceRuleLine seqRuleLine = (SequenceRuleLine) ruleLine;
					int seqLength = seqRuleLine.getSizee().intValue();
					rightIdLength += seqLength;
				}
			}
			// 长度不一致,匹配失败
			if (rightIdLength != carrierId.length()) {
				return false;
			}

			//校验每一条编码规则明细
			int cursor = 0;
			for (int i = 0; i < matchRuleLines.size(); i++) {
				GeneratorRuleLine ruleLine = matchRuleLines.get(i);
				if (StringUtils.equals("F", ruleLine.getDsType())) {
					FixedStringRuleLine fixRuleLine = (FixedStringRuleLine) ruleLine;
					int fixLength = fixRuleLine.getFixedString().length();
					// 将固定值部分字符截取出来
					String idSegment = carrierId.substring(cursor, cursor + fixLength);
					if (!StringUtils.equals(idSegment, fixRuleLine.getFixedString())) {
						result = false;
						break;
					}
					// 校验通过将游标往后移动
					cursor += fixLength;
				} else if (StringUtils.equals("D", ruleLine.getDsType())) {
					// 日期暂时只能校验java时间格式化字母
					DateRuleLine dateRuleLine = (DateRuleLine) ruleLine;
					int dateLength = dateRuleLine.getDateFormat().length();
					// 将日期部分取出来
					String idSegment = carrierId.substring(cursor, cursor + dateLength);
					String formatString = dateRuleLine.getDateFormat();

					//根据SimpleDateFormat校验合法性
					SimpleDateFormat formatter = new SimpleDateFormat(formatString);
					formatter.setLenient(false);
					try {
						formatter.parse(idSegment);
					} catch (ParseException e) {
						result = false;
						break;
					}
					if (formatString.length() != idSegment.length()) {
						result = false;
						break;
					}

					cursor += dateLength;
				} else if (StringUtils.equals("S", ruleLine.getDsType())) {
					// 序列号暂时只能校验数字类型的序列号，后面再补充
					SequenceRuleLine seqRuleLine = (SequenceRuleLine) ruleLine;
					int seqLength = seqRuleLine.getSizee().intValue();
					// 将序列号部分取出来
					String idSegment = carrierId.substring(cursor, cursor + seqLength);
					long idValue = NumberUtils.toLong(idSegment);
					long min = NumberUtils.toLong(seqRuleLine.getMin());
					long max = NumberUtils.toLong(seqRuleLine.getMax());
					if (!(idValue >= min && idValue <= max)) {
						result = false;
						break;
					}
					cursor += seqLength;
				}
			}
			return result;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 生成ComponentUnitId
	 * @param unit
	 * @param transType
	 * @param initParams
	 * @param idNum
	 * @param sc
	 * @return
	 */
	@Deprecated
	public List<String> generateComponentUnitId(ComponentUnit unit, Map<String, Object> initParams,
			int idNum, SessionContext sc) throws ClientException{
		List<String> componentIds = componentManager.generateComponentIds(unit, initParams, idNum, sc);
		return componentIds;
	}
	
	/**
	 * 生成在制品批次
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	@Override
	public Lot newLotStartByWorkOrder(PvcNewLotCdiContext context, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			
			WorkOrder workOrder = context.getWorkOrder();
			WorkOrderLot workOrderLot = context.getWorkOrderLot();
			boolean identityComponent = context.isIdentityComponent();
			
			Part part = null;
			if (workOrder.getPartVersion() == null) {
				part = (Part) prdManager.getActivePart(sc.getOrgRrn(), workOrder.getPartName(), true);
			} else {
				part = prdManager.getPartById(sc.getOrgRrn(), workOrder.getPartName(), workOrder.getPartVersion());
			}
			if (part == null) {
				throw new ClientException("lot.part_not_found");
			}
			
			Lot lot = new Lot();
			lot.setOrgRrn(sc.getOrgRrn());
			lot.setIsActive(true);
			lot.setCreatedBy(sc.getUserName());
			lot.setCreated(new Date());
			lot.setUpdatedBy(sc.getUserName());
			lot.setCurSeq(sc.getTransRrn());
			lot.setOperator1(sc.getUserName());
			lot.setPartType(part.getMaterialType());
			lot.setMainMatType(part.getMainMatType());
			lot.setSubMatType(part.getSubMatType());
			lot.setWoId(workOrder.getDocId());
			lot.setMainQty(workOrderLot.getMainQty());
			lot.setSubQty(workOrderLot.getSubQty());
			lot.setEquipmentId(workOrderLot.getEquipmentId());
			lot.setPartRrn(part.getObjectRrn());
			lot.setPartName(workOrder.getPartName());
			lot.setPartVersion(workOrder.getPartVersion());
			lot.setPartDesc(part.getDescription());
			lot.setCustomerCode(workOrder.getCustomerCode());
			lot.setCustomerOrder(workOrder.getCustomerOrder());
			lot.setCustomerPartId(workOrder.getCustomerPartId());
			lot.setPriority(workOrder.getPriority());
			lot.setGrade1(workOrder.getGrade1());
			lot.setGrade2(workOrder.getGrade2());
			lot.setLotType(StringUtils.defaultIfEmpty(workOrderLot.getLotType(), workOrder.getWorkOrderType()));
			lot.setLineId(workOrder.getLineId());
			lot.setPlanStartDate(workOrderLot.getPlanStartDate() != null ? workOrderLot.getPlanStartDate()
					: workOrder.getPlanStartDate());
			lot.setPlanEndDate(
					workOrderLot.getPlanEndDate() != null ? workOrderLot.getPlanEndDate() : workOrder.getPlanEndDate());
			lot.setRequireDate(workOrder.getRequireDate());
			lot.setPreTransType(LotStateMachine.TRANS_SCHEDLOT);
			lot.setComClass(LotStateMachine.COMCLASS_WIP);
			lot.setState(LotStateMachine.STATE_WAIT);
			lot.setLotComment(workOrder.getComments());
			
			// SubUnitType通过系统参数配置，分别对应单片追溯和花篮级追溯
			boolean trackByComponent = PvcCode.isTrackByComponent(sc.getOrgRrn(), sysParamManager);
			lot.setSubUnitType(trackByComponent ? ProcessUnit.UNIT_TYPE_COMPONENT : ProcessUnit.UNIT_TYPE_QTY);
			
			// 生成批号
			if (!StringUtils.isEmpty(workOrderLot.getLotId())) {
				lot.setLotId(workOrderLot.getLotId());
			} else {
				PvcGenerateLotIdAction action = pvcCdiManager.getGenerateLotIdActions().get(0);
				String lotId = action.generateLotIdByRule(lot, sc);
				lot.setLotId(lotId);
				workOrderLot.setLotId(lotId);
			}
			Process process = prdManager.getPartProcess(part);
			if (process == null) {
				throw new ClientException("no process found!");
			}
			lot.setProcessRrn(process.getObjectRrn());
			lot.setProcessName(process.getName());
			lot.setProcessVersion(process.getVersion());
			
			if (!StringUtil.isEmpty(workOrderLot.getDurableId())) {
				Carrier carrier = durableManager.getCarrierById(sc.getOrgRrn(), workOrderLot.getDurableId(), true);
				Lot carrierLot = carrierLotManager.getLotByCarrierId(sc.getOrgRrn(), workOrderLot.getDurableId());
				if (carrierLot != null) {
					// 载具中存在脏数据
					deassignCarrierLot(carrier, carrierLot, sc);
					carrier = durableManager.getCarrierById(sc.getOrgRrn(), workOrderLot.getDurableId());
				}
				lot = assignCarrierLot(carrier, lot, sc);
			}
			
			lot = lotManager.saveLot(lot);
			
			Long processInstanceRrn = prdManager.startProcess(part.getObjectRrn(), lot.getObjectRrn(), lot.getLotParameters());
			lot.setProcessInstanceRrn(processInstanceRrn);
			
			Step step = null;
			if (!StringUtil.isEmpty(workOrderLot.getEquipmentId())) {
				step = getStepByEquipmentIdCache(sc.getOrgRrn(), workOrderLot.getEquipmentId());
			} else {
				//获取第一步工序
				step = getFirstStepCache(sc.getOrgRrn());
			}
			if (step == null) {
				throw new ClientException("no step found!");
			}						
			lot.setStepRrn(step.getObjectRrn());
			lot.setStepName(step.getName());
			lot.setStepDesc(step.getDescription());
			lot.setStepVersion(step.getVersion());
			lot = lotManager.saveLot(lot);
			
			LotHis lotHis = new LotHis(lot, sc);
			lotHis.setTransType(LotStateMachine.TRANS_NEWLOTSTART);
			lotManager.getEntityManager().persist(lotHis);
			
			workOrderLot.setState(WorkOrderLot.STATUS_STARTED); //状态改为STARTED
			if (workOrderLot.getObjectRrn() == null) {
				lotManager.getEntityManager().persist(workOrderLot);
			} else {
				lotManager.getEntityManager().merge(workOrderLot);
			}
			
			// 生成Component数据
			if (identityComponent) {
				List<ComponentUnit> components = new ArrayList<ComponentUnit>();
				ComponentUnit componUnit = null;
				for (int i = 0; i < workOrderLot.getMainQty().intValue(); i++) {
					componUnit = new ComponentUnit();
					components.add(componUnit);
				}
				
				MLot mLot = null;
				if (!StringUtils.isEmpty(workOrderLot.getSourceMLotId())) {
					mLot = mmManager.getMLotByMLotId(sc.getOrgRrn(), workOrderLot.getSourceMLotId());
				}
				
				PvcGenerateComponentsByLotAction action = pvcCdiManager.getGenerateComponentActions().get(0);
				context.setmLot(mLot);
				context.setLot(lot);
				context.setComponentUnits(components);
				//context.setRootLot(isRootLot);
				
				action.identityComponents(context, sc);
			}
			return lot;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 根据批次生成Component信息,生成Wafer的几个场景:
	 * <br/>1,自动化从硅片料盒经导片机进制绒设备开始作业
	 * <br/>2,自动化设备作业前缺账补片(LotInfoDownload没账时生成,MES重新生成ID)
	 * <br/>3,自动化设备作业后丢账补片(设备退料时生成,设备生成ID上报或MES重新生成ID)
	 * <br/>4,手动工单投料生成(暂未支持)
	 * 
	 * @param lot
	 * 			wafer的主批
	 * @param componentUnits
	 * 			待生成的wafer信息
	 * @param mLot
	 * 			原物料批次
	 * @param generateParaMap
	 * 			生成ID所需要的额外参数
	 * @param sc
	 * @throws ClientException
	 */
	@Override
	public Lot identityComponents(Lot lot, List<ComponentUnit> componentUnits, MLot mLot,
			Map<String, Object> generateParaMap, SessionContext sc) throws ClientException {
		try {
			if (CollectionUtils.isEmpty(componentUnits)) {
				// componentUnits不能为空
				return lot;
			}
			
			Vendor vendor = null;
			if (mLot != null) {
				if (!StringUtils.isEmpty(mLot.getPartnerCode())) {
					List<Vendor> vendorList = adManager.getEntityList(sc.getOrgRrn(), Vendor.class, Integer.MAX_VALUE,
							"name='" + mLot.getPartnerCode() + "'", "");
					if (!CollectionUtils.isEmpty(vendorList)) {
						vendor = vendorList.get(0);
					}
				}
			}
			
			sc.buildTransInfo();
			List<String> componentIds = new ArrayList<String>();
			ComponentUnit tempComponent = new ComponentUnit();
			// 没有指定ComponentUnit ID，则需要用ID规则生成
			if (StringUtils.isEmpty(componentUnits.get(0).getComponentId())) {
				componentIds = pvcCdiManager.getGenerateComponentIdAction().get(0)
						.generateComponentUnitId(tempComponent, generateParaMap, componentUnits.size(), sc);
			}
			
			List<ProcessUnit> processUnits = new ArrayList<ProcessUnit>();
			int position = 0;
			for (ComponentUnit comp : componentUnits) {
				String componentId = comp.getComponentId();
					// 指定了WaferID，校验WaferID是否已存在
				if (!StringUtils.isEmpty(componentId)) {
					ComponentUnit existComp = componentManager.getComponentByComponentId(sc.getOrgRrn(), componentId, false);
					if (existComp != null) {
						throw new ClientParameterException("wip.component_id_repet", componentId);
					}
				} else {
					componentId = componentIds.get(position);
				}
				
				ComponentUnit component = new ComponentUnit();
				component.setIsActive(true);
				component.setCreatedBy(sc.getUserName());
				component.setCreated(sc.getTransTime());
				component.setOrgRrn(sc.getOrgRrn());
				component.setComponentId(componentId);
				component.setComponentType(lot.getLotType());
				component.setDurable(lot.getDurable());
				component.setPosition(String.valueOf(position + 1));
				component.setParentUnitRrn(lot.getObjectRrn());
				component.setLineId(lot.getLineId());
				
				if (mLot != null) {
					component.setSubstrateId1(mLot.getmLotId());
				}
				if (vendor != null) {
					component.setCustomerCode(vendor.getName());
					component.setCustomerPartId(vendor.getReserved3());
				}
				
				component.setWoId(lot.getWoId());
				component.setEquipmentId(lot.getEquipmentId());
				component.setSubstrateId2(lot.getSubstrateId2());
				
				component.setPartRrn(lot.getPartRrn());
				component.setPartName(lot.getPartName());
				component.setPartVersion(lot.getPartVersion());
				component.setPartType(lot.getPartType());
				component.setPartDesc(lot.getPartDesc());
				
				component.setComClass(LotStateMachine.COMCLASS_WIP);
				component.setState(LotStateMachine.STATE_WAIT);
				component.setParentUnitRrn(lot.getObjectRrn());
				component.setLastMainLotId(lot.getLotId());
				component.setMainQty(BigDecimal.ONE);
				component.setSubUnitType(lot.getSubUnitType());
				
				//TODO RootLotRrn为原始批次的RRN,可追溯到当时的源物料批次信息(LotSource)
				component.setRootLotRrn(comp.getRootLotRrn());
				processUnits.add(component);
				componentManager.getEntityManager().persist(component);
				
				position++;
			}
			
			componentManager.getEntityManager().flush();
			componentManager.getEntityManager().clear();
			
			StringBuffer sql = new StringBuffer(" INSERT INTO ComponentUnitProcessHis ");
			sql.append(" ( ");
			sql.append(" orgRrn ");
			sql.append(" ,isActive ");
			sql.append(" ,created ");
			sql.append(" ,createdBy ");
			sql.append(" ,woId ");
			sql.append(" ,partName ");
			sql.append(" ,partDesc ");
			sql.append(" ,partType ");
			sql.append(" ,componentId ");
			sql.append(" ,sourceMLotId ");
			sql.append(" ,durable ");
			sql.append(" ,lineId ");
			sql.append(" ,stepName ");
			sql.append(" ,equipmentId ");
			sql.append(" ,transType ");
			sql.append(" ,transTime ");
			sql.append(" ,hisSeq ");
			sql.append(" ,hisSeqNo ");
			sql.append(" )  ");

			sql.append(" SELECT ");
			sql.append(" orgRrn ");
			sql.append(" ,isActive ");
			sql.append(" ,created ");
			sql.append(" ,createdBy ");
			sql.append(" ,woId ");
			sql.append(" ,partName ");
			sql.append(" ,partDesc ");
			sql.append(" ,partType ");
			sql.append(" ,componentId ");
			sql.append(" ,substrateId1 ");
			sql.append(" ,:durable ");
			sql.append(" ,:lineId ");
			sql.append(" ,:stepName ");
			sql.append(" ,:equipmentId ");
			sql.append(" ,:transType");
			sql.append(" ,:transTime");
			sql.append(" ,:hisSeq");
			sql.append(" ,:hisSeqNo");
			sql.append(" FROM ComponentUnit ");
			sql.append(" WHERE ");
			sql.append(" parentUnitRrn = :parentUnitRrn ");
			
			Query query = em.createQuery(sql.toString());
			query.setParameter("durable", lot.getDurable());
			query.setParameter("lineId", lot.getLineId());
			query.setParameter("stepName", lot.getStepName());
			query.setParameter("equipmentId", lot.getEquipmentId());
			query.setParameter("transType", LotStateMachine.TRANS_IDENTIFY_COMPONENT);
			query.setParameter("transTime", sc.getTransTime());
			query.setParameter("hisSeq", sc.getTransRrn());
			query.setParameter("hisSeqNo", sc.getTransSeqNo());
			query.setParameter("parentUnitRrn", lot.getObjectRrn());
			query.executeUpdate();
			
			if (processUnits.size() == 0) {
				throw new ClientException("error.lot_can_not_identity");
			}
			
			lot.setSubProcessUnit(processUnits);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
		return lot;
	}
	
	/**
	 * 获取Bom详细，默认找工单Bom，如果没有再找产品Bom
	 * @param workOrder
	 * @param sc
	 * @return
	 * @throws ClientException
	 */
	@Override
	public List<AbstractBomLine> getBomLines(WorkOrder workOrder, SessionContext sc) throws ClientException {
		try {
			List<AbstractBomLine> bomLines = new ArrayList<AbstractBomLine>();
			// 工单BOM
			List<WorkOrderBomLine> woBomLines = ppManager.getWorkOrderBomLines(workOrder, sc);
			if (!CollectionUtils.isEmpty(woBomLines)) {
				bomLines = woBomLines.stream().map( line -> (AbstractBomLine)line).collect(Collectors.toList());
			} else {
				// 产品BOM
				Bom partBom = mmManager.getActiveBom(sc.getOrgRrn(), workOrder.getPartName(), null, null);
				if (partBom != null) {
					List<BomLine> partBomLines = partBom.getBomLines();
					bomLines = partBomLines.stream().map( line -> (AbstractBomLine)line).collect(Collectors.toList());
				}
			}
			return bomLines;
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 清理PVC_LOT_EQUIPMENT_QUEUE的残留数据
	 * @param days
	 * 		清理多少天以前的数据
	 * @throws Exception
	 */
	public void cleanLotQueue(String days) throws ClientException {
		try {
			StringBuffer sql = new StringBuffer("DELETE FROM PVC_LOT_EQUIPMENT_QUEUE WHERE");
			sql.append(" TRACK_IN_TIME > :trackInTime ");
			
			Query query = em.createNativeQuery(sql.toString());
			
			// 7天以前的数据都清理掉
			Calendar calendar = Calendar.getInstance();
			int day = Integer.valueOf(StringUtils.defaultIfEmpty(days, "7"));
			calendar.add(Calendar.DAY_OF_MONTH, -day);
			query.setParameter("trackInTime", calendar.getTime());
			query.executeUpdate();
		}catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
	}
	
	/**
	 * 批量保存Component加工历史
	 * @param componentUnitList 待保存历史的Component信息
	 * @param transType 事务类型
	 * @param sc
	 * @throws ClientException
	 */
	public void saveComponentUnitProcessHisByList(List<ComponentUnit> componentUnitList, String transType, SessionContext sc) throws ClientException {
		for (ComponentUnit componentUnit : componentUnitList) {
			ComponentUnitProcessHis componentUnitProcessHis = new ComponentUnitProcessHis(componentUnit, sc);
			componentUnitProcessHis.setTransType(transType);
			em.persist(componentUnitProcessHis);;
		}
	}
	
}
